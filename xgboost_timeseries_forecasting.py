"""
XGBoost时间序列预测模型
预测埃塞俄比亚咖啡出口"IN MILLION USD"
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("=== XGBoost时间序列预测模型 ===")
print("预测目标: IN MILLION USD")
print("主要特征: sales price（usd/lb）和 Exchange(Domestic currency per US Dollar)")

class XGBoostTimeSeriesPredictor:
    """XGBoost时间序列预测器"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.model = None
        self.feature_names = []
        
    def load_and_clean_data(self, file_path):
        """加载和清理数据"""
        print("\n1. 数据加载和清理")
        print("-" * 50)
        
        try:
            df = pd.read_csv(file_path, encoding='gbk')
        except:
            df = pd.read_csv(file_path, encoding='utf-8')
        
        # 转换日期列
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        print(f"✓ 数据加载完成: {df.shape}")
        print(f"✓ 时间范围: {df.index.min()} 到 {df.index.max()}")
        
        # 检查目标列和特征列
        target_col = 'IN MILLION USD'
        feature_cols = ['sales price（usd/lb）', 'Exchange(Domestic currency per US Dollar)']
        
        print(f"\n数据列信息:")
        for col in [target_col] + feature_cols:
            if col in df.columns:
                missing_count = df[col].isnull().sum()
                print(f"  {col}: 缺失值 {missing_count} ({missing_count/len(df)*100:.1f}%)")
            else:
                print(f"  ❌ 列 '{col}' 不存在")
        
        # 处理缺失值
        print(f"\n处理缺失值...")
        df_clean = df.copy()
        
        # 前向填充和后向填充
        for col in [target_col] + feature_cols:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].fillna(method='ffill').fillna(method='bfill')
        
        # 异常值处理
        print(f"处理异常值...")
        for col in [target_col] + feature_cols:
            if col in df_clean.columns:
                Q1 = df_clean[col].quantile(0.25)
                Q3 = df_clean[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers_count = ((df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)).sum()
                if outliers_count > 0:
                    print(f"  {col}: 发现 {outliers_count} 个异常值，进行截断处理")
                    df_clean[col] = df_clean[col].clip(lower=lower_bound, upper=upper_bound)
        
        print(f"✓ 数据清理完成: {df_clean.shape}")
        return df_clean
    
    def create_time_series_features(self, df):
        """创建时间序列特征"""
        print("\n2. 特征工程")
        print("-" * 50)
        
        target_col = 'IN MILLION USD'
        feature_cols = ['sales price（usd/lb）', 'Exchange(Domestic currency per US Dollar)']
        
        # 创建特征DataFrame
        features_df = pd.DataFrame(index=df.index)
        
        # 1. 基础特征
        for col in feature_cols:
            if col in df.columns:
                features_df[f'{col}_current'] = df[col]
        
        # 2. 时间特征
        features_df['month'] = df.index.month
        features_df['quarter'] = df.index.quarter
        features_df['year'] = df.index.year
        features_df['month_sin'] = np.sin(2 * np.pi * features_df['month'] / 12)
        features_df['month_cos'] = np.cos(2 * np.pi * features_df['month'] / 12)
        
        # 3. 滞后特征
        lag_periods = [1, 2, 3, 6, 12]
        for col in feature_cols + [target_col]:
            if col in df.columns:
                for lag in lag_periods:
                    features_df[f'{col}_lag_{lag}'] = df[col].shift(lag)
        
        # 4. 滚动统计特征
        windows = [3, 6, 12]
        for col in feature_cols + [target_col]:
            if col in df.columns:
                for window in windows:
                    features_df[f'{col}_rolling_mean_{window}'] = df[col].rolling(window=window).mean()
                    features_df[f'{col}_rolling_std_{window}'] = df[col].rolling(window=window).std()
                    features_df[f'{col}_rolling_min_{window}'] = df[col].rolling(window=window).min()
                    features_df[f'{col}_rolling_max_{window}'] = df[col].rolling(window=window).max()
        
        # 5. 差分特征
        for col in feature_cols + [target_col]:
            if col in df.columns:
                features_df[f'{col}_diff_1'] = df[col].diff(1)
                features_df[f'{col}_diff_12'] = df[col].diff(12)
                features_df[f'{col}_pct_change_1'] = df[col].pct_change(1)
                features_df[f'{col}_pct_change_12'] = df[col].pct_change(12)
        
        # 6. 交互特征
        if all(col in df.columns for col in feature_cols):
            features_df['price_exchange_ratio'] = df[feature_cols[0]] / df[feature_cols[1]]
            features_df['price_exchange_product'] = df[feature_cols[0]] * df[feature_cols[1]]
        
        # 7. 趋势特征
        features_df['trend'] = np.arange(len(features_df))
        features_df['trend_squared'] = features_df['trend'] ** 2
        
        # 8. 季节性指示变量
        features_df['is_peak_season'] = features_df['month'].isin([5, 6]).astype(int)  # 5-6月高峰
        features_df['is_low_season'] = features_df['month'].isin([12, 1]).astype(int)  # 12-1月低谷
        
        # 删除包含NaN的行
        features_df = features_df.dropna()
        target_values = df[target_col].loc[features_df.index]
        
        self.feature_names = features_df.columns.tolist()
        
        print(f"✓ 特征工程完成:")
        print(f"  - 创建了 {len(self.feature_names)} 个特征")
        print(f"  - 有效样本数: {len(features_df)}")
        print(f"  - 特征类别:")
        print(f"    * 基础特征: {len(feature_cols)} 个")
        print(f"    * 时间特征: 7 个")
        print(f"    * 滞后特征: {len(lag_periods) * (len(feature_cols) + 1)} 个")
        print(f"    * 滚动统计: {len(windows) * 4 * (len(feature_cols) + 1)} 个")
        print(f"    * 差分特征: {4 * (len(feature_cols) + 1)} 个")
        print(f"    * 其他特征: 7 个")
        
        return features_df, target_values
    
    def train_model(self, X_train, y_train, X_test=None, y_test=None):
        """训练XGBoost模型"""
        print("\n3. XGBoost模型训练")
        print("-" * 50)
        
        # XGBoost参数
        xgb_params = {
            'objective': 'reg:squarederror',
            'max_depth': 6,
            'learning_rate': 0.1,
            'n_estimators': 200,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'n_jobs': -1
        }
        
        print(f"XGBoost参数:")
        for key, value in xgb_params.items():
            print(f"  {key}: {value}")
        
        # 创建和训练模型
        self.model = xgb.XGBRegressor(**xgb_params)
        
        # 如果有测试集，使用早停
        if X_test is not None and y_test is not None:
            self.model.fit(
                X_train, y_train,
                eval_set=[(X_test, y_test)],
                early_stopping_rounds=20,
                verbose=False
            )
        else:
            self.model.fit(X_train, y_train)
        
        print(f"✓ 模型训练完成")
        
        # 特征重要性
        feature_importance = self.model.feature_importances_
        importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        print(f"\n前10个最重要特征:")
        for i, (_, row) in enumerate(importance_df.head(10).iterrows()):
            print(f"  {i+1:2d}. {row['feature']}: {row['importance']:.4f}")
        
        return importance_df
    
    def predict(self, X):
        """预测"""
        if self.model is None:
            raise ValueError("模型尚未训练")
        return self.model.predict(X)
    
    def calculate_metrics(self, y_true, y_pred):
        """计算评估指标"""
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)
        
        # MAPE和SMAPE
        mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
        smape = np.mean(2 * np.abs(y_true - y_pred) / (np.abs(y_true) + np.abs(y_pred))) * 100
        
        # 方向准确性
        if len(y_true) > 1:
            direction_accuracy = np.mean(np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred))) * 100
        else:
            direction_accuracy = 0
        
        # 趋势相关性
        trend_corr = np.corrcoef(y_true, y_pred)[0, 1]
        
        return {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R2': r2,
            'MAPE': mape,
            'SMAPE': smape,
            'Direction_Accuracy': direction_accuracy,
            'Trend_Correlation': trend_corr
        }

# 主执行流程
if __name__ == "__main__":
    # 创建预测器
    predictor = XGBoostTimeSeriesPredictor()
    
    # 1. 数据加载和清理
    df = predictor.load_and_clean_data('2005(2013)-2017(2025) monthly data(1).csv')
    
    # 2. 特征工程
    X, y = predictor.create_time_series_features(df)
    
    print(f"\n数据准备完成:")
    print(f"  特征矩阵形状: {X.shape}")
    print(f"  目标变量形状: {y.shape}")
    
    # 3. 数据分割
    print("\n4. 数据分割")
    print("-" * 50)

    # 时间序列分割 - 保持时间顺序
    test_size = 0.2
    split_index = int(len(X) * (1 - test_size))

    X_train = X.iloc[:split_index]
    X_test = X.iloc[split_index:]
    y_train = y.iloc[:split_index]
    y_test = y.iloc[split_index:]

    print(f"训练集: {len(X_train)} 样本 ({X_train.index[0]} 到 {X_train.index[-1]})")
    print(f"测试集: {len(X_test)} 样本 ({X_test.index[0]} 到 {X_test.index[-1]})")

    # 4. 模型训练
    importance_df = predictor.train_model(X_train, y_train, X_test, y_test)

    # 5. 预测和评估
    print("\n5. 模型预测和评估")
    print("-" * 50)

    # 训练集预测
    train_pred = predictor.predict(X_train)
    train_metrics = predictor.calculate_metrics(y_train, train_pred)

    # 测试集预测
    test_pred = predictor.predict(X_test)
    test_metrics = predictor.calculate_metrics(y_test, test_pred)

    print("📊 训练集评估结果:")
    for metric, value in train_metrics.items():
        print(f"   {metric}: {value:.4f}")

    print("\n📊 测试集评估结果:")
    for metric, value in test_metrics.items():
        print(f"   {metric}: {value:.4f}")

    # 6. 结果可视化
    print("\n6. 结果可视化")
    print("-" * 50)

    # 创建综合可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('XGBoost时间序列预测结果分析', fontsize=16, fontweight='bold')

    # 1. 时间序列预测结果
    ax1 = axes[0, 0]
    train_dates = X_train.index
    test_dates = X_test.index

    ax1.plot(train_dates, y_train, 'b-', label='训练集真实值', linewidth=2)
    ax1.plot(train_dates, train_pred, 'b--', label='训练集预测值', linewidth=1, alpha=0.7)
    ax1.plot(test_dates, y_test, 'r-', label='测试集真实值', linewidth=2)
    ax1.plot(test_dates, test_pred, 'r--', label='测试集预测值', linewidth=2)
    ax1.set_title('时间序列预测结果')
    ax1.set_xlabel('时间')
    ax1.set_ylabel('IN MILLION USD')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 测试集散点图
    ax2 = axes[0, 1]
    ax2.scatter(y_test, test_pred, alpha=0.6, s=50)
    ax2.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    ax2.set_xlabel('真实值')
    ax2.set_ylabel('预测值')
    ax2.set_title(f'测试集预测准确性 (R²={test_metrics["R2"]:.3f})')
    ax2.grid(True, alpha=0.3)

    # 3. 残差分析
    ax3 = axes[0, 2]
    test_residuals = y_test - test_pred
    ax3.plot(test_dates, test_residuals, 'o-', markersize=4)
    ax3.axhline(y=0, color='r', linestyle='--', linewidth=2)
    ax3.set_title('测试集残差分析')
    ax3.set_xlabel('时间')
    ax3.set_ylabel('残差')
    ax3.grid(True, alpha=0.3)

    # 4. 特征重要性
    ax4 = axes[1, 0]
    top_features = importance_df.head(15)
    ax4.barh(range(len(top_features)), top_features['importance'])
    ax4.set_yticks(range(len(top_features)))
    ax4.set_yticklabels(top_features['feature'], fontsize=8)
    ax4.set_title('前15个最重要特征')
    ax4.set_xlabel('重要性')
    ax4.grid(True, alpha=0.3)

    # 5. 残差分布
    ax5 = axes[1, 1]
    ax5.hist(test_residuals, bins=15, alpha=0.7, edgecolor='black')
    ax5.set_title('测试集残差分布')
    ax5.set_xlabel('残差')
    ax5.set_ylabel('频次')
    ax5.grid(True, alpha=0.3)

    # 6. 评估指标总结
    ax6 = axes[1, 2]
    metrics_text = f"""XGBoost预测性能总结

测试集指标:
• MAPE: {test_metrics['MAPE']:.2f}%
• RMSE: {test_metrics['RMSE']:.2f}
• R²: {test_metrics['R2']:.3f}
• 方向准确性: {test_metrics['Direction_Accuracy']:.1f}%
• 趋势相关性: {test_metrics['Trend_Correlation']:.3f}

模型特点:
• 特征数量: {len(predictor.feature_names)}
• 训练样本: {len(X_train)}
• 测试样本: {len(X_test)}
• 主要特征: 价格和汇率"""

    ax6.text(0.1, 0.5, metrics_text, transform=ax6.transAxes,
             fontsize=10, verticalalignment='center',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    ax6.axis('off')

    plt.tight_layout()
    plt.show()

    # 7. 总结报告
    print("\n7. XGBoost预测总结报告")
    print("=" * 60)

    print(f"\n🎯 模型性能:")
    print(f"   - MAPE: {test_metrics['MAPE']:.2f}% ({'优秀' if test_metrics['MAPE'] < 10 else '良好' if test_metrics['MAPE'] < 20 else '可接受' if test_metrics['MAPE'] < 30 else '需改进'})")
    print(f"   - R²: {test_metrics['R2']:.3f} ({'优秀' if test_metrics['R2'] > 0.8 else '良好' if test_metrics['R2'] > 0.6 else '可接受' if test_metrics['R2'] > 0.4 else '需改进'})")
    print(f"   - 方向准确性: {test_metrics['Direction_Accuracy']:.1f}% ({'优秀' if test_metrics['Direction_Accuracy'] > 80 else '良好' if test_metrics['Direction_Accuracy'] > 70 else '可接受' if test_metrics['Direction_Accuracy'] > 60 else '需改进'})")
    print(f"   - 趋势相关性: {test_metrics['Trend_Correlation']:.3f}")

    print(f"\n📊 关键发现:")
    print(f"   - 最重要特征: {importance_df.iloc[0]['feature']}")
    print(f"   - 特征重要性: {importance_df.iloc[0]['importance']:.4f}")
    print(f"   - 价格相关特征在前10中的数量: {sum('price' in feat.lower() for feat in importance_df.head(10)['feature'])}")
    print(f"   - 汇率相关特征在前10中的数量: {sum('exchange' in feat.lower() for feat in importance_df.head(10)['feature'])}")

    print(f"\n💡 模型优势:")
    print(f"   ✅ 使用了 {len(predictor.feature_names)} 个特征进行建模")
    print(f"   ✅ 包含滞后特征、滚动统计、差分等时间序列特征")
    print(f"   ✅ XGBoost能够自动处理特征交互和非线性关系")
    print(f"   ✅ 模型训练快速，预测效率高")
    print(f"   ✅ 提供特征重要性分析，模型可解释性强")

    print(f"\n🚀 实际应用建议:")
    print(f"   • 重点关注价格和汇率变化对出口的影响")
    print(f"   • 利用季节性模式进行出口规划")
    print(f"   • 定期更新模型以适应市场变化")
    print(f"   • 结合外部经济指标进一步提升预测精度")

    print(f"\n✅ XGBoost时间序列预测完成!")
    print(f"   模型已训练完成，可用于埃塞俄比亚咖啡出口预测")
