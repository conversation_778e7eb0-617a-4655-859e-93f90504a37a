{"cells": [{"cell_type": "code", "execution_count": 1, "id": "be0a548b-d8c2-4426-b325-b821320a0344", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Random Forest - MSE: 5841.5261, RMSE: 76.4299, MAE: 48.4640, R2: 0.3012\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import matplotlib.pyplot as plt\n", "\n", "# 1. 读取数据\n", "file_path = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "df = pd.read_csv(file_path, parse_dates=['datetime'], index_col='datetime', encoding='gb18030')\n", "df.sort_index(inplace=True)\n", "\n", "# 2. 选择变量并删除末尾含空值行\n", "cols = [\n", "    'sales price（usd/lb）',\n", "    'Exchange(Domestic currency per US Dollar)',\n", "    'IN MILLION USD'\n", "]\n", "df = df[cols].copy()\n", "df.dropna(subset=cols, how='any', inplace=True)\n", "\n", "# 3. 特征工程：添加滞后特征和周期哑变量\n", "# 3A. 目标变量滞后\n", "for lag in [1, 2, 3, 6, 12]:\n", "    df[f'y_lag{lag}'] = df['IN MILLION USD'].shift(lag)\n", "\n", "# 3B. 外生变量滞后\n", "exogs = ['sales price（usd/lb）', 'Exchange(Domestic currency per US Dollar)']\n", "for var in exogs:\n", "    for lag in [1, 2, 3]:\n", "        df[f'{var}_lag{lag}'] = df[var].shift(lag)\n", "\n", "# 3C. 月份哑变量\n", "df['month'] = df.index.month\n", "df = pd.get_dummies(df, columns=['month'], prefix='m', drop_first=True)\n", "\n", "# 4. 删除滞后产生的空值行\n", "df.dropna(inplace=True)\n", "\n", "# 5. 构建特征和标签\n", "feature_cols = [c for c in df.columns if c != 'IN MILLION USD']\n", "X = df[feature_cols].values\n", "y = df['IN MILLION USD'].values\n", "\n", "# 6. 划分训练集/测试集 (8:2)\n", "split = int(len(df) * 0.8)\n", "X_train, X_test = X[:split], X[split:]\n", "y_train, y_test = y[:split], y[split:]\n", "\n", "# 7. 归一化 (仅用训练集拟合)\n", "scaler_x = MinMaxScaler().fit(X_train)\n", "scaler_y = MinMaxScaler().fit(y_train.reshape(-1, 1))\n", "X_train_scaled = scaler_x.transform(X_train)\n", "X_test_scaled = scaler_x.transform(X_test)\n", "\n", "# 8. 随机森林训练\n", "rf = RandomForestRegressor(\n", "    n_estimators=100,\n", "    max_depth=None,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "rf.fit(X_train_scaled, y_train)\n", "\n", "# 9. 预测与评估\n", "y_pred = rf.predict(X_test_scaled)\n", "mse  = mean_squared_error(y_test, y_pred)\n", "rmse = np.sqrt(mse)\n", "mae  = mean_absolute_error(y_test, y_pred)\n", "r2   = r2_score(y_test, y_pred)\n", "print(f\"Random Forest - MSE: {mse:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}, R2: {r2:.4f}\")\n", "\n", "# 10. 可视化预测 vs 实际\n", "plt.figure(figsize=(10, 4))\n", "plt.plot(df.index[split:], y_test, label='True')\n", "plt.plot(df.index[split:], y_pred, label='RF Pred')\n", "plt.title('IN MILLION USD: True vs RF Predicted')\n", "plt.xlabel('Date')\n", "plt.ylabel('IN MILLION USD')\n", "plt.xticks(rotation=45)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "03d505ae-5076-4a87-8e97-f0d201ae0320", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}