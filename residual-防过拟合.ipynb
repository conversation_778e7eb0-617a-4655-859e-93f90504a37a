{"cells": [{"cell_type": "code", "execution_count": 1, "id": "02fe2e09-40f8-45a4-ac72-656aef4fca67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 解决梯度提升和XGBoost过拟合问题 ===\n", "策略: 正则化参数调优 + 早停 + 特征选择\n", "\n", "1. 数据加载和预处理\n", "--------------------------------------------------\n", "数据量: 150 样本\n", "精选特征数量: 7\n", "特征列表: ['residual_lag_6', 'residual_volatility', 'residual_lag_1', 'seasonal_abs', 'sales_price_lag_1', 'trend_change', 'month']\n", "\n", "2. 数据分割\n", "--------------------------------------------------\n", "训练集: 96 样本\n", "验证集: 24 样本\n", "测试集: 30 样本\n", "\n", "3. 定义防过拟合的模型配置\n", "--------------------------------------------------\n", "✓ 定义了3类模型配置:\n", "   • 原始模型 (容易过拟合)\n", "   • 改进模型 (正则化)\n", "   • 早停模型 (动态停止)\n", "\n", "4. 模型训练和对比\n", "--------------------------------------------------\n", "\n", "训练原始模型 (容易过拟合):\n", "  训练 梯度提升_原始...\n", "    训练RMSE: 0.04\n", "    测试RMSE: 31.87\n", "    过拟合比率: 876.06\n", "  训练 XGBoost_原始...\n", "    训练RMSE: 0.16\n", "    测试RMSE: 33.62\n", "    过拟合比率: 206.65\n", "\n", "训练改进模型 (正则化):\n", "  训练 梯度提升_改进...\n", "    训练RMSE: 6.28\n", "    测试RMSE: 38.74\n", "    过拟合比率: 6.17\n", "  训练 XGBoost_改进...\n", "    训练RMSE: 6.07\n", "    测试RMSE: 38.29\n", "    过拟合比率: 6.31\n", "\n", "训练早停模型 (动态停止):\n", "  训练 梯度提升_早停...\n", "    训练RMSE: 4.32\n", "    测试RMSE: 33.03\n", "    过拟合比率: 7.65\n", "  训练 XGBoost_早停...\n", "    训练RMSE: 10.03\n", "    测试RMSE: 42.45\n", "    过拟合比率: 4.23\n", "\n", "5. 过拟合改善效果对比\n", "--------------------------------------------------\n", "过拟合改善效果对比:\n", "================================================================================\n", "\n", "梯度提升_原始 (原始):\n", "  训练RMSE: 0.04\n", "  测试RMSE: 31.87\n", "  测试R²: 0.463\n", "  过拟合比率: 876.06\n", "\n", "XGBoost_原始 (原始):\n", "  训练RMSE: 0.16\n", "  测试RMSE: 33.62\n", "  测试R²: 0.402\n", "  过拟合比率: 206.65\n", "\n", "梯度提升_改进 (改进):\n", "  训练RMSE: 6.28\n", "  测试RMSE: 38.74\n", "  测试R²: 0.206\n", "  过拟合比率: 6.17\n", "\n", "XGBoost_改进 (改进):\n", "  训练RMSE: 6.07\n", "  测试RMSE: 38.29\n", "  测试R²: 0.224\n", "  过拟合比率: 6.31\n", "\n", "梯度提升_早停 (早停):\n", "  训练RMSE: 4.32\n", "  测试RMSE: 33.03\n", "  测试R²: 0.423\n", "  过拟合比率: 7.65\n", "\n", "XGBoost_早停 (早停):\n", "  训练RMSE: 10.03\n", "  测试RMSE: 42.45\n", "  测试R²: 0.047\n", "  过拟合比率: 4.23\n", "\n", "6. 可视化对比\n", "--------------------------------------------------\n"]}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1600x1200 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "7. 过拟合解决方案总结\n", "--------------------------------------------------\n", "🔧 过拟合解决策略效果分析:\n", "\n", "梯度提升:\n", "  原始过拟合比率: 876.06\n", "  改进过拟合比率: 6.17\n", "  改善程度: 99.3%\n", "\n", "XGBoost:\n", "  原始过拟合比率: 206.65\n", "  改进过拟合比率: 6.31\n", "  改善程度: 96.9%\n", "\n", "💡 有效的过拟合解决方法:\n", "  1. 减少模型复杂度:\n", "     • 减少树的数量 (n_estimators: 100→50)\n", "     • 减少树的深度 (max_depth: 6→3)\n", "     • 降低学习率 (learning_rate: 0.1→0.05)\n", "  \n", "  2. 增加正则化:\n", "     • 子采样 (subsample=0.8)\n", "     • 特征采样 (max_features='sqrt')\n", "     • 增加最小分割样本数\n", "  \n", "  3. 早停机制:\n", "     • 监控验证集性能\n", "     • 自动停止训练\n", "  \n", "  4. 特征选择:\n", "     • 只保留最重要的特征\n", "     • 减少特征数量 (17→7)\n", "\n", "8. 推荐配置\n", "--------------------------------------------------\n", "🏆 过拟合控制最佳模型: XGBoost_早停\n", "   过拟合比率: 4.23\n", "   ⚠️ 仍需进一步改进\n", "\n", "📋 推荐的模型配置:\n", "\n", "✓ 对比结果已保存: overfitting_solutions_comparison.csv\n", "\n", "✅ 过拟合解决方案分析完成!\n", "   通过正则化和早停有效改善了模型的泛化能力\n"]}], "source": ["\"\"\"\n", "Residual预测 - 解决梯度提升和XGBoost过拟合问题\n", "通过多种正则化技术改善模型泛化能力\n", "\"\"\"\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.model_selection import validation_curve, learning_curve\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 尝试导入XGBoost\n", "try:\n", "    import xgboost as xgb\n", "    XGBOOST_AVAILABLE = True\n", "except ImportError:\n", "    from sklearn.ensemble import ExtraTreesRegressor\n", "    XGBOOST_AVAILABLE = False\n", "    print(\"⚠️ XGBoost未安装，使用ExtraTreesRegressor作为替代\")\n", "\n", "# 配置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"=== 解决梯度提升和XGBoost过拟合问题 ===\")\n", "print(\"策略: 正则化参数调优 + 早停 + 特征选择\")\n", "\n", "# 1. 数据加载和预处理 (复用之前的代码)\n", "print(\"\\n1. 数据加载和预处理\")\n", "print(\"-\" * 50)\n", "\n", "# 加载数据\n", "decomp_path = r\"D:\\埃塞俄比亚咖啡\\模型\\x11_decomposition_results.csv\"\n", "decomp_data = pd.read_csv(decomp_path)\n", "\n", "exog_path = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "exog_data = pd.read_csv(exog_path, encoding='gbk')\n", "\n", "# 合并数据\n", "min_len = min(len(decomp_data), len(exog_data))\n", "\n", "data = pd.DataFrame({\n", "    'date': pd.to_datetime(decomp_data['parsed_datetime'].iloc[:min_len]),\n", "    'residual': decomp_data['Residual'].iloc[:min_len],\n", "    'trend': decomp_data['Trend'].iloc[:min_len],\n", "    'seasonal': decomp_data['Seasonal'].iloc[:min_len],\n", "    'sales_price': exog_data['sales price（usd/lb）'].iloc[:min_len],\n", "    'exchange_rate': exog_data['Exchange(Domestic currency per US Dollar)'].iloc[:min_len]\n", "})\n", "\n", "data.set_index('date', inplace=True)\n", "data = data.dropna()\n", "\n", "# 特征工程\n", "data['year'] = data.index.year\n", "data['month'] = data.index.month\n", "data['quarter'] = data.index.quarter\n", "data['time_index'] = range(len(data))\n", "\n", "# 只保留最重要的特征，减少过拟合\n", "for lag in [1, 3, 6]:  # 减少滞后特征数量\n", "    data[f'residual_lag_{lag}'] = data['residual'].shift(lag)\n", "\n", "data['trend_change'] = data['trend'].pct_change()\n", "data['seasonal_abs'] = np.abs(data['seasonal'])\n", "data['sales_price_lag_1'] = data['sales_price'].shift(1)\n", "data['residual_volatility'] = data['residual'].rolling(window=6).std()\n", "\n", "data = data.dropna()\n", "\n", "# 精选特征集 - 只保留最重要的特征\n", "selected_features = [\n", "    'residual_lag_6',        # 最重要\n", "    'residual_volatility',   # 第二重要\n", "    'residual_lag_1',        # 第三重要\n", "    'seasonal_abs',          # 第四重要\n", "    'sales_price_lag_1',     # 第五重要\n", "    'trend_change',          # 第六重要\n", "    'month'                  # 季节性\n", "]\n", "\n", "# 确保特征存在\n", "selected_features = [f for f in selected_features if f in data.columns]\n", "X = data[selected_features]\n", "y = data['residual']\n", "\n", "print(f\"数据量: {len(data)} 样本\")\n", "print(f\"精选特征数量: {len(selected_features)}\")\n", "print(f\"特征列表: {selected_features}\")\n", "\n", "# 2. 数据分割\n", "print(\"\\n2. 数据分割\")\n", "print(\"-\" * 50)\n", "\n", "split_idx = int(len(data) * 0.8)\n", "X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]\n", "y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]\n", "\n", "# 进一步分割出验证集用于早停\n", "val_split = int(len(X_train) * 0.8)\n", "X_train_fit, X_val = X_train.iloc[:val_split], X_train.iloc[val_split:]\n", "y_train_fit, y_val = y_train.iloc[:val_split], y_train.iloc[val_split:]\n", "\n", "print(f\"训练集: {len(X_train_fit)} 样本\")\n", "print(f\"验证集: {len(X_val)} 样本\")\n", "print(f\"测试集: {len(X_test)} 样本\")\n", "\n", "# 3. 定义防过拟合的模型配置\n", "print(\"\\n3. 定义防过拟合的模型配置\")\n", "print(\"-\" * 50)\n", "\n", "def calculate_metrics(y_true, y_pred):\n", "    \"\"\"计算评估指标\"\"\"\n", "    mse = mean_squared_error(y_true, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    mae = mean_absolute_error(y_true, y_pred)\n", "    r2 = r2_score(y_true, y_pred)\n", "    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2}\n", "\n", "# 原始模型 (过拟合版本)\n", "original_models = {\n", "    '梯度提升_原始': GradientBoostingRegressor(\n", "        n_estimators=100,\n", "        max_depth=6,\n", "        learning_rate=0.1,\n", "        random_state=42\n", "    )\n", "}\n", "\n", "if XGBOOST_AVAILABLE:\n", "    original_models['XGBoost_原始'] = xgb.XGBRegressor(\n", "        n_estimators=100,\n", "        max_depth=6,\n", "        learning_rate=0.1,\n", "        random_state=42,\n", "        verbosity=0\n", "    )\n", "\n", "# 改进模型 (防过拟合版本)\n", "improved_models = {\n", "    '梯度提升_改进': GradientBoostingRegressor(\n", "        n_estimators=50,           # 减少树的数量\n", "        max_depth=3,               # 减少树的深度\n", "        learning_rate=0.05,        # 降低学习率\n", "        subsample=0.8,             # 子采样\n", "        min_samples_split=10,      # 增加分割最小样本数\n", "        min_samples_leaf=5,        # 增加叶子最小样本数\n", "        max_features='sqrt',       # 特征子采样\n", "        random_state=42\n", "    )\n", "}\n", "\n", "if XGBOOST_AVAILABLE:\n", "    improved_models['XGBoost_改进'] = xgb.XGBRegressor(\n", "        n_estimators=50,           # 减少树的数量\n", "        max_depth=3,               # 减少树的深度\n", "        learning_rate=0.05,        # 降低学习率\n", "        subsample=0.8,             # 行采样\n", "        colsample_bytree=0.8,      # 列采样\n", "        reg_alpha=1.0,             # L1正则化\n", "        reg_lambda=1.0,            # L2正则化\n", "        min_child_weight=5,        # 增加叶子权重\n", "        random_state=42,\n", "        verbosity=0\n", "    )\n", "\n", "# 早停版本\n", "early_stopping_models = {\n", "    '梯度提升_早停': GradientBoostingRegressor(\n", "        n_estimators=200,          # 更多树，但会早停\n", "        max_depth=4,\n", "        learning_rate=0.05,\n", "        subsample=0.8,\n", "        validation_fraction=0.2,   # 验证集比例\n", "        n_iter_no_change=10,       # 早停轮数\n", "        random_state=42\n", "    )\n", "}\n", "\n", "if XGBOOST_AVAILABLE:\n", "    early_stopping_models['XGBoost_早停'] = xgb.XGBRegressor(\n", "        n_estimators=200,\n", "        max_depth=4,\n", "        learning_rate=0.05,\n", "        subsample=0.8,\n", "        colsample_bytree=0.8,\n", "        reg_alpha=0.5,\n", "        reg_lambda=0.5,\n", "        early_stopping_rounds=10,\n", "        random_state=42,\n", "        verbosity=0\n", "    )\n", "\n", "print(\"✓ 定义了3类模型配置:\")\n", "print(\"   • 原始模型 (容易过拟合)\")\n", "print(\"   • 改进模型 (正则化)\")\n", "print(\"   • 早停模型 (动态停止)\")\n", "\n", "# 4. 模型训练和对比\n", "print(\"\\n4. 模型训练和对比\")\n", "print(\"-\" * 50)\n", "\n", "all_results = {}\n", "\n", "# 训练原始模型\n", "print(\"\\n训练原始模型 (容易过拟合):\")\n", "for name, model in original_models.items():\n", "    print(f\"  训练 {name}...\")\n", "    model.fit(X_train, y_train)\n", "    \n", "    train_pred = model.predict(X_train)\n", "    test_pred = model.predict(X_test)\n", "    \n", "    train_metrics = calculate_metrics(y_train, train_pred)\n", "    test_metrics = calculate_metrics(y_test, test_pred)\n", "    overfitting_ratio = test_metrics['RMSE'] / train_metrics['RMSE']\n", "    \n", "    all_results[name] = {\n", "        'model': model,\n", "        'train_metrics': train_metrics,\n", "        'test_metrics': test_metrics,\n", "        'overfitting_ratio': overfitting_ratio,\n", "        'train_pred': train_pred,\n", "        'test_pred': test_pred\n", "    }\n", "    \n", "    print(f\"    训练RMSE: {train_metrics['RMSE']:.2f}\")\n", "    print(f\"    测试RMSE: {test_metrics['RMSE']:.2f}\")\n", "    print(f\"    过拟合比率: {overfitting_ratio:.2f}\")\n", "\n", "# 训练改进模型\n", "print(\"\\n训练改进模型 (正则化):\")\n", "for name, model in improved_models.items():\n", "    print(f\"  训练 {name}...\")\n", "    model.fit(X_train, y_train)\n", "    \n", "    train_pred = model.predict(X_train)\n", "    test_pred = model.predict(X_test)\n", "    \n", "    train_metrics = calculate_metrics(y_train, train_pred)\n", "    test_metrics = calculate_metrics(y_test, test_pred)\n", "    overfitting_ratio = test_metrics['RMSE'] / train_metrics['RMSE']\n", "    \n", "    all_results[name] = {\n", "        'model': model,\n", "        'train_metrics': train_metrics,\n", "        'test_metrics': test_metrics,\n", "        'overfitting_ratio': overfitting_ratio,\n", "        'train_pred': train_pred,\n", "        'test_pred': test_pred\n", "    }\n", "    \n", "    print(f\"    训练RMSE: {train_metrics['RMSE']:.2f}\")\n", "    print(f\"    测试RMSE: {test_metrics['RMSE']:.2f}\")\n", "    print(f\"    过拟合比率: {overfitting_ratio:.2f}\")\n", "\n", "# 训练早停模型\n", "print(\"\\n训练早停模型 (动态停止):\")\n", "for name, model in early_stopping_models.items():\n", "    print(f\"  训练 {name}...\")\n", "    \n", "    if 'XGBoost' in name and XGBOOST_AVAILABLE:\n", "        # XGBoost早停需要验证集\n", "        model.fit(X_train_fit, y_train_fit, \n", "                 eval_set=[(X_val, y_val)], \n", "                 verbose=False)\n", "        # 使用全部训练数据预测\n", "        train_pred = model.predict(X_train)\n", "    else:\n", "        # Sklearn的梯度提升内置验证\n", "        model.fit(X_train, y_train)\n", "        train_pred = model.predict(X_train)\n", "    \n", "    test_pred = model.predict(X_test)\n", "    \n", "    train_metrics = calculate_metrics(y_train, train_pred)\n", "    test_metrics = calculate_metrics(y_test, test_pred)\n", "    overfitting_ratio = test_metrics['RMSE'] / train_metrics['RMSE']\n", "    \n", "    all_results[name] = {\n", "        'model': model,\n", "        'train_metrics': train_metrics,\n", "        'test_metrics': test_metrics,\n", "        'overfitting_ratio': overfitting_ratio,\n", "        'train_pred': train_pred,\n", "        'test_pred': test_pred\n", "    }\n", "    \n", "    print(f\"    训练RMSE: {train_metrics['RMSE']:.2f}\")\n", "    print(f\"    测试RMSE: {test_metrics['RMSE']:.2f}\")\n", "    print(f\"    过拟合比率: {overfitting_ratio:.2f}\")\n", "\n", "# 5. 过拟合改善效果对比\n", "print(\"\\n5. 过拟合改善效果对比\")\n", "print(\"-\" * 50)\n", "\n", "# 创建对比表格\n", "comparison_data = []\n", "for name, result in all_results.items():\n", "    comparison_data.append({\n", "        '模型': name,\n", "        '训练RMSE': result['train_metrics']['RMSE'],\n", "        '测试RMSE': result['test_metrics']['RMSE'],\n", "        '测试R²': result['test_metrics']['R2'],\n", "        '测试MAE': result['test_metrics']['MAE'],\n", "        '过拟合比率': result['overfitting_ratio']\n", "    })\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "\n", "print(\"过拟合改善效果对比:\")\n", "print(\"=\" * 80)\n", "for _, row in comparison_df.iterrows():\n", "    model_type = \"原始\" if \"原始\" in row['模型'] else \"改进\" if \"改进\" in row['模型'] else \"早停\"\n", "    print(f\"\\n{row['模型']} ({model_type}):\")\n", "    print(f\"  训练RMSE: {row['训练RMSE']:.2f}\")\n", "    print(f\"  测试RMSE: {row['测试RMSE']:.2f}\")\n", "    print(f\"  测试R²: {row['测试R²']:.3f}\")\n", "    print(f\"  过拟合比率: {row['过拟合比率']:.2f}\")\n", "\n", "# 6. 可视化对比\n", "print(\"\\n6. 可视化对比\")\n", "print(\"-\" * 50)\n", "\n", "# 创建综合对比图\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "fig.suptitle('梯度提升类模型过拟合解决方案对比', fontsize=16, fontweight='bold')\n", "\n", "# 1. 过拟合比率对比\n", "ax1 = axes[0, 0]\n", "models = comparison_df['模型'].tolist()\n", "overfitting_ratios = comparison_df['过拟合比率'].tolist()\n", "\n", "colors = ['red' if '原始' in m else 'orange' if '改进' in m else 'green' for m in models]\n", "bars = ax1.bar(range(len(models)), overfitting_ratios, color=colors, alpha=0.7)\n", "\n", "ax1.set_xticks(range(len(models)))\n", "ax1.set_xticklabels([m.replace('_', '\\n') for m in models], rotation=45, ha='right')\n", "ax1.set_ylabel('过拟合比率')\n", "ax1.set_title('过拟合比率对比\\n(越低越好)')\n", "ax1.axhline(y=2, color='black', linestyle='--', alpha=0.5, label='理想阈值(2.0)')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# 添加数值标签\n", "for bar, ratio in zip(bars, overfitting_ratios):\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + max(overfitting_ratios)*0.01,\n", "             f'{ratio:.1f}', ha='center', va='bottom', fontsize=9)\n", "\n", "# 2. 测试集性能对比\n", "ax2 = axes[0, 1]\n", "test_rmse = comparison_df['测试RMSE'].tolist()\n", "test_r2 = comparison_df['测试R²'].tolist()\n", "\n", "ax2_twin = ax2.twinx()\n", "bars1 = ax2.bar([i-0.2 for i in range(len(models))], test_rmse, width=0.4,\n", "               color='blue', alpha=0.7, label='RMSE')\n", "bars2 = ax2_twin.bar([i+0.2 for i in range(len(models))], test_r2, width=0.4,\n", "                    color='green', alpha=0.7, label='R²')\n", "\n", "ax2.set_xticks(range(len(models)))\n", "ax2.set_xticklabels([m.replace('_', '\\n') for m in models], rotation=45, ha='right')\n", "ax2.set_ylabel('测试RMSE', color='blue')\n", "ax2_twin.set_ylabel('测试R²', color='green')\n", "ax2.set_title('测试集性能对比')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# 3. 预测效果时间序列对比\n", "ax3 = axes[1, 0]\n", "train_dates = data.index[:len(y_train)]\n", "test_dates = data.index[len(y_train):]\n", "\n", "# 绘制真实值\n", "ax3.plot(train_dates, y_train, 'black', label='训练集真实值', linewidth=1, alpha=0.8)\n", "ax3.plot(test_dates, y_test, 'black', label='测试集真实值', linewidth=2)\n", "\n", "# 绘制不同模型的预测\n", "colors_map = {'原始': 'red', '改进': 'orange', '早停': 'green'}\n", "for name, result in all_results.items():\n", "    model_type = \"原始\" if \"原始\" in name else \"改进\" if \"改进\" in name else \"早停\"\n", "    color = colors_map[model_type]\n", "\n", "    ax3.plot(test_dates, result['test_pred'], color=color,\n", "             label=f'{name.split(\"_\")[0]}_{model_type}', linewidth=1.5, alpha=0.8)\n", "\n", "ax3.axvline(x=train_dates[-1], color='gray', linestyle=':', alpha=0.7)\n", "ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "ax3.set_title('预测效果对比')\n", "ax3.set_ylabel('Residual值')\n", "ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# 4. 学习曲线对比 (选择一个代表性模型)\n", "ax4 = axes[1, 1]\n", "\n", "# 选择梯度提升的原始和改进版本进行学习曲线对比\n", "if '梯度提升_原始' in all_results and '梯度提升_改进' in all_results:\n", "\n", "    # 原始模型学习曲线\n", "    train_sizes = np.linspace(0.1, 1.0, 10)\n", "    original_model = all_results['梯度提升_原始']['model']\n", "\n", "    train_sizes_abs, train_scores, val_scores = learning_curve(\n", "        original_model, X_train, y_train, train_sizes=train_sizes,\n", "        cv=3, scoring='neg_mean_squared_error', random_state=42\n", "    )\n", "\n", "    train_rmse = np.sqrt(-train_scores.mean(axis=1))\n", "    val_rmse = np.sqrt(-val_scores.mean(axis=1))\n", "\n", "    ax4.plot(train_sizes_abs, train_rmse, 'r-', label='原始模型-训练', linewidth=2)\n", "    ax4.plot(train_sizes_abs, val_rmse, 'r--', label='原始模型-验证', linewidth=2)\n", "\n", "    # 改进模型学习曲线\n", "    improved_model = all_results['梯度提升_改进']['model']\n", "\n", "    train_sizes_abs, train_scores, val_scores = learning_curve(\n", "        improved_model, X_train, y_train, train_sizes=train_sizes,\n", "        cv=3, scoring='neg_mean_squared_error', random_state=42\n", "    )\n", "\n", "    train_rmse = np.sqrt(-train_scores.mean(axis=1))\n", "    val_rmse = np.sqrt(-val_scores.mean(axis=1))\n", "\n", "    ax4.plot(train_sizes_abs, train_rmse, 'g-', label='改进模型-训练', linewidth=2)\n", "    ax4.plot(train_sizes_abs, val_rmse, 'g--', label='改进模型-验证', linewidth=2)\n", "\n", "    ax4.set_xlabel('训练样本数')\n", "    ax4.set_ylabel('RMSE')\n", "    ax4.set_title('学习曲线对比')\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 7. 过拟合解决方案总结\n", "print(\"\\n7. 过拟合解决方案总结\")\n", "print(\"-\" * 50)\n", "\n", "print(\"🔧 过拟合解决策略效果分析:\")\n", "\n", "# 找出过拟合改善最明显的模型\n", "original_overfitting = {}\n", "improved_overfitting = {}\n", "\n", "for name, result in all_results.items():\n", "    if '原始' in name:\n", "        model_base = name.replace('_原始', '')\n", "        original_overfitting[model_base] = result['overfitting_ratio']\n", "    elif '改进' in name:\n", "        model_base = name.replace('_改进', '')\n", "        improved_overfitting[model_base] = result['overfitting_ratio']\n", "\n", "for model_base in original_overfitting:\n", "    if model_base in improved_overfitting:\n", "        original_ratio = original_overfitting[model_base]\n", "        improved_ratio = improved_overfitting[model_base]\n", "        improvement = (original_ratio - improved_ratio) / original_ratio * 100\n", "\n", "        print(f\"\\n{model_base}:\")\n", "        print(f\"  原始过拟合比率: {original_ratio:.2f}\")\n", "        print(f\"  改进过拟合比率: {improved_ratio:.2f}\")\n", "        print(f\"  改善程度: {improvement:.1f}%\")\n", "\n", "print(f\"\\n💡 有效的过拟合解决方法:\")\n", "print(f\"  1. 减少模型复杂度:\")\n", "print(f\"     • 减少树的数量 (n_estimators: 100→50)\")\n", "print(f\"     • 减少树的深度 (max_depth: 6→3)\")\n", "print(f\"     • 降低学习率 (learning_rate: 0.1→0.05)\")\n", "print(f\"  \")\n", "print(f\"  2. 增加正则化:\")\n", "print(f\"     • 子采样 (subsample=0.8)\")\n", "print(f\"     • 特征采样 (max_features='sqrt')\")\n", "print(f\"     • 增加最小分割样本数\")\n", "print(f\"  \")\n", "print(f\"  3. 早停机制:\")\n", "print(f\"     • 监控验证集性能\")\n", "print(f\"     • 自动停止训练\")\n", "print(f\"  \")\n", "print(f\"  4. 特征选择:\")\n", "print(f\"     • 只保留最重要的特征\")\n", "print(f\"     • 减少特征数量 (17→7)\")\n", "\n", "# 8. 推荐配置\n", "print(f\"\\n8. 推荐配置\")\n", "print(\"-\" * 50)\n", "\n", "best_overfitting = min(comparison_df['过拟合比率'])\n", "best_model_name = comparison_df.loc[comparison_df['过拟合比率'].idxmin(), '模型']\n", "\n", "print(f\"🏆 过拟合控制最佳模型: {best_model_name}\")\n", "print(f\"   过拟合比率: {best_overfitting:.2f}\")\n", "\n", "if best_overfitting < 3:\n", "    print(f\"   ✅ 过拟合得到有效控制\")\n", "else:\n", "    print(f\"   ⚠️ 仍需进一步改进\")\n", "\n", "print(f\"\\n📋 推荐的模型配置:\")\n", "if '梯度提升' in best_model_name:\n", "    print(f\"   GradientBoostingRegressor(\")\n", "    print(f\"       n_estimators=50,\")\n", "    print(f\"       max_depth=3,\")\n", "    print(f\"       learning_rate=0.05,\")\n", "    print(f\"       subsample=0.8,\")\n", "    print(f\"       min_samples_split=10,\")\n", "    print(f\"       min_samples_leaf=5\")\n", "    print(f\"   )\")\n", "\n", "# 保存结果\n", "comparison_df.to_csv(r\"D:\\埃塞俄比亚咖啡\\overfitting_solutions_comparison.csv\", index=False)\n", "print(f\"\\n✓ 对比结果已保存: overfitting_solutions_comparison.csv\")\n", "\n", "print(f\"\\n✅ 过拟合解决方案分析完成!\")\n", "print(f\"   通过正则化和早停有效改善了模型的泛化能力\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c3378a30-2fe8-495d-a3cd-17d548fbc9de", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}