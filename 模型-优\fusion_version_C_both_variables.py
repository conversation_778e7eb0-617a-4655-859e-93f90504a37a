"""
版本C：X11分解融合预测模型 - 同时使用prod_lag1和productivity_lag1
Trend: 线性回归9特征模型 (添加prod_lag1和productivity_lag1)
Seasonal: 完全周期性模式
Residual: 梯度提升13特征模型 (不添加产量变量)
最终预测 = Trend预测 + Seasonal预测 + Residual预测

实验目的: 测试绝对产量+产能双变量对预测效果的影响
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("=== 版本C：X11分解融合预测模型 - 同时使用prod_lag1和productivity_lag1 ===")
print("Trend: 线性回归9特征模型 (添加prod_lag1和productivity_lag1)")
print("Seasonal: 完全周期性模式")
print("Residual: 梯度提升13特征模型 (不添加产量变量)")
print("实验目的: 测试绝对产量+产能双变量对预测效果的影响")

# 1. 数据加载
print("\n1. 数据加载")
print("-" * 50)

# 加载X11分解结果
decomp_path = r"D:\埃塞俄比亚咖啡\模型\x11_decomposition_results.csv"
decomp_data = pd.read_csv(decomp_path)

# 加载外生变量（包含prod_lag1和productivity_lag1）
exog_path = r"D:\埃塞俄比亚咖啡\2005(2013)-2017(2025) monthly data(1).csv"
exog_data = pd.read_csv(exog_path, encoding='gbk')

print(f"X11分解数据: {decomp_data.shape}")
print(f"外生变量数据: {exog_data.shape}")

# 检查数据列
if 'prod_lag1' in exog_data.columns:
    print("✓ 找到prod_lag1列")
if 'productivity_lag1' in exog_data.columns:
    print("✓ 找到productivity_lag1列")

# 2. 数据合并和预处理
print("\n2. 数据合并和预处理")
print("-" * 50)

# 合并数据
min_len = min(len(decomp_data), len(exog_data))

# 处理prod_lag1变量
prod_lag1_col = exog_data['prod_lag1'].iloc[:min_len]
if prod_lag1_col.dtype == 'object':
    prod_lag1_col = prod_lag1_col.astype(str).str.replace(',', '').str.replace('"', '')
    prod_lag1_col = pd.to_numeric(prod_lag1_col, errors='coerce')

# 处理productivity_lag1变量
productivity_lag1_col = exog_data['productivity_lag1'].iloc[:min_len]
if productivity_lag1_col.dtype == 'object':
    productivity_lag1_col = productivity_lag1_col.astype(str).str.replace(',', '').str.replace('"', '')
    productivity_lag1_col = pd.to_numeric(productivity_lag1_col, errors='coerce')

print("✓ 产量变量已转换为数值格式")

data = pd.DataFrame({
    'date': pd.to_datetime(decomp_data['parsed_datetime'].iloc[:min_len]),
    'original': decomp_data['Original'].iloc[:min_len],
    'trend': decomp_data['Trend'].iloc[:min_len],
    'seasonal': decomp_data['Seasonal'].iloc[:min_len],
    'residual': decomp_data['Residual'].iloc[:min_len],
    'sales_price': exog_data['sales price（usd/lb）'].iloc[:min_len],
    'exchange_rate': exog_data['Exchange(Domestic currency per US Dollar)'].iloc[:min_len],
    'prod_lag1': prod_lag1_col,  # 绝对产量
    'productivity_lag1': productivity_lag1_col  # 产能
})

data.set_index('date', inplace=True)
data = data.dropna()

print(f"合并后数据量: {len(data)} 样本")
print(f"时间范围: {data.index[0]} - {data.index[-1]}")
print(f"prod_lag1统计: 最小值={data['prod_lag1'].min():.0f}, 最大值={data['prod_lag1'].max():.0f}")
print(f"productivity_lag1统计: 最小值={data['productivity_lag1'].min():.2f}, 最大值={data['productivity_lag1'].max():.2f}")

# 分析两个变量的相关性
correlation = data['prod_lag1'].corr(data['productivity_lag1'])
print(f"prod_lag1与productivity_lag1相关系数: {correlation:.4f}")

# 3. 数据分割
print("\n3. 数据分割")
print("-" * 50)

split_date = '2022-12-01'
train_data = data[data.index < split_date]
test_data = data[data.index >= split_date]

print(f"训练集: {len(train_data)} 样本 ({train_data.index[0]} - {train_data.index[-1]})")
print(f"测试集: {len(test_data)} 样本 ({test_data.index[0]} - {test_data.index[-1]})")

# 4. Trend预测 - 添加prod_lag1和productivity_lag1
print("\n4. Trend预测 - 线性回归9特征模型（添加双产量变量）")
print("-" * 50)

def create_trend_features(data):
    """创建Trend预测的9个特征（添加prod_lag1和productivity_lag1）"""
    features_data = data.copy()
    
    # 时间特征
    features_data['year'] = features_data.index.year
    features_data['month'] = features_data.index.month
    features_data['time_index'] = range(len(features_data))
    
    # Trend历史特征
    features_data['trend_lag_1'] = features_data['trend'].shift(1)
    features_data['trend_lag_12'] = features_data['trend'].shift(12)
    
    # 外生变量
    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)
    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)
    
    # 版本C：同时使用绝对产量和产能
    features_data['prod_lag1'] = features_data['prod_lag1']
    features_data['productivity_lag1'] = features_data['productivity_lag1']
    
    return features_data.dropna()

# Trend特征列表（9个特征）
trend_features_list = [
    'time_index', 'trend_lag_1', 'trend_lag_12', 'sales_price_lag_1', 
    'exchange_rate_lag_1', 'year', 'month', 'prod_lag1', 'productivity_lag1'
]

trend_train_data = create_trend_features(train_data)
trend_test_data = create_trend_features(test_data)

if len(trend_test_data) == 0:
    full_trend_data = create_trend_features(data)
    trend_split_idx = int(len(full_trend_data) * 0.8)
    trend_train_data = full_trend_data.iloc[:trend_split_idx]
    trend_test_data = full_trend_data.iloc[trend_split_idx:]

X_trend_train = trend_train_data[trend_features_list]
y_trend_train = trend_train_data['trend']
X_trend_test = trend_test_data[trend_features_list]
y_trend_test = trend_test_data['trend']

# 标准化和训练
trend_scaler = StandardScaler()
X_trend_train_scaled = trend_scaler.fit_transform(X_trend_train)
X_trend_test_scaled = trend_scaler.transform(X_trend_test)

trend_model = LinearRegression()
trend_model.fit(X_trend_train_scaled, y_trend_train)

trend_train_pred = trend_model.predict(X_trend_train_scaled)
trend_test_pred = trend_model.predict(X_trend_test_scaled)

print(f"✓ Trend模型训练完成（版本C：含双产量变量）")
print(f"  训练集RMSE: {np.sqrt(mean_squared_error(y_trend_train, trend_train_pred)):.2f}")
print(f"  测试集RMSE: {np.sqrt(mean_squared_error(y_trend_test, trend_test_pred)):.2f}")
print(f"  测试集R²: {r2_score(y_trend_test, trend_test_pred):.4f}")

# 特征重要性分析
trend_feature_importance = pd.DataFrame({
    'feature': trend_features_list,
    'coefficient': trend_model.coef_,
    'abs_coefficient': np.abs(trend_model.coef_)
}).sort_values('abs_coefficient', ascending=False)

# 分析两个产量变量的重要性
prod_importance_rank = trend_feature_importance[trend_feature_importance['feature'] == 'prod_lag1'].index[0] + 1
prod_coefficient = trend_feature_importance[trend_feature_importance['feature'] == 'prod_lag1']['coefficient'].iloc[0]
productivity_importance_rank = trend_feature_importance[trend_feature_importance['feature'] == 'productivity_lag1'].index[0] + 1
productivity_coefficient = trend_feature_importance[trend_feature_importance['feature'] == 'productivity_lag1']['coefficient'].iloc[0]

print(f"  prod_lag1重要性排名: {prod_importance_rank}/9, 系数: {prod_coefficient:.6f}")
print(f"  productivity_lag1重要性排名: {productivity_importance_rank}/9, 系数: {productivity_coefficient:.6f}")

# 5. Seasonal预测 - 不变
print("\n5. Seasonal预测 - 完全周期性模式")
print("-" * 50)

seasonal_pattern = train_data.groupby(train_data.index.month)['seasonal'].mean()
print(f"✓ 季节性模式计算完成")

def predict_seasonal(dates, pattern):
    months = [date.month for date in dates]
    return np.array([pattern[month] for month in months])

seasonal_train_pred = predict_seasonal(trend_train_data.index, seasonal_pattern)
seasonal_test_pred = predict_seasonal(trend_test_data.index, seasonal_pattern)

seasonal_train_actual = trend_train_data['seasonal'].values
seasonal_test_actual = trend_test_data['seasonal'].values

print(f"  测试集RMSE: {np.sqrt(mean_squared_error(seasonal_test_actual, seasonal_test_pred)):.4f}")
print(f"  测试集R²: {r2_score(seasonal_test_actual, seasonal_test_pred):.4f}")

# 6. Residual预测 - 不添加产量变量
print("\n6. Residual预测 - 梯度提升13特征模型（不添加产量变量）")
print("-" * 50)

def create_residual_features(data):
    """创建Residual预测的13个特征（不添加产量变量）"""
    features_data = data.copy()

    features_data['month'] = features_data.index.month
    
    for lag in [1, 3, 6]:
        features_data[f'residual_lag_{lag}'] = features_data['residual'].shift(lag)

    features_data['residual_volatility'] = features_data['residual'].rolling(window=6).std()
    features_data['trend_change'] = features_data['trend'].pct_change()
    features_data['seasonal_abs'] = np.abs(features_data['seasonal'])
    
    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)
    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)
    features_data['sales_price_change'] = features_data['sales_price'].pct_change()
    features_data['exchange_rate_change'] = features_data['exchange_rate'].pct_change()
    features_data['exchange_sales_interaction'] = features_data['exchange_rate'] * features_data['sales_price']
    
    features_data['exchange_rate'] = features_data['exchange_rate']
    features_data['sales_price'] = features_data['sales_price']

    return features_data.dropna()

# Residual特征列表（13个特征，不含产量变量）
residual_features_list = [
    'residual_lag_1', 'residual_lag_6', 'residual_volatility',
    'exchange_rate', 'sales_price',
    'exchange_rate_lag_1', 'sales_price_lag_1',
    'exchange_rate_change', 'sales_price_change',
    'exchange_sales_interaction',
    'seasonal_abs', 'trend_change',
    'month'
]

residual_train_data = create_residual_features(train_data)
residual_test_data = create_residual_features(test_data)

if len(residual_test_data) == 0:
    full_residual_data = create_residual_features(data)
    residual_split_idx = int(len(full_residual_data) * 0.8)
    residual_train_data = full_residual_data.iloc[:residual_split_idx]
    residual_test_data = full_residual_data.iloc[residual_split_idx:]

available_features = [f for f in residual_features_list if f in residual_train_data.columns]
print(f"可用Residual特征: {len(available_features)}/{len(residual_features_list)}")

X_residual_train = residual_train_data[available_features]
y_residual_train = residual_train_data['residual']
X_residual_test = residual_test_data[available_features]
y_residual_test = residual_test_data['residual']

residual_model = GradientBoostingRegressor(
    n_estimators=50, max_depth=3, learning_rate=0.05, subsample=0.8,
    min_samples_split=10, min_samples_leaf=5, max_features='sqrt', random_state=42
)

residual_model.fit(X_residual_train, y_residual_train)

residual_train_pred = residual_model.predict(X_residual_train)
residual_test_pred = residual_model.predict(X_residual_test)

print(f"✓ Residual模型训练完成（版本C：不含产量变量）")
print(f"  训练集RMSE: {np.sqrt(mean_squared_error(y_residual_train, residual_train_pred)):.2f}")
print(f"  测试集RMSE: {np.sqrt(mean_squared_error(y_residual_test, residual_test_pred)):.2f}")
print(f"  测试集R²: {r2_score(y_residual_test, residual_test_pred):.4f}")

# 7. 最终融合预测
print("\n7. 最终融合预测")
print("-" * 50)

split_date = pd.to_datetime('2022-12-01')
original_train_data = data[data.index < split_date]
original_test_data = data[data.index >= split_date]

def predict_step_by_step(test_data, train_data, models, scalers, seasonal_pattern):
    trend_model, residual_model = models
    trend_scaler = scalers

    trend_predictions = []
    seasonal_predictions = []
    residual_predictions = []
    final_predictions = []

    full_data = pd.concat([train_data, test_data])

    for i, test_date in enumerate(test_data.index):
        print(f"  预测 {test_date.strftime('%Y-%m')}...")

        current_data = full_data[full_data.index <= test_date]

        # Trend预测
        trend_features_data = create_trend_features(current_data)
        if len(trend_features_data) > 0 and test_date in trend_features_data.index:
            trend_X = trend_features_data.loc[[test_date], trend_features_list]
            trend_X_scaled = trend_scaler.transform(trend_X)
            trend_pred = trend_model.predict(trend_X_scaled)[0]
        else:
            trend_pred = current_data['trend'].iloc[-1]

        # Seasonal预测
        seasonal_pred = seasonal_pattern[test_date.month]

        # Residual预测
        residual_features_data = create_residual_features(current_data)
        if len(residual_features_data) > 0 and test_date in residual_features_data.index:
            residual_X = residual_features_data.loc[[test_date], available_features]
            residual_pred = residual_model.predict(residual_X)[0]
        else:
            residual_pred = 0.0

        final_pred = trend_pred + seasonal_pred + residual_pred

        trend_predictions.append(trend_pred)
        seasonal_predictions.append(seasonal_pred)
        residual_predictions.append(residual_pred)
        final_predictions.append(final_pred)

    return (np.array(trend_predictions), np.array(seasonal_predictions),
            np.array(residual_predictions), np.array(final_predictions))

print("开始逐步预测测试集...")

models = (trend_model, residual_model)
scalers = trend_scaler

(trend_test_pred_full, seasonal_test_pred_full,
 residual_test_pred_full, final_test_pred_full) = predict_step_by_step(
    original_test_data, original_train_data, models, scalers, seasonal_pattern)

# 训练集预测对齐
train_available_mask = original_train_data.index.isin(trend_train_data.index)
final_train_dates = original_train_data.index[train_available_mask]
final_train_actual = original_train_data['original'][train_available_mask].values

train_trend_aligned = []
train_seasonal_aligned = []
train_residual_aligned = []

for date in final_train_dates:
    if date in trend_train_data.index:
        idx = trend_train_data.index.get_loc(date)
        train_trend_aligned.append(trend_train_pred[idx])
        train_seasonal_aligned.append(predict_seasonal([date], seasonal_pattern)[0])

        if date in residual_train_data.index:
            residual_idx = residual_train_data.index.get_loc(date)
            train_residual_aligned.append(residual_train_pred[residual_idx])
        else:
            train_residual_aligned.append(0.0)

final_train_pred = np.array(train_trend_aligned) + np.array(train_seasonal_aligned) + np.array(train_residual_aligned)

final_test_dates = original_test_data.index
final_test_actual = original_test_data['original'].values
final_test_pred = final_test_pred_full

print(f"✓ 版本C预测完成!")

# 8. 性能评估
def calculate_metrics(y_true, y_pred):
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100

    if len(y_true) > 1:
        direction_accuracy = np.mean(np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred))) * 100
    else:
        direction_accuracy = 0

    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2, 'MAPE': mape, 'Direction_Accuracy': direction_accuracy}

final_train_metrics = calculate_metrics(final_train_actual, final_train_pred)
final_test_metrics = calculate_metrics(final_test_actual, final_test_pred)
final_overfitting_ratio = final_test_metrics['RMSE'] / final_train_metrics['RMSE']

print(f"\n🎉 版本C：同时使用双产量变量的融合预测完成!")
print(f"\n训练集性能:")
print(f"  RMSE: {final_train_metrics['RMSE']:.2f} 百万美元")
print(f"  R²: {final_train_metrics['R2']:.4f}")
print(f"  MAPE: {final_train_metrics['MAPE']:.2f}%")

print(f"\n测试集性能:")
print(f"  RMSE: {final_test_metrics['RMSE']:.2f} 百万美元")
print(f"  R²: {final_test_metrics['R2']:.4f}")
print(f"  MAPE: {final_test_metrics['MAPE']:.2f}%")
print(f"  方向准确性: {final_test_metrics['Direction_Accuracy']:.1f}%")
print(f"  过拟合比率: {final_overfitting_ratio:.3f}")

# 9. 多重共线性分析
print("\n9. 多重共线性分析")
print("-" * 50)

# 计算VIF（方差膨胀因子）的简化版本
from sklearn.linear_model import LinearRegression as LR

def calculate_vif_simplified(X, feature_idx):
    """计算简化版VIF"""
    feature_name = X.columns[feature_idx]
    X_temp = X.drop(X.columns[feature_idx], axis=1)
    y_temp = X.iloc[:, feature_idx]
    
    model = LR()
    model.fit(X_temp, y_temp)
    r_squared = model.score(X_temp, y_temp)
    
    if r_squared >= 0.99:  # 避免除零错误
        return float('inf')
    
    vif = 1 / (1 - r_squared)
    return vif

# 计算产量变量的VIF
X_for_vif = X_trend_train[['prod_lag1', 'productivity_lag1']]
vif_prod = calculate_vif_simplified(X_for_vif, 0)
vif_productivity = calculate_vif_simplified(X_for_vif, 1)

print(f"多重共线性分析:")
print(f"  prod_lag1与productivity_lag1相关系数: {correlation:.4f}")
print(f"  prod_lag1 VIF: {vif_prod:.2f}")
print(f"  productivity_lag1 VIF: {vif_productivity:.2f}")

if vif_prod > 5 or vif_productivity > 5:
    print("  ⚠️ 存在多重共线性问题（VIF > 5）")
elif vif_prod > 2.5 or vif_productivity > 2.5:
    print("  ⚠️ 轻微多重共线性（VIF > 2.5）")
else:
    print("  ✅ 多重共线性问题不严重")

# 10. 结果保存
print("\n10. 结果保存")
print("-" * 50)

# 保存详细结果
results_df = pd.DataFrame({
    'date': np.concatenate([final_train_dates, final_test_dates]),
    'actual': np.concatenate([final_train_actual, final_test_actual]),
    'predicted': np.concatenate([final_train_pred, final_test_pred]),
    'trend_pred': np.concatenate([train_trend_aligned, trend_test_pred_full]),
    'seasonal_pred': np.concatenate([train_seasonal_aligned, seasonal_test_pred_full]),
    'residual_pred': np.concatenate([train_residual_aligned, residual_test_pred_full]),
    'dataset': ['train'] * len(final_train_dates) + ['test'] * len(final_test_dates)
})

results_path = r"D:\埃塞俄比亚咖啡\模型\version_C_both_variables_results.csv"
results_df.to_csv(results_path, index=False)
print(f"✓ 版本C结果已保存: {results_path}")

# 保存性能指标
performance_df = pd.DataFrame({
    '版本': ['C_both_variables'],
    '产量变量': ['prod_lag1+productivity_lag1'],
    'Trend特征数': [9],
    'Residual特征数': [len(available_features)],
    '测试集RMSE': [final_test_metrics['RMSE']],
    '测试集R²': [final_test_metrics['R2']],
    '测试集MAPE': [final_test_metrics['MAPE']],
    '方向准确性': [final_test_metrics['Direction_Accuracy']],
    '过拟合比率': [final_overfitting_ratio],
    'prod_lag1_rank': [prod_importance_rank],
    'prod_lag1_coef': [prod_coefficient],
    'productivity_lag1_rank': [productivity_importance_rank],
    'productivity_lag1_coef': [productivity_coefficient],
    '变量相关系数': [correlation],
    'prod_VIF': [vif_prod],
    'productivity_VIF': [vif_productivity]
})

performance_path = r"D:\埃塞俄比亚咖啡\模型\version_C_performance.csv"
performance_df.to_csv(performance_path, index=False)
print(f"✓ 版本C性能指标已保存: {performance_path}")

# 11. 可视化对比分析
print("\n11. 可视化对比分析")
print("-" * 50)

fig, axes = plt.subplots(3, 2, figsize=(16, 18))
fig.suptitle('版本C：同时使用双产量变量的融合预测分析', fontsize=16, fontweight='bold')

# 1. 最终预测效果对比
ax1 = axes[0, 0]
ax1.plot(final_train_dates, final_train_actual, 'black', label='训练集真实值', linewidth=1, alpha=0.8)
ax1.plot(final_test_dates, final_test_actual, 'black', label='测试集真实值', linewidth=2)
ax1.plot(final_train_dates, final_train_pred, 'blue', label='训练集预测', linewidth=1, alpha=0.7)
ax1.plot(final_test_dates, final_test_pred, 'red', label='测试集预测', linewidth=2)

ax1.axvline(x=final_train_dates[-1], color='gray', linestyle=':', alpha=0.7, label='训练/测试分割')
ax1.set_title('最终预测效果 (含双产量变量)')
ax1.set_ylabel('出口额 (百万美元)')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 添加性能指标
textstr = f'测试集RMSE: {final_test_metrics["RMSE"]:.1f}\n测试集R²: {final_test_metrics["R2"]:.3f}\n测试集MAPE: {final_test_metrics["MAPE"]:.1f}%'
props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
ax1.text(0.02, 0.98, textstr, transform=ax1.transAxes, fontsize=10,
         verticalalignment='top', bbox=props)

# 2. 预测准确性散点图
ax2 = axes[0, 1]
ax2.scatter(final_test_actual, final_test_pred, alpha=0.7, s=60, color='red')
ax2.plot([final_test_actual.min(), final_test_actual.max()],
         [final_test_actual.min(), final_test_actual.max()], 'k--', lw=2)

ax2.set_xlabel('真实出口额 (百万美元)')
ax2.set_ylabel('预测出口额 (百万美元)')
ax2.set_title(f'预测准确性 (双产量变量)\nR²={final_test_metrics["R2"]:.3f}')
ax2.grid(True, alpha=0.3)

# 添加拟合线
z = np.polyfit(final_test_actual, final_test_pred, 1)
p = np.poly1d(z)
ax2.plot(final_test_actual, p(final_test_actual), "b--", alpha=0.8, linewidth=1)

# 3. 预测误差分析
ax3 = axes[1, 0]
test_errors = final_test_actual - final_test_pred
ax3.plot(final_test_dates, test_errors, 'ro-', markersize=4, linewidth=1)
ax3.axhline(y=0, color='black', linestyle='--', alpha=0.7)
ax3.axhline(y=test_errors.std(), color='red', linestyle=':', alpha=0.7, label='+1σ')
ax3.axhline(y=-test_errors.std(), color='red', linestyle=':', alpha=0.7, label='-1σ')

ax3.set_ylabel('预测误差 (百万美元)')
ax3.set_title('测试集预测误差 (含双产量变量)')
ax3.legend()
ax3.grid(True, alpha=0.3)

# 4. 各组件预测对比
ax4 = axes[1, 1]
ax4.plot(final_test_dates, trend_test_pred_full, 'blue', label='Trend预测(含双产量变量)', linewidth=2)
ax4.plot(final_test_dates, seasonal_test_pred_full, 'green', label='Seasonal预测', linewidth=2)
ax4.plot(final_test_dates, residual_test_pred_full, 'orange', label='Residual预测', linewidth=2)
ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)

ax4.set_title('各组件预测对比')
ax4.set_ylabel('组件值')
ax4.legend()
ax4.grid(True, alpha=0.3)

# 5. 双产量变量时间序列对比
ax5 = axes[2, 0]
ax5_twin = ax5.twinx()

# 绘制双产量变量和出口额的关系
line1 = ax5.plot(data.index, data['original'], 'red', label='出口额', linewidth=1, alpha=0.7)
line2 = ax5_twin.plot(data.index, data['prod_lag1']/1000, 'blue', label='绝对产量(千吨)', linewidth=1, alpha=0.7)
line3 = ax5_twin.plot(data.index, data['productivity_lag1'], 'green', label='产能(吨/公顷)', linewidth=1, alpha=0.7)

ax5.axvline(x=final_train_dates[-1], color='gray', linestyle=':', alpha=0.7)
ax5.set_ylabel('出口额 (百万美元)', color='red')
ax5_twin.set_ylabel('产量(千吨) / 产能(吨/公顷)', color='blue')
ax5.set_title('双产量变量与出口额关系')

# 合并图例
lines = line1 + line2 + line3
labels = [l.get_label() for l in lines]
ax5.legend(lines, labels, loc='upper left')
ax5.grid(True, alpha=0.3)

# 6. Trend特征重要性（高亮双产量变量）
ax6 = axes[2, 1]
trend_importance_plot = trend_feature_importance.head(9)
bars = ax6.barh(range(len(trend_importance_plot)), trend_importance_plot['abs_coefficient'], alpha=0.7)
ax6.set_yticks(range(len(trend_importance_plot)))
ax6.set_yticklabels(trend_importance_plot['feature'])
ax6.set_xlabel('回归系数绝对值')
ax6.set_title('Trend模型特征重要性 (含双产量变量)')
ax6.grid(True, alpha=0.3)

# 高亮双产量变量
for i, (_, row) in enumerate(trend_importance_plot.iterrows()):
    if row['feature'] in ['prod_lag1', 'productivity_lag1']:
        bars[i].set_color('orange')
        bars[i].set_alpha(0.9)

plt.tight_layout()
plt.show()

print(f"\n✅ 版本C完成！同时使用双产量变量的融合预测模型已准备就绪!")