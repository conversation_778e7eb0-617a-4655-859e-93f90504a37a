{"cells": [{"cell_type": "code", "execution_count": 2, "id": "cec66575-b3ec-4ebb-a10a-ee26afda6e6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== X11分解最终融合预测模型 ===\n", "Trend: 线性回归7特征模型\n", "Seasonal: 完全周期性模式\n", "Residual: 梯度提升模型\n", "\n", "1. 数据加载\n", "--------------------------------------------------\n", "X11分解数据: (156, 5)\n", "外生变量数据: (156, 13)\n", "\n", "2. 数据合并和预处理\n", "--------------------------------------------------\n", "合并后数据量: 156 样本\n", "时间范围: 2012-07-01 00:00:00 - 2025-06-01 00:00:00\n", "\n", "3. 数据分割\n", "--------------------------------------------------\n", "训练集: 125 样本 (2012-07-01 00:00:00 - 2022-11-01 00:00:00)\n", "测试集: 31 样本 (2022-12-01 00:00:00 - 2025-06-01 00:00:00)\n", "\n", "4. Trend预测 - 线性回归7特征模型\n", "--------------------------------------------------\n", "✓ Trend模型训练完成\n", "  训练集RMSE: 1.22\n", "  测试集RMSE: 9.42\n", "  测试集R²: 0.9387\n", "\n", "5. Seasonal预测 - 完全周期性模式\n", "--------------------------------------------------\n", "✓ 季节性模式计算完成\n", "  12个月的季节性模式:\n", "     1月: -43.0720\n", "     2月: -14.1933\n", "     3月:  16.3299\n", "     4月:  25.2782\n", "     5月:  46.6846\n", "     6月:  56.7363\n", "     7月:   6.0503\n", "     8月:  14.2378\n", "     9月: -16.1471\n", "    10月: -16.7780\n", "    11月: -29.9412\n", "    12月: -45.1855\n", "  训练集RMSE: 0.0000\n", "  测试集RMSE: 0.0000\n", "  测试集R²: 1.0000\n", "\n", "6. Residual预测 - 梯度提升模型\n", "--------------------------------------------------\n", "可用Residual特征: 13/13\n", "✓ Residual模型训练完成\n", "  训练集RMSE: 6.19\n", "  测试集RMSE: 39.55\n", "  测试集R²: 0.2655\n", "\n", "7. 最终融合预测\n", "--------------------------------------------------\n", "原始训练集: 125 样本 (2012-07-01 00:00:00 - 2022-11-01 00:00:00)\n", "原始测试集: 31 样本 (2022-12-01 00:00:00 - 2025-06-01 00:00:00)\n", "开始逐步预测测试集...\n", "  预测 2022-12...\n", "  预测 2023-01...\n", "  预测 2023-02...\n", "  预测 2023-03...\n", "  预测 2023-04...\n", "  预测 2023-05...\n", "  预测 2023-06...\n", "  预测 2023-07...\n", "  预测 2023-08...\n", "  预测 2023-09...\n", "  预测 2023-10...\n", "  预测 2023-11...\n", "  预测 2023-12...\n", "  预测 2024-01...\n", "  预测 2024-02...\n", "  预测 2024-03...\n", "  预测 2024-04...\n", "  预测 2024-05...\n", "  预测 2024-06...\n", "  预测 2024-07...\n", "  预测 2024-08...\n", "  预测 2024-09...\n", "  预测 2024-10...\n", "  预测 2024-11...\n", "  预测 2024-12...\n", "  预测 2025-01...\n", "  预测 2025-02...\n", "  预测 2025-03...\n", "  预测 2025-04...\n", "  预测 2025-05...\n", "  预测 2025-06...\n", "✓ 逐步预测完成!\n", "最终训练数据: 113 样本\n", "最终测试数据: 31 样本 (完整覆盖，无空缺)\n", "🎉 X11融合预测完成!\n", "\n", "训练集性能:\n", "  RMSE: 6.08 百万美元\n", "  MAE: 4.57 百万美元\n", "  R²: 0.9700\n", "  MAPE: 6.60%\n", "\n", "测试集性能:\n", "  RMSE: 35.91 百万美元\n", "  MAE: 24.72 百万美元\n", "  R²: 0.8513\n", "  MAPE: 16.83%\n", "  方向准确性: 86.7%\n", "\n", "过拟合分析:\n", "  过拟合比率: 5.9044\n", "  ⚠️ 存在过拟合\n", "\n", "8. 组件贡献分析\n", "--------------------------------------------------\n", "� 各组件方差贡献:\n", "  Trend贡献: 27.3%\n", "  Seasonal贡献: 12.8%\n", "  Residual贡献: 1.2%\n", "\n", "📈 各组件预测精度(R²):\n", "  Trend预测精度: 0.9387\n", "  Seasonal预测精度: 1.0000\n", "  Residual预测精度: 0.2655\n", "\n", "9. 综合可视化分析\n", "--------------------------------------------------\n"]}, {"data": {"image/png": "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***************************/76i6CgIIYOHQro/Ck92wic5/wJnLudbrrfcygV+zKoESNGkDVrVrp37467uzteXl5Jlnt7e3Pjxo277uPy5csMHTqUWbNm4ebmlp5x5QGlRX/PnTuXfPnysX79ekaOHMnGjRvZsmUL69atS8/och/Sor+XLFnC6dOnOXjwICEhIZQvX54uXbqkZ2y5T/fT33369OGJJ55wXIX33+1S8z0i5kiL/k7tMnEOadHnOmdzHWnR3zpnc31p+bv+6aef5ueffwZwfMDw888/s2fPnmRfjRo1cplzAmdto0OHDvH1118zderUND/me+WsbXTTtWvXWLZsWbKr4s1gZlvdjTP+PKb3ecmuXbtu21blypVzyva4HWdto/Xr17N582bGjBmTHod9z5y1nW46cOAAx48fp3Xr1ml52PfFzLa6G2f7mUyLdho6dChr166lUqVKVKxYkQkTJrBkyRJA50/p2UbOdP4EzttONz3IOZSG8cyAfvvtN7766iv++usvPDw8yJUrF/v370+yTmRkJJ6ennfdT9++fenWrRuVKlVKz7jygNKqv8+ePUujRo3w9vYGIHv27JQqVYrjx4/z9NNPp1t+uTdp1d8LFiygd+/ePProowBMmjQJPz8/wsLCnOaKSbm//p47dy6///47e/fudczLlSsXly9fvuM24hzSqr9Ts0ycQ1r1uc7ZXENa9bfO2VxbWv+ud3d3p27dunzxxRe0bduWTz755K5Xl7vCOYGztpFhGLz22mt8+OGHFCxYMA2O9P45axvd6ocffqB27dopDmeV3sxuq7txtp/Hh3FeUqxYsTu+vrO1x+04axvFxMTQq1cvvvnmG7Jmzfogh5gmnLWdbvXdd9/Rtm1b0y+UM7ut7saZfibT+nf5Tf7+/ly9epXY2FidP93Bg7aRM50/gfO2060e6Bzqnp8iKE7t5MmThr+/vzFv3jzHvPXr1xslSpRIso63t7eRkJDgmBcUFJTs4b2AkT17dsPX19fw9fU13NzcjKxZsxpjx45N/wORVEnL/h41apTRqVMnx7TNZjMKFixo/PTTT+l4BHIv0rK/W7VqZbz33nuO6cOHDxuAceXKlXQ8ArkX99PfO3bsMLJnz57sQdwzZ840GjZs6Jj+7bffjFKlSqXvAcg9Scv+TmmZOIe07HOdszm/tOxvnbO5rrT8Pvj++++NTz75xDG9ZcsWI3fu3ClmSM05we+//24UK1bsXg4tzThzGwUHBxuA43etr6+vYbFYjOzZsxsLFiy430O+Z87cRrdq3LixMXPmzHs6trTmDG110+3ekznTOboznJe8//77Rrdu3RzTs2bNMho3bpxkndmzZxt169a9z6N8MM7cRhs2bDAsFkuS3083f19t3rw5DY4+9Zy5nW5VqlQpY/369fd7mGnCGdrqptv97U9NOz4MadlOHTp0SPIzMWvWLCNfvnwpZshM509p3UbOcv5kGM7dTrd6kHMoFfsykBs3bhjlypUzevToYURGRjq+4uLijLx58xqzZs0yDMMwunfvbjzzzDNJtr3diWdQUFCSr5o1axrfffedERoa+rAOSe4irfv74MGDho+Pj7FkyRLjzJkzxuDBg428efMa169ff2jHJHeW1v09YcIEw9/f35g6daoxZ84co3LlysYTTzzx0I5H7u5++vvSpUtG/vz5jdGjRyfZxjAM4/Lly4a3t7exdu1aIy4uzmjatKnxxhtvmHZ8klRa9/fdlolzSOs+1zmbc0vr/tY5m2tK6++D3bt3G9myZTN++OEHIygoyGjcuLHRq1evFHOk5pzArA+rnL2N4uPjk/2+LVSokLF58+aH9nfW2dvo1pyenp7GiRMn0qEVUsdZ2uqm270nc5ZzdGc5L9mzZ4+RNWtWY9++fUZkZKRRuXLlJAVWwzCv2OfsbRQdHZ1sn4ARFBRkREdHp2vb3MrZ2+mmEydOGJ6eng+1bf7LWdrqptv97U/Nz2R6S+t2Gj16tFG9enVj8+bNxo8//mjky5fPGDlyZIo5MtP5U1q3kTOcPxmG87fTrTkf5BxKxb4M5KeffjKAZF9BQUHGsmXLDB8fHyN37txG3rx5jQMHDiTZ9nYnnv9Vt25d3SHgRNKjv5ctW2YEBgYa3t7eRoUKFYw///zzYR2OpCCt+zsmJsZ46623jIIFCxqenp5G3bp1jZMnTz7MQ5K7uJ/+njRp0m23uWnq1KmGh4eHkTNnTqN48eLGxYsXzTo8+Y+07u+UvhfEfOnxM34rnbM5l/Tob52zuZ70+D5YsGCBERAQYPj5+Rndu3c3oqKiUpUlpXMCsz6scqU2uqlYsWJGUFDQAx97arlKG61bty5VV7enJ2dqK8O483twZzhHd6bzkmHDhhmenp5Gjhw5jGrVqhk3btxIstysYp8rtdFNZpz/u0o7zZgxw6hZs+YDH++DcKa2Mow7/+1P7fdbeknrdoqLizNeffVVI2vWrEb+/PmNUaNGGfHx8anKklnOn9KzjW562OdPhuE67fSg51AWwzAMJFO4ePEiu3btolatWuTOndvsOJLO1N+Zi/o7c7nf/g4KCuLw4cPUrl2bbNmypWNCSUv6+c581OeZi/pb4OF/H7jiOYHaKGVqo9Rzpt+9zt6OD7utDh48yLlz56hbt67TPRPrTtRGqaN2Sj1n+h3lzO2ov3spUxulTkZpJxX7RERERERERERERERERFyU1ewAIiIiIiIiIiIiIiIiInJ/VOwTERERERERERERERERcVEq9omIiIiIiIiIiIiIiIi4KBX7RERERERERERERERERFyUin0iIiIiIiIiIiIiIiIiLkrFPhEREREREREREREREREXpWKfiIiIiIiIiIiIiIiIiItSsU9ERERERERERERERETERanYJyIiIiIiIiIiIiIiIuKiVOwTERERERERERERERERcVEq9omIiIiIiIiIiIiIiIi4KBX7RERERERERERERERERFyUin0iIiIiIiIiIiIiIiIiLkrFPhEREREREREREREREREXpWKfiIiIiIiIiIiIiIiIiItSsU9ERERERERERERERETERanYJyIiIiIiIiIiIiIiIuKiVOwTERGXFRMTQ2xsLIZhJJlvGAYxMTHExcUBOP5NrUuXLrFz584U1xs+fDjjxo0DYOTIkYwaNeqeXucmm83G008/zaJFi1Jc9/jx4/z44493XL5z505CQ0PvK4eIiIiIiIiIiIi4HhX7RETE6VWpUoWCBQsSEBDg+OrXrx9vvPEG3t7eWK1WLBaL48tqtZIlSxamTJlCQkICNWvWZNCgQSQkJHD69GlWr16d7OvWguDKlStp1aqVY3rVqlUsXLgwSaaEhAS++uorDh06BEClSpUYPXo0c+bMue0xnDhxgmPHjhEcHExwcDAnTpzg1KlTACxcuJBNmzZRrVq1FNti/fr19OzZM8m8qKgox/8fe+wxfv/99xT3IyIiIiIiIiIiIhmDu9kBREREUvL9998D4OnpyYoVK3j33Xdp3bo1tWrV4ssvv8TLy4uOHTuSL18+Jk+e7Lizz83NDXd3d4YNG0a3bt0ICgqiadOm9O/fn3LlygEQGxvLnj17iIiIIDIyEm9vbzw9PfH09HS8/pEjRxg8eDDZsmVzFAG///57rl27xttvvw1AmzZt+Prrr6lduzaGYRAfH+/IDNCzZ0+2b9/OjRs3cHNzw9PTk4YNGzJr1izeeecd3N3dqVatGoZhEBERwciRIxkxYkSytvhvNoDChQsza9Ys2rRpg7e3N97e3mncAyIiIiIiIiIiIuKsVOwTERGnV6ZMGcaMGUNAQACjRo1i6dKl1KtXL8k6x44do0mTJgBYLBayZMniWPbcc88RGBhIWFgYR48epWrVqmzYsAGA4OBgihcvjoeHB08//TTt27cnX758Sfbdt29fbty4QYcOHVi5ciUNGjRgwoQJAFStWvWOud99910+/PBDANatWwdAsWLFePfdd3nttdcwDINnn32WQoUKceLECbJkycL3339Pv379GDhwYJJ92Ww24uLi8PDwwN3dnWnTptGsWTNsNhthYWGOuwKtVitWa+KN+wkJCdhsNry8vO6luUVERERERERERMSFaBhPERFxCSVLlqRHjx40bNiQxo0bA4nP1suTJw958uRh79699O/f3zH9+OOPO7aNjY2lTJky1KxZE4vFAsDGjRt56qmnCA8Pd6zn6el5x8LYsGHD6NGjB+Hh4Xz77bfs27ePIkWKEBoaSmhoqOPuw9DQUEJCQjh37hyDBg1Kso+zZ89y+vRpGjRoAMCHH37IH3/8wdKlS8mSJQt2u53Ro0czZMgQsmbNmmTbvXv34ufnh81mA2D16tVMnTqV3bt3A4lFRIvFQlRUFM2aNcNiseDh4eG481BEREREREREREQyJhX7RETEqRmGQVxcHB06dODXX3+lbNmyjmUeHh5cvXqVbdu2ERYWxsaNGzl16hTjx4/nxo0bAKxYsYJy5cqxd+/eJPu1Wq388ccfSYpqNwuBdzJ58mRq1qzJgAEDyJEjB1arFT8/P/z8/Bz78fX1JVeuXPj5+eHr65tk+x9//JHAwEBKliwJQL9+/Zg1axbFihUjT5485MqVi8OHDzN69Gjy5MmDm5sbS5YsAeDkyZMULVoUNzc3ADp37syCBQtYu3Yt7dq148KFC1y4cAEfHx8WLlzIhQsXOHPmDKNHj76fZhcREREREREREREXoWE8RUTEqV26dIkCBQpgtVodxbiPPvoIwFEIc3Nz4/jx41SrVo2goCCsViseHh4ANGjQgBo1avDkk0+yevVqx37d3d3x9vbG3f3e/hQGBwfj5+fH888/z+jRo5MVCG8OoQkQGRlJtmzZALDb7UydOhWbzUaXLl0AeOmll6hZsyZubm5cuXKFLVu20KVLF4KDg4HEZ/HdfD7foUOHkhQ6n3nmGby8vHj11VeZNGkS+fPnBxILljlz5nRMi4iIiIiIiIiISMamO/tERMSp5cuXj+vXr5OQkMCzzz7Le++9x65duwCoVauWY73o6Gg8PDwoVqxYku1v3uk2aNAgKlasmGSZt7f3PWUJDg7mySefZN++ffj7+992GM9r165x+fJlTp06leSuwa+++opDhw5Ru3ZtmjZtyp49ewgKCkrxNW8W+3bs2EGFChUc8728vMiRIweGYdC6det7Og4RERERERERERHJOHRnn4iIODWLxZLs+XWXL1+mU6dOjrvmILHYly9fvtsOxWm1Whk8eDBubm7Y7XYArly5kmSYzZvz78QwDGrXrs2ECRPo1KmTY79+fn4Ajow5c+ZMtu327dt555138PHxoVatWnTp0oU5c+bg5eWFzWbDZrM5nscXFRXl2GdERIRjHyNHjiRbtmxs377dMa9OnTqcPHmSw4cP4+Pjg8ViwTAMzp49y6FDh4iNjaVw4cLkyZPnrscmIiIiIiIiIiIirkt39omIiMtp1KgR8+fPxzAMx7yzZ89SsGBBx/StywBmz55NnTp1iImJAeDChQtJ1o+Njb3ra27ZsoWzZ89SrVo1xzy73U5YWBhhYWFERUUBEBoaytWrVzl//rwjwyOPPML48eOpXbt2sv3GxMTg5uZGWFgYq1atomjRoo593pqvatWqlC5dOtn2Hh4ePP7449SqVYtatWpx48YN+vbtS61atahZsyYrV66863GJiIiIiIiIiIiIa9OdfSIi4rISEhIc/9++fXuSZ9rFxcUlWXf69OkEBgZSrVo1smTJwvr16ylatCi5c+dm6tSpeHt7Y7PZkm136/ZPPfUUpUqVcsw7e/YsAQEBSbIEBARgs9mIjY0lNDSUbNmykSdPHt544w1WrFiRbL+RkZHY7XaeeeYZQkNDCQkJ4ZlnngHg6tWrKbaBt7d3kszZsmVjyZIlNG3aNMVtRURERERERERExPXpzj4REXFZCQkJFCtWjPj4eH744QcaN24MQMGCBalevbpjvR07drB3714GDRpEtWrV6Ny5M2vXrqVOnTosXbqUXr16kSVLFmJjY29b7Dt16hTff/89r776apL5t96Fd/OZfeHh4Vy/fp34+Pgkw4ze1KNHDywWC+vXrwcS7zDMkycPK1asYNy4cfj7+7NixQpWrFhB7ty506ytREREREREREREJGNSsU9ERFxW3rx5CQ4OZsqUKXh6elKvXj2efvpp/P39mT17tmO9jz76iObNm1OhQgUAFi5cyKVLl8iePTtdu3Zl+vTpQOLdgUOGDEn2OqNHj8bHx4eOHTumKpdhGCQkJHDjxo1ky6ZPn45hGDRs2JD4+Hh27dpF1apV7+fwRURERERERERERDSMp4iIOL/Tp09z7tw5Dh8+nOS5d3FxcQwcOJA5c+awatUqChYsSNu2balfvz4ffvghffr0Yf369Sxbtow1a9YAEBUVxahRo3j11Vd58cUX2bdvH/369aNBgwaUKFECAJvN5ngNu93OkSNH6Nq1Kz4+Po75hmFw6tQpLBZLkqy3Tnt5eTmeEfjf/dapUwd/f38mTJjA4MGDb3vcdrs92TzDMBzPAoyJieHKlStkyZIFNzc3xzpRUVGEhYWRkJBAdHQ0+fLlw9PT8y4tLCIiIiIiIiIiIq5KxT4REXF6R44coU2bNtSsWdNxd922bdvo0KEDWbNmZcuWLQQGBgLQq1cvqlSpQosWLYiJiSEqKoqSJUvSqFEjDMPgxRdf5MqVK4wcORKADz/8kGXLljF58mQmTZrEiBEjWLx4Mb6+vgBYrVY2btxIVFRUkkw2m43ChQuzY8eOZHkNw7jt8/8sFgseHh4ADB8+nN69exMXF0eXLl2SrBcdHc24ceO4ePEiWbJkSbLs1qFG//rrL+rXr5/s9du3b59k+p9//nHc1SgiIiIiIiIiIiIZi4p9IiLi9Bo3bsz169eTzKtZsyZTpkyhcePGye5aq1mzJtu2baNQoUJ4e3vz2muvYbFYiI2NpUqVKrzyyisULFgQSLz7btmyZZQsWRIAHx8fqlatSt++fR37s1qtZM+ePclrxMbG4ubmRv78+VN9HDfvLrzplVdeoVOnTnh7ewNQrVo11q9fT5YsWTh69CgjRozgqaeeSrJNfHy8o9hXu3Ztrl+/jpeXF+7uyf+k22w2YmJikhUMRUREREREREREJOOwGDfHAhMRERERERERERG5T/Hx8Y7RTEREROThsZodQEREJDVCQ0M5f/682TFcyoEDB8yOICIiIiIimcjbb79Nq1at7mmbK1eukBb3IgwePJh58+Ylmz99+nS++eabe9pXTEwMkyZNSvLc9dQ6c+YMS5YsSTZ/7NixnDx5MsVtr169SlhY2B2/Ll26xKlTp+7aZlu2bGHdunW3XTZ//ny2bt16bwclIiJOT8N4ioiI04uPj6ddu3Y0atSIYcOGpbj+F198wQ8//JDq/UdGRjJx4kTq1av3ACmdy4kTJ6hXrx5r166lcuXKZscREREREZEMLj4+nqVLlzJixIh72q5v374EBwezefNmLBYLkPge7XaFNqvVSo4cOZLNv379OlOmTGHixIkAbNy4kVq1auHl5cWff/6JzWajZ8+eqc506NAh+vfvj4eHB3369Lmn4/n111/p06cPZcqUoWLFigAEBQUxbNgwfH19ef3112+7XUJCAkWLFk3160RHRzseCfFfCxcu5OrVqzRq1CjZsu+++47AwEDKli0LQM6cOVP9miIi4rx0Z5+IiDi9YcOGkT17doYMGUJAQAAWiwWLxYK3tzeVK1dm/vz5SdaPi4tjw4YNqf6aOHEiCQkJ95Tp6tWrdOrUCV9fX8qXL8+GDRvuaXvDMHjnnXcoVKgQWbNm5fHHH+evv/5Kss7LL7/sONabX126dEm2rzVr1lCiRIkk80qUKMHUqVNp06YNN27cuKdsIiIiIiIi92ratGmEhITQp0+fZO9jLBYLxYoVS/Ys9tjYWH755RfHNjfVq1ePnDlzJvsqU6bMbV971qxZFChQgO7du2O322nVqhXjx48HwM3NDav13j4CrVKlCu3bt2fUqFHExMSkuP7x48c5duwYwcHBNGzYkGrVqnH8+HGCg4MJDg7m448/pkKFCjRv3pzg4GCOHz/OuXPnkuzD3d2dK1eucP36daKjo4mOjmb58uUASeZFRERw+fLlJIW+0NBQLl68yLVr1wgLCwPAYrEQFhbGtWvXuHDhguM4cuTIgYeHB126dOHDDz+8p3YRERHnpTv7RETEqR08eJB58+Zx4MABxxu0Dh06MGjQIEJDQ1m4cCEvvvgihmHw4osvPrRcbdu2JSgoiDlz5nDy5ElatmzJvn37KF68eKq2//rrr5k/fz6TJ0/G19eXL7/8ksaNG3Ps2DHy588PwK5duxg1ahTNmzd3bJc7d+4k+zl48CAvvPAC2bJlS/Ya7du356effmLs2LGMHj36AY5WRERERETkzq5cucIHH3xA9erV8fb25ueff3Ys2717N02aNGHq1KnJ3rcsXbqU4sWL06lTJy5evOh4L5Q9e3ZGjBjByJEjHevOnz+f9957L9lrx8bGMn78eLp164abmxvbtm0jJiaGbt26ERMTg81mw2azOYpd0dHR+Pr6Ot5f7tq1Cy8vL9zdk35M2rVrVzp06EBwcHCS+YZhkJCQQN68eR15O3bsyMGDB/Hy8nKs9+KLLxIVFYWvr69jXuXKlR3bd+zYkVmzZiXZ93/f7918/uGt+by9vcmePXuS9YYNG8asWbPw8vLCarUSExOD3W5n9erV2O12YmJiWLVqFQ0bNnSs07dvX5577jk+/PBDsmTJkqxdRUTEtajYJyIiTu2zzz6jX79+5MmTxzEvb968VK9eHYDGjRtz4MABZs+e/dCKfWvWrGHTpk1s27aNxx57DIC9e/fyySef8NVXX6VqHwsXLuTtt9+mbdu2ADz55JNkz56dtWvX8uKLLxIdHc3hw4dp1qyZ41j/a/v27TRt2pSSJUsSEhJy23XGjRtHYGAg77//Pp6envdxtCIiIiIiIndmGAZdu3alQoUK/PTTT5QpU4bly5fz0ksvce3aNXr06MHLL7+c5CLGmz755BNGjhyJxWLh1VdfJWfOnCxYsCBZ4e1uPvroI86dO+cojH3//ffExcVRqFChJOvdOiJMUFAQAQEBJCQk3PH9VkrGjh3LkCFDgMSCYXR0tKOQBjB+/Hhmz57NoUOHkmxns9mIi4tLUmA7fvw4FosFNze3JOteunQJgFOnTiVZZhgGcXFxuLu7O0Z1mTp1qmP5wIEDuXLlCnPmzEmW+2a+Ro0acejQIRX6REQyCBX7RETEadntdpYtW8aOHTvuul716tXv+PDxe7Vhwwbq169/x+VBQUGsX7+ekiVLOgp9AM8++yxDhw5N9ev89yH0CQkJ2Gw2xxutPXv24Obm5njGw+1s2rSJiRMnYrFYklzxeqtChQoRGBjImjVreOaZZ1KdT0REREREJDVGjBjBli1b2L17N9mzZ+fzzz+nW7duBAQE0L9/f4oWLXrbiyLnzZuHxWKhVatWBAUFsWbNGpYsWeJYHhMT4xiSErjt4wl27drF+PHjyZUrFwBRUVHMnTuXBQsW0KBBAwAGDBhAQkICn3/+OYZhEB0dTeHChYHEoTOjo6Px9PR0FMGKFy9O//79efPNN+94zLd7DETx4sUdxblb3To86U0lSpTg+PHjjul27dpx5MgRvLy8kq3v6+tLtWrVkswzDAObzUblypXZsmULADVq1CAoKAhIbCu73c6KFSsc23Tq1Il58+YRFxeH1Wrlyy+/JCEhgaFDh/LOO+/c8VhFRMQ1qNgnIiJO6/z583h6elKsWLG7rnfx4kXy5s2bJq9ZvXp1du/efcflBQsW5Ny5cwQGBiaZ/8gjjxAUFITNZkt2NebtNGzYkK+++opmzZpRtGhRhg4dSp48eWjSpAkAf//9N97e3tSqVYsjR45QqFAh+vTpQ79+/Rz76N+/P1ar9bZXa97qySefZN++fSr2iYiIiIhImnvjjTeoV68eAQEBADz33HMsX76cunXrUr58eTZu3HjbUUaWL1/OyZMnKVasGGFhYTRu3Jhnn33WsXzcuHGMGzcuyTa3vje8fv06rVu35uWXX+bIkSNA4p2Cfn5+dOrUyVG8y5IlCwkJCY4hN//r1mffAcTHx6d4t9vt7jzcvXs3Hh4euLu7ExUVxSOPPMK8efMc7/Hg3yLdf4uFe/fuBSAkJISIiIi7vjYkjnZz6/CgkFjg++mnn/Dz88PPz4+4uDguXLhAtWrVyJIlC8OHD+fdd9/lhRde4OTJk+zdu5cbN27ozj4RkQzi3p5OKyIi8hBduHCBAgUK3HF5XFwcixYtYvny5UneFD6IbNmyUbly5Tt+eXp6Eh0djZ+fX7Lt4uPjk1x5ejcTJkzAz8+PSpUqkTNnTubOncuqVascz17Yvn07np6edO/enRUrVtCmTRv69+/PzJkzHftI7UPmCxQowPnz51O1roiIiIiIyL3w9/d33EV3/PhxXnvtNRYtWkTz5s05duwYL7zwAqtWrUpW4BoyZAg//PADv/zyC3a7nUmTJiVZPmLECAzDcHx9++23SZZny5aN4cOH8+mnnzrmtWvXji+//DLV75VuJywsjB49emCxWG771bt37yTrh4aGEhISgre3t6MIuHHjRmw2G0888USSdS0WC+7u7nh6enL27Fni4uKSLB86dCilSpVK8eu/bQH/Pt9v8uTJLFu2jFWrVvH55587lvv7+1OgQAEuXrxISEgIfn5+FCxYkJw5c953W4mIiPPQnX0iIuK0rFYrdrs92fyvvvrKMQyM1Wqla9eu9O3bN01e8+aVlnfi7u6Ol5dXsrv3bg61Eh0dnarXef/99wkLC2PKlCnkyJGDKVOm0LJlSzZv3kyJEiUYOXIko0ePpmjRokDinYAXL15kypQpdOvW7Z6OKbV3G4qIiIiIiNyrq1evsnz5cr777js2bNhA27Zt+fvvvylfvjzHjh1j7NixtGnTBk9PTx577DECAwNp0qSJ4463F154gV69elGmTJl7fu3XXnstybSnpydFixbl8OHDjnnh4eEkJCQ45tntdmJiYqhYsaKjQHZTZGQkUVFRLFy4kCpVqiR7vQYNGlCkSJEk8253B+JNN9/P3cmhQ4coW7Zskvzt2rVzDGeakJCAm5tbkqE9S5YsecfnscfGxlK7dm3i4uJYv349LVu2TFJQTEhI4MSJE8THx3Pp0iXy5ct313wiIuI6VOwTERGndac70jp27MiQIUOYPn068+bNY/z48cnepN2vjRs3pvjMPn9/f44ePZpk/tWrVwHImjVriq9x+fJlJk2axLZt2xzPXmjfvj3ly5fnk08+YerUqRQvXjzZdk8++ST/+9//sNvt93Slakp3SIqIiIiIiNyrK1eu0LRpU3bv3k1AQAC1a9dm3bp1jgJXcHAwHh4eDB8+nLfeeot169axd+9evv32Wzp27AjAl19+yaZNm/j77785fPgwly5dom7dukDiHXbBwcFJXi8lb775Jlu2bMHd3T3JBZmGYfD7778DicN0xsbGcvbs2WRDe+7evRuLxcIzzzzjGHXlVqGhockKZKNGjWL06NGO15wyZQr9+vXj0KFDPPLII7fNabPZiI2NTTaE6H+LeD/99BNdunTh4MGDSfZ1p/e/U6dOZd26dcTExACwbNmyJO12+PBh4uPjKV++PNu2baNVq1a33Y+IiLgeFftERMRpFShQAKvVytGjRyldurRjfp48eahcuTIjR45kzpw5TJo0iQ8++CBNXjM1z+yrVKkS8+fPJz4+3vEma9euXWTJkiXZ8J63c+LECWw2GxUqVHDM8/Ly4tFHH+XkyZMYhsGGDRuoXr16kjeYV69eJSEhgYSEhDteyXk7GzZsYPTo0aleX0REREREJCV58uThnXfeoUSJElStWhU3Nzfmzp17x/VvXsx58+LFffv28eabb+Lh4UG+fPnIli0blSpVYsuWLQB8/vnnSYahBFJ8nvuvv/6abF737t1JSEhI8VnnAIsWLeLJJ5+8baEvIiKCmJgYChUqlGS+l5eX4/8bN25kwIABtG3blvj4+CTFSkgcSSY2NhZ/f39y5cqV7DUMw0gyHRwcTL58+ZIVDf+73k19+vTh+++/p1evXmTLlo1x48YRFxdH//79Adi6dStFixalbt26/PXXXyr2iYhkIHpmn4iIOC2LxULbtm1v+zwCSHwoee/evZk8eXKqHmKeGql5Zl/Lli2JjIxk+vTpQOKzA7/++msaNmyYZHiVO8mTJw8AO3bscMwLDQ3lr7/+crxx7Ny5M/Pnz3csj4+P57vvvqNKlSr3VOg7fvw4J06coF69eqneRkREREREJDWee+45qlatCiTelfbzzz8nec7eza/Zs2c7imI3RykJDAxk8uTJrFq1ipCQECIjIx2FPkj5mX1p7fTp08yZM4fu3bvfdvmlS5cAKFy48G2Xr1y5kubNm9OwYUN27NhB5cqVqVSpEmXKlKFUqVKO95SVKlVi8eLFt91HfHx8kunly5fTsmVLYmNj77reTXPnzsXDw4NZs2bx+eef4+3tzTfffONYvnr1ap566inq1KnDihUrbt8QIiLiklTsExERpzZgwAC+/vprTp8+fdvlgwYNIjY2li+//PKhZcqTJw/vv/8+b7/9Nh06dOCxxx7jwIEDDB8+3LHOkSNHOHHixG23L1GiBBUqVKBdu3a89dZb9O/fn2rVqnHt2jV69+6NxWLhjTfeYPDgwYwZM4Zp06ZRv359Dh48yKhRo+4pa9++fXnnnXccD4oXERERERFJDyk9WuF2zxF/4403aNiwITabjd9++42ffvopndLdXXx8PC+99BJly5blxRdfvO06N9/f/ffOvri4ON59911atmzJSy+9xE8//cTx48eJjo4mPDycrl270qRJE8LCwggPDyc2NpaePXve9jWqVq1KnTp1AJg/fz5//PEHPXr04KWXXqJp06Zs2bKFPn36UKlSpdtu37VrV+x2O6+++ipvv/028fHxjteKjIxk1apVtG7dmqZNm3Ly5Em2bt16X+0lIiLOR5/8iYiIUwsICGDgwIE8//zzrFu3LtnyfPny0bNnTz777DP69u2Lj4/PQ8n13nvvUaRIEb755hvy5MnDxo0bqVGjhmN5z5498fPzu+2bVYvFwpo1axg4cCDffvst169fp0SJEsydO9exjyFDhhATE8MXX3xBZGQk1atX59dff6Vx48apzvj5559z5swZ3nrrrQc+XhERERERkZSEhIQkG7oSEp+399+hJydNmsSCBQsICgrCzc2NwMBA6tevzyOPPMKNGze4cuUKhw8fdqx/4cIF4uPj2bdvH3ny5KFgwYKOZXa7HbvdfttMNpsNm812x8zR0dG88MILHDp0iG3btiV5Pvq5c+c4duwY586dY8yYMRQrVoycOXMm2f7VV19l+fLlLFiwgHLlynHo0KEkhc+wsDCioqKSHEtcXBwWi4WKFSsm2VePHj24cOECgwYN4rPPPuPDDz+kUqVKDBs2jBEjRlC7dm1q166d5JEQtx6nm5tbktFm3N3diYyMJHv27MyZMwdfX19at26Nl5cXXbt2ZfDgwWzatClVI9SIiIhzsxh3GuRZRETESRiGQZcuXShVqhQjR45Mcf1PPvnknoYkCQsL45NPPqFRo0YPkNK5nDhxgrp16/LHH3+k+FwLERERERGRB+Xt7Z1suMlbFSpUiLNnzzqm//zzT7Zs2UKLFi0oX748kDhCSqVKlfD09ExSdLvJMAwSEhJ45513koysUrNmTRo1asRHH32UbJtOnTphs9luO3SmzWbj6aefZv/+/axbty5Z8e3IkSOULVsWgIoVKzJp0iQaNGiQZJ0bN24QEhJCQEAA5cqV49y5cykWz+Lj4ylZsiR79+4FYN68eWzbto0dO3awa9cuSpcuzZgxY2jTpk2S7Xbt2sU777zD+vXrqVOnDnPnziUgIACAUqVKMXv2bJ566inef/99fHx8ePTRR/nyyy958skn+eabb+jfvz+DBw8GEt8HBwYG8uqrr6bqfbaIiDg3FftERMQlxMXFER8fT9asWc2O4jKuXbt224e+i4iIiIiIpLVTp07h7+9PlixZHvprR0ZG4uHhgbe39z1ve/HiRaKioihRosRtl//999+UKFECX1/fB415R1988QWzZ8+mfv36tG7dmjp16ty1YLh8+XK++OILVqxYkapjNgyDP//8kypVqiQZDefUqVMULFgwxSFYRUTE+anYJyIiIiIiIiIiIiIiIuKikt8PLyIiIiIiIiIiIiIiIiIuQcU+ERERERERERERERERERelYp+IiIiIiIiIiIiIiIiIi3I3O8DDZLfbOX/+PNmzZ7/rQ25FRERERETMYhgGkZGRFCxYEKtV12eKiIiIiIjI3WWqYt/58+cpUqSI2TFERERERERSdObMGQoXLmx2DBEREREREXFymarYlz17diDxTXOOHDlMTvNg7HY7ly9fJm/evLra10moT5yP+sT5qE+cj/rE+ahPnI/6xPlk9D6JiIigSJEijvcvIiIiIiIiIneTqYp9N4fuzJEjR4Yo9sXExJAjR44M+QGHK1KfOB/1ifNRnzgf9YnzUZ84H/WJ88ksfaJHD4iIiIiIiEhqZNx3xiIiIiIiIiIiIiIiIiIZnIp9IiIiIiIiIiIiIiIiIi5KxT4RERERERERERERERERF6Vin4iIiIiIiIiIiIiIiIiLUrFPRERERERERERERERExEWp2CciIiIiIiIiIiIiIiLiolTsExEREREREREREREREXFRKvaJiIiIiIiIiIiIiIiIuCgV+0RERERERERERERERERclIp9IiIiIiIiIiIiIiIiIi5KxT4RERERERERERERERERF6Vin4iIiIiIiIiIiIiIiIiLUrFPRERERERERERERERExEWp2CciIiIiIiIiIiIiIiLiolTsExEREREREREREREREXFRKvaJiIiIiIiIiIiIiIiIuCgV+0REREREJNMxDINVq1bx448/mh1FRERERERE5IGo2CciIiIiIpnKzp07adiwIc2bN6dPnz5ERUWZHUlERERERETkvqnYJyIiIiIimYZhGPTq1Yvff/8dgAsXLrBgwQKTU4mIiIiIiIjcPxX7REREREQk07BYLIwbNw6AEiVKsGjRInr06GFyKhEREREREZH7p2KfiIiIiIhkSDdu3GDs2LFs3bo1yfyGDRvyww8/cPDgQZ577jksFotJCUVERCQzmTNnDhaLBYvFgo+PD7Vr12b37t1pst969eo98Dpp+XoiIvJwqdgnIiIiIiIZis1mY9asWZQuXZphw4YxcOBADMNIsk6bNm3w9PQ0KaGIiIhkVhUqVCA0NJQDBw5QqlQp2rRpQ3x8/APt84UXXmDFihVplFBERFyRin0iIiIiIpIhGIbBypUrqVSpEt26dePcuXMA/PXXX+zZs8fccCIiIiKAm5sbfn5+FC9enMmTJ3PmzBmOHz/+QPv09PQkW7ZsaZRQRERckYp9IiIiIiLi8rZv3079+vV55plnOHDggGN+y5Yt+eeff6hSpYqJ6URERESS8/Lywmq1cuPGDXbs2EHNmjXx9fWlbdu2hIeHO9b77rvvKF68OFmzZqVJkyZcuXIlyX7uNKzm6NGj8ff3p3Tp0kmGCx05ciSvvPKKY3rDhg0EBAQ4pqdPn07RokXJnj07bdu25fr162l2zCIikj6cptjXtGlT5syZA8DGjRt59NFHyZMnD59++mmS9ZYsWUKxYsUoWLAg3333nQlJRURERETEWZw4cYKOHTtSs2ZNNm7c6Jj/2GOPsXHjRpYvX065cuVMTCgiIiKSnGEYTJ48mezZs5M/f36aNWtGs2bN2LdvHxEREQwYMACAyMhIXn75ZcaOHcuBAwdwd3dn4sSJKe5/+fLlfPbZZyxZsoQ5c+Ywf/78VOU6cOAAvXv3ZsaMGRw8eJCrV68yZcqUBzpWERFJf+5mBwBYsGABv/76K506deLy5cu0atWKAQMG8Pzzz9OpUyeqVKlC/fr12b9/P507d+arr76iZs2atG3blqpVq1KmTBmzD0FEREREREzQs2dP1q9f75guWbIkY8eOpV27dlgsFhOTiYiIiCT3zz//4OfnR1xcHNmyZWPevHls2LABDw8PRowYgcViYeDAgbz00ksAuLu74+7uTlxcHAUKFGD58uXY7fYUX+fHH3/khRdeoE6dOgB069aN7du3p7hdiRIluHjxIt7e3mzbto34+HiOHDnyYActIiLpzvQ7+65du8aAAQMcBbsFCxZQsGBB3n//fUqVKsXw4cOZOXMmADNmzKB+/fp0796dihUr8sYbb/Dtt9+aGV9EREREREz04YcfApA3b16+/PJLDh48SPv27VXoExEREadUpkwZ9uzZQ/fu3SldujTPPPMMZ8+e5fLly+TMmRM/Pz86dOjA5cuXiYmJIUuWLHz//fdMmzYNf39/WrVqxZkzZ1J8nQsXLlC0aFHHdIkSJe647o0bNxz/j46OpkePHgQEBDBmzBjc3Nyw2WwPdtAiIpLuTL+zb8CAAbRp04bo6GgA9u7dS/369R1vzh977DGGDBniWNasWTPHto899hgffPDBHfcdGxtLbGysYzoiIgIAu92eqitgnJndbscwDJc/joxEfeJ81CfOR33ifNQnzkd94nzUJ87BZrMxZ84cSpQoQZ06dRx98thjjzF//nxatGhBjhw5AFy+r1w9v4iIiNyZp6cnAQEBDB8+nEceeYR169ZRuHBhqlWrxv/+9z8gcYjP8PBwPDw8uHbtGvny5WPLli3ExMTQs2dP+vbty/Lly+/6Ov7+/pw/f94xffr0acf/LRZLkvONXbt2Of7/+eefEx4ezoULF/Dw8GDw4MGEhISk1eGLiEg6MbXY9/vvv7N+/XoOHDjAm2++CSQW5G59pkaOHDkcf5giIiIoXrz4bZfdztixYxk1alSy+TevjHFldrud8PBwDMPAajX9Bk1BfeKM1CfOR33ifNQnzkd94nzUJ+YyDIN169bx0UcfceTIESpUqMCqVauIjIx09EnDhg2JiYlx+XP8myIjI82OICIiIuksT548vP3224waNYqff/6ZgQMHsn37durWrcu8efOYOHEiZ8+eJSQkhHr16vHjjz8SGBgIQEJCQor7b9WqFb1796ZDhw64ubkxffp0x2euhQoVYtGiRcTHx3Pq1Cm++eYb3N0TPyaOjIzEbrdz+fJlNmzYwNSpU2nbtm36NYSIiKQJ04p9N69EmTp1KtmzZ/83kLs7Xl5ejmlvb2/HreR3W3Y7Q4cOpX///o7piIgIihQpQt68eR1X/boqu92OxWIhb968+tDJSahPnI/6xPmoT5yP+sT5qE+cj/rEPNu3b+edd95h06ZNjnn79+/n0KFDVKhQIcP2ibe3t9kRRERE5CEYOHAgX331FTt37mT58uW88cYbdO3alfLly7N8+XLc3d0pW7YsEydOpHfv3ly8eJFKlSo5Hnl0N+3atWPPnj20bt2a3Llz07p1a44dOwbA888/z6JFiyhTpgyFChXi/fffZ+zYsQD07duXDh06ULp0aR5//HG6devG77//nq7tICIiD85iGIZhxgu/++67BAcHs2DBAgBeeeUV6tWrx7Zt28iTJw+jR48GICwsjEKFChEVFUWzZs1o37493bp1A2DPnj107tyZAwcOpOo1IyIi8PX1JTw8PEMU+0JCQvD398+QH3C4IvWJ81GfOB/1ifNRnzgf9YnzUZ88fMePH2fYsGEsXrw4yfxatWoxYcIEnnjiiQzdJxnpfYuIiIiIiIikP9Pu7Fu4cCGXL1/Gz88PSHwQ7KJFiwB44oknHOvt3r2bQoUKAVCjRg22bt3qKPbdukxERERERFzb5cuXGT16NFOnTk0yPFWpUqX4+OOPadOmTbJnzIiIiIiIiIhkdqYV+zZv3pzkDfzAgQOpVasWr7zyCkWKFGHdunXUrVuX8ePH06RJEyDx9vMnn3ySt99+m+LFi/PFF1/QpUsXsw5BRERERETS0Ouvv86SJUsc0/7+/owcOZLu3bvj4eFhYjIRERERERER52Vasa9w4cJJprNly0aePHnIkycPn332Gc2bNydbtmz4+fkxZ84cACpVqsTbb79N9erV8fb2plSpUrz++usmpBcRERERkbQ2fPhwli5dio+PDwMHDmTAgAFJnu8tIiIiIiIiIsmZVuz7r5sFPYBevXrRpEkTDh8+TO3atcmWLZtj2UcffUTnzp05d+4cdevWxdPT04S0IiIiIiJyvwzD4OeffyZLliw0btzYMb9ixYrMmTOHp59+mvz585uYUERERERERMR1OE2x77+KFy9O8eLFb7usXLlylCtX7iEnEhERERGRB7Vt2zYGDRrE5s2bKVWqFAcOHEgyROdLL71kYjoRERERERER12M1O4CIiIiIiGR8x44d47nnnqNWrVps3rzZMe/HH380OZmIiIiIiIiIa1OxT0RERERE0k1ISAhvvPEG5cqVY8mSJY75pUuX5ocffuC5554zMZ2IiIiIiIiI63PaYTxFRERERMR1RUVF8emnnzJ+/HiuX7/umO/v78+oUaPo1q1bkuE7RUREREREROT+qNgnIiIiIiJprl+/fkyfPt0xnTVrVgYOHMiAAQPInj27iclEREREREREMhYN4ykiIiIiImlu8ODBuLu74+bmRq9evTh+/DgjR45UoU9EREREREQkjenOPhEREREReSB//fUX4eHhNGnSxDGvZMmSTJs2jSeeeIIyZcqYmE5EREQkuYQECAuD0NDEf+PiwGb798tuh7KFjlE05wmwWP//yw3cfMA9G3hkS/zXPRu4eZl9OCIiksmp2CciIiIiIvfl6NGjDBs2jKVLl1KkSBGOHDlClixZHMu7du1qYjoRERHJTOx2OHMGgoLg5Ek4ezaxkHfz69bCXmgo3PJI4TvaNHkeRXN9mPKKVo9/C3+eOcErL3j7J35lKQDeBcCnEGQpCD6FwUMjHYiISNpSsU9ERERERO7JpUuX+OCDD/jmm2+w2WwAnDlzhjlz5tC7d2+T04mIiEhGdfVqYiHvZkHv1n9Pn4b4eJOC2eMhLjTx68aZlNf39odsJSF7Kcj+n389cqR/XhERyXBU7BMRERERkVS5fv06n376KRMmTOD6LZfD58uXj1GjRtGtWzcT04mIiEhGcuIE/P134teuXbB7N1y5YnaqNBITkvh15c/ky7z9wbcC+FWCnJWI96sMvuXwcPN46DFFRMR1qNgnIiIiIiJ3lZCQwMyZMxkxYgSXLl1yzM+aNSuDBw+mf//+ZMuWzcSEIiIi4qoMA44eTSzo3Szu7d6dONxmphQTAjG/waXfALjmW4Niu/dRMV9FqheoTvWCiV8V/CvgZnUzOayIiDgLFftEREREROSuhg8fztixYx3Tbm5u9OzZk+HDh5MvXz4Tk4mIiIirSUiAHTtg3Tr47bfEIl9kpNmpnNcRuw+xtlh2nt/JzvM7YVfi/Oye2aldrDb1A+rToHgDKuevjNViNTesiIiYRsU+ERERERG5q9dff53PPvuMmJgY2rVrx5gxYyhdurTZsURERMRFHDyYWNxbtw42boSICLMTuY6tUTG3nR8ZF8kvx37hl2O/AJDTOyd1itWhQfEG1A+oTwX/ClgslocZVURETKRin4iIiIiIOBw9epSTJ0/StGlTx7zChQvz5ZdfUq5cOR5//HET04mIiIgrOH/+3+Le+vWJ03J/fr58NlXrhcaEsuzIMpYdWQZAXp+81A2oS8PiDWlZuiWFchRKz5giImIyFftERERERIRLly4xatQopk2bRq5cuThx4gTZs2d3LO/WrZuJ6URERMTZHTwIixbB0qWwf7/ZaTIGu4cff1w7d1/bXr5xmSUHl7Dk4BJeX/k6NQvXpG3ZtrQr145Hcj6SxklFRMRsKvaJiIiIiGRi169fZ+LEiUyYMIGoqCgALl++zOTJkxk2bJjJ6URERMSZHT4M//sfLF4MBw6YnSbjCc8SAOx54P0YGPx19i/+OvsXg9cNplK+SrR9tC3tHm1Hef/yD7x/ERExn4p9IiIiIiKZUHx8PDNnzmTkyJFcunTJMT9btmwMHjyYt99+28R0IiIi4qyOHEm8g2/RIt3Bl95OkiNd9rv30l72XtrLiA0jKJO7DG3KtqFduXZUL1g9XV5PRETSn4p9IiIiIiKZiGEY/PTTTwwdOpQjR4445ru7u9OzZ0+GDx+Ov7+/iQlFRETE2Zw4Ad99l3gH3759ZqfJPHZE29L9NY5cPcLHf3zMx398TNk8ZelWpRsvV3qZvFnzpvtri4hI2rGaHUBERERERB6ezz77jLZt2yYp9LVv356DBw/y5ZdfqtAnIiIiACQkwA8/wNNPQ6lS8P77KvQ9bL9eu5TySmno8JXDDFo7iEKfFqLdonasOrYKu2F/qBlEROT+qNgnIiIiIpKJvPjii+TIkTgkVO3atdm6dSuLFy+mVKlSJicTERERZ3DuHIwcCQEB0K4drF0LhmF2qszHcPNmVUiQKa8db4/nh0M/0HxhcwImBTDi9xGcCjtlShYREUkdFftERERERDKoixcv8uuvvyaZlzdvXiZNmsTy5cvZuHEjtWrVMimdiIiIOAvDgDVroE0bKFYMRo1KLPqJeaJ8HiHWnv7DeKbkTMQZPtj0AY988QhN5jdh8YHFxNvizY4lIiL/oWf2iYiIiIhkMJGRkXzyySdMnDgRd3d3Tpw4Qe7cuR3Lu3btamI6ERERcRZXrsDs2fDNN4nP5RPnccaaO+WVHiK7YWfNiTWsObGGIjmK0LdWX16r9hrZPLOZHU1ERNCdfSIiIiIiGUZ8fDxTpkyhZMmSfPDBB0RFRREeHs64cePMjiYiIiJOJCgIevaEwoVh8GAV+pzR7lizE9zZmYgzDFgzgCKfFeHd9e9y6frDfbagiIgkp2KfiIiIiIiLMwyDpUuXUr58efr06UNISAgA7u7uvPHGGwwcONDkhCIiIuIMjh6FV16B0qVh2jSIdeKCUma3Puya2RFSFBYTxpgtYwj4PICeP/fk2NVjZkcSEcm0VOwTEREREXFhmzdv5oknnqB9+/YcO/bvByzPPfccBw8eZPLkyfj7+5uYUERERMy2fz88/zw8+ijMnQsJCWYnkrsxLG78eNF1breMSYhh2t/TKPtVWdotasf2c9vNjiQikumo2CciIiIi4qK+/fZb6tSpw19//eWYd3N60aJFlCpVysR0IiIiYra//4a2bSEwEL7/Hux2sxNJasT6BBAaH2N2jHtmN+z8cOgHas6oSf259dlyeovZkUREMg0V+0REREREXFSrVq3IkycPAOXKlWP58uVs2LCBmjVrmpxMREREzPTXX9CiBVSrBj/+CIZhdiK5Fxfc85kd4YFtCN5A7dm1afldSw6EHDA7johIhqdin4iIiIiIC4iMjGT9+vVJ5vn6+jJx4kSmT5/O3r17admyJRaLxaSEIiIiYrZ//oFmzeDxx+GXX8xOI/drf7yH2RHSzIqjKwj8OpCuy7pyJvyM2XFERDIsFftERERERJxYfHw8X331FSVKlKBly5acP38+yfKXXnqJ7t274+7ublJCERERMduFC9CtG1SuDKtXm51GHtSGyHCzI6Qpu2Fnzp45lP6yNAPXDORa9DWzI4mIZDgq9omIiIiIOCHDMFiyZAnly5fnjTfe4PLly0RHRzNq1Cizo4mIiIiTiIqCkSOhVCmYNUvP5MsofrgQZHaEdBGTEMPErRMp8UUJPt7yMdHx0WZHEhHJMFTsExERERFxMps3b+aJJ57gueee49ixY475HTt2ZPDgwSYmExEREWcxfz6ULg2jRiUW/SRjSMhSiODojHVn33+FxYQxdP1QSk0uxZw9c8yOIyKSIajYJyIiIiLiJA4dOkTr1q2pU6cOf/31l2N+3bp12bZtG99//z0lSpQwMaGIiIiY7e+/4ckn4cUX4T+je0sGEOJZ2OwID825yHN0XdaVunPqcujyIbPjiIi4NBX7REREREScwKpVq6hQoQLLly93zCtfvjwrVqzg999/57HHHjMxnYiIiJjtyhXo0QNq1IA//zQ7jaSXwzZvsyM8dJtObaLS15V4d/27GtpTROQ+qdgnIiIiIuIE6tWrR4ECBQAoVKgQM2fOZO/evbRo0QKLxWJyOhERETHTwoXw6KMwY4aey5fR/XH9htkRTBFvj2fMljFUmFqB1cdXmx1HRMTlqNgnIiIiIvKQxcfHs2XLliTzsmTJwoQJExgzZgxHjx7l1Vdfxc3NzaSEIiIi4gwuXIDWraFz58Q7+yTjW375jNkRTHUy9CTNFjSj45KOXIi8YHYcERGXoWKfiIiIiMhDYhgGixcvply5cjRo0ICTJ08mWf78888zdOhQfHx8TEooIiIizmLuXChfHm4Z4VsyOLtnbnaGXTQ7hlNYdGARZb8qy5fbv8Ru6HZWEZGUqNgnIiIiIvIQbNq0iccff5wOHTpw/Phx4uPjee+998yOJSIiIk7m7Flo0QJeeQVCQ81OIw9TqHcxsyM4lYjYCN5c9SZPzHyC49eOmx1HRMSpqdgnIiIiIpKODh48SKtWrahbty7btm1zzK9Xrx79+/c3MZmIiIg4m+nTE+/m++UXs5OIGY6T3ewITmnbuW1U/royM/6eYXYUERGnpWKfiIiIiEg6OH/+PD169KBixYr8/PPPjvkVKlRg5cqV/Pbbb1SvXt3EhCIiIuIsTp2Cxo3htdcgIsLsNGKW7TfizI7gtKLio+jxcw/a/q8tV29cNTuOiIjTUbFPRERERCSNbdu2jZIlSzJjxgzs9sRnjBQqVIhZs2axZ88emjdvjsViMTmliIiIOINvv4UKFWDdOrOTiNlWXdXz+lLy4+EfqTi1ImtOrDE7ioiIU1GxT0REREQkjVWpUoVChQoBkCNHDsaOHcuxY8fo2rUrbm5uJqcTERERZxATA927w0svwfXrZqcRsxluWVlzOdjsGC7hwvULNJ3flL6r+xKbEGt2HBERp6Bin4iIiIjIAzAMgx07diSZ5+npyfjx4+nbty8nTpxgyJAhZMmSxaSEIiIi4myOHoWaNWHmTLOTiLO47lMcm2GYHcNlGBh8vu1zakyvwT+X/jE7joiI6VTsExERERG5Txs2bKBmzZrUqlWLAwcOJFnWpk0bPvvsM/LkyWNSOhEREXFG//sfVK8O+/aZnUScSbA1l9kRXNI/If9QY3oNJm+bbHYUERFTmV7sCwsLY9u2bYSGhpodRUREREQkVQ4cOMAzzzxD/fr12bFjB3a7nSFDhpgdS0RERJxYbCy8/jp06gSRkWanEWfzd4zd7AguK9YWy1ur36LLD124EX/D7DgiIqYwtdi3ePFiAgIC6N69O4ULF2bx4sUAvPXWW1gsFsdXyZIlHdvs37+fGjVqkDNnTgYNGoSh29tFRERE5CE5d+4c3bp1IzAwkJUrVzrmV6hQgd69e5uYTERERJzZyZPw5JMwdarZScRZrQ29YnYEl7fgnwU8PvNxTlw7YXYUEZGHzrRiX3h4OK+//jqbNm3in3/+4auvvmLQoEEA7Ny5k5UrVxIaGkpoaCi7d+8GIDY2lpYtW1KtWjV27tzJwYMHmTNnjlmHICIiIiKZREREBO+++y6lSpVi1qxZ2O2JV14XLlyY2bNns2fPHpo3b25yShEREXFGP/4IVavCrl1mJxFnZVjcWX7xpNkxMoR9l/ZRfXp1fjn2i9lRREQeKtOKfREREUyaNInAwEAAqlatytWrV0lISODAgQPUqVMHPz8//Pz8yJ49OwCrVq0iPDycTz/9lBIlSjBmzBhm6knGIiIiIpKODh8+TK1atfj444+Jjo4GwNfXl48//pijR4/yyiuv4ObmZnJKERERcTaGAe+9B23bQni42WnEmcVkfYRIW5zZMTKMsJgwWn7Xko+3fGx2FBGRh8bdrBcuUqQInTt3BiA+Pp7PPvuMNm3a8M8//2C326lcuTLnzp2jbt26TJs2jaJFi7J3715q1aqFj48PAIGBgRw8ePCOrxEbG0tsbKxjOiIiAgC73e64GttV2e12DMNw+ePISNQnzkd94nzUJ85HfeJ81CfOp0SJEuTPn5/Q0FA8PDzo06cPw4YNI3fu3ADqKxNk9J+TjHpcIiKZSWwsdO0K331ndhJxBefc8gJHzY6RodgNO0PXD+WfkH+Y2Wom3u7eZkcSEUlXphX7btq7dy8NGjTA09OTQ4cOsXLlSsqUKcPkyZPJkycP/fr147XXXmP16tVERERQvHhxx7YWiwU3NzdCQ0PJmTNnsn2PHTuWUaNGJZt/+fJlYmJi0vW40pvdbic8PBzDMLBaTX30ovw/9YnzUZ84H/WJ81GfOB/1ifmOHDlCmTJlHNN2u52+ffuyevVqhgwZQtGiRbHZbISEhJiYMnPL6D8nkZGRZkcQEZEHcPUqPPssbNlidhJxFfviNUpEeln4z0KOXj3Ksk7LKJi9oNlxRETSjcUwDMPMAIZh8Pfff9OvXz/8/f1ZsmRJkuWnT5+mePHihIaG8tFHHxEfH8+nn37qWF6kSBH++usvChUqlGzft7uzr0iRIoSGhpIjR470O6iHwG63c/nyZfLmzZshP+BwReoT56M+cT7qE+ejPnE+6hPz7N+/nyFDhrBq1Sq2bt3KY489BqhPnFFG75OIiAhy5sxJeHi4y79vERHJbI4fh+bN4dgxs5NIWtg0+X1q5/ow3V/nzfhAvgzel+6vk5kVyFaAVZ1XUSl/JbOjiIikC9Pv7LNYLFSrVo25c+dSokQJwsLC8PPzcyz39/fHbrdz4cIFcuXKxf79+5NsHxkZiaen52337eXlhZeXV7L5Vqs1Q3woYLFYMsyxZBTqE+ejPnE+6hPnoz5xPuqTh+vs2bMMHz6cuXPnOoZPHDJkCL///jsWiwVQnzijjNwnGfGYREQygz/+SLyj78oVs5OIKzGw8OPFILNjZHgXrl+g7py6LOu0jLoBdc2OIyKS5kx7F7lx40YGDRrkmPb09MRisTBq1CgWLlzomL9161asVitFihShRo0abN261bEsKCiI2NhYcuXK9VCzi4iIiIjrCw8PZ+jQoZQqVYrZs2c7Cn1FihSha9euJqcTERERV/K//0HDhir0yb1L8CnCuRgN4f0whMeG02R+E3489KPZUURE0pxpxb7SpUszbdo0pk2bxpkzZxg2bBhPP/001apV47333mP9+vWsWbOGXr168dJLL+Hj40OdOnWIiIhg9uzZAIwZM4ZGjRrh5qZxrUVEREQkdWJjY5k0aRIlSpTg448/djzL2dfXl/Hjx3PkyBFefvllx119IiIiInczdiw8/zzc8iQZkVS75KHnyD1MsbZYnlv8HNN2TTM7iohImjJtGM8CBQqwZMkS+vbty8CBA2nSpAnz5s0jb968HDhwgHbt2uHm5kaXLl0YM2ZMYlh3d2bMmMHzzz/PoEGDsFqtbNiwwaxDEBEREREXc+7cOWrXrk1Q0L9DJXl6evLmm28ybNgwjRghIiIiqWazQe/eMH262UnElR20JX8EkaQvm2Gj54qeXLx+keF1h5sdR0QkTZj6zL7GjRtz4MCBZPPHjh3L2LFjb7tNq1atOHHiBLt27aJWrVrkzp07vWOKiIiISAZRsGBB8uXL5yj2de7cmQ8//JCAgABzg4mIiIhLsdngxRfhu+/MTiKubktklNkRMq0RG0YQEhXCF82+wGrRM5NFxLW55G+x/Pnz06JFCxX6REREROSubr2DD8BisTBhwgQaNWrErl27mD9/vgp9IiIick8SEhKH7VShT9LCTyGnzI6QqX214yueX/o8cbY4s6OIiDwQlyz2iYiIiIjczZkzZ+jatSslSpTg999/T7LsqaeeYu3atVStWtWkdCIiIuKq4uOhY0dYvNjsJJIR2Lz8+SfistkxMr1FBxbRfEFzbsTfMDuKiMh9U7FPRERERDKMsLAwhgwZQunSpZkzZw6GYTB48GDsdrvZ0URERMTFxcVB+/bwww9mJ5GM4qp3UbMjyP9bH7SeVt+1IiYhxuwoIiL3RcU+EREREXF5sbGxfPrpp5QoUYJx48YRE5P4Jt3Pz48OHTpgs9lMTigiIiKuLDYW2raF5cvNTiIZyTF7VrMjyC3WB62nzf/aEJsQa3YUEZF7pmKfiIiIiLgsu93OwoULKVu2LAMGDODatWsAeHp6MmDAAE6cOMGgQYPw8PAwOamIiIi4qpgYaN0aVq40O4lkNNtuqKjkbFYfX81zi58j3hZvdhQRkXuiYp+IiIiIuKSwsDBq1KhB586dCQ4OBsBisdClSxeOHDnCJ598Qq5cucwNKSIiIi4tOhpatoRffzU7iWREK6+cNzuC3MbPR3+m09JOJNgTzI4iIpJqKvaJiIiIiEvy8/NLUsxr3Lgxu3bt4ttvvyUgIMC8YCIiIpIhREdDixawbp3ZSSQjMtxz8PuV02bHkDv44dAPvPjji9jsehyAiLgGFftERERExCVcuHABwzCSzBs3bhxVq1ZlzZo1rFmzhipVqpiUTkRERDISmw06dYLffzc7iWRUET4BGCmvJib6fv/3vLr8VeyG3ewoIiIpUrFPRERERJxaWFgY77zzDsWLF2fFihVJllWtWpWdO3fSuHFjk9KJiIhIRvT667B8udkpJCMLsviZHUFSYd7eefT8uWeyiw5FRJyNin0iIiIi4pRiY2P59NNPeeSRRxg/fjyxsbEMGTKEhISkz86wWCwmJRQREZGM6IMPYNo0s1NIRrczWsNDuooZu2fQ/9f+ZscQEbkrFftERERExKnY7XYWLFhAmTJlGDBgAKGhoQB4eXnRokUL4uLiTE4oIiIiGdWMGTBihNkpJDNYc+2y2RHkHkzaNonJ2yabHUNE5I5U7BMRERERp7Fu3TqqV69Oly5dOHXqFJB4597LL7/M0aNHGT9+PD4+PianFBERkYxo5Uro1cvsFJIZGFZPVlw6aXYMuUf9fu3HiqMrUl5RRMQEKvaJiIiIiOliY2Np2rQpjRs3Zvfu3Y75TZo0Yffu3cyZM4eiRYuamFBEREQysm3boEMHsGlkRXkIon0eIdqekPKK4lRsho1OSzrx94W/zY4iIpKMin0iIiIiYjovL68kd+xVqVKFtWvXsnr1aipVqmRiMhEREcnojh6FZ56BGzfMTiKZxRm3PGZHkPsUFR9Fy+9acjbirNlRRESSULFPRERERB66sLAw7HZ7knljx46lZMmSLFiwgJ07d9KoUSOT0omIiEhmcfEiNG0KV66YnUQyk71x+kjWlZ2PPE+LhS2IjI00O4qIiIP+soiIiIjIQxMTE8PEiRN55JFH+N///pdkWZkyZThy5AgvvPACVqtOU0VERCR9xcZCy5YQFGR2Esls1oddMzuCPKB9l/bRYUkHEjQcq4g4CX2KIiIiIiLpzm63M3/+fMqUKcPAgQMJDQ1l2LBhxMbGJllPRT4RERF5WPr0gZ07zU4hmY1hsfLjxZNmx5A0sPr4at785U2zY4iIACr2iYiIiEg6W7t2LdWrV+fFF1/k9OnTAFgsFurWrcsNPRxHRERETDB9OsycaXYKyYzishTjcpzOgTOKr3d9zWdbPzM7hoiIin0iIiIikj727NlDkyZNePrpp9m9e7djftOmTdmzZw9z5swhZ86cJiYUERGRzGjHDnhTN+NIisI4GHyO0Ki03esF93wQnrb7FHMNXjeYP07/YXYMEcnkVOwTERERkTRlt9t55ZVXqFq1KmvWrHHMr1q1KuvWrWPVqlUEBgaamFBEREQyqytXoH37xOf1idzZYiCAcQtXUPhNWLwt5S02HoJHB0GenvDpL//On7sJcvZI/Bdg/s4oCEuPzGKWBHsCHZd05HLUZbOjiEgmpmKfiIiIiKQpq9WK3W7HMAwAAgICWLBgATt27KBhw4YmpxNJFBQEixaZnUJERB4mmw06dYL/H1Vc5A7CgdeBTcwd1pOvXoZBC+++xeUIaDURnn8cto6CBX/A7wcSl325Fha9lfgvwKq9l6FYeuYXM5yLPEfnHzpjN+xmRxGRTErFPhERERF5IDExMSQkJCSZN3r0aAoWLMinn37K4cOHeeGFF7Badeop5goLSyzwvfQSPPccfP01hGsYLRGRTOO992D9erNTiPOLACYBiSNRVC0OV6/ffYsFf0DBnPB+GyiVH4a3gZkbE5dduw71Hk3898xVOO4ekZ7hxURrT65l9MbRZscQkUxKn7iIiIiIyH2x2WzMmzePMmXKMHv27CTLihUrRnBwMP369cPLy8ukhCJg//+Lq222xALfxImQNy+MHw+rV4Ovr7n5RETk4fjxR/j4Y7NTiGsoAnQGIMFm47NV0Kb63bfYexrqlwOLJXH6sRKwKyjx/9m94dhFyJEFFuzITkipG+kXXUz3waYPWHdyndkxRCQTUrFPRERERO6JYRj8+uuvVKtWjZdffpnTp08zfPhwoqKikqzn4eFhUkLJ7AwD/vkn8UPd5s0hIgLc3GDMGPj118SCX4MG4OlpdlIREXkYjhyBV14xO4W4nr08O2wSq/fBFy/dfc2IaCie99/pHFngfGji/59/AgKHQrsacM2eA3QdXIZmN+x0/qEz5yPPmx1FRDIZFftEREREJNX+/vtvGjduTNOmTdm7d69jfpUqVQgLCzMvmMj/mzsX2rWDrl1h0yZ45pl/7+6rUQP8/EyNJyIiD1lsbOKd3REaOVHuWSAT+7xAqXzQffrd13S3gpf7v9PennAjLvH/77SEq98kDu+Zv0xu+AZYDBjplVvMFhIVQqclnUiwJ6S8sohIGlGxT0RERERSFBwcTJcuXahWrRrrb3nYzc3pX375hUKFCpmYUDKrGzdg5Uq4+djIw4ehYkWYMgVWrIA33lCBT0QkM3v//cS7vUXunYUyRQswtxf8sBPCou68Zq5scDny3+nIaPC8pfjn6wP7z8KmoHAIIPGxgJfTKbY4hc2nN/Pu+nfNjiEimYh7yquIiIiISGZlGAZDhgxh0qRJxMXFOeYXL16cjz76iI4dO2K16voxebjsdti1y4OtWy38/jvExUGRIhAYmDhU583n5YiISOa2ZUvi0M0i92YjsAKYACQW7SyA9S7nFzUegYV//ju9OxgK5fx3+sBZqFAY5p28BnmBKCA6zYOLk5nw5wSeLvE0DR9paHYUEckE9MmMiIiIiNyRxWIhMjLSUejLnTs3kyZN4tChQzz//PMq9Ikp3n7bwrBhOTh4ELp3h59/Tiz0gQp9IiKS6Pp1ePnlf4dyFkm90sA0YBqXQsMZtgiergg5fCDiBsTfZmTGVtXgj6Owbn/i8vEroEngv8t/2AHPPuHLaSMSQkm8s8/74RyNmMfAoNvybkTGRqa8sojIA9KnMyIiIiLiYLPZiI+PTzJvxIgR5M2bl6FDh3LixAnefvttvLy8TEoomc21a/Ddd9ClCxw9mjjvpZcMvvgijMWLDbp2hfz573PnwcGJO58xI/Hf4OA0Si0iImYbOBBOnjQ7hbimAsAS4HNeHvMNN2JhXu/EJYFDYeWe5FvkyQ6fdYHm4yHf63DkArz3bOKyBBv4ZoEb2YtDGWA/4EbiHX6S4Z0KP8XANQPNjiEimYCG8RQRERERDMPg119/5Z133uHll1+mf//+jmX58uXj1KlTZMmSxcSEktn88QcsXZr4r8UCder8e9dejRoQEmK7/7v4LlyATz6BzZshIgKs1sRbP3LkSHyhAQOgQIE0OxYREXm4Vq+Gb74xO4W4tsbAAVZPeJ/auT50zA3+/M5b9GqUeDff4fNQuyxk+/8799zd4K2msBNf8APeTs/c4oym/T2N9uXa07hEY7OjiEgGpjv7RERERDK5v//+m8aNG9OsWbP/Y+++42s8/z+Ov062hCRCEDMRoygdtlr9VrW2Dq1uNErpsEerKDVqVluqpa0ObXX4oXaHvYq2ttaKGSQiCdnJOb8/bg6pkCDJfXLyfj4e58F93Xfu+31yC875nOtzsXPnTt555x3Onz+f4RgV+iS32Wywcydc/qO3ZYsxq2/AAFixAiZMgMqVc+BCERHQq5fR+9PdHUJDrzzc3WHRImN/REQOXExERPLa+fPw4otmp5CCKqQEtLr7SqHvalsTU68dlAIj7Ocw4pLjzI4hIk5MxT4RERGRAurIkSM888wz1K5dm99++80+XqlSJSIjI01MJgXJqVMwaxY88gh06wa//GKMv/46zJkDnTqBn18OXnDSJNizBypWhIAAY1YfGL8GBBjje/bAlCk5eFEREckrr7xi/Nsi4miWnztjdgQx0bHYY2rnKSK5SsU+ERERkQLm3Llz9OvXjzvuuINvvvnGPl6xYkXmzZvHli1bqFKliokJpaD4+GNo3x6++gruuQdmzoTHHzf2ubrmwgXDw43WnYGBxiy+zLi7G/vXrNEafiIi+cyPP8JV/7URcRg210IsO3vE7Bhisll/zmLloZVmxxARJ6U1+0REREQKkGnTpjFixAhiY2PtY8WLF+ett96iZ8+eeHh4mJhOnFl6utGac8kSaNEC7r8fGjWCcuWM3+dJp9hNm4w1+kJDb3ycvz8cOgSbN0NwcB4EExGR23XmDLz8stkpRDIX712RVNses2OIAwhbFMbuXrvx9fQ1O4qIOBnN7BMREREpQE6dOmUv9BUqVIg33niDgwcP8tprr6nQJ7ni6FF47z1o3Rpeew0OHDDW5wOoWdMYz7MlIePjjXadLlm8DLp8zMWLeZNLRERuW79+EBVldgqRzB1zCTA7gjiI43HH6b+iv9kxRMQJqdgnIiIi4qRsNhtpaWkZxoYMGUJgYCAvvvgiBw4cYMyYMfjl6IJoIhAdbcywANi+HRYvhgcfNNp1zpsH//ufScF8fMBqNR43cvmYwoXzJpeIiNyWVavUvlMc21/JZicQRzL7r9msPbrW7Bgi4mRU7BMRERFxQtu2beOBBx5gzJgxGcaLFi3KwYMHmT17NmXKlDEpnTij5GRYuRJefx0efhg+/dQYb9sWli+HAQOgWjWwWEwM2bAh+PpCTMyNj4uJMY5r2DAvUomIyG1ITYXevc1OIXJjv8acMzuCOJjXlr1GujXd7Bgi4kRU7BMRERFxIocPH+app56ibt26rFq1iokTJ3Lm8hSrS3x9tT6E5Kw1a+Chh+CNN+DCBRg8GF55xdjn4QFujrJSeHAwNGkCkZHGu8OZSU01+sA1awYVKuRpPBERuXlTpsC+fWanELk+m8WVhacPmx1DHMyOMzuY9ecss2OIiBNxlJfdIiIiInIboqKiGDNmDNOnTyf1qiJGyZIlOXbsGCVLljQxnTibEydg6VIoVgweewwqVoQnn4Q2baB8ebPTZWHAADh8GPbsgcBA8Pc31uezWo0ZfVFRUL26sfiTiIg4tOPHYfRos1OI3FiydwjnUw+aHUMc0LDfh/FkjScpWqio2VFExAloZp+IiIhIPpaYmMj48eMJDQ3lvffesxf6ihcvzvvvv8++ffuoW7euySnFGVy8CPPnw4svQseO8PXXxgQ5gHLl4OWX80GhDyAoCGbMgA4djFl8hw5deaSmQvv2xv6gILOTiohIFgYOhPh4s1OI3NgpN33oTjJ3LvEcw1cNNzuGiDgJzewTERERyae+/fZbBg0axIkTJ+xjhQoVol+/fgwaNEjtOuW2paVBdDSUKAH//APjx0ODBjBmjNHl0svL7IS3KCgIJk6E8HDYvNmoZBYubKzRp9adIiL5wvr1MG+e2SlEsrYrVW+/yvV9tO0jetTpwZ0l7jQ7iojkc/rXRkRERCSfOnjwoL3Q5+LiQrdu3Rg5ciRlypQxOZnkZzYbHDgAixfD8uXGMneffAL33GO07ixe3OyEOSg42HiIiEi+YrXC66+bnUIke9bExZodQRxYui2d15a9xu8v/G52FBHJ59TGU0RERCSfsNlsGbb79etHqVKlaNeuHbt27WLWrFkq9MltOXIEnnoKnn7aKPQ9/DD072/sc3FxskKfiIjkW599Bn/+aXYKkez5v9NHzI4gDm5V+Cp+3Puj2TFEJJ/TzD4RERERB3fo0CHefPNNKlSowLvvvmsf9/HxYefOnQQGBpqYTvKzpCRYswbOnoXnnoNSpaByZXjlFaNdp5teLYiIiIO5cAHefNPsFCLZk1qoLOGJJ7I+UAq8ASsH0KZyGwq5FzI7iojkU6bP7IuJiWHLli2cP3/e7CgiIiIiDiUyMpLXX3+datWqMW/ePKZNm8bRo0czHKNCn9wsq9WYDTFqFLRsabxhumWL0b6zUCEYPRoaN1ahT0REHNPUqcaHVETyg0gPdd2Q7Dkae5Qpm6aYHUNE8jFTi30//PADwcHBhIWFUbZsWX744QcAdu/eTd26dSlatCgDBw7M0LJqzZo1VKtWjeLFizNliv4CFBEREeeTkJDAuHHjCA0N5f333yc1NRUAX19f/v33X5PTSX514YLxa1QU9OgB27bBs8/CggXw4YdgsZgaT0REJEsxMUaxTyS/2J/uZXYEyUcmbZpEbJLWeBSRW2NasS82NpZevXqxdu1adu3axfTp0xk4cCDJycm0a9eO2rVrs23bNvbu3cucOXMA49Pt7du356mnnmLTpk3MnTuXVatWmfUURERERHJUeno6n376Kffddx/Dhg3jwqXqjLe3N8OGDePgwYM8+OCDJqeU/CQuDn76Cbp1gyefBOvhcEr89i3fdPyehZ2/5aWW4ZQta3ZKERGR7JkyxSj4ieQXGy4mmB1B8pGYpBje2/ye2TFEJJ8yrTlPXFwc7733HrVq1QLg3nvv5dy5cyxbtozY2FimTJmCt7c3Y8eOpXfv3nTt2pW5c+dSunRp3nrrLSwWC8OHD+fTTz/l/vvvz/QaycnJJCcnZ7gmgNVqxWq15v6TzEVWqxWbzZbvn4cz0T1xPLonjkf3xPHonjiOlStXMmDAAPbs2WMfc3FxoVu3bowYMYLSpUsD6F6ZID/+nCQkwNtvW1i/HtLToUGteF4rNJf0znPgYiyhLi7YrFZsRYpAkybQr5+xYF8+kR/vyc1w1uclInI7oqNh2jSzU4jcnEWRx82OIPnM1M1Teb3B6/h7+efK+Tdv3kzv3r35999/qV+/Pl988QVlyuSvdrMjR44kPDzcPkFIRAymFfvKlSvHM888A0BqaipTp07lkUceYceOHTRo0ABvb28AatWqxd69ewHYsWMH999/P5ZLPYbq1avHkCFDrnuNcePG8fbbb18zHhkZSVJSUk4/pTxltVqJjY3FZrPh4mL60ouC7okj0j1xPLonjkf3xHFs2bIlQ6GvZcuWvPHGG1StWhWAs1qcxjT54efEZoMDB1z5+293nngiCZsNEhML89xzaTS/+wwB334Ikcc4d0dl8PEBFxdj8b74eDhwAMaNg5dfhoAAs59KtuSHe3I7Ls/qFRGRKyZNMmasi+QXVo9ibIs5bXYMyWdik2OZsmkKo+4flePnTkhIoEOHDowfP54WLVrw2muv8eqrrzJ//vwcv5aI5D3Tin2X7dixg//97394eHiwb98+Ro8eTUhIiH2/xWLB1dWV8+fPExcXR/Xq1e37fH19OXXq1HXPPXToUPr162ffjouLo1y5cgQGBuLr65s7TyiPWK1WLBYLgYGBTvkGR36ke+J4dE8cj+6J49E9MY/NZrN/gAlg0KBBzJkzhxIlSjB06FDatm2re+IgHPnn5OxZWLYMli61cOSIUat79llf/P3h/fcvHTRwNPzyC4SEGL3P/tv/LDXV2O/lBe++m7dP4BY58j3JCV5eWt9HRORqUVHwwQdmpxC5Oee9KgDnzI4h+dC0LdPo26AvRQsVzdHz7tu3j5iYGLp27QrA8OHDGTlyZI5eQ0TMY3qxr1atWqxcuZK+ffsSFhZGaGgonp6eGY7x8vIiISEBNze3DPsuj1+Pp6fnNecCoyWWM7wpYLFYnOa5OAvdE8eje+J4dE8cj+5J3oqMjGT06NFYLBamXdWLqlChQqxbt46goCAiIyN1TxyMI/2cpKWBm5sxMe+554zJec2bQ9++UL8+uLpeKSITHg7r1kGxYsYX2WzXntDNzdi/Zg0cOwbBwXn0TG6PI92TnOaMz0lE5HZMmAAXL5qdQuTmHKSI2REkn4pLjmPSxkmMeWBMjp63XLlyuLi48M477zBkyBDuueceFi5cCMDy5csZOHAgx48f5/HHH2f69On299VnzZrF6NGjOX/+PA8++CBffvklhQsXBmDq1KlMnDiRCxcu0Lp1a7744gv7B9emT5/OxIkTSU5OpkePHgwfPhwXFxe6dOlCuXLliI6O5osvviA4OJiffvrJ3tVm9OjRfPjhh6SkpPDYY4/x8ccf4+rqmqPfCxFnZPqrSIvFQu3atfniiy+YP38+AQEBREZGZjjmwoULeHh4XLPv8riIiIiIo0tISGDMmDGEhobywQcfMH36dP75558Mx5QtWzbDbD+Ry6xW2LYN3n4bWrY0Jue5uMDkybByJYwdC40awTWvgTdtMnqe+fvf+AL+/sZxmzfnzhMQERG5RWfPwvTpZqcQuXlbElLMjiD52Ad/fMC5hJydGVqiRAm++uorJk2aRKVKlfjqq68AOHjwIB06dOD1119n69at/PHHH0ycOBGAPXv28PLLLzN79mz27t3LuXPnmDFjBgD79+9n4MCBfPfdd/z5558cPHiQL774AoCffvqJt99+mzlz5rBkyRLmzp3L+/bWI/Dxxx/j6+vL7t27KVGiBGPGGIXNpUuXMmHCBJYuXcq2bdtYv349P/30U45+H0SclWnFvjVr1jBw4ED7toeHBxaLhWrVqrFp0yb7+JEjR0hOTiYgIIC6detm2PfXX3/luwVERUREpGBJS0vj008/pXLlygwbNsy+Fpenpyd///23ueEkX/jkE2jfHnr2hL//hqefhss14Vq14NKHajMXH29UBbOaKXb5GE2bEBERBzN+PNygqZOIw1oWpfX65NZdSLnApI2Tcvy8jz/+OEePHqVLly689NJLDBw4kHnz5nH33XcTFhZG5cqV6dWrF4sWLQIgNDSU06dP06hRI/79919SU1PtH1q9PPMvJSWFkJAQtmzZwosvvgjAJ598Qp8+fWjevDn33nsvI0eOZObMmfYc5cqVY9y4cQQHB9O5c2eOHz8OQLNmzTh27BghISHs27cP4JoPyYpI5kwr9lWpUoVPPvmETz75hOPHj/PGG2/QsmVLWrduTVxcHJ9//jkAY8eOpUWLFri6utK+fXs2bNjAr7/+SmpqKhMmTOChhx4y6ymIiIiIXJfNZmPx4sXcddddhIWF2dcZdnV1pUePHhw8eJAnn3zS5JTiiGJj4aefIOXSh8EjIoxZe599BvPnQ1gY+Pll82Q+Psa0QKv1xsddPuaGlUMREZG8FREBV703LJJv2Fx9+CUq3OwYks99uPVDohKicux8p06d4tChQ/j5+TFy5EiWLVvG5MmTOXjwIH/99Rf+/v74+/szYMAAjh07BkBiYiLdu3cnODiYsWPH4urqSnp6OgAhISF8/PHHDB06lMDAQJ577jnOnz8PwPHjx6lYsaL92qGhofZzglHUu8zDwwPbpeUGoqKiePTRR6lWrRofffQRXl5e9uuJyI2ZVuwLCgrixx9/ZNq0adSoUYOEhAS+/PJL3NzcmD17Nq+88grFixdn4cKFvPvuuwAUL16cqVOn0rp1a0qWLMk///zDsGHDzHoKIiIiIpnatm0bzZs3p127duzdu9c+3rFjR3bv3s3MmTMJCgoyMaE4mtRUWL0aBg6Ehx6Cd9+FSx9kZcQIeOMNYxbfTXd5bdgQfH2Nvp83EhNjHNew4c2HFxERySXvvw+JiWanELl5F3wqkp7ZWskiN+FiykWmbZ6W9YHZNG/ePMLCwuzbTZs2xc3NjeDgYNq1a8fff//N33//zY4dO/jll18AmDZtGrGxsURERPDbb7/R8KrXC6dOnaJOnTps3bqV8PBwoqKiGD16NADly5fn8OHD9mMPHz5MhQoV7Nu+vr6ZZhwxYgTlypXj9OnTLFmyhCpVquTY8xdxdqau2ffggw+yZ88e4uLi+OGHHwgMDASgffv2HDp0iC+++IJ9+/ZRvXp1+9f07NmTf/75h7lz57Jz505KlixpVnwRERGRTP3999+sXbvWvt2wYUPWr1/P//3f/3HHHXeYmEwcic1mPAD69IEBA4wZDK+/DsuXw1135cBFgoOhSROIjDQqiplJTYWoKGjWDK56AS4iImKmpCSYNcvsFCK3JtxS1OwI4iRmbp9JUlpSjpyrRYsWbNy4kW+//ZaTJ08ycuRIgoKC6Ny5M+vWrePAgQN4enrywQcf0LVrVwAuXLiA1WolMjKSb775ho8++sg+C2/37t20bNmSDRs22JerSEtLA6B79+689957rFmzhr/++osRI0bQs2fPLDNeuHCB9PR0Tp8+zYcffsj8+fPt1xORG3MzO8D1lCpVijZt2mS6LyQkhJCQkDxOJCIiIpI9Xbp0YcqUKaSnpzNu3DgeeeQRLDc9JUuc1ZkzsHQpLFkCo0ZB9erw0kvQrx+EhubCBQcMgMOHYc8eCAwEf39jfT6r1ZjRFxVlhOjXLxcuLiIicmvmzoVz58xOIXJr/kzKooW6SDZFJUTx1Y6v6F67+22fq2bNmnz++eeMGDGCU6dOcc8997Bw4UKqVq3Kl19+Sb9+/Th8+DD169fn22+/BaBPnz488cQTVKlShYYNG/Liiy+yatUqAFq2bEmPHj3o1KkTMTExNGrUyN6F77HHHiMiIoLnn3+elJQUevTowauvvpplxrfeeovnnnuOO+64gwcffJDOnTvz119/3fZzFykILLYCVBqPi4vDz8+P2NjY604Vzi+sVitnz56lRIkSuLiYOkFTLtE9cTy6J45H98Tx6J7cnvj4eKZOncqpU6eYMWNGhn1HjhyhbNmyuLu739Q5dU8cT07dk9WrYd482LYNPDzg/vuhWze4aimL3BMRAVOmwJo1EBd3pdjn62vM6OvXD/JRa1ln/zlxptctIiK36u67YccOs1NIQbf2g7doEvDOTX/dMwl38M3J/bmQSAqi6oHV2dNrj9kxRMTBOezMPhERERFHlZaWxmeffcbIkSOJiIgAjDYl99xzj/0YdSEQq9Uo7IWGQrFisHu30bZz+HB44AHw9s7DMEFBMHEihIfD5s1w8SIULmys0afWnSIi4mDWrFGhT/Ivm4s7C08fMjuGOJG9kXtZcXAFD1V6yOwoIuLAVOwTERERySabzcaiRYsYMmQI+/df+aSuq6srGzduzFDsk4LryBGjRefSpXD2LAwaBE88Ab16GRPqTBUcbDxEREQc2Pvvm51A5NYleYcQn/6v2THEyUzbMk3FPhG5IRX7RERERLJh8+bNDBw4kPXr12cY79ixI+PGjeOOO+4wKZk4ko8+gk8/NbpjPvQQtGkDNWoY+0wv9ImIiOQDx47BwoVmpxC5dSdcSwAq9knOWn5wOYeiDxEakBuLfIuIM7ipYl9SUhLLli1jx44dnD17lsKFC1O+fHlat25NxTxZbEREREQkbx04cIChQ4fy008/ZRhv2LAhEydO5L777jMpmZgtJQXWr4fFi43C3kMPGevwVa0KjRsb6/KJiIjIzZkxA9LTzU4hcut2pugTXpLzbNj4aNtHTGo5yewoIuKgsvWvT3JyMqNHj6ZWrVr8/vvvVKpUiY4dO9KgQQMSExPp0qULDz30EPv27cvtvCIiIiJ5avv27RkKfVWqVGH+/Pls2LBBhb4C6tAhGD8eHn7YaNEZFQWFChn77rgD/vc/FfpERERuRWIizJ5tdgqR27MqNsbsCOKkPvvrMxJTE82OISIOKsuZfUeOHOHxxx/nkUceYefOnXh5eV1zzMCBA1mzZg2dO3dmwIABPPfcc7kSVkRERCSvPfHEE0yaNIkTJ04wcuRIXnzxRdzd3c2OJXnszBkXUlOhXDnYswfWroVHHzXadIaEmJ1ORETEOcydC+fOmZ1C5NbZsDD/9GGzY4iTOp90nm93f0u3e7qZHUVEHFCWxb6tW7cyefJkmjdvfsPjmjVrxpo1a5g6dWpOZRMRERHJM2lpaXz66af89ddfzJw50z7u4uLCd999R6lSpShcuLCJCSWvxcfDb7/B4sUWtmwpymOPwfDh0Lo1tG2rNfhERERy2vTpZicQuT2p3uWJSD5qdgxxYrP+nKVin4hkKsti3xNPPJGtE1mtVlxdXXn77bdvO5SIiIhIXrHZbCxatIghQ4awf/9+AJ599lkaN25sP6ZSpUpmxROTrFoFw4YZ6/LVqQMDB16kY0c/ANxuatVrERERyY5du+Dvv81OIXJ7zrgHASr2Se7ZfGIzh6IPERoQanYUEXEwN/15ZJvNxsqVK0lLS8swPn/+fIoXL8748eNzLJyIiIhIbtq0aRNNmjShY8eO9kIfwC+//GJiKjHDoUPw/vvwzTfG9h13QFgYLF4M06fbaNEiGW9vczOKiIg4s7lzzU4gcvv2pnuaHUEKgK93fm12BBFxQNkq9tlsNnbt2gUYM/hatWpFQkJChmPatWvHTz/9xKhRo3I+pYiIiEgO+vfff3nsscdo1KgRGzZssI/fd999bNiwQZ0KCojYWPj2W3j2WXjySVi4EBIvrXcfFARdu0LJkuZmFBERKQhsNuPfZJH8bsOFeLMjSAEwd5c+HSEi18pWsS89PZ1GjRoB4Orqis1mw+0//Ys8PT1p27YtSUlJOZ9SREREJAecOXOGXr16Ub16debPn28fr1q1KgsWLGDdunX2//OIc0pJgZMnjd8fPQrTphmFvcmTYflyePFFc/OJiIgUROvXw7FjZqcQuX0LzqqFp+S+A9EH2HJii9kxRMTBZGvFETc3twzFPYvFgsViuea4c+fOZTouIiIi4gi2bt3KRx99ZN8uVaoUb7/9Nt26dbvmg0ziPGw2Yx2gxYvhl18gOBg+/xxq1oQVK8DPz+yEIiIiBZtaeIozSPcswa64s2bHkALi651fU79sfbNjiIgDyfa7WlcX8Ww2G7Vq1bqmsHf27FkqV66cc+lEREREclCbNm1o2rQpf/75J4MGDaJfv374+PiYHUty0ZEj0K8fHD8OpUpBp07Qpo2xz2JRoU9ERMRsqanwww9mpxC5fee8ygMq9knemLdnHlMfnoqbiz60KiKGbP9tYLPZMmzPmjWLQoUKZRhzdXXljjvuyJlkIiIiIrfIZrOxYMECli9fzsyZM+0fULJYLMyePRs/Pz9KlChhckrJDRcvwm+/wZkz8NJLUKYM1K0Lb74J994LLtlqYi8iIiJ5ZflyiI42O4XI7Ttg1YcIJe9EJkSy4uAK2lRpY3YUEXEQt1z6r1+//jXFPhERERGzbdy4kYEDB7Jx40YAHn30UR566CH7fnUhcD7p6bBli9Gmc/VqY4ZA48ZG+04PD3jjDbMTioiIyPWohac4i80JyWZHkALmq51fqdgnIna3VewrUqQIlSpV4sEHH+Txxx/Hy8srJ7OJiIiIZNs///zD0KFD+b//+78M4z/99FOGYp84j+hoCAiAmBjo08dYi69nT3j4YdDETREREcd34QIsWmR2CpGcsSTylNkRpIBZ9M8iLiRfoIhnEbOjiIgDyHYjo/+uz/fGG2/Qq1cvypQpw9ixY6lYsSKLFy/O8YAiIiIiN3L69GlefvllatSokaHQd8cdd7Bw4UI+/vhjE9NJTjt3zpgB8PTT0LkzpKVBsWLw008wbx48/7wKfSIiIvnF//0fJCaanULk9tncfFl97pjZMaSASUxLZNE/+sSEiBiyNbMvPT2dlJQU+7bFYqF9+/Z4e3sDMHbsWGbNmsWTTz7JzJkzee6553InrYiIiMglFy9eZPLkyUycOJH4+Hj7eKlSpRg1ahRdu3bFzU2LlTuLxEQYMgQ2bQJXV2ja1JjFd3kNvnLlzM0nIiIiN+/7781OIJIzYr1DsLHD7BhSAC05sIRnaj1jdgwRcQDZegfMYrEwc+ZMwCj82Ww2UlNTMxzTvXt3fHx8ePHFF6lTpw7VqlXL+bQiIiIil2zfvp2RI0fatwsXLszgwYPp27cvPj4+5gWTHGGzwd9/G2vx9egBhQpB0aIweDA8+CD4+pqdUERERG5HUhKsWmV2CpGccQQ/syNIAbX84HLSrem4uriaHUVETJatYp+LiwvPPvssADabjR49emT6Sfmnn36apUuX2mf8iYiIiOSWZs2a0aZNG1asWEHPnj156623KKH+jfneiROwdCksXgynTkHp0vDkk0ah76rabv4WHm5MUYyPBx8faNjQWHBQRESkAFm7FhISzE4hkjO2JaWbHUEKqPNJ59l0YhONyzc2O4qImOyme1u5ubnx0UcfXXf/V199dc36fiIiIiK3Y8OGDcydO5fp06dn+H/GtGnTsFqtVK5c2cR0cruSksDLC6xWePFFo2Xngw9CmzZw991XWnXmexERMGkSrFsHcXHGE7NajWmKTZtC//4QFGR2ShERkTyxbJnZCURyzoros2ZHkAJs6YGlKvaJyM0X+7KSnJyMl5dXTp9WRERECqD9+/czdOhQFixYAMD//vc/Hn/8cfv+0NBQk5LJ7UpLg82bjRl8GzbAggVQrBi8/z5UqGAU/5xKRAT06gV79kBgIISGXin2xcTAokVw6BDMmKGCn4iIFAjLl5udQCRn2Fw8WXzmsNkxpABbcmAJYx8Ya3YMETFZtj8nffDgQWw2m307MTGRBx54gPT0dD744AP7eFhYGLNmzcrZlCIiIlKgRERE0LNnT+688057oQ/gyy+/NC+U5JgPP4TWraFPHzhyBF56CdzdjX1VqzphoQ+MGX179kDFihAQcGW6oouLsV2xorF/yhRzc4qIiOSB8HDYv9/sFCI5I8G7IslWtfEU8+w8s5PjscfNjiEiJst2sa9du3akpaWRnp7OnDlz8PDw4Ny5cwAZ2npu2LCBkiVL5nxSERERcXoXLlxgxIgRVKpUiY8//pj0dONFc1BQELNmzWL+/PkmJ5RbERkJc+dCcrKxnZAADz8M33wD330Hzz1ndLJ0WuHhRuvOwMArVc3/cnc39q9ZYxwvIiLixDSrT5zJcdfiZkfIHxKBE5d+zUuxeXw9kyw9sNTsCCJisiyLfWlpaYCxVp/7pTcnPvroI1xdXXFxcbH/CkahLzk5mdatW+diZBEREXE2qampzJgxg0qVKjFq1CgSEhIAKFKkCO+88w4HDhwgLCwMN7cc70AuuSQpyXgj79VXjbX3pk+/8gn+QYOgXz+oUgUKxFLPmzYZa/T5+9/4OH9/47jNm/MilYiIiGm0Xp84k7+TC8J/aG/THuA9YBEw5dL2ZfGX9p2/yXOeA8b/Z+wg8C6w9tJ2FHD0Js+bTy09qGKfSEGX5TtmPXv2pHDhwqSmpgLg6uqKp6dnhmMuF/sGDx7MmDFj9EaciIiI3JTdu3fTu3dv+7abmxsvv/wyb731FoGBgSYmk5thtRrFO4vFKOb98QfcfTcMHQotWkCRImYnNEl8vNGu0yWLz9ldPubixbzJJSIiYoKUFPj9d7NTiOSc32OjzY7g2JKAJUBXoBTwF7ASqIFR6PsGiLnJc0YDcy+d+2p/Au2BFUBTYC/Q6BZz5zO/Hf6N5LRkPN08sz5YRJxSljP73nnnHQCOHDly3WNsNhsDBw6kXLlydO3aNefSiYiISIFwzz330LlzZwCeeOIJ9u3bx/vvv69CXz5x7Bh89BF06AA7dxpjr7wCCxbA7NnwyCMFuNAH4ONjVEKt1hsfd/mYwoXzJpeIiIgJ1q/X51rEedgsLiw4fdjsGI4tGXgYo9AHEMSVVp4/AjVv4ZzfArUzGU8ESgI2IAWwkI2pLs4hPjWeNUfXmB1DREyUZbGvVKlSvPfee1SpUoV+/frRr18/jhw5Qr9+/QCYOHEie/fu5fTp03z55Ze5HlhERETyt3379tGnTx/7enyXjRs3js2bNzNv3jwqVapkUjq5GStXQrdu8OijMG8eNGgAfn7GvurVoWxZc/M5jIYNjUUJY2JufFxMjHFcw4Z5kUpERMQUWq9PnEmKdzCRKQlmx3BsfkCtS79PBzYDd1zabg80uIVzPg1Uz2TcA2O2IBitQu+8hXPnY2uPrs36IBFxWlkW+652xx13ULVqVby8vKhatSo2m42IiAhcXV3ZuXMnR48WkCbIIiIictMiIiLo0aMHd955J9OmTeOrr77KsD84OJj69eublE6yIy0N1q6FM2eM7aNHjdrUuHGwYgW8+SYEB5sa0TEFB0OTJhAZCZda418jNRWioqBZM6hQIU/jiYiI5KVffjE7gUjOiXArlfVBYjgNTMJYV6/VpbGit3iu633dncDnQGWMNQBv9fz51MbjG82OICImyrLYFxkZSefOnYmPj+ell16iR48eBAUF0aNHDywWC1OmTKFq1aqMGTOGli1bcvr06bzILSIiIvnEhQsXGD58OJUqVeKTTz7BeqmV4cyZM7HZbCank6zYbLBvH0yaBA8/bKzFt+ZSd5iwMHjvPXjwQfDU0hA3NmAA1KgBhw9DdPSVlp5Wq7F95IgxHfJS9wwRERFnFB8Pu3aZnUIk5+xJczc7Qv5REngOCAAW5dI1agKDgLsvXe+LS4/rfN7O2fxx8g/SrelZHygiTinLYt/MmTO56667KFSokH3sv2/MWSwW2rZtS+/evRk8eHDOpxQREZF8JzU1lenTpxMaGsro0aNJSDDa2xQpUoQxY8bw+++/Y7FYTE4pWZkxA557zvgUftu28O238MQTxj7dvpsQFGR8Mzt0MGbxHTp05ZGaCu3bG/uDgsxOKiIikmu2b4d0vQ8tTmRtXJzZEfIPC1AaeATYx5V1+3KaF3AAY60+70uPI7l0LQcTnxrPjjM7zI4hIibJconSt956C4C5c+dis9lIS0sjMfHK38aXxwBeeeUVKlSowNGjR6mg9kMiIiIFks1m46effmLo0KEcPHjQPu7u7s7LL7/MsGHDCAwMNDGhXE9iIqxaBUuWGLP42rUzfr3nHqhfH1xdzU6YzwUFwcSJEB4OmzfDxYtQuLCxRp/+7ywiIgXAli1mJxDJWQvOakmjLIUD/wItL227YhT+cuuDgwlAISAJKHZpLLcKiw5o4/GN3Bt0r9kxRMQEWRb7LktISCAxMREvLy8mTZqEzWbDarWSmppK6qW1Rzw9PWnevDk//vgj/fv3z7XQIiIi4rgOHjzIk08+aW/XCfDkk08yZswYQkNDTUwm17N/P3z3Hfz2m1Hwu/deKHbphXFoqPGQHBQcrMUNRUSkQFKxT5xJmlcQ/16MMDuG4ysGbMdo31kZ+B0IxZiBdyNJgDtGcfBm7MRo53kCiL00VuYmz5GPbTy+kVfqvWJ2DBExQZZtPC/bsWMH3t7euLi40Lx5c1JSUqhevTru7u6899579uOee+45HnzwwdzIKiIiIvlA5cqV6datGwDNmzfnjz/+4LvvvlOhz8GEhxsPMJaR27EDXngBFi2CTz6BRo3MTCciIiLOSMU+cSZRnuXMjpA/FAGeALYA0zHWz3skG1/3EcaMwJtlBXyAYODspUfwLZwnn9p4fKPZEUTEJNme2VekSJEM256ennzzzTcAtGnTxj5+9e9FRETEuZ06dYr333+fUaNG4eHhYR9/++23eeSRR2jVqpXW5XMgsbGwYoXRpnPPHmOZuOHDjVadrVppDT4RERHJPadOwYkTZqcQyTn/WAuZHSH/CAV632D/yEzG+mZxzqLX+brLH1r0BHpkFcz5HI09yqkLpyhdpLTZUUQkj2W72HfgwAEqV66cm1lEREQkn4iLi2PChAlMmTKFxMREypQpw6uvvmrfX7p0aUqX1osLR/L77/DGG2CzwX33wbvvQpMmxj6XbPd6EBEREbk1mtUnzmZjfAFaCE7ylQ3HNtCpRiezY4hIHstWsS85OZlatWqRmKh/xERERAqylJQUPv74Y0aNGkVUVJR9fNq0afTq1QtX15tdUMEE4eGwaRPEx4OPDzRs6HTrp9lssHevMYOveHHo1g1q1oQ+faBlSwgIMDuhiIiIFDQq9omzWXz2pNkRRDK18fhGFftECqBsFfs8PDxwd3e3b3/xxRf4+fkREBBA0aJFCQgIoHjx4nh6euZaUBERETGPzWbjhx9+4I033uDQoUP2cXd3d3r16sWwYcMcv9AXEQGTJsG6dRAXZ0xns1rB1xeaNoX+/SEoyOyUtyU6GhYsMIp8R49CYCA89ZSxLzAQOnc2NZ6IiIgUYH/8YXYCkZxjdS/KxvMq9olj2haxzewIImKCbBX7LBYLbm5XDg0LC6NmzZrExcURExNDbGwsVquVihUr8u2331KnTp1cCywiIiJ5a82aNQwaNIg//vMOTefOnRkzZgwVK1Y0KdlNiIiAXr2MheoCAyE09EqxLyYGFi2CQ4dgxox8V/BLSICzZ43JiadPw6efwv/+B4MGQd26atEpIiIi5rNaYZveexYnElMoGDhvdgyRTO2N3Gt2BBExQbbX7LPZbBm2//zzzwzbZ8+eZezYsYwePZqFCxfmTDoREREx1ZkzZ2jZsiUpKSn2sfvvv58JEybkrw/3TJpkFPoqVoSruhXg4mL0tCxSxNg/ZQpMnGhezmy6/IbZ4sXGWnwVK8KXX0K1avDLL+DtbXZCERERkSv274cLF8xOIZJzDuNrdgSR64pOjOb0xdOUKlzK7Cgikodu6bPeFovlmrESJUrQokUL9uzZc9uhRERExDGULFmSXr16AXDnnXeydOlSfvvtt/xV6AsPN1p3BgZmLPRdzd3d2L9mjXG8AztyBNq2NSYq7t5trMf37rvGPotFhT4RERFxPHs1yUSczB+JqWZHELkhze4TKXiyXezLrMD3X/Xq1WPHjh23FUhERETMERsby5gxY0hISMgwPmzYMD7//HP+/vtvWrVqla3/EziUTZuMNfr8/W98nL+/cdzmzXmRKtvOn4d58+D9943tsmWhRQuYMwd++sko9uWzzqMiIiJSwPz7r9kJRHLW8nNnzI4gckMq9okUPNku9qWmprJ69Wp27dp13WNKlCiBj49Pti++cOFCKlasiJubG3fffTf79u0D4LXXXsNisdgflSpVsn/N7t27qVu3LkWLFmXgwIHXtBcVERGRm5OSksIHH3xApUqVGDZsGNOmTcuwv1ixYnTp0gVXV1eTEt6m+HijXWdWi9ddPubixbzJdQOpqUZ7zn794OGHje6iJ08a7Tvd3Y3xO+80ZvKJiIiIODoV+8SZ2FwLsfzsEbNjiNyQin0iBU+2i32JiYn07NmT1q1bY7FYCAoKolOnTnz11Vekpt781PVDhw7RtWtXxo8fz8mTJ6lSpQphYWEAbNu2jSVLlnD+/HnOnz/PX3/9BUBycjLt2rWjdu3abNu2jb179zJnzpybvraIiIgY6/HOmzePpk2b0qdPH6KiogCYOnUqSUlJJqfLQT4+RpXMar3xcZePKVw4b3L9h80GERHG7xMSLAwbZiEqCvr3hxUrjFadWdUrRURERByRin3iTOK9K5Jqy+K1hYjJVOwTKXjcsntgkSJF2L9/PwBWq5XDhw+zevVqZs+ezcCBA3nnnXfsxbrs2LdvH+PHj+eJJ54A4OWXX6ZNmzakpaWxZ88emjZtSuH/vNm2bNkyYmNjmTJlCt7e3owdO5bevXvTtWvXTK+RnJxMcnKyfTsuLs6e35rVG34Ozmq1YrPZ8v3zcCa6J45H98Tx6J44jtWrVzNkyBC2bt2aYfypp55i9OjReHh4OM99atAA/PwgNhYCAq5/XGyscVyDBlkXBnPQqVOwbBksXWohNhYWL7bi62tl/vx0SpW6Ut1zltuRH+nvLsfj7PfEWZ+XiBRcKvaJMznqcoPXFCIOYl/UPrMjiEgey3ax7+r1eVxcXKhUqRKVKlUiLCyMX375he7du/Pzzz/z/fff4+npmeX52rZtm2H7n3/+oXLlyuzatQur1crdd9/NyZMnadasGZ988gnly5dnx44dNGjQAG9vbwBq1arF3hus8jxu3Djefvvta8YjIyPz/YwFq9VKbGwsNpsNF33M3yHonjge3RPHo3tivv379zNmzBh+/fXXDOP33Xcfb731FnfddRcAZ8+eNSNe7ihUCNq3hz/+gFKlwC2T//6kpRnT5urXBy8vyIPnn5QEb73ly86d7nh52WjcOIUWLZKJjk7mwoVY/PxsnD2rnxNHoL+7HI+z35MLFy6YHUFEJMdER8O5c2anEMk5f+XvtxSlgDgbf5ZzCeco5l3M7CgikkeyVeyzWq2kpaVdd/+DDz7Itm3baN26NY888ghLly69qRApKSlMnjyZfv36sXfvXqpWrcoHH3xA8eLF6du3Ly+99BLLly8nLi6OkJAQ+9dZLBZcXV05f/48RYsWvea8Q4cOpV+/fvbtuLg4ypUrR2BgIL6+vjeV0dFYrVYsFguBgYFO+QZHfqR74nh0TxyP7om5Lly4QPv27TO8iVyzZk2GDBlCp06d8u+afNnRtSts3w5r1kDx4uDvbxT3rFaIiTHegapWDV54AUqUyJUIVqtRb9y8GV5/3Vhv78474Ykn4P77wdvbDfDGarXi6qqfE0eiv7scj7PfEy8vL7MjiIjkGM3qE2fzS0yU2RFEsmVv5F6aVGhidgwRySPZLva9+OKLNzymePHiLF26lHnz5t10iBEjRuDj40NYWBju7u4888wz9n0zZswgJCSEuLg43Nzcrpk16OXlRUJCQqbFPk9Pz0xnGbq4uDjFmwIWi8Vpnouz0D1xPLonjkf3xDx+fn706dOH0aNHU65cOUaPHs3TTz/NuXPncHV1de57Uro0TJ8OU6YYBb+DB68U+3x9oW1b6NcPgoJy/NKHDsGSJbB0KURFQXAwdOlidBQdPDjzr9HPiePRPXE8znxPnPE5iUjBpWKfOBObxY2FZw6bHUMkW1TsEylYslXsc3NzY+rUqVkeV7x4cXr37n1TAX7//XemT5/O5s2bcXd3v2Z/iRIlsFqtREREEBAQwO7duzPsv3DhAh4eHjd1TREREWeXnJzMrFmzeO655/Dz87OPDxw4kKJFi9KzZ08KFSpUsNaFCgqCiRMhPNyYXnfxIhQuDA0bQoUKOXqpCxegSBGjlvjqq5CcDA89ZNQUq1UzZvWJiIiIFAQq9okzSfYOJjb1oNkxRLIlPCbc7AgikoeyLPZZrVa2bNlCw4YNc/ziR44c4amnnmL69OlUr14dMN6EvOeee3j66acB2LRpEy4uLpQrV466desya9asDF+fnJxMQIAWxhUREQHj3+3vv/+eN954gyNHjnDq1CnGjh1r31+kSBH69u1rYkIHEBxsPHJYSgqsXQuLFxu1xIULoWRJY0Jh2bKQyWeaRERERJyein3iTE66lQRU7JP84dTFU2ZHEJE8lGV/GKvVSqdOnXL8womJibRt25YOHTrwyCOPcPHiRS5evEitWrUYNmwYv/32GytXrqRnz548//zzeHt707RpU+Li4vj8888BGDt2LC1atHDuNYZERESyadWqVdSrV4+nnnqKI0eOADBt2jRiYmLMDVYATJ1qzNwbMgRiY2HAAGPSIEBIiAp9IiIiUnAdOGB2ApGcsys1W03SRBzCqQsq9okUJFn+C+Xm5oab25XDWrVqRVRUVKYtN61WK0lJSfz9999ZXnjlypXs3buXvXv3XjNb78knn+Sxxx7D1dWVZ5991j4jwc3NjdmzZ/PUU08xcOBAXFxcWL16dTaepoiIiPPatWsXgwcPZtmyZRnGH3jgASZMmIC/v785wZzYyZOwbBk8/TR4e4OrK3TqBG3a5HhHUBEREZF87cQJsxOI5Jw1cTFmRxDJNhX7RAqWbH0c5eoF4vfv38+cOXMAsNlsdOzYkUWLFmG1WmnXrh1LlizJ1oU7dOiAzWbLdN+4ceMYN25cpvvat2/PoUOH2L59Ow0aNKBYsWLZup6IiIizOXHiBMOHD2fOnDkZ/k2tVasWEyZMoGXLlli0OFyOuXgRfv0VliyBv/4yinx168Jdd8Frr5mdTkRERMTxWK0QHW12CpGcs+B0uNkRRLJNxT6RgiVbxb6r30C0WCw0a9bMvu3u7k7Tpk2v+X1uKlWqFG3atMn164iIiDiqlJQU6tWrR0REhH2sXLlyvPPOOzzzzDNqcZ1D0tPBYgEXF6NF55YtUL8+jB4NzZtDoUJmJxQRERFxXOfOGQU/EWeQWqgs4Ymaqir5R0xSDAmpCXi7e5sdRUTyQJZr9gE3nBWgGQMiIiJ5z8PDgz59+gDg7+/PhAkT+Pfff3n++edV6MsBBw4Y6/C1agV//mmM9eljzOr78ENjXIU+ERERkRuLjDQ7gUjOifQoY3YEkZum2X0iBUe2ZvadOXOGkSNH0rFjx2tab16vFaeIiIjkDKvVyrx582jRogWBgYH28VdffZWEhARee+01AgICTEzoPJYuha+/hn//haJF4eGHoWRJY1+lSuZmExEREclvVOwTZ7Iv3cvsCCI37dSFU1QK0ItZkYIgWzP7vLy8iIyM5Mknn+TYsWO5nUlEREQu+e2336hbty5PP/00o0ePzrCvUKFCjBw5UoW+25CcDCtXwqlLH3aMioJy5WDKFFi2DPr3N7ZFRERE5Oap2CfOZMPFBLMjiNw0zewTKTiyNbPP19eX6dOnAxASEpKrgURERAR27tzJ4MGDWb58uX3so48+YtCgQZQtW9bEZPmfzQZ//2205PzlF4iPN9bje/xxeP55s9OJiIiIOA8V+8SZLDp73OwIIjdNxT6RgiNbxb6r1+X77xp9WrNPREQk5xw/fpy33nqLL7/8MkOr7LvuuosJEyao0HcbbDawWGD6dJgzB4KC4KmnoHVrKF/e7HQiIiIizkfFPnEWVo/ibI89bXYMkZsWcSHC7AgikkeyVey7ms1mo2LFivbt6Oho+3ZsbCwVK1bk8OHDOZdQRESkAIiJiWHcuHFMmzaN5ORk+3j58uUZM2YMTz/9NC4u2eq+LVeJi4Nff4XFi6FNG3jsMWjXDho1grvvBn1LRURERHKPin3iLKILVQCizI4hctMupFwwO4KI5JEsi32pqamkpaXZt5cuXQqQ6RuO6enppKSk5GA8ERER52ez2WjUqBH79u2zj/n7+zNs2DB69+6Nl5cWgr9Zu3fD11/D2rWQlgYNGsDlSZEVKhgPEREREcldKvaJszhoK2x2BJFbkpCqtSZFCoosi33p6ek0aNDAvl2tWrVcDSQiIlLQWCwWXnnlFXr37o2npyevvvoqQ4cOJSAgwOxo+YbNBv/+C66uUKkSRERAeDj06gUPPwzFi5udUERERKTgUbFPnMWWeE1ukPwpPjXe7AgikkeyLPZ5eXnx/fff50UWERGRAuG3336jatWqGdbf6969O4cPH+bVV1+lgqadZVtkJCxfbrTpPHQI2reH4cOhRQt48EGz04mIiIgUbNHRZicQyRlLz2ndM8mfNLNPpODIcqWamTNn8vXXX2frZMeOHaNbt263HUpERMQZ7dixg4cffpgWLVowfPjwDPvc3d2ZNGmSCn034fffjXX4PvoIQkNh2jR4801jn8VibjYRERERgcREsxOI3D6bW2F+iQw3O4bILVGxT6TgyLLY9+STT/LFF18QFhbG2bNnr3vcl19+yUMPPcSzzz6bowFFRETyu2PHjvHCCy9wzz33sGLFCgDmzJmTYY0+uTGrFbZvh1GjYMYMY+zuu2HoUFixAsaOhfvuM9p4ioiIiIhjSE42O4HI7bvgHYLN7BAityg+RW08RQqKLNt4Fi1alJUrV/L555/TtGlTqlSpQv369SlZsiQXL17k4MGDrFy5ksaNG7Nq1SpKlSqVF7lFREQc3vnz5xk/fjzTpk0j+ap3OipUqMCYMWOoWrWqienyh8hI+PFHWLrUWIevTBno3NnYFxAAjzxibj4RERERuT4V+8QZhFuKmh1B5JZpZp9IwZFlsQ/AYrHQrVs3unXrxrZt29i5cydnz57Fx8eHli1bMm7cOIoUKZLbWUVERPKFpKQkpk+fzpgxYzh//rx9vGjRogwbNoxevXrh5eVlYkLHFhcHp09DlSpw/jzMmwctWxotO2vVUotOERERkfxCxT5xBtsTrWZHELllKvaJFBzZKvZdrU6dOtSpUyc3soiIiDiFhx56iLVr19q3PT09ef311xkyZAhFi+pToZlJTYVNm2DxYli3DipWhLlzjYLfypXg4WF2QhERERG5WSr2iTNYeT7S7Agityw+VW08RQqKmy72iYiIyI2FhYWxdu1aLBYLzz//PKNGjaJ8+fJmx3JYR45A9+4QE2MU9159FR566Mp+FfpERERE8qfUVLMTiNyeVJsrP585bHYMkVummX0iBYeKfSIiIrfh77//xs/Pj5CQEPvYM888wx9//EFYWBh33XWXiekc09mzsGwZnDkDgwZB+fLw2GPQogVUrmx2OhERERHJKenpZicQuT0HU4oQn66qteRfyWmaYi1SUKjYJyIicguOHj3KsGHDmDt3Lo899hg//PCDfZ+LiwsffPCBiekcT0oK/Pqr0aZz61Zjtt4DD4DVCq6u8PLLZicUERERkZxms5mdQOT2bE9WxVryNzcXvf0vUlC4mB1AREQkP4mOjmbAgAFUqVKFr7/+GpvNxo8//sgff/xhdjSHY7XC0aPG75OT4Z13IC0N3nrLWIdv1Chw0f9ERERERJyW1Wp2ApHbs2bFQ1TyrWF2DJFb5u7qbnYEEckjKu2LiIhkQ1JSEh988AFjx44lJibGPl60aFHeeustteu8Sng4LFkCS5fCxYtGYa9IEaN1p5+f2elEREREJC9oVp84g3/W3oXblr9oNngcm93HkpyuloiSv7i7qNgnUlCo2CciInID6enpzJ07l7feeotjx47Zxz09PenTpw9DhgzB39/fvIAOJDkZXnoJ9uwxinstW0KbNkbLTlChT0RERKQgUbFPnEVasjtrRg0npO4TFHm6Oztj15sdSSTbNLNPpOBQsU9EROQGOnfuzI8//mjftlgsvPDCC4waNYpy5cqZmMx8qamwfj1s2gRDh4KnJ9SpA88/D02aXCnyiYiIiEjB4+JirM2criXPxEkc2XoHbFtLkz4z2RE4hLiUOLMjiWRJM/tECg6tlCMiInIDTz/9tP33rVq14u+//+bzzz8vsIU+m82YuTdhAjz0EAwcCHv3wvnzxv5XX4UHHlChT0RERESgcGGzE4jkMJuFdVNfxvvzvdTz7WB2GpEsaWafSMGhmX0iIiKXhIeHk56eTmhoqH2sY8eOvPTSSzzxxBM88MADJqYzV3Q0BAQYxb6hQ41ZfR07Gm06r/p2iYiIiIjYFS4MsbFmpxDJeacPlOF0vwXU7/ojR6q9ytmE02ZHEsmUh6s+iStSUGQ5sy81NZXJkydn62TR0dFs27bttkOJiIjkpejoaPr370/VqlV55ZVXMuyzWCx8/PHHBbLQl5AAixfDyy/Dww/DyZNGO6aZM2HJEnjtNRX6REREROT6NLNPnN2Wzx8nafI+Gnu/aHYUkUypjadIwZFlsc9isfDee+/Zt9u1a0epUqUoXbo0QUFBBAUFERgYyB9//EGbNm0YN25cbuYVERHJMUlJSUycOJHQ0FCmTJlCSkoKy5cv57fffjM7mukmTICWLWHkSGM23/Dhxsw+gNKljaKfiIiIiMiNFClidgKR3Bd3xp/1g2Zz19+rCC5S2ew4IhmojadIwZFlG083NzesVitRUVEUL16cixcvMnr0aNq1a4efnx8elxblefPNNwkICOD777/P9dAiIiK3Iz09nblz5zJs2DCOHz9uH/fy8qJPnz7Url3bxHTmOHwYli+HLl2MT2D7+0O3btCqFQQFmZ1ORERERPIjzeyTgmTHguZ4/rKTZoPfZgOTSLOmmR1JRDP7RAqQbH0uPzIykqpVq1K+fHl27NiBq6srYWFh+Pr6Uq1aNZ555hnuu+8+vv32W1xdXXM7s4iIyC2x2WwsX76ce++9lxdeeMFe6LNYLHTt2pUDBw4wbtw4/P39zQ2aR86fh+++g1de8aNzZwvz5xtFP4CXXjKKfSr0iYiIiMitUrFPCprkeC/WDB9HxV+2Uc2vjtlxRCjkXsjsCCKSR7Kc2QdQtmxZDh8+zLlz52jdujUWi4UFCxaQnJxMbGwsf/75J1OmTGH16tXZXt9PREQkr73++ut88MEHGcZat27N+PHjqVmzpkmp8lZKCri5GW04R4yAP/6wcM89Vl5+2UaTJhY8tHa3iIiIiOQQFfukoPp33V24bNpM0wHvsa3IcBJSE8yOJAVUQKEAsyOISB7JcmZfUlISSUlJABQrVoxChQrRo0cPfH19CQwMJDQ0lA4dOjB8+HBiY2MZPnx4rocWERG5Fe3atbP/vk6dOqxatYolS5Y4faHPZoOdO2HcOHj4YdiyxRjv3x+WLrUxYsQF7r8fFfpEREREJEdpzT4pyKxprqwd35+A73ZT26+l2XGkgArwUrFPpKDIcmafp6cnv/zyC19++SUJCQn8/vvvuLhcqREuWbLEPtuvYcOG1K5dm4EDB1JE/6MTERETnTt3jvPnz1OpUiX72IMPPkj37t154IEH6NSpU4Z/z5zVwoXwxRdw7BiUKAGPPQYVKhj7KlQAqxXOnjU3o4iIiIg4J83sE4ETu0I40XcFjV7+kv0V+hGddM7sSFKAaGafSMGRZbHPYrFQo0YNOnfuzAsvvICLiws9e/ZkwoQJbNiwgSFDhrBq1Sp8fHyw2Ww0aNBAhT4RETFNYmIiH3zwAWPHjqVmzZqsXbsWi8Vi3//JJ5+YmC73xcfDb7/B3XdD+fKQlAQ1a8KQIVCnjtG+U0REREQkL6jYJ3LFxo+ep1i5VjR8/XU2XfzW7DhSQBTzLmZ2BBHJI9las+/o0aMkJiYyYMAAAObPn8/GjRtJS0vjww8/5IUXXuCVV17hvffeo2/fvrkaWEREJDPp6el8/fXXDBs2jBMnTgCwfv16fv75Z9q3b3/tF4SHw6ZNRnXMxwcaNoTg4DzNnFOsVqM155IlsGqVsS7f0KFGse/JJ81OJyIiIiIFlb+/2QlEHMu544FsGvANdZ56loh7X+Zk/DGzI4mTK1ZIxT6RgiLLYl/fvn2ZNm0aLi4u7N+/n7i4OCpUqEDPnj157bXXuOuuuwgKCmLw4MF8/fXXDBo0KC9yi4iIAGCz2VixYgWDBg1i165d9nEXFxe6dOnCvffem/ELIiJg0iRYtw7i4oypblYr+PpC06bGQnZBQXn8LG6N1WrEnz7daNUZHAzdu0Pr1kbLThERERERM5UpY3YCEce07dvW+CzfQ9NBb7I+5UOsNqvZkcRJlfDRmwMiBUWWzbz69u3LqVOnqFatGtu2beN///sfiYmJrF27lt69e/Paa68RHR3NN998Q2xsLN9+q2noIiKSN7Zv306LFi1o1apVhkJfmzZt2LFjB59++illy5a98gUREdCrF/z8M7i7Q2jolYe7OyxaZOyPiDDh2WRPdDR88w08/TR8/70x9uij8OWX8MMP0KWLCn0iIiIi4hjKlTM7gYjjij9fmLVDp1Ftw0Yq+d5pdhxxUiULlzQ7gojkkSyLfeXLl6dUqVIAWK1W5s+fj7u7Oy4uLkyYMIGzZ8/i5+fH1q1befTRR1m7dm2uhxYRERk7dix16tTh999/t4/VrVuXVatWsXjxYu68M5MXS5MmwZ49ULEiBARcWcDOxcXYrljR2D9lSh49i+zbsQP69IGHH4b334eyZaFKFWNfmTJQvTpctTShiIiIiIjpVOwTydqelfUJH/onzdJH4+nqaXYccTKlCpcyO4KI5JFsrdkH0L59e9atW0evXr3YsGEDXbt2JT4+noEDB7Jq1SrGjRuXmzlFREQyuP/+++2/r1ixIuPGjaNTp05YrlfxCg83WncGBhqz+DLj7m7sX7PGON7ENfxsNti504hUvTrExEBsLAwaBC1bGl1HRUREREQcWenS4OoK6elmJxFxbGnJ7qwZPYyQOp0o8kx3dsauMzuSOImSPprZJ1JQZLvYN2bMmAzb8+bNA6Bly5a0bNkyZ1OJiIhcJTExkVOnThEaGmofa9iwIS+99BJ33nknPXr0wMPD48Yn2bTJWKPvqnNkyt8fDh2CzZtNKfadPAlLlsDSpXDiBLRvD8OHG8sJNmuW53FERERERG6Zq6uxHPaJE2YnEckfjmyrCtvX0KTPx+wIHExcSpzZkSQf8/X0pZB7IbNjiEgeybKNp4iIiFnS09P5/PPPqVy5Mp06dcJqzbho+ccff8yrr76adaEPID7eaNfpksU/fZePuXjxNpLfmt9+gw4d4Ouv4d574eOPYdgwY59adIqIiIhIfqRWniI3yWZh3dSeFPpsH/V9O5qdRvKxcr76C1ikIMn2zD6ApUuX4unpift12p+lpaWRlJRE69atcySciIgUTDabjWXLljF48GB2794NwMmTJ/n222955plnbu2kPj5gtRqPGxX8Lh9TuPCtXSeb0tNhyxZYvNjoHNq3L9SpA6NHQ/PmUEgfvhMRERERJ1CunNFkQ0RuzpmDpTnT7/+o3+Unwqu/ypmECLMjST5TuVhlsyOISB66qWJfhw4dqFatGjabLdP9+/fvp1q1air2iYjILdu2bRsDBw5k9erVGcbbtm3LPffcc+snbtjQWOguJgYCAq5/XEyMcVzDhrd+rRs4cwa++QaWLYPoaKhYEWrXNvb5+UGrVrlyWRERERERU2hmn8jt2TLnMXxLPkDj/gPZkPApNjJ/X1bkvyoHqNgnUpDcVLHP39+fnTt3Xnd/0aJFb7hfRETkeg4fPsybb77Jd999l2G8bt26TJw4kWa3u2BdcDA0aQI//wxFikBms9RTUyEqylgor0KF27veVaKi4PRpuPNOSEgw1uN7+GFo0waqVlWLThERERFxXir2idy+uDP+rB80i7s6PkNsk5cIv3DA7EiSD6jYJ1Kw3FSxz5LFu5FZ7RcREcnMrFmz6N27N6mpqfax0NBQxo0bx+OPP55z/74MGACHD8OePUbvTH9/o6Wn1WrM6IuKgurVoV+/275UcjKsWWO06dy8GSpVMmb0hYTA8uXg6nrblxARERERcXgq9onknB0LmuP5y06aDR7FBiaSZk0zO5I4sEoBlcyOICJ56AaLFomIiOSNevXqkZZmvEgpXrw477//Pnv37qVTp045+0GSoCCYMQM6dDBm8R06dOWRmmrM6JsxwzjuNoSHQ8uW8MYbcPEiDBkCM2de2a9Cn4iIiIgUFCr2ieSs5Hgv1gwfS8VftlHNr47ZccSBac0+kYLF1GLfwoULqVixIm5ubtx9993s27cPgN27d1O3bl2KFi3KwIEDM6wRuGbNGqpVq0bx4sWZMmWKWdFFROQWpaenc/To0Qxjd911Fz179uTNN9/k0KFDvPrqq3h4eOROgKAgmDgRvv8eRo40ZvGNHAk//GCM30Kh7/hx+PhjePttY7t8eejaFf7v/+Czz+DRR41lAEVERERECpqqVc1OIOKc/l13F/8M3EzTxMl4u3ubHUccTCG3QpQpUsbsGCKSh0wr9h06dIiuXbsyfvx4Tp48SZUqVQgLCyM5OZl27dpRu3Zttm3bxt69e5kzZw4AkZGRtG/fnqeeeopNmzYxd+5cVq1aZdZTEBGRm2Cz2ViyZAkPPPAArVq1ss/ku2zGjBm88847+OZVVSw4GDp3hrAw49ebXKMvKQnmz4du3eCRR2DuXHBzMzqCurhAly76FLOIiIiIiK9vji6HLSJXsaa5svbdfgR8t5vafi3NjiMOpFJAJS25JVLA3NSafTlp3759jB8/nieeeAKAl19+mTZt2rBs2TJiY2OZMmUK3t7ejB07lt69e9O1a1fmzp1L6dKleeutt7BYLAwfPpxPP/2U+++/P9NrJCcnk5ycbN+Oi4sDwGq1YrVac/9J5iKr1YrNZsv3z8OZ6J44Ht0Tx7F161aGDBnC6tWr7WOzZs2iR48e5oW6BWlpxpJ/VaoYv582zUKtWvDOOzaaNgUvL+O4/PRHTj8njkf3xPHonjgeZ78nzvq8RKTgqlUL/tPcQ0Ry0IldIZzou4JGL3/J/gr9iE46Z3YkMZlaeIoUPKYV+9q2bZth+59//qFy5crs2LGDBg0a4O1tTD+vVasWe/fuBWDHjh3cf//99k8l1KtXjyFDhlz3GuPGjePtyz3VrhIZGUlSUlJOPRVTWK1WYmNjsdlsuLho6UVHoHvieHRPzBceHs64ceNYtGhRhvF77rmHsmXLcvbsWZOSZZ/NBocOufLrr56sWuVJUpKF776LplAh+PxzuPTPFXFxxiO/0c+J49E9cTy6J47H2e/JhQsXzI4gIpKjatWCn382O4WI89v40fMUK9eKRq/3YePFb8yOIyaqHKBin0hBc1PFvujoaMqXL3/d/bGxsZQvX55jx47dVIiUlBQmT55Mv379OHjwICEhIfZ9FosFV1dXzp8/T1xcHNWrV7fv8/X15dSpU9c979ChQ+nXr599Oy4ujnLlyhEYGJh3beJyidVqxWKxEBgY6JRvcORHuieOR/fEPJGRkYwZM4aZM2eSmppqH69UqRKDBg2iS5cuuLq6mpgwe5KToWtXCwcPQtGi0KGDjdatoUKFEmZHyzH6OXE8uieOR/fE8Tj7PfG6PFVcRMRJ1KpldgKRguPc8UA2DphL7Sef43SdnpyM17TagkjFPpGC56aKfatXr8bT0xMPD49M91ut1gxtM7NrxIgR+Pj4EBYWxrBhw/D09Myw38vLi4SEBNzc3DLsuzx+PZ6entecC8DFxcUp3hSwWCxO81yche6J49E9yXsLFizg+eefzzArITAwkBEjRhAWFsb58+dxdXXN+3sSHg6bNkF8PPj4QMOGxrp9V0lKglWrYMMGGDUKChWCBx6AV1+FBg3A1dU5+93r58Tx6J44Ht0Tx+PM98QZn5OIFGwq9onkve3zHsZ7xR6aDnqT9akfYLWpTXhBcnepu82OICJ57KaKfY0bN87xAL///jvTp09n8+bNuLu7ExAQwO7duzMcc+HCBTw8PAgICCAyMvKacRERcRw1atQgMTERAG9vbwYMGMCAAQMoUqRIzq1BlI3CnV1EBEyaBOvWGX02XVyMRfV8faFpU2z9+vNnRBBLlsCvv0JCAtxzD5w/D8WKQffuORNZRERERKSgqlzZWN86n6+oIpLvJMT4sPaN96j+4NOktgrjQNwusyNJHvB09aRWSX3KQqSgMW3NPoAjR47w1FNPMX36dHt7zrp16zJr1qwMxyQnJxMQEEDdunX55psr/ab/+usvypQpk+e5RUTEYLPZOH36NEFBQfaxypUr07t3b5KSkhgxYkSGfbcti8Id/fvD1deLiIBevWDPHggMhNBQ+9f8cTyI2bPqEvXNUWx3FcXm6cVzz0Hr1qB/WkREREREco6rK1SvDn/+aXYSkYJp7y/1cFu7nWaD3mWzxzskp998ZzbJP2qVrIW7q7vZMUQkj5lW7EtMTKRt27Z06NCBRx55hIsXLwLQpEkT4uLi+Pzzz+natStjx46lRYsWuLq60r59e3r37s2vv/5Ks2bNmDBhAg899JBZT0FEpEDbsmULgwYNIjw8nH/++SfD+kJTp07FYsnhlpc3KNwREwOLFsGhQzBjxpWC36RJxvEVK4K7O8cSA3l029CrTmqjWOwZvgoZT+D0keR0ZBERERERMdSqpWKfiJnSkt1ZM3oYIXU64fvMS+yIXWt2JMkldUvXNTuCiJgg28W+v//+m+3bt9vfvHV1dWXz5s0sW7Ysw5oSKSkpnDhxIsvzrVy5kr1797J3795rZvLNnj2bp556ioEDB+Li4sLq1asBKF68OFOnTqV169YULlwYf39/5syZk92nICIiOeDgwYO88cYb/PDDD/ax6dOn079/f/t2jhf64JrCnZ2LCwQEQJEixv4pU2DiRKPV57p1JAaU4YOjT/J9RMZW1P8rtpO3qsyjSNxJ2JYKR7tcvxWoiIiIiIjcFq3bJ+IYjmyrCttX0/j1T9hZYjBxKbFmR5IcVreMin0iBVG2i33Hjh3j999/txf2VqxYwbZt23jttddwcXHBZrNRv359/vjjj2ydr0OHDthstkz3BQcHc+jQIbZv306DBg0oVqyYfV/Pnj156KGH2L9/P02aNKFw4cLZfQoiInIbzp49y+jRo5k5cyZpaWn28cqVK1O1atXcvfilwh2BgRkLfVdzdzf2r1mD7Ug4n48/w4ydn4GnJ1yqPZbyjGFS9c+4o/BVH0rx9zdmBG7erGKfiIiIiEguUbFPxIHYLKx/rwclK7WjWq/ebIlbYHYiyUGa2SdSMGW72Ne+fXvat29v3w4MDKR8+fIZjnF1dc2xN3xLlSpFmzZtMt0XEhJCSEhIjlxHRERuLD4+nqlTp/Luu+/aWy4DlChRgpEjRxIWFob79QpwOWXTJmONvtDQGx622/UuXvv7eeIe8AWrD1jOcJffEZ4svZ6WgX9l/kUuLsbjqucmIiIiIiI56957r3ThFxHHcOZgac70+z/qd/mJ8OqvciYhwuxIcpsKexSmWmA1s2OIiAluec2+XGnRJiIiDmXNmjU89dRTRERc+Q+/t7c3AwcOpH///hQpUiRvgsTHXynK/Ud0SmHmn27EzKMPGwPWZLCmU7FUIl97PoVHpfKZfp2d1Wo8NFNcRERERCTXFC0KNWvCjh1mJxGR/9oy5zF8Sz5A436D2JA4GxuZd2MTx3dv0L24WG7wHoiIOK1sFfv27duHt7e3vcBns9mwWq0cP37c3orz8piIiDiP0NBQzp8/Dxizt8PCwhgxYgRBQUF5G8TH50pRzsWFdJsLPXf24q+4ihkOaxawi8EukyjxzmvQoAE8UQhiYow1/a4nJgZ8faFhw1x9CiIiIiIiBV2zZir2iTiquDP+rB/8CXd1eIbYpi8RfuFfsyPJLVALT5GCK1vFvkaNGuHl5ZVh7Pz589SrVy/D2EW1QBMRydeio6MJuKowVrZsWV5//XX++ecfxo0bxx133GFOsIYNwdeX1UdDeONUb1KsV/75csHGJ7U+5G6/IxAdDalpxvEVKkCTJvDzz1CkSOZr/aWmQlQUtG9vHC8iIiIiIrmmeXN4/32zU4jIjexY2AzPX3fQbPAoNjCRNGua2ZHkJtQpXcfsCCJikmwV+y7P6rhaYGBghrZuAEWLFs2ZVCIikqcOHDjAG2+8wbp16zhw4ECG9pxjx47F5UZtMHPZwYMwZEgw4cd+MmbhebqCBXqUX86L5X/BxXKpvUhmhbsBA+DwYdizBwIDwd//ykIhMTHG8dWrQ79+Jj07EREREZGCo2lTsFjApg6BIg4tOd6LNcPHUrlxZ9wfC2Nv7FazI0k2NSjbwOwIImKSW373NrM1+7SOn4hI/nL27FleeeUVqlevzo8//siZM2eYPHlyhmPMKPQlJMDw4VCnDnTuDOHhQMmSPF15G6sDn2BbjS50L7fcKPRZrcaMviNHri3cBQXBjBnQoYNRDDx06MojNdUoDM6YYRwnIiIiIiK5qlgxqFHD7BQikl0H1tdi/8BNNE2cgo+7j9lxJAuVAioR7B9sdgwRMUm2ZvaJiIhziY+PZ8qUKUyYMCFDC+aSJUtSwaR2ljYb/Pkn9Ohx7b6vvoJq1Twg4mGYshfWrDEKdpdn6fn6GoW7fv2uLdwFBcHEiUbFcPNmuHgRChe+0upTRERERETyTLNmsHu32SlEJLusaa6sfbcvZe58hDte7Mn22BVmR5LreLDig2ZHEBETZbvYN3HiRD744APc3Nyw2WzYbDa2bt1qb/Fps9lIS1MPZxERR5aWlsZnn33GiBEjOH36tH3cx8eHgQMH0r9/fwoXLpynmVasgDffzDjm4QGjRkGLFv85+HYKd8HBxkNEREREREzTrBlMn252ChG5WSd3B3Oy73Ia9fyaf4L7ci4pyuxI8h8q9okUbNku9j333HO0adMGV1dXAKxWK59//jl//PGHvX1nzZo1cyeliIjcth07dtC5c2f2799vH3N1deWll15i+PDhlCpVKs+yREUZNbtVqzKO9+gBL75oTNi7IRXuRERERETypWbNzE5QUB0HypkdQpzAxpnPElD2YRr16cPGi3PNjiOXuFpc+V/I/8yOISImynaxr1SpUte8ETxhwoQcDyQiIrmjdOnSnDx50r796KOPMnbsWKpWrZon109NNZbHW7bMn6ioK2u8VqkC48dD+fJ5EkNERERERExUogRUqwb79pmdxEyDgb3Az5e2XwM+uGp/KHAwi3PYgInALOA88CQwAfABVgJPA32BN4F/gG3AMzkTXwq86BPF2Tjga2o/+Syn6/TkZPxRsyMVePXK1MPPy8/sGCJiIq3ZJ5JbwsNh0yaIjwcfH6PNoGYiSR66ePFihpacgYGBDB48mGXLljFhwgQaNWqUJzl++w0GD768ZSE11ZVGjYxZfHffnScRRERERETEgTRrVpCLfTuBGcCOq8a2AUuAy6/RXLNxnk+BacD/AX7As0BP4CuMAuAsoB9Gse8noH8OZBfJaPu8h/FesYemg4axPvV9rDar2ZEKLLXwFJEsi3179+5l/PjxuLllXRe0WCy0aNGCp556KkfCieRLp0/D5Mmwbh3ExRn9CK1W8PWFpk2hf39j3TGRXHL27FlGjRrF999/z759+yhWrJh93+DBg3njjTfs7ZdzS3Q09OwJhw9nHH/uORtPPHGOkiVL4OKSuxlERERERMQxPfggzJxpdgozWIGXMGbcVbw0lgbsAZoCN7N++pcYxbx6l7bfBjpf+n00cBfG7L8EwAXwvJ3gIteVEOPD2jemUv3Bp0htFcaBuF1mRyqQHgxVsU+koMuygufn50edOnXw9Mz6PwVnzpwhLCyMxx9/HHd39xwJKJKvREfDsGGwezcEBkJo6JViX0wMLFoEhw4ZvQxV8JMcdvHiRaZMmcLEiRO5ePEiAGPHjmXy5Mn2Y7LzwY1bdWLTcTo+4238eXdxgcKFwcOT1q1hyBDwvrTr7NlciyAiIiIiIvnAQw+BlxckJZmdJK/NBHZhFPwWAQ9jFPqswN3ASaAZ8AmQ1ToHUf85xpUrMwKLAJdfeM3DaPEpkrv2/lIPt7XbaTZwAps9R5Ocnmx2pAKjiEcRGpRtYHYMETFZlu/6lilThtdee43/+7//Y8mSJbi4uGTYn5aWRmpqKl999RUpKSkkJCSQmpqqYp8UTP/3f7B3L1SsCFf/DLi4QEAAFCkCe/bAlCkwcaJ5OcWppKWl8emnnzJy5EhOnz5tH/fx8SEwMDBXr221woj+F1j2bQxcuAjpsWCBO9wP86H/e/g3vxde7g/eKm6LiIiIiIjBxwdatjQ+D1twXARGYMzoO4rRbvMd4HWgKsaafcUxZv29BCzP4nz3AguBTpe25wCXZ/Y8iTFTMAw4AnTNoecgcmNpye6seedNQuo8ju8zL7Ejdq3ZkQqE+0Pux81Fq3WJFHTZ/lugfPny3HfffdcU+6xWK2lpaQC4u7szfPhwvL29czalSH5w9KhR6CtePGOh72ru7saMvzVrjDX9tIaf3AabzcbChQsZOnQo+/fvt4+7urrSo0cPhg8fTsmSJXPl2rNmwccfA6kpcOwMJCaCmzs1ip/mi3umXZrNmqzZrCIiIiIikqlHHiloxb75QDywCqOolwbUvDS27arjZgAhQBzge4PzjQVaAfcBFzBmDF4urDwFtAb2A8eABy6NLwYK3f5TEcnCkW1VYftqmrw+ix0lBhGXEmt2JKem9fpEBLJZ7Fu0aBFLly7FxcUFm80GGOvzpaWlkZyczOzZszl69CjPPPMM9erVY8qUKbkaWsQhbd4MCQng73/j4/z9jeLH5s0q9sktO3z4MM8//zwbNmzIMP7YY48xduxYqlSpkuPXPHIEOnX6z+CZM1RJ28cXTWfi7nnVh0E0m1VERERERG6gXTtwdYX0dLOT5JUTQAOMQh8Yb8nVAg7+57gSGG09I7hxsa88sBujoDcIKAU0uWq/H7AMqH3VNVdhFAFF8oDNwrr3XqJkpbZU6/UKW+L+z+xETsmChY53dDQ7hog4gGwV+3bt2kVqaipNmzbFYrFcs//w4cM0a9aMsLAw3nrrrRwPKZIvxMeDxWIUOS4VxTPl4mI8Lq2pJnIrihUrlmE2X+PGjZkwYQINGzY0BsLDYdMm48+ljw80bHhLxeWUFAgLMyat/td3k05QadBjxoxVz4DMT/Df2azls1p3QkRERERECoJixaBJE1i92uwkeaUskPifsaPASoz1+p6+NLYJcAHKZeOcFoyC4K/Axv/sOwcUBWIw2oReHhPJW2cOluZMv/nU6zKfo9Vf4UxChNmRnErDcg0p61vW7Bgi4gCy3cbzvvvuw9vbm+7du+Pn50ehQoUIDAykTJkynDt3jilTpvD0009nfSIRZ+XjYxT5rFaj6Hc9VqvxKFw477JJvpecnIynp6d928/Pj+HDhzNjxgzeffdd2rdvb3wYIyICJk2CdesgLs4oLFut4OsLTZtC//7ZaqfZvj2cOnXt+LvvwgOXO8B8e+kaoaE3PtnVs1lV7BMRERERkUseeaQgFfvaAK8CM4G2GG09dwCfAMOAkkD6pWOeBy4vkROH0XrzOsuF8A7Gun33/Gd8LkYBcTNGURGgbg48D5Fb88ecR/ENfIDGAwayIXE2Nm7wQXnJtidrPGl2BBFxEC5ZH3JFixYt+Ouvv9i4cSMrV65k6tSptGrVik2bNtG9e3f69OnDRc1WkoKqQQPw9oaYmBsfFxNjFF4uz8ASuYGLFy8ycuRIKlSoQERExk+/vfzyy+zevZsOHTpcKfT16gU//2zMqAsNvfJwdzcWxOjVyzguE3/+CXXqGI+rC339+8PWrbBt21WFPjBmDV6eqXojms0qIiIiIiKZ6NjR7AR5qRiwFPgCqAJMA74HngOeBB7DWGvvYeDDq76uFrDkOuc8CHyDsX7ff6UCgUBzjHafu4H7b/M5iNyeuEg/1g/+hJp/ria4SM4vP1LQuFhceLz642bHEBEHka2ZfS4uLvTt25chQ4ZkGE9LSyMpKYmjR48yZswYevfuTf369fnrr7/w8PDIlcAiDqtCBaheHbZvN2btuWfyqbvUVIiKMqZNVaiQ9xkl30hNTWX27Nm8/fbbnDlzBoCRI0fy8ccf249x/++fsUmTjPXxKlbM+OfvBuvnnToFzz5rTND7r2XLjA6c1+Xjc2Wm6o0KfprNKiIiIiIimShfHmrXNl5GFwz3YbTp/K9xlx6ZCb/B+SoBsdfZ1//Sr0WAAvMNlnxi56KmePy6k2aDR7HRMpFUa6rZkfKlxuUbU7pIabNjiIiDyFaxb8iQITRv3ty+FlRaWhpubsaXfvHFFxQuXJjAwECWLVvGggULVOiTguuRR4xXKbt3G1USf/8rbRRjYoxCX/Xq0K+f2UnFQdlsNhYsWMCQIUP4999/7eNubm54eXlhs9kyXTuV8HCjdWdgYOaFZrCvn2ddvZYODyQQEet9zSHffQeVKmUzbMOGxizVmBijmHg9ms0qIiIiIiLXcflltIgULCkJnqwZMYbK93XG4/Ew9sT+YXakfOeJ6k+YHUFEHEi22ngOHz6cXr16YbPZsFqtPProo7z33nscPnyYDz/8kJCQEKZOnUpycjKPPfZYbmcWcVwBAfDhh9ChgzGL79ChK4/UVGNG34wZ2VozTQqeDRs20LhxYx599NEMhb7HH3+cvXv3Mm3atMwLfQCbNhnT8/z9r3v+RafrUWf3HOrt+pSII0n28U6djBad27bdRKEPIDgYmjSByEjjz3dmLs9mbdZMs1lFREREROQajzxidgIRMdOBDTXZ238TTROm4uPuY3acfEMtPEXkv7Kc2ZeQkMCCBQtYsGABFouFN998k8jISLp3746Pjw9bt25lw4YNjBw5ktGjRzNt2jSee+65vMgu4phKlTJaJIaHw+bNxjplhQsbs5pU7JBMnDlzhp49e7JgwYIM402aNGHChAk0aNAg65NcZ/289dHV6bMn7MqAxXjcW/YsM34PwC1b87tvYMAAOHzYaA+q2awiIiIiInKTqleHmjVh1y6zk4iIWWxWF9ZO6EOZ6o9QrXtPtsUuNzuSw2tWoRklC5c0O4aIOJAs3+b19vZm586d9tkk3bt357XXXsPH58onLe677z5++eUX5syZw/33a7FfEcCY9RQcbHYKyQeKFCnC1q1b7dvVqlVj/PjxtGvX7voz+f7rqvXzEmyFaLrx2vUeKvlEMPWOTwg6uQ16jQS3O24/fFCQMVt1yhRYs8aYxXq52Ofra8xm7ddPs1lFREREROS6unWDvn3NTiEiZju5twIn+y6jUc+v+Se4L+eSosyO5LCeqKEWniKSUbbmdFz9ZnNISMh1j+vSpQtWq/X2U4mIgDE7ctMmY9aaj48xO9IJCqhXr3sKxocq3n77bYYNG8aoUaPo2rVrhv3Z0rAh7yW8xNdrWoOba4ZdYeVX0rPCpU/FRUfn/Pp5QUGazSoiIiIiIrfsuedgyBBITjY7iYg4go0znyWg7MM06tOXjRe/NjuOw3G1uPJYNS2lJSIZZflucmJiIm3atOH3338H4KeffsJqteLqaryZbLVaSUpK4pFHHiEtLY3mzZuzePFiypQpk7vJRcR5RUTApEmwbp2xDt3VM8WaNoX+/fPlTLHU1FRmz57NuHHjWLNmTYYPT3Tp0oXOnTtnmDWdHYsWwahRAMGQ+Cgkx0K6G3V89vFRrY+w+HhfHcBoq9m+fe4U4TSbVUREREREbkGxYtCxI8ybZ3YSEXEU0SeKs3HAV9R+4lnO1OvJiYvhZkdyGK0qtyLQJ9DsGCLiYLIs9nl5eREVdWXK9KBBg2jUqFGGYywWCy1btqR79+506NBBhT4RuXUREdCr15U14EJDM64Bt2iR0Spyxox8U/Cz2Wz83//9H0OHDuXff/8FYNiwYcydO9d+jKura7YLfXFx0LUrHD16aSA1Bc6cgfgE1ni2xCflPCS4wZ8exqvm4GBIStL6eSIiIiIi4rBefFHFPhG51vbvH8J75W6aDhrG+tT3sdrUVa5n7Z5mRxARB5Rlsc9isVzTbu6zzz4jPj4ef39/+/j3339Peno6I0eOzI2cIlJQTJpkFPoqVgR39yvjLi4QEABFihj7p0wxWkdmx9GjRntJE9qBrl+/nkGDBrFp06YM4+np6de087yR1FS47z6j5plxRwozig6jXuICCA0ErzuNdppRUUYPnBMn4OxZo2iq9fNERERERMRBtWhhvEwLDzc7iYg4moQYH9a+MZXqDz5FWqvu/Bu30+xIpqngV4FWlVuZHUNEHFC23mU+dOgQXbp0oVKlSiQkJLBjxw7q16+Pn58fpUuXplq1arRu3TrDLBURyQccbU288HCjdWdgYMZC39Xc3Y39a9YYx98o7+nT8PnnxmzA2NjrtwPNhe/D/v37GTJkCAsXLsww3rRpUyZMmED9+vWzdZ6vvoJp064dnzwZmjUD+g+Fn3/OWBytXh0SEoyZkMnJcO4c1KuX/eKoiIiIiIhIHrNYjA4mI0aYnUREHNXeX+rhumYbzQZNZIvXaJLSksyOlOe639sdF4uL2TFExAFlq9hXtGhRGjduzJkzZ0hLS6NOnTqkp6eTkJBAZGQkf/zxB9OnT+fTTz9lxYoVN73mlIhcR24V4xx1TbxNm4w8oaE3Ps7f32jluXnz9b8fERHwyiuQnm4UwTJrB7pnD5QvD3/9lWPfh7i4OAYNGsTs2bNJT0+3j1evXp13332XNm3aYLFYbniO/fvh2Wcz37d5M9gnA96oOOrtbTwA/Pzg77+zLo6KiIiIiIiYqFs3ePvtTDqaiIhckp7izpp33iC49uP4PdudHbFrzY6UZ9xd3Hnx3hfNjiEiDirLYl96ejqFCxcmLCwMgNmzZ/O///3vmuPee+89PvvsM7p168Y8NVkXuT25WYxz5DXx4uONLC5ZfELp8jEXL17/mEmTYO9eY/qbzWY8Ln9tQAB4eBjfX3d3YyZcDn0fvLy8+PXXX+2FvtKlSzNq1CheeOGFG7bsjIuDTP5qpVAh+OknKFEiky/KyeKoiIiIiIiIycqWhYcegmXLzE4iIo4ufHsV+HM1jV+bxc6Sg4hLiTU7Uq7rcEcHShUuZXYMEXFQWRb7UlJSKFOmjH37q6++wmKxkJiYiK+vL1arleTkZEJCQpg6dSr33nsvW7dupW7durkaXMRp5XYxLjfWxMspPj7G87Rab1zwu3xM4cKZ778846148aumwf3HsWPGrD9XV/DyunK9m/w+WK1WXK7K6uHhwdixYwkLC2PIkCH06dMH78sz7P7DZoMvvoAPP7x23/vvQ6NG172sISeLoyIiIiIiIg7gxRdV7BORbLJZWD/tJUqEtqNa71fYEjff7ES5qmftnmZHEBEHlmWD30KFCnH33Xfz77//AtC4cWPuu+8+Hn74Ybp3746vry/NmjXDz88PV1dXPv30U2rXrp3rwUWc1tXFuICAa4tQFSteKULdrFtZEy8vNWxozF6MibnxcTExxnENG2a+//KMN3//zPcnJBjr2Hl5GQW/zK6XxffBZrPx448/UqNGDfbu3ZthX6dOnThy5AhvvPFGpoW+H3+EOnWgbt2Mhb5atWDbNuORZaEPMhZHbySr4qiIiIiIiIiDaN/+Op1NRESu4+yhILb0+4n6R+ZTyru02XFyRZViVfhfSCYtoURELsnWap4//PADTZs25bPPPiMlJYWkpCSqVavGTz/9RLly5TIcW6dOnQyzXETkJuR2MS6rIthl/v7GcZs339z5b1dwMDRpApGRkJqa+TGpqRAVZbTnrFAh82OymvF2/jykpRnfS4vF+H1mrvN9WLduHQ0bNqRTp07s37+fIUOGZNhvsVgoVqxYhrHTp40CX506MH78lfHmzWHjRqPA99lnmce4rpwqjoqIiIiIiDgId3ej2Y2IyM3a8sUjJEzcS5NCPbBgMTtOjnrp3pewWJzrOYlIzspWVc7Pz4+NGzeyePFiypQpw5gxYzh06BBPPPEEzZo149577+Xee++lXr16fPzxx7mdWcR55XYxLj+0fRwwAGrUgMOHITr6yqw1q9XYPnLEWGOvX7/rnyOrGW+X1tPDYjF6aV6v1ed/vg/79u2jQ4cONG3alC1bttgPi4uLIyEh4ZovT0uDIUOMAl/bthn3zZ5tFPgmTTKWD7wlOVUcFRERERERcSCvvALXWQ1BROSG4iL9WDd4JjW3ryGkSFWz4+QILzcvutzdxewYIuLgslyzDyApKYmKFStSrlw5HnjgAUaOHMm8efP49ttvMxx38uRJunXrRo8ePXIlrIjTy+1iXE6tiZebgoKM9QinTDFmLx46dGXNQl9fo6dLv343Xq/w6hlvmRW4XF2NX1NSjEJf0aKZn+fS9+FUSgojX3qJTz/9FOtVBcQaNWrw7rvv0rp16wyfrurVC/7449rTDRoETzyRje/BzRgwwCiMXl7j0d8/4xqPUVFZF0dFREREREQcSLFiEBZmrGUuInIrdv7cBI/fdtBs8Gg2WiaQar3Oh6TzgedqPUcx72JZHygiBVq2in0xl1rEjRs3Dg8PD9LT07FYLFStmvHTEaVLl+b555/P8ZAiBUZuF+OuLoIFBFz/OLPbPgYFwcSJRpvSzZuNombhwkae7MxOuzzjbcmSzFt0Fi1qFPySkqB0aShUKNPTJEVHMyYujikDBpCQmGgfL126NKNHj+aFF17A9VLhMHz9CR5/vtCVe1e4MHh40qgRvPde1vXbW5YTxVEREREREREH06+f8VLneqsuiIhkJSXBkzUj3qHyfU/i8XgYe2Iz+WS2g3O1uDL4vsFmxxCRfCBbxb6IiAgAvC/1ULBarXzzzTfXHFekSBFGjRqVg/FECpjcLsZdLoL9/DMUKZL5uoCX2z62b29+28dm1fM/AAEAAElEQVTgYONxKwYMMFp+nj5ttP/087tSBEtKMn7v6grly2f+9ampuJ87x4LUVHuhz9fXlyFDhvD666/j7e3N+fPwYPMUOHMGLlyE9BiwADb4seqbBDevDP37g0suF9putzgqIiIiIiLiYCpUgM6d4euvzU4iIvndgQ01sWzaRNMBH7Dd703iU+PNjpRtne/sTGhAqNkxRCQfyFax779cXFyoXbt2TmcRkbwoxhWUto9BQfDhh/DFF7BgwbUz3jp3Nopjhw7Zvw82iwWLzWb/PrjWqMG7jz1Gx27d6NWrF8OGDaNYseI8+yz88w+QmgLHjkFiIri581X9D6nme/LK93LRPuP8M2bkzcy62ymOioiIiIiIOJjBg2HuXGOpdRGR22GzurB2wuuUqd6Rat17si12udmRsmTBwtDGQ82OISL5xC0V+0QkF+V2Ma4gtX0sVQq6djUKe1u2XDvjLSLC/n1Ys2sXgyMj+bBkSeqUKGH/PrQqVYrDzZtz/HhZWrc2vk12Z87QmPW81/y7jIVZFxdjZmaRIsZ9nDLFmHknIiIiIiIi2XbnndC6tbFCg4hITji5twIn+y6jYY+5/BvSh3NJUWZHuq6Od3SkRokaZscQkXxCxT4RR5MXxbiC1vaxQgUICbl2PCiIPV26MOTPP1m8dSsAA318+P3777EEB7NrF3RtB1A2w5eVKAE/Tj6K9wudjCKf+3Varrq7GwXbNWuM77Vm3YmIiIiIiNyUwYNV7BORnLfp42cIKPsQjfr0ZeNFx+wX/EaTN8yOICL5iIp9Io4or4pxBbjt46lTpxgxYgSfffYZ1qum60Va3HhpTEn++uvar1m4EMqUubTx7UaIi4PQLPqm+/sbBdvNmwvs91pERERERORWNWkCjRrBxo1mJxERZxN9ojgbB3zFvZ2e42z9Hpy4GG52JLuWoS2pU7qO2TFEJB9RsU/EkRXgYlxuiY2NZcKECUydOpXExET7eKFCCylRohFeXsX46y+Lfbx9exg+PJMTxccbMy5dXG58wcvHXLyYQ89ARERERESkYBk8GDp0MDuFiDirP39oifcvu2k68C02pL1Pui3d7Ei82eRNsyOISD6jYp+IFAjp6enMmDGDUaNGERV1uR/7c7i49CUoqBQlSpTE5VLh7qWXoHt3sFiufz58fIzWqlbrjQt+l48pXDjHnouIiIiIiEhB0q4d1KoFO3eanUREnFVCjA9r35xCtRZPkd46jH/jzPsLp3H5xjSt0NS064tI/pTFlJTcFRUVRUhICOHh4fax1157DYvFYn9UqlTJvm/37t3UrVuXokWLMnDgQGw2mwmpRSQ/cnFxYe7cuURFpQFbsVi2UaLEGGrWvJNSpYJwcXHhxx9h2zaj2HfDQh8YLVV9fSEm5sbHxcQYxzVsmDNPREREREREpICxWGDMGLNTiEhBsO/XuhwavJ1maWPxcvMyJYNm9YnIrTCt2BcVFUXbtm0zFPoAtm3bxpIlSzh//jznz5/nr0sLZyUnJ9OuXTtq167Ntm3b2Lt3L3PmzMn74CKS71itULeuhZiYX4BfCAgIoEaNOylXrhyvvurOtm1Gke+mOqYGBxuLR0RGQmpq5sekpkJUFDRrlrNrLYqIiIiIiBQwbdtC48ZmpxCRgiA91Y017wyl5Pyd3O3XPE+v3Ty4OQ9XejhPrykizsG0Np6dO3fm6aefZsuWLfaxtLQ09uzZQ9OmTSn8n5Z3y5YtIzY2lilTpuDt7c3YsWPp3bs3Xbt2ve41kpOTSU5Otm/HxcUBYLVasVqtOfyM8pbVasVms+X75+FMdE8cx549exg6dCgVKkxh/fpg3NwsWCw2ChcuTI0ad/LCCx707w9g3KtbvmX9+8ORI7B3LxQvDv7+RktPq9WY0XfuHNSoAX373sZFnIt+ThyP7onj0T1xPLonjsfZ74mzPi8Rkds1frwKfiKSd47+WZmjf/1O49dms6vUIGKTY3L1ehYsTHpwUq5eQ0Scl8VmUi/MI0eOEBISgsVi4ciRIwQHB/PXX3/RtGlTSpYsycmTJ2nWrBmffPIJ5cuX5+2332bLli0sXboUAJvNRrFixYiOjr7uNUaOHMnbb799zfi///5LkSJFcu255QWr1UpsbCx+fn72dcbEXLon5ouIiGDw4IX88ktbAAoV8qZixRBcXV0BC/PnR+Pjk8N/5UVHw8KFsHs3JCQY/WVsNvD2hjvvNFaRDwjI2WvmY/o5cTy6J45H98Tx6J44Hme/JxcuXKBKlSrExsbi6+trdhwREYfSrh0sXmx2ChEpaAJDTlPx1VfYEvdTrl3j2VrP8tUjX+Xa+UXEuZlW7LMHuKrYN3fuXKZOncoHH3xA8eLF6du3L2lpaSxfvpz+/fuTlJTE9OnT7V8bGBjIv//+S9GiRTM9d2Yz+8qVK8f58+fz/Ytmq9VKZGQkgYGBTvkGR37kkPfk6FHYvBni48HHBxo0cMp2kidOxFK/fjxnzpzFZrvySXh3dw+++MKFTp3uyP17cvQobNkCFy9C4cLG97p8+dy9Zj7kkD8nBZzuiePRPXE8uieOx9nvSVxcHEWLFlWxT0QkE7t3w913Q3q62UlEpCCq98ICjtXozemEUzl63kJuhfjnlX8o51cuR88rIgWHaW08M/PMM8/wzDPP2LdnzJhBSEgIcXFxuLm54enpmeF4Ly8vEhISrlvs8/T0vOZrAFxcXJziTQGLxeI0z8VZOMw9iYiASZNg3TqIi7vSWtLXF5o2NdpPBgWZmzEHTJmSxnvvnePUqQjS09Ps4x4eGxg1KolXXnmFCxcu5M09CQkxHpIlh/k5ETvdE8eje+J4dE8cjzPfE2d8TiIiOeXOO6FbN5g1y+wkIlIQ/fFFR3wD76fJgMGsT/wEGzkzj6ZPgz4q9InIbXHoV5ElSpTAarUSERFBQEAAkZGRGfZfuHABDw8Pk9KJOKiICOjVC37+GdzdITT0ysPdHRYtMvZHRJid9JbMmgV16tgIDY1m6NB9HD9+3F7oc3dvTL9+33Lq1LMMHjyYQoUKmZxWREREREREctro0ZDPV2cRkXwsLtKPdYNnUnP7GkKKVL3t85XwKcHQxkNzIJmIFGQOVewbOHAg33zzjX1706ZNuLi4UK5cOerWrcumTZvs+44cOUJycjIBWgtLJKNJk2DPHqhY0Vgr7vInw11cjO2KFY39U6aYm/MmnD4NdeoYj48/NsYiIyNJSUkG/gfU5Zln3uPff/czefJkihUrZmZcERERERERyUUlS8KQIWanEJGCbufPTTj51g6aMQx3F/dbPs/IZiMp4qlPMIjI7XGoYt9dd93FsGHD+O2331i5ciU9e/bk+eefx9vbm6ZNmxIXF8fnn38OwNixY2nRogWurq4mpxZxIOHhRuvOwEBjFl9m3N2N/WvWGMc7KKsV2rc3Cnxt22bc17evhVWrLgJ1eeCBemzbto2vv/6a4OBgM6KKiIiIiIjkC3PmzMFisVzzWL16da5fe/Xq1Tn6mq1fPy2TLiLmS0nwZM3I0VRY/ic1/Orf9NdXK16Nl2q/lAvJRKSgcag1+5599ln27NnDY489hqurK88++yxjx44FwM3NjdmzZ/PUU08xcOBAXFxc8uQ/oyL5yqZNxhp9oaE3Ps7fHw4dgs2bwcEKZN9+C5MnX9lOSUnh1KlT1KtnYenSClcdWZu///6bWrVqYbFY8jyniIiIiIhIfvP000/TsWNHdu3aRdOmTTl//jwAhQsXNjnZzfPyggkToHNns5OIiMDBjXdi2byRpv0/4E//YVxMvZitr5vw4ARcXTSZRURun+nFPpst4yKm48aNY9y4cZke2759ew4dOsT27dtp0KCBWvWJ/Fd8vNGu0yWLSbuXj7mYvf945LZNm+DVVzOOpaenERV1nMjIu0hOvsCxYzVIT9+RYTbvXXfdlcdJRURERERE8i8PDw88PDwocmnBO39/f3MD3aYnn4Q5c2D5crOTiIiAzerC2omvU6Z6R+7o/jLbYpfd8PiHQh+ibZW2NzxGRCS7HKqNZ3aUKlWKNm3aqNAnkhkfH6P/pdV64+MuH2PipzcvXLiyDt/VhT6bzUrr1p9x7FhJTpyoSHLyBQBOnDjB3r17TUorIiIiIiLi3Lp06cLIkSP5+uuvqVq1Kh999JF939atW6lfvz5+fn48+uijxMbGAkZb0ObNmzNr1ixKlixJyZIlmT9/vv3rZs+eTdmyZSlbtiwrV67MldwzZxovhUVEHMXJvRXY1ncpDU/NpXihwEyPKeRWiBltZuRxMhFxZvmu2CciN9CwIfj6QkzMjY+LiTGOa9gwL1Jl8PrrRoHv/vszjj/xhJX+/b8lOjqU0aNfJDo6GjA+edqvXz8OHTpEzZo18zyviIiIiIhIQbFixQo+/PBDJk2aRPv27QGIiYmhVatWtGrVip07dxIXF0f//v3tX7N7927mz5/Phg0b6Nq1K3369AFgx44dvPLKK0yfPp1ly5bx3Xff5UrmChVg1KhcObWIyG3Z9MnTpL+/j0aFn7tm31tN36Ji0YompBIRZ6Vin4gzCQ6GJk0gMhJSUzM/JjUVoqKgWTPjVVEe+OCDK7P4NmzIuG/rVpgw4Xe+/74eTz/9NOHh4fZ9zzzzDP/88w+TJ0/WbF4REREREZFcdujQIVasWEG7du0oU6YMAEuWLMHd3Z0RI0ZQoUIFBgwYwKJFi+xfEx8fzxdffEGlSpXo1q0bx48fB2DBggW0aNGCDh06ULNmTQYMGJBruS9/qFRExNGcP1mMjQO+5N49KyhXOASAO0vcyYBGufd3oogUTKav2SciOWzAADh8GPbsgcBA8Pc31uezWo0ZfVFRUL069OuXqzGOHYNHH8183y+/QNGiV7bff/99tm/fbt9+4IEHmDBhAvfee2+uZhQREREREZErXnjhBfz8/DKMnThxgsjISIpeehFntVq5cOECSUlJAFSrVo0SJUoARmeWyyIiIihfvrx9OzQ0NNdyu7rCrFlQty6kpeXaZUREbtmfP7Sk0IrdNBvyFuPaPo67q7vZkUTEyajYJ+JsgoJgxgyYMgXWrIFDh64U+3x9oX17o9AXFJTjl05LgwYNMt83aBA88UTm+8aNG8fPP/9MzZo1effdd2nZsiUWiyXH84mIiIiIiMj1+WSy+F3ZsmWpXbs28+bNA8BmsxEbG4u7u/FGta+vb6bnKlGiBDt37rRvHzt2LBcSX3H33dC3L0ycmKuXERG5ZYlx3tQ8NZmG5cxOIiLOSMU+EWcUFGS8wgkPh82b4eJFKFzYWKMvF1p3DhoEv/9+7XiTJjB16pXtmJgYxo8fT926dXnsscfs49WqVWP9+vXUq1cPV1fXHM8nIiIiIiIit6ZNmzYMGDCAP/74g2bNmvHll18yefJkTpw4ccOva9euHRMnTmTp0qVUqFCBiXlQhRs5En76yWh2IyLiaIKDYfx4s1OIiLNSsU/EmQUHG49c8PffEBaW+b5Nm8D9qm4EycnJzJgxg3feeYfo6GhCQkJo27Ytnp6e9mMaBgXB999DfDz4+BiFyVzKLiIiIiIiItnj7+/PokWLeOWVV+jatSs1atRg0aJFuLnd+C2lunXrMmnSJMLCwnB3d6djx44sXLgwV7N6e8PMmdCyZa5eRkTklsyaZbzlJSKSGyw2m81mdoi8EhcXh5+fH7GxsddtM5FfWK1Wzp49S4kSJXBxcTE7jlAw7kl09PVfNH39NdxxR8Yxq9XKd999x5tvvkl4eLh93MPDg19//ZUmTZpARARMmgTr1kFcXMaWo02bQv/+t9xytCDck/xG98Tx6J44Ht0Tx6N74nic/Z440+sWEREzPP88fPWV2SlERK548UWYPdvsFCLizDSzT0Sy9NBDcO7cteODB0OnTpl/zW+//cagQYP4888/7WMWi4Vnn32W0aNHU6FCBaPQ16sX7NkDgYEQGnql2BcTA4sWGWsOzpiRK2sMioiIiIiIiPN57z1jqYmTJ81OIiICZcvC5MlmpxARZ+d8H4MVkRyxaBHUqWM8/lvo27oVtm3LvNC3c+dOWrVqRYsWLTIU+lq2bMmff/7Jl19+aRT6wJjRt2cPVKwIAQFGoQ+MXwMCjPE9e2DKlFx6liIiIiIiIuJsAgKM7jNOOPlbRPIZV1eYOxf8/MxOIiLOTjP7RG5XeLixSF18vNF6sm5dCAkxO9UtOXsWWrfOfN/KlcYLpqxMnDiR5cuX27fvvvtuJkyYwIMPPpjxwPBwo3VnYGDGBf6u5u5u7F+zxjhea/iJiIiIiIhINjRvDm++CaNHm51ERAqyYcOMVWpERHKbin0it+q/a825uUHlysa8/CZNbmutubyUlmZ00rxqEp7dzJnGzL6bMfr/2bvzOBvr/o/j73NmX42xzdiHEMqWNT+kSFkmpBTuJCpRyJYUUllClgoVorQoJRVpcWcpUcg6IlmLwYxtZsw+5/r9cd1zOGYxw8ycMzOv5/24HnOf77Wcz3V9u3DN5/p+P6+8os8++0yhoaGaNGmSHn744cxr6WzebF636tWzP2BQkDmV55YtJPsAAAAAADk2YYK0bp30yy/OjgRAcdSmjTRunLOjAFBckOwDrkdmtebc3KSQECkqqlDUmnv3XXO52qhRUs+e194/MTFRc+fOVWhoqHr16mVvr1q1qn744Qc1a9ZM3t7eWR/g0iVzTpVrzauSvk1c3LWDAgAAAADgf9Knz2vQQDp/3tnRAChOSpc2//xxc3N2JACKC5J9wPW4stbclVNQptea8/e/XGtu+nTnxXmVAwek3r0ztgcGSj/+mLN/gNhsNn3yySd64YUXdOzYMZUvX1733Xef/Pz87Nu0adPm2gfy85NsNnPJLuGXvo2//7WPCQAAAADAFSpXlhYtkrp3d3YkAIqTJUukChWcHQWA4oRkH5BbhazW3MWL0l13Zb5u9WqpXLksdryyFqGfn9Sihdb+/bdGjx6tHTt22DeLjIzU2rVrdd999+UusBYtzCzjhQvZFwO8cMHcrkWL3B0fAAAAAABJ3bpJTz0lzZ/v7EgAFAfPPit16uTsKAAUNyT7gNwqBLXmDMOcZXTr1ozr3nhDuv32bHa+uhah1apd8fEaff68foiJcdi0Q4cOeu2111S/fv3cB1m1qlnb8JtvpICAzBOnKSlSdLQUHi5VqZL77wAAAAAAQObEOz//LO3d6+xIABRljRtLU6c6OwoAxRHJPiC3XLjW3Pr10siRGdubNjXLB17TVbUIj5cvr3EHD2rpv//KuGKzhrfcommzZqldu3Y3FvDIkdLhw5drHwYFmdfMZjNH9EVHS3XqSMOH39j3AAAAAACKNW9v6dNPzV/EJyQ4OxoARVFAgLRsmeTp6exIABRHJPuA3HKxWnMnT5oD367m52dO05mrr7+qFuFre/bog3//ta+u4u2tSSVK6OEOHWS90USfJIWGmlnImTPNKU8PHbqc7AsMNE9s+HBzOwAAAAAAbkCdOtLcudJjjzk7EgBF0dtvX3siMADILyT7gNzK61pzmdTGu9a0n6mpUs+e0rFjGdctWybddNM1ziGrOK6qRTiuRg29/++/8rRa9WKNGhpcpYq8Ll6UNm7Mu1qEoaHS9Onm8bZsMUdC+vub14GpOwEAAAAAeahfP2n3bmn2bGdHAqAoGTZM6tXL2VEAKM5I9gG5lVe15jKpjWcf0da6tTRiRIYRbbNnSx9+mPFQzz4r9e59/adks9n08fTpSvvnH/Vt1MjeHuLtrS8bN1bjEiVUMn0OgvyqRVi1aoHXNgQAAAAAFD8zZkgHDkhr1jg7EgBFQadO0uuvOzsKAMUdyT7gemRWa87NzUzWnTsnRUVlX2vuqtp4ql7dsVbd11+bCbV58/Tb8VANHpzxEPfcI7388rVLB17Ljz/+qNGjR2vnzp0KdnPTfWlpCrrioO3LlHHcwQm1CAEAAAAAyCtubuasOC1aSPv2OTsaAIXZrbdKn3xy47+fA4AbRbIPuB6Z1ZpzdzefGFJSrl1r7qraeHZWqxQcrFifsuq/9nEdbpgqVXTc9dtvpbJlb/wUduzYoeeee04//vijve1cWpq+OHlS/bObPrOAahECAAAAAJBfAgPNCXuaNpXOnnV2NAAKo7JlL0/8BQDORrIPuF6Z1ZoLDDSfFLKbjjKT2niSZBhSrx0jdfBS+f81pEmxcVJykqa87qX27fMm7GPHjunFF1/Uh1fNB9rolls0PSVFd17rXyg5rUUIAAAAAIALq1ZNWrFCatfOfG8XAHLKy0v68susq/cAQEEj2QfcqPRaczabdObMtYfdbd5s1uirXl2StOh4e80/dm+GzR6psl5D0mZLY1+S2j90w2GeO3dOkydP1ptvvqnk5OQrwq+qyZMnq2fPnrKOGnXjtQgBAAAAACgkWreW5s+XBgxwdiQACpNFi6Tbb3d2FABwGck+oKBduqTdyTfrsU3TM1298fbn5euWZH44lHe18WbMmKHXr6gWHBwcrBdffFGDBg2Sl5eX2ZhZLcIrawlGR2dfixAAAAAAgEKmf3/zMXjWLGdHAqAweOEFqXdvZ0cBAI5I9gEFJD7efGNQ57pJJ5tLXpIs5rr36r+heoFHHXfI49p4I0eO1Pz585WYmKihQ4dqzJgxCgoKctwos1qE6cm+wMBr1yIEAAAAAKAQmjFDOnBA+vZbZ0cCwJX16CG98oqzowCAjEj2Afls6lTp88+vaPD3l9zcdHfAZk1uuDzrHW+gNt4PP/ygEydOqF+/fva24OBgffLJJ6pbt64qVaqU9c6Z1SL09zfjYOpOAAAAAEARZLVKy5ZJbdtK27c7OxoArqhxY+n99yWLxdmRAEBGJPuAfLBxY9YzXf6+00vWUQvN2ngp1fK0Nt6OHTs0evRorV27Vv7+/urUqZPKXlFD8J577sn5SaTXIgQAAAAAoBgICJC+/15q08ac1hMA0tWtK333neTr6+xIACBzVmcHABQVFy9KHTqUUtOmlgyJvjVrpG3bzMVqlVkbr25dsz7euXPmNJmS+fPcOenIkVzVxjt69Kj69OmjRo0aae3atZKkuLg4LVq0KA/PEAAAAACAoq1UKenHH6WbbnJ2JABcRY0a0tq15p8PAOCqSPYBN8AwzCk6b79dat/ecQz/tGmXE3xlyly1Y3ptvPvuM0fxHTp0eUlJMUf0zZt3zdp4586d04gRI1SrVi199NFH9vawsDB98skneu655/LqVAEAAAAAKBZCQ81f7GdXAQNA8VClivTf/0ohIc6OBACyxzSewHVYs0YaNy5je8+eCRozxl9Waw4m776B2ngJCQl68803NWXKFF24cMHeXqpUKY0bN04DBw6Ul5dX7k4KAAAAAABIMh/L166VWreWTp92djQAnKF8eTPRR+IfQGFAsg/IIcMw/4IfM8ax/Y47pKlTJavV0Jkz8ZL8c3fg66iN99ZbbzmM2vP29tazzz6r5557TiVKlMjd9wMAAAAAgAxq1pR++MF87j9/3tnRAChIZcqYCf/q1Z0dCQDkDNN4AtlITpYeflhq3Fjq2VP644/L61asMKfonDFDci/gtPlTTz2lcuXKyWq16rHHHtPBgwc1efJkEn0AAAAAAOShevWk776TAgKcHQmAglKypFm7s3ZtZ0cCADnHyD4gEx9/LM2c6dhWsaI0cqQ0enTBxrJ9+3bt2rVLjz32mL3N399fixcvVqVKlXTLLbcUbEAAAAAAABQjTZtKq1ZJ99wjJSQ4OxoA+SkgwEzw16/v7EgAIHdI9gH/s3Gj9OGHjqP3JKljR2niRMmSgzJ8eenIkSN64YUX9Mknn8jLy0vt2rVT5cqV7evvvffegg0IAAAAAIBiqnVr6csvpa5dpcREZ0cDID/4+pqJ/aZNnR0JAOQeyT4Ua6dPS506ObYFBEgVKkjvvmv+JV/Qzp49q1dffVVz585VSkqKJCkpKUlvvPGGZsyYUfABAQAAAAAAdeggff+91KWLFBPj7GgA5KXAQOnrr83EPgAURiT7UHwcPSpt3iwj7pK+iLhZU39qInl6OWzy5ptSixbOCS8hIUFz5szR1KlTdfHiRXt7qVKlNH78eA0cONA5gQEAAAAAAElmImD9enNKzzNnnB0NgLxQrpw5dWeDBs6OBACuH8k+FH2RkdKMGZq4rJa+OXu7ZJFkSHI7LAX4q0ufIE2Y4bxK22lpafrggw80fvx4/fvvv/Z2Hx8fPfvssxo9erRKlCjhtPgAAAAAAMBlDRtKv/witW8vHTvm7GgA3IiwMOmHH6SbbnJ2JABwY0j2oUg7t++URt0boV2nH5XcPSQvN8kivVJjqe72WCe36NPSobpS5DwpNNQpMS5ZskQDBgywf7ZarerXr58mTpyoChUqOCUmAAAAAACQtRo1pE2bzITfn386OxoA1+PWW82peZ30K0EAyFNWZwcA5LXkZOmtt6TGjaW770zRrtPlJC8vyd1NSxrM1rZWw3VvyA65lQqSqlWTIiKkmTOdFm+fPn1UpUoVSVLnzp21e/duLVy4kEQfAAAAAAAurEIF6eefpaZNnR0JgNz6v/+TNm4k0Qeg6GBkH4qM776TXnzxiobkJAXGn9a82q/r5opxme/k4SGVKSNt2GDW9KtaNV9jPHLkiDZs2KBHH33U3ubl5aV33nlH3t7eatOmTb5+PwAAAAAAyDulSkn//a/UrZu0dq2zowGQE507S599Jvn4ODsSAMg7JPtQqB06JD37rHTypGP72LFSt8QVsrw8USpfXdkOYg0KMg+0ZUu+Jfuio6M1adIkzZ07VzabTbfffrtq1qxpX9+hQ4d8+V4AAAAAAJC//P2l1aulXr2kL75wdjQAsvOf/0jvvSe581txAEUM03ii0ImOlh55xJyms2fPy4m+++4zp8/Ytk3q3l2yxF+SrFYpMVE6cUI6ftz8GR/veECr1Vzishj9dwMSEhI0depUVa9eXbNnz1ZKSorS0tI0ZcqUPP8uAAAAAADgHJ6e0qefSk8/7exIAGRl+HDp/fdJ9AEomvijDYWCzSZ9+KH0xhuO7d26SX37ShUrZrJTcrJ06pSZ4EtLkywWyTDMv9FLlTLr9Xl7mwe32cxX8fJIWlqaPvjgA40bN04nTpywt/v4+GjEiBEaNWpUnn0XAAAAAABwPjc36c03pQYNpEGDzF9LAHA+T09p3jypf39nRwIA+cfpI/uio6MVFhamo0eP2tv27t2rJk2aqGTJkho1apQMw7Cv27Bhg2rXrq3SpUtr5syZTogYBem336QhQ8xi11cm+oYPN0fwvfBCFom+yEhp5UpzFJ9hSH5+kq+v+dNqlU6flvbsMUf9XbggBQZKLVrccLyGYejbb79VgwYN9Nhjj9kTfVarVY8//rj+/vtvvfLKKwoMDLzh7wIAAAAAAK6nf39p3TopJMTZkQAIDZXWryfRB6Doc2qyLzo6Wp07d3ZI9CUlJalLly667bbbtG3bNu3bt09LliyRJEVFRSk8PFwPP/ywNm/erI8++kjr1q1zTvDIN5GR5hSdjRtLgwdLv/5qtg8ZIv3+u5nk69XrGgeZMUM6etT8l3X6yL10Hh5m4i8uTvr7b3Ne0DZtpCpVzPVHj0qffCItXGj+vOK/z2v57rvv1KVLF+3du9feFh4erj179ujdd99V+fLlc3wsAAAAAABQON1+u/n7iyZNnB0JUHw1by5t354n7/cDgMtz6jSeDz30kHr16qXffvvN3rZmzRpdvHhRM2fOlK+vryZPnqzBgwerX79++uijj1S+fHmNGzdOFotF48eP16JFi9S2bdtMj5+UlKSkpCT755iYGEmSzWaT7crkTyFks9lkGEahP490ycnS7NnS559bHNpbtpQmTDAUFHS57ZqnfOyY9MsvUtmyZlIvOdlM7Hl4mMuV03mePi21aiU9+6xZ/O/**************************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", "text/plain": ["<Figure size 1800x1600 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "�🚀 继续生成详细分析...\n"]}], "source": ["\"\"\"\n", "X11分解最终融合预测模型\n", "Trend: 线性回归7特征模型\n", "Seasonal: 完全周期性模式\n", "Residual: 梯度提升模型\n", "最终预测 = Trend预测 + Seasonal预测 + Residual预测\n", "\"\"\"\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.ensemble import GradientBoostingRegressor\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import joblib\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 配置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"=== X11分解最终融合预测模型 ===\")\n", "print(\"Trend: 线性回归7特征模型\")\n", "print(\"Seasonal: 完全周期性模式\")\n", "print(\"Residual: 梯度提升模型\")\n", "\n", "# 1. 数据加载\n", "print(\"\\n1. 数据加载\")\n", "print(\"-\" * 50)\n", "\n", "# 加载X11分解结果\n", "decomp_path = r\"D:\\埃塞俄比亚咖啡\\模型\\x11_decomposition_results.csv\"\n", "decomp_data = pd.read_csv(decomp_path)\n", "\n", "# 加载外生变量\n", "exog_path = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "exog_data = pd.read_csv(exog_path, encoding='gbk')\n", "\n", "print(f\"X11分解数据: {decomp_data.shape}\")\n", "print(f\"外生变量数据: {exog_data.shape}\")\n", "\n", "# 2. 数据合并和预处理\n", "print(\"\\n2. 数据合并和预处理\")\n", "print(\"-\" * 50)\n", "\n", "# 合并数据\n", "min_len = min(len(decomp_data), len(exog_data))\n", "\n", "data = pd.DataFrame({\n", "    'date': pd.to_datetime(decomp_data['parsed_datetime'].iloc[:min_len]),\n", "    'original': decomp_data['Original'].iloc[:min_len],\n", "    'trend': decomp_data['Trend'].iloc[:min_len],\n", "    'seasonal': decomp_data['Seasonal'].iloc[:min_len],\n", "    'residual': decomp_data['Residual'].iloc[:min_len],\n", "    'sales_price': exog_data['sales price（usd/lb）'].iloc[:min_len],\n", "    'exchange_rate': exog_data['Exchange(Domestic currency per US Dollar)'].iloc[:min_len]\n", "})\n", "\n", "data.set_index('date', inplace=True)\n", "data = data.dropna()\n", "\n", "print(f\"合并后数据量: {len(data)} 样本\")\n", "print(f\"时间范围: {data.index[0]} - {data.index[-1]}\")\n", "\n", "# 3. 数据分割 - 使用与之前Residual模型相同的分割点\n", "print(\"\\n3. 数据分割\")\n", "print(\"-\" * 50)\n", "\n", "# 使用固定的分割点：2022-12-01，与之前的Residual模型保持一致\n", "split_date = '2022-12-01'\n", "train_data = data[data.index < split_date]\n", "test_data = data[data.index >= split_date]\n", "\n", "train_dates = train_data.index\n", "test_dates = test_data.index\n", "\n", "print(f\"训练集: {len(train_data)} 样本 ({train_dates[0]} - {train_dates[-1]})\")\n", "print(f\"测试集: {len(test_data)} 样本 ({test_dates[0]} - {test_dates[-1]})\")\n", "\n", "# 4. Trend预测 - 线性回归7特征模型\n", "print(\"\\n4. Trend预测 - 线性回归7特征模型\")\n", "print(\"-\" * 50)\n", "\n", "def create_trend_features(data):\n", "    \"\"\"创建Trend预测的7个特征\"\"\"\n", "    features_data = data.copy()\n", "    \n", "    # 时间特征\n", "    features_data['year'] = features_data.index.year\n", "    features_data['month'] = features_data.index.month\n", "    features_data['time_index'] = range(len(features_data))\n", "    \n", "    # Trend历史特征\n", "    features_data['trend_lag_1'] = features_data['trend'].shift(1)\n", "    features_data['trend_lag_12'] = features_data['trend'].shift(12)\n", "    \n", "    # 外生变量\n", "    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)\n", "    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)\n", "    \n", "    return features_data.dropna()\n", "\n", "# 创建Trend特征\n", "trend_features_list = ['time_index', 'trend_lag_1', 'trend_lag_12', 'sales_price_lag_1', 'exchange_rate_lag_1', 'year', 'month']\n", "\n", "trend_train_data = create_trend_features(train_data)\n", "trend_test_data = create_trend_features(test_data)\n", "\n", "# 确保测试集有足够的历史数据\n", "if len(trend_test_data) == 0:\n", "    # 如果测试集没有足够历史数据，使用全部数据重新分割\n", "    full_trend_data = create_trend_features(data)\n", "    trend_split_idx = int(len(full_trend_data) * 0.8)\n", "    trend_train_data = full_trend_data.iloc[:trend_split_idx]\n", "    trend_test_data = full_trend_data.iloc[trend_split_idx:]\n", "\n", "X_trend_train = trend_train_data[trend_features_list]\n", "y_trend_train = trend_train_data['trend']\n", "X_trend_test = trend_test_data[trend_features_list]\n", "y_trend_test = trend_test_data['trend']\n", "\n", "# 标准化\n", "trend_scaler = StandardScaler()\n", "X_trend_train_scaled = trend_scaler.fit_transform(X_trend_train)\n", "X_trend_test_scaled = trend_scaler.transform(X_trend_test)\n", "\n", "# 训练Trend模型\n", "trend_model = LinearRegression()\n", "trend_model.fit(X_trend_train_scaled, y_trend_train)\n", "\n", "# Trend预测\n", "trend_train_pred = trend_model.predict(X_trend_train_scaled)\n", "trend_test_pred = trend_model.predict(X_trend_test_scaled)\n", "\n", "print(f\"✓ Trend模型训练完成\")\n", "print(f\"  训练集RMSE: {np.sqrt(mean_squared_error(y_trend_train, trend_train_pred)):.2f}\")\n", "print(f\"  测试集RMSE: {np.sqrt(mean_squared_error(y_trend_test, trend_test_pred)):.2f}\")\n", "print(f\"  测试集R²: {r2_score(y_trend_test, trend_test_pred):.4f}\")\n", "\n", "# 5. Seasonal预测 - 完全周期性模式\n", "print(\"\\n5. Seasonal预测 - 完全周期性模式\")\n", "print(\"-\" * 50)\n", "\n", "# 计算每个月的平均季节性成分\n", "seasonal_pattern = train_data.groupby(train_data.index.month)['seasonal'].mean()\n", "\n", "print(f\"✓ 季节性模式计算完成\")\n", "print(f\"  12个月的季节性模式:\")\n", "for month, value in seasonal_pattern.items():\n", "    print(f\"    {month:2d}月: {value:>8.4f}\")\n", "\n", "# 生成Seasonal预测\n", "def predict_seasonal(dates, pattern):\n", "    \"\"\"根据日期生成季节性预测\"\"\"\n", "    months = [date.month for date in dates]\n", "    return np.array([pattern[month] for month in months])\n", "\n", "seasonal_train_pred = predict_seasonal(trend_train_data.index, seasonal_pattern)\n", "seasonal_test_pred = predict_seasonal(trend_test_data.index, seasonal_pattern)\n", "\n", "# 对应的真实值\n", "seasonal_train_actual = trend_train_data['seasonal'].values\n", "seasonal_test_actual = trend_test_data['seasonal'].values\n", "\n", "print(f\"  训练集RMSE: {np.sqrt(mean_squared_error(seasonal_train_actual, seasonal_train_pred)):.4f}\")\n", "print(f\"  测试集RMSE: {np.sqrt(mean_squared_error(seasonal_test_actual, seasonal_test_pred)):.4f}\")\n", "print(f\"  测试集R²: {r2_score(seasonal_test_actual, seasonal_test_pred):.4f}\")\n", "\n", "# 6. Residual预测 - 梯度提升模型\n", "print(\"\\n6. Residual预测 - 梯度提升模型\")\n", "print(\"-\" * 50)\n", "\n", "def create_residual_features(data):\n", "    \"\"\"创建Residual预测的特征 - 使用与之前梯度提升模型相同的配置\"\"\"\n", "    features_data = data.copy()\n", "\n", "    # 时间特征\n", "    features_data['month'] = features_data.index.month\n", "\n", "    # 残差历史特征 - 包含更多滞后项\n", "    for lag in [1, 3, 6]:\n", "        features_data[f'residual_lag_{lag}'] = features_data['residual'].shift(lag)\n", "\n", "    # 残差波动性\n", "    features_data['residual_volatility'] = features_data['residual'].rolling(window=6).std()\n", "\n", "    # X11分解相关特征\n", "    features_data['trend_change'] = features_data['trend'].pct_change()\n", "    features_data['seasonal_abs'] = np.abs(features_data['seasonal'])\n", "\n", "    # 外生变量特征 - 完整配置\n", "    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)\n", "    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)\n", "    features_data['sales_price_change'] = features_data['sales_price'].pct_change()\n", "    features_data['exchange_rate_change'] = features_data['exchange_rate'].pct_change()\n", "    features_data['exchange_sales_interaction'] = features_data['exchange_rate'] * features_data['sales_price']\n", "\n", "    # 当前外生变量 - 重要！\n", "    features_data['exchange_rate'] = features_data['exchange_rate']\n", "    features_data['sales_price'] = features_data['sales_price']\n", "\n", "    return features_data.dropna()\n", "\n", "# Residual特征列表 - 与之前梯度提升模型相同的13个特征\n", "residual_features_list = [\n", "    # 残差历史信息\n", "    'residual_lag_1', 'residual_lag_6', 'residual_volatility',\n", "\n", "    # 当前外生变量 (重要)\n", "    'exchange_rate', 'sales_price',\n", "\n", "    # 外生变量历史信息\n", "    'exchange_rate_lag_1', 'sales_price_lag_1',\n", "\n", "    # 外生变量变化\n", "    'exchange_rate_change', 'sales_price_change',\n", "\n", "    # 交互特征\n", "    'exchange_sales_interaction',\n", "\n", "    # X11分解相关\n", "    'seasonal_abs', 'trend_change',\n", "\n", "    # 时间特征\n", "    'month'\n", "]\n", "\n", "# 创建Residual特征\n", "residual_train_data = create_residual_features(train_data)\n", "residual_test_data = create_residual_features(test_data)\n", "\n", "# 确保测试集有足够的历史数据\n", "if len(residual_test_data) == 0:\n", "    full_residual_data = create_residual_features(data)\n", "    residual_split_idx = int(len(full_residual_data) * 0.8)\n", "    residual_train_data = full_residual_data.iloc[:residual_split_idx]\n", "    residual_test_data = full_residual_data.iloc[residual_split_idx:]\n", "\n", "# 确保特征存在\n", "available_features = [f for f in residual_features_list if f in residual_train_data.columns]\n", "print(f\"可用Residual特征: {len(available_features)}/{len(residual_features_list)}\")\n", "\n", "X_residual_train = residual_train_data[available_features]\n", "y_residual_train = residual_train_data['residual']\n", "X_residual_test = residual_test_data[available_features]\n", "y_residual_test = residual_test_data['residual']\n", "\n", "# 训练Residual模型 - 使用与之前相同的梯度提升配置\n", "residual_model = GradientBoostingRegressor(\n", "    n_estimators=50,\n", "    max_depth=3,\n", "    learning_rate=0.05,\n", "    subsample=0.8,\n", "    min_samples_split=10,\n", "    min_samples_leaf=5,\n", "    max_features='sqrt',\n", "    random_state=42\n", ")\n", "\n", "residual_model.fit(X_residual_train, y_residual_train)\n", "\n", "# Residual预测\n", "residual_train_pred = residual_model.predict(X_residual_train)\n", "residual_test_pred = residual_model.predict(X_residual_test)\n", "\n", "print(f\"✓ Residual模型训练完成\")\n", "print(f\"  训练集RMSE: {np.sqrt(mean_squared_error(y_residual_train, residual_train_pred)):.2f}\")\n", "print(f\"  测试集RMSE: {np.sqrt(mean_squared_error(y_residual_test, residual_test_pred)):.2f}\")\n", "print(f\"  测试集R²: {r2_score(y_residual_test, residual_test_pred):.4f}\")\n", "\n", "# 7. 最终融合预测 - 修正版：确保测试集完整覆盖\n", "print(\"\\n7. 最终融合预测\")\n", "print(\"-\" * 50)\n", "\n", "# 重新处理，确保测试集从原始分割点开始，没有空缺\n", "split_date = pd.to_datetime('2022-12-01')\n", "\n", "# 使用原始的训练和测试分割\n", "original_train_data = data[data.index < split_date]\n", "original_test_data = data[data.index >= split_date]\n", "\n", "print(f\"原始训练集: {len(original_train_data)} 样本 ({original_train_data.index[0]} - {original_train_data.index[-1]})\")\n", "print(f\"原始测试集: {len(original_test_data)} 样本 ({original_test_data.index[0]} - {original_test_data.index[-1]})\")\n", "\n", "# 对于测试集的每个时间点，逐步预测\n", "def predict_step_by_step(test_data, train_data, models, scalers, seasonal_pattern):\n", "    \"\"\"逐步预测测试集，使用滚动窗口方式\"\"\"\n", "\n", "    trend_model, residual_model = models\n", "    trend_scaler = scalers\n", "\n", "    # 初始化预测结果\n", "    trend_predictions = []\n", "    seasonal_predictions = []\n", "    residual_predictions = []\n", "    final_predictions = []\n", "\n", "    # 合并训练和测试数据用于特征构建\n", "    full_data = pd.concat([train_data, test_data])\n", "\n", "    for i, test_date in enumerate(test_data.index):\n", "        print(f\"  预测 {test_date.strftime('%Y-%m')}...\")\n", "\n", "        # 使用到当前时间点为止的所有数据构建特征\n", "        current_data = full_data[full_data.index <= test_date]\n", "\n", "        # Trend预测\n", "        trend_features_data = create_trend_features(current_data)\n", "        if len(trend_features_data) > 0 and test_date in trend_features_data.index:\n", "            trend_X = trend_features_data.loc[[test_date], trend_features_list]\n", "            trend_X_scaled = trend_scaler.transform(trend_X)\n", "            trend_pred = trend_model.predict(trend_X_scaled)[0]\n", "        else:\n", "            # 如果没有足够的历史数据，使用最近的趋势值\n", "            trend_pred = current_data['trend'].iloc[-1]\n", "\n", "        # Seasonal预测（完全确定性）\n", "        seasonal_pred = seasonal_pattern[test_date.month]\n", "\n", "        # Residual预测\n", "        residual_features_data = create_residual_features(current_data)\n", "        if len(residual_features_data) > 0 and test_date in residual_features_data.index:\n", "            residual_X = residual_features_data.loc[[test_date], available_features]\n", "            residual_pred = residual_model.predict(residual_X)[0]\n", "        else:\n", "            # 如果没有足够的历史数据，预测为0\n", "            residual_pred = 0.0\n", "\n", "        # 最终预测\n", "        final_pred = trend_pred + seasonal_pred + residual_pred\n", "\n", "        # 保存预测结果\n", "        trend_predictions.append(trend_pred)\n", "        seasonal_predictions.append(seasonal_pred)\n", "        residual_predictions.append(residual_pred)\n", "        final_predictions.append(final_pred)\n", "\n", "        # 更新full_data中的预测值，用于下一步预测\n", "        # 注意：这里我们使用真实值而不是预测值来避免误差累积\n", "        # 在实际应用中，如果没有真实值，需要使用预测值\n", "\n", "    return (np.array(trend_predictions), np.array(seasonal_predictions),\n", "            np.array(residual_predictions), np.array(final_predictions))\n", "\n", "print(\"开始逐步预测测试集...\")\n", "\n", "# 执行逐步预测\n", "models = (trend_model, residual_model)\n", "scalers = trend_scaler\n", "\n", "(trend_test_pred_full, seasonal_test_pred_full,\n", " residual_test_pred_full, final_test_pred_full) = predict_step_by_step(\n", "    original_test_data, original_train_data, models, scalers, seasonal_pattern)\n", "\n", "# 训练集预测（使用已有的结果，但需要对齐到原始训练集）\n", "# 找到训练集中有预测结果的部分\n", "train_available_mask = original_train_data.index.isin(trend_train_data.index)\n", "final_train_dates = original_train_data.index[train_available_mask]\n", "final_train_actual = original_train_data['original'][train_available_mask].values\n", "\n", "# 对应的训练集预测\n", "train_trend_aligned = []\n", "train_seasonal_aligned = []\n", "train_residual_aligned = []\n", "\n", "for date in final_train_dates:\n", "    if date in trend_train_data.index:\n", "        idx = trend_train_data.index.get_loc(date)\n", "        train_trend_aligned.append(trend_train_pred[idx])\n", "        train_seasonal_aligned.append(predict_seasonal([date], seasonal_pattern)[0])\n", "\n", "        if date in residual_train_data.index:\n", "            residual_idx = residual_train_data.index.get_loc(date)\n", "            train_residual_aligned.append(residual_train_pred[residual_idx])\n", "        else:\n", "            train_residual_aligned.append(0.0)\n", "\n", "final_train_pred = np.array(train_trend_aligned) + np.array(train_seasonal_aligned) + np.array(train_residual_aligned)\n", "\n", "# 测试集结果\n", "final_test_dates = original_test_data.index\n", "final_test_actual = original_test_data['original'].values\n", "final_test_pred = final_test_pred_full\n", "\n", "print(f\"✓ 逐步预测完成!\")\n", "print(f\"最终训练数据: {len(final_train_pred)} 样本\")\n", "print(f\"最终测试数据: {len(final_test_pred)} 样本 (完整覆盖，无空缺)\")\n", "\n", "# 计算最终指标\n", "def calculate_metrics(y_true, y_pred):\n", "    \"\"\"计算评估指标\"\"\"\n", "    mse = mean_squared_error(y_true, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    mae = mean_absolute_error(y_true, y_pred)\n", "    r2 = r2_score(y_true, y_pred)\n", "    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100\n", "\n", "    # 方向准确性\n", "    if len(y_true) > 1:\n", "        direction_accuracy = np.mean(np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred))) * 100\n", "    else:\n", "        direction_accuracy = 0\n", "\n", "    return {\n", "        'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2,\n", "        'MAPE': mape, 'Direction_Accuracy': direction_accuracy\n", "    }\n", "\n", "final_train_metrics = calculate_metrics(final_train_actual, final_train_pred)\n", "final_test_metrics = calculate_metrics(final_test_actual, final_test_pred)\n", "final_overfitting_ratio = final_test_metrics['RMSE'] / final_train_metrics['RMSE']\n", "\n", "print(f\"🎉 X11融合预测完成!\")\n", "print(f\"\\n训练集性能:\")\n", "print(f\"  RMSE: {final_train_metrics['RMSE']:.2f} 百万美元\")\n", "print(f\"  MAE: {final_train_metrics['MAE']:.2f} 百万美元\")\n", "print(f\"  R²: {final_train_metrics['R2']:.4f}\")\n", "print(f\"  MAPE: {final_train_metrics['MAPE']:.2f}%\")\n", "\n", "print(f\"\\n测试集性能:\")\n", "print(f\"  RMSE: {final_test_metrics['RMSE']:.2f} 百万美元\")\n", "print(f\"  MAE: {final_test_metrics['MAE']:.2f} 百万美元\")\n", "print(f\"  R²: {final_test_metrics['R2']:.4f}\")\n", "print(f\"  MAPE: {final_test_metrics['MAPE']:.2f}%\")\n", "print(f\"  方向准确性: {final_test_metrics['Direction_Accuracy']:.1f}%\")\n", "\n", "print(f\"\\n过拟合分析:\")\n", "print(f\"  过拟合比率: {final_overfitting_ratio:.4f}\")\n", "if final_overfitting_ratio < 1.5:\n", "    print(\"  ✅ 过拟合控制优秀\")\n", "elif final_overfitting_ratio < 2.0:\n", "    print(\"  ✅ 过拟合控制良好\")\n", "else:\n", "    print(\"  ⚠️ 存在过拟合\")\n", "\n", "# 8. 组件贡献分析\n", "print(\"\\n8. 组件贡献分析\")\n", "print(\"-\" * 50)\n", "\n", "# 计算各组件的贡献\n", "trend_contribution = np.var(trend_test_pred_full) / np.var(final_test_actual) * 100\n", "seasonal_contribution = np.var(seasonal_test_pred_full) / np.var(final_test_actual) * 100\n", "residual_contribution = np.var(residual_test_pred_full) / np.var(final_test_actual) * 100\n", "\n", "print(f\"� 各组件方差贡献:\")\n", "print(f\"  Trend贡献: {trend_contribution:.1f}%\")\n", "print(f\"  Seasonal贡献: {seasonal_contribution:.1f}%\")\n", "print(f\"  Residual贡献: {residual_contribution:.1f}%\")\n", "\n", "# 计算各组件的预测精度\n", "trend_accuracy = r2_score(y_trend_test, trend_test_pred)\n", "seasonal_accuracy = r2_score(seasonal_test_actual, seasonal_test_pred)\n", "residual_accuracy = r2_score(y_residual_test, residual_test_pred)\n", "\n", "print(f\"\\n📈 各组件预测精度(R²):\")\n", "print(f\"  Trend预测精度: {trend_accuracy:.4f}\")\n", "print(f\"  Seasonal预测精度: {seasonal_accuracy:.4f}\")\n", "print(f\"  Residual预测精度: {residual_accuracy:.4f}\")\n", "\n", "# 9. 综合可视化分析\n", "print(\"\\n9. 综合可视化分析\")\n", "print(\"-\" * 50)\n", "\n", "fig, axes = plt.subplots(3, 2, figsize=(18, 16))\n", "fig.suptitle('X11分解最终融合预测分析', fontsize=16, fontweight='bold')\n", "\n", "# 1. 最终预测效果 - 完整覆盖，无空缺\n", "ax1 = axes[0, 0]\n", "ax1.plot(final_train_dates, final_train_actual, 'black', label='训练集真实值', linewidth=1, alpha=0.8)\n", "ax1.plot(final_test_dates, final_test_actual, 'black', label='测试集真实值', linewidth=2)\n", "ax1.plot(final_train_dates, final_train_pred, 'blue', label='训练集预测', linewidth=1, alpha=0.7)\n", "ax1.plot(final_test_dates, final_test_pred, 'red', label='测试集预测', linewidth=2)\n", "\n", "ax1.axvline(x=final_train_dates[-1], color='gray', linestyle=':', alpha=0.7, label='训练/测试分割')\n", "ax1.set_title('X11融合最终预测效果 (完整覆盖)')\n", "ax1.set_ylabel('出口额 (百万美元)')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# 添加性能指标\n", "textstr = f'测试集RMSE: {final_test_metrics[\"RMSE\"]:.1f}\\n测试集R²: {final_test_metrics[\"R2\"]:.3f}\\n测试集MAPE: {final_test_metrics[\"MAPE\"]:.1f}%'\n", "props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)\n", "ax1.text(0.02, 0.98, textstr, transform=ax1.transAxes, fontsize=10,\n", "         verticalalignment='top', bbox=props)\n", "\n", "# 2. 组件分解预测对比\n", "ax2 = axes[0, 1]\n", "ax2.plot(final_test_dates, trend_test_pred_full, 'blue', label='Trend预测', linewidth=2)\n", "ax2.plot(final_test_dates, seasonal_test_pred_full, 'green', label='Seasonal预测', linewidth=2)\n", "ax2.plot(final_test_dates, residual_test_pred_full, 'orange', label='Residual预测', linewidth=2)\n", "ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "\n", "ax2.set_title('各组件预测对比')\n", "ax2.set_ylabel('组件值')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# 3. 预测准确性散点图\n", "ax3 = axes[1, 0]\n", "ax3.scatter(final_test_actual, final_test_pred, alpha=0.7, s=60, color='red')\n", "ax3.plot([final_test_actual.min(), final_test_actual.max()],\n", "         [final_test_actual.min(), final_test_actual.max()], 'k--', lw=2)\n", "\n", "ax3.set_xlabel('真实出口额 (百万美元)')\n", "ax3.set_ylabel('预测出口额 (百万美元)')\n", "ax3.set_title(f'预测准确性\\n(R²={final_test_metrics[\"R2\"]:.3f})')\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# 添加拟合线\n", "z = np.polyfit(final_test_actual, final_test_pred, 1)\n", "p = np.poly1d(z)\n", "ax3.plot(final_test_actual, p(final_test_actual), \"b--\", alpha=0.8, linewidth=1)\n", "\n", "# 4. 组件贡献饼图\n", "ax4 = axes[1, 1]\n", "contributions = [trend_contribution, seasonal_contribution, residual_contribution]\n", "labels = ['Trend', 'Seasonal', 'Residual']\n", "colors = ['blue', 'green', 'orange']\n", "\n", "wedges, texts, autotexts = ax4.pie(contributions, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)\n", "ax4.set_title('各组件方差贡献')\n", "\n", "# 5. 预测误差分析\n", "ax5 = axes[2, 0]\n", "test_errors = final_test_actual - final_test_pred\n", "ax5.plot(final_test_dates, test_errors, 'ro-', markersize=4, linewidth=1)\n", "ax5.axhline(y=0, color='black', linestyle='--', alpha=0.7)\n", "ax5.axhline(y=test_errors.std(), color='red', linestyle=':', alpha=0.7, label='+1σ')\n", "ax5.axhline(y=-test_errors.std(), color='red', linestyle=':', alpha=0.7, label='-1σ')\n", "\n", "ax5.set_ylabel('预测误差 (百万美元)')\n", "ax5.set_title('测试集预测误差')\n", "ax5.legend()\n", "ax5.grid(True, alpha=0.3)\n", "\n", "# 6. 各组件预测精度对比\n", "ax6 = axes[2, 1]\n", "components = ['Trend', 'Seasonal', 'Residual', '最终融合']\n", "accuracies = [trend_accuracy, seasonal_accuracy, residual_accuracy, final_test_metrics['R2']]\n", "colors_bar = ['blue', 'green', 'orange', 'red']\n", "\n", "bars = ax6.bar(components, accuracies, color=colors_bar, alpha=0.7)\n", "ax6.set_ylabel('R²')\n", "ax6.set_title('各组件预测精度对比')\n", "ax6.grid(True, alpha=0.3)\n", "\n", "# 添加数值标签\n", "for bar, acc in zip(bars, accuracies):\n", "    ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\n", "             f'{acc:.3f}', ha='center', va='bottom', fontsize=10)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n�🚀 继续生成详细分析...\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "f6958337-b92d-4cd4-bf98-3a82d0e9947d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}