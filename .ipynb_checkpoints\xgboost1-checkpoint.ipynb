{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e1ee55b5-b90c-40a5-82f0-a0ccfa79a819", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MSE: 15137.7168, RMSE: 123.0354, MAE: 87.8667, R2: -0.4473\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import xgboost as xgb\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import matplotlib.pyplot as plt\n", "\n", "# 1. 读取数据并处理\n", "file_path = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "df = pd.read_csv(file_path, parse_dates=['datetime'], encoding='gb18030')\n", "\n", "# 设置 datetime 为索引并排序\n", "df.set_index('datetime', inplace=True)\n", "df.sort_index(inplace=True)\n", "\n", "# 2. 选择变量、删除含空值的末尾行\n", "cols = ['sales price（usd/lb）',\n", "        'Exchange(Domestic currency per US Dollar)',\n", "        'IN MILLION USD']\n", "df = df[cols].copy()\n", "df.dropna(subset=cols, how='any', inplace=True)\n", "\n", "# 3. 提取 numpy 数组\n", "values = df.values.astype(np.float32)  # shape (T, 3)\n", "\n", "# 4. 构建时间序列数据集函数\n", "def create_dataset(data, n_in, n_out):\n", "    X, y = [], []\n", "    for i in range(len(data) - n_in - n_out + 1):\n", "        # 前两列作为特征，作为多变量输入\n", "        X.append(data[i:i+n_in, :-1].flatten())\n", "        # 最后一列作为目标\n", "        y.append(data[i+n_in:i+n_in+n_out, -1][0])\n", "    return np.array(X), np.array(y)\n", "\n", "# 5. 参数设置\n", "n_in = 12       # 输入步长\n", "n_out = 1       # 预测步长\n", "train_ratio = 0.8\n", "\n", "# 划分训练集和测试集\n", "n_train = int(len(values) * train_ratio)\n", "train_vals = values[:n_train]\n", "test_vals  = values[n_train:]\n", "\n", "# 6. 创建训练和测试样本\n", "X_train, y_train = create_dataset(train_vals, n_in, n_out)\n", "X_test,  y_test  = create_dataset(test_vals,  n_in, n_out)\n", "\n", "# 7. 归一化（仅用训练集拟合）\n", "scaler_x = MinMaxScaler().fit(X_train)\n", "scaler_y = MinMaxScaler().fit(y_train.reshape(-1, 1))\n", "\n", "X_train_scaled = scaler_x.transform(X_train)\n", "y_train_scaled = scaler_y.transform(y_train.reshape(-1, 1)).flatten()\n", "X_test_scaled  = scaler_x.transform(X_test)\n", "\n", "# 8. 构建 DMatrix 并训练 XGBoost（无验证集）\n", "dtrain = xgb.DMatrix(X_train_scaled, label=y_train_scaled)\n", "dtest  = xgb.DMatrix(X_test_scaled)\n", "\n", "params = {\n", "    'objective': 'reg:squarederror',\n", "    'learning_rate': 0.01,\n", "    'max_depth': 6,\n", "    'subsample': 1,\n", "    'colsample_bytree': 1,\n", "    'seed': 42\n", "}\n", "\n", "model = xgb.train(params, dtrain, num_boost_round=1000)\n", "\n", "# 9. 预测并反归一化\n", "y_pred_scaled = model.predict(dtest)\n", "y_pred = scaler_y.inverse_transform(y_pred_scaled.reshape(-1, 1)).flatten()\n", "y_true = y_test\n", "\n", "# 10. 评估指标\n", "mse  = mean_squared_error(y_true, y_pred)\n", "rmse = np.sqrt(mse)\n", "mae  = mean_absolute_error(y_true, y_pred)\n", "r2   = r2_score(y_true, y_pred)\n", "\n", "print(f\"MSE: {mse:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}, R2: {r2:.4f}\")\n", "\n", "# 11. 可视化预测结果\n", "plt.figure(figsize=(10, 4))\n", "plt.plot(y_true, label='True')\n", "plt.plot(y_pred, label='Pred')\n", "plt.title('IN MILLION USD: True vs Predicted')\n", "plt.xlabel('Time Step')\n", "plt.ylabel('IN MILLION USD')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "c6057ec3-a489-47eb-93ae-0ebd000eb9fe", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}