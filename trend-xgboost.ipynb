{"cells": [{"cell_type": "code", "execution_count": 3, "id": "52726086-9b66-446e-8f96-3f593b9710ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== XGBoost Trend预测模型 ===\n", "目标: 基于X11分解的Trend进行预测\n", "外生变量: 销售价格 + 汇率(权重放大)\n", "\n", "1. 数据加载\n", "--------------------------------------------------\n", "✓ Trend数据加载完成: (156, 5)\n", "✓ 外生变量数据加载完成: (156, 13)\n", "\n", "Trend数据列: ['parsed_datetime', 'Original', 'Trend', 'Seasonal', 'Residual']\n", "外生变量数据列: ['Index', 'Ethiopia Calendar', 'Unnamed: 2', 'datetime', 'QTY (Ton)', 'IN MILLION USD', 'sales price（usd/lb）', 'ICO Composite Indicator Price(I-CIP)(usd/lb)', 'Exchange(Domestic currency per US Dollar)', 'Production Cost', 'Production', 'Total/year QYT', 'Total/year MILLION USD']\n", "\n", "2. 数据预处理\n", "--------------------------------------------------\n", "✓ 合并后数据量: 0\n", "✓ 时间范围: NaT - NaT\n", "\n", "3. 特征工程\n", "--------------------------------------------------\n", "✓ 创建汇率权重放大特征...\n", "✓ 特征工程完成，最终数据量: 0\n", "\n", "4. 特征选择\n", "--------------------------------------------------\n", "✓ 选择特征数量: 33\n", "✓ 特征矩阵形状: (0, 33)\n", "✓ 目标变量形状: (0,)\n", "\n", "5. 数据分割\n", "--------------------------------------------------\n", "✓ 训练集: 0 样本\n", "✓ 测试集: 0 样本\n"]}, {"ename": "IndexError", "evalue": "single positional indexer is out-of-bounds", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 173\u001b[0m\n\u001b[0;32m    171\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m✓ 训练集: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mX_train\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m 样本\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    172\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m✓ 测试集: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mX_test\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m 样本\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m--> 173\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m✓ 训练期: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdate\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39miloc[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m - \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdate\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39miloc[split_idx\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    174\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m✓ 测试期: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdate\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39miloc[split_idx]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m - \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdate\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39miloc[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    176\u001b[0m \u001b[38;5;66;03m# 6. 特征缩放\u001b[39;00m\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\pandas\\core\\indexing.py:1073\u001b[0m, in \u001b[0;36m_LocationIndexer.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   1070\u001b[0m axis \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxis \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m   1072\u001b[0m maybe_callable \u001b[38;5;241m=\u001b[39m com\u001b[38;5;241m.\u001b[39mapply_if_callable(key, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj)\n\u001b[1;32m-> 1073\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_getitem_axis\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmaybe_callable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\pandas\\core\\indexing.py:1625\u001b[0m, in \u001b[0;36m_iLocIndexer._getitem_axis\u001b[1;34m(self, key, axis)\u001b[0m\n\u001b[0;32m   1622\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCannot index by location index with a non-integer key\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   1624\u001b[0m \u001b[38;5;66;03m# validate the location\u001b[39;00m\n\u001b[1;32m-> 1625\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_integer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1627\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_ixs(key, axis\u001b[38;5;241m=\u001b[39maxis)\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\pandas\\core\\indexing.py:1557\u001b[0m, in \u001b[0;36m_iLocIndexer._validate_integer\u001b[1;34m(self, key, axis)\u001b[0m\n\u001b[0;32m   1555\u001b[0m len_axis \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mobj\u001b[38;5;241m.\u001b[39m_get_axis(axis))\n\u001b[0;32m   1556\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m len_axis \u001b[38;5;129;01mor\u001b[39;00m key \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m-\u001b[39mlen_axis:\n\u001b[1;32m-> 1557\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mIndexError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msingle positional indexer is out-of-bounds\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mIndexError\u001b[0m: single positional indexer is out-of-bounds"]}], "source": ["\"\"\"\n", "XGBoost模型预测Trend - 基于X11分解结果\n", "特点：适当放大汇率影响权重，提升预测准确性\n", "\"\"\"\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split, TimeSeriesSplit\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "from sklearn.ensemble import GradientBoostingRegressor, RandomForestRegressor\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 配置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"=== XGBoost Trend预测模型 ===\")\n", "print(\"目标: 基于X11分解的Trend进行预测\")\n", "print(\"外生变量: 销售价格 + 汇率(权重放大)\")\n", "\n", "# 1. 数据加载\n", "print(\"\\n1. 数据加载\")\n", "print(\"-\" * 50)\n", "\n", "# 加载Trend数据\n", "trend_path = r\"D:\\埃塞俄比亚咖啡\\模型\\x11_decomposition_results.csv\"\n", "trend_data = pd.read_csv(trend_path)\n", "\n", "# 加载外生变量数据\n", "exog_path = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "exog_data = pd.read_csv(exog_path, encoding='gbk')\n", "\n", "print(f\"✓ Trend数据加载完成: {trend_data.shape}\")\n", "print(f\"✓ 外生变量数据加载完成: {exog_data.shape}\")\n", "\n", "# 检查数据结构\n", "print(f\"\\nTrend数据列: {list(trend_data.columns)}\")\n", "print(f\"外生变量数据列: {list(exog_data.columns)}\")\n", "\n", "# 2. 数据预处理\n", "print(\"\\n2. 数据预处理\")\n", "print(\"-\" * 50)\n", "\n", "# 处理日期索引\n", "if 'datetime' in trend_data.columns:\n", "    trend_data['date'] = pd.to_datetime(trend_data['datetime'], errors='coerce')\n", "elif 'date' in trend_data.columns:\n", "    trend_data['date'] = pd.to_datetime(trend_data['date'], errors='coerce')\n", "else:\n", "    # 如果没有日期列，创建一个\n", "    trend_data['date'] = pd.date_range(start='2013-07', periods=len(trend_data), freq='M')\n", "\n", "if 'datetime' in exog_data.columns:\n", "    exog_data['date'] = pd.to_datetime(exog_data['datetime'], errors='coerce')\n", "else:\n", "    exog_data['date'] = pd.date_range(start='2013-07', periods=len(exog_data), freq='M')\n", "\n", "# 合并数据\n", "data = pd.merge(trend_data[['date', 'Trend']], \n", "                exog_data[['date', 'sales price（usd/lb）', 'Exchange(Domestic currency per US Dollar)']], \n", "                on='date', how='inner')\n", "\n", "# 重命名列以便处理\n", "data.columns = ['date', 'trend', 'sales_price', 'exchange_rate']\n", "\n", "# 处理缺失值\n", "data = data.dropna()\n", "print(f\"✓ 合并后数据量: {len(data)}\")\n", "print(f\"✓ 时间范围: {data['date'].min()} - {data['date'].max()}\")\n", "\n", "# 3. 特征工程\n", "print(\"\\n3. 特征工程\")\n", "print(\"-\" * 50)\n", "\n", "# 基础特征\n", "data['year'] = data['date'].dt.year\n", "data['month'] = data['date'].dt.month\n", "data['quarter'] = data['date'].dt.quarter\n", "\n", "# 时间趋势特征\n", "data['time_index'] = range(len(data))\n", "data['time_squared'] = data['time_index'] ** 2\n", "\n", "# 滞后特征\n", "for lag in [1, 2, 3, 6, 12]:\n", "    data[f'trend_lag_{lag}'] = data['trend'].shift(lag)\n", "    data[f'sales_price_lag_{lag}'] = data['sales_price'].shift(lag)\n", "    data[f'exchange_rate_lag_{lag}'] = data['exchange_rate'].shift(lag)\n", "\n", "# 移动平均特征\n", "for window in [3, 6, 12]:\n", "    data[f'trend_ma_{window}'] = data['trend'].rolling(window=window).mean()\n", "    data[f'sales_price_ma_{window}'] = data['sales_price'].rolling(window=window).mean()\n", "    data[f'exchange_rate_ma_{window}'] = data['exchange_rate'].rolling(window=window).mean()\n", "\n", "# 变化率特征\n", "data['sales_price_pct_change'] = data['sales_price'].pct_change()\n", "data['exchange_rate_pct_change'] = data['exchange_rate'].pct_change()\n", "\n", "# 汇率权重放大特征\n", "print(\"✓ 创建汇率权重放大特征...\")\n", "data['exchange_rate_weighted'] = data['exchange_rate'] * 2.0  # 放大2倍权重\n", "data['exchange_rate_squared'] = data['exchange_rate'] ** 2    # 平方项增强非线性\n", "data['exchange_rate_log'] = np.log(data['exchange_rate'])     # 对数变换\n", "\n", "# 汇率与其他变量的交互项\n", "data['exchange_sales_interaction'] = data['exchange_rate'] * data['sales_price']\n", "data['exchange_time_interaction'] = data['exchange_rate'] * data['time_index']\n", "\n", "# 汇率变化的累积效应\n", "data['exchange_rate_cumsum'] = data['exchange_rate_pct_change'].fillna(0).cumsum()\n", "\n", "# 删除包含NaN的行\n", "data = data.dropna()\n", "print(f\"✓ 特征工程完成，最终数据量: {len(data)}\")\n", "\n", "# 4. 特征选择\n", "print(\"\\n4. 特征选择\")\n", "print(\"-\" * 50)\n", "\n", "# 定义特征列\n", "feature_cols = [\n", "    # 基础特征\n", "    'year', 'month', 'quarter', 'time_index', 'time_squared',\n", "    \n", "    # 外生变量（汇率权重放大）\n", "    'sales_price', 'exchange_rate', 'exchange_rate_weighted', \n", "    'exchange_rate_squared', 'exchange_rate_log',\n", "    \n", "    # 滞后特征\n", "    'trend_lag_1', 'trend_lag_2', 'trend_lag_3', 'trend_lag_6', 'trend_lag_12',\n", "    'sales_price_lag_1', 'sales_price_lag_3', 'sales_price_lag_6',\n", "    'exchange_rate_lag_1', 'exchange_rate_lag_3', 'exchange_rate_lag_6',\n", "    \n", "    # 移动平均\n", "    'trend_ma_3', 'trend_ma_6', 'trend_ma_12',\n", "    'sales_price_ma_3', 'sales_price_ma_6',\n", "    'exchange_rate_ma_3', 'exchange_rate_ma_6',\n", "    \n", "    # 变化率\n", "    'sales_price_pct_change', 'exchange_rate_pct_change',\n", "    \n", "    # 交互项和累积效应\n", "    'exchange_sales_interaction', 'exchange_time_interaction',\n", "    'exchange_rate_cumsum'\n", "]\n", "\n", "# 确保所有特征列都存在\n", "feature_cols = [col for col in feature_cols if col in data.columns]\n", "print(f\"✓ 选择特征数量: {len(feature_cols)}\")\n", "\n", "X = data[feature_cols]\n", "y = data['trend']\n", "\n", "print(f\"✓ 特征矩阵形状: {X.shape}\")\n", "print(f\"✓ 目标变量形状: {y.shape}\")\n", "\n", "# 5. 数据分割\n", "print(\"\\n5. 数据分割\")\n", "print(\"-\" * 50)\n", "\n", "# 时间序列分割 - 80/20\n", "split_idx = int(len(data) * 0.8)\n", "X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]\n", "y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]\n", "\n", "print(f\"✓ 训练集: {X_train.shape[0]} 样本\")\n", "print(f\"✓ 测试集: {X_test.shape[0]} 样本\")\n", "print(f\"✓ 训练期: {data['date'].iloc[0]} - {data['date'].iloc[split_idx-1]}\")\n", "print(f\"✓ 测试期: {data['date'].iloc[split_idx]} - {data['date'].iloc[-1]}\")\n", "\n", "# 6. 特征缩放\n", "print(\"\\n6. 特征缩放\")\n", "print(\"-\" * 50)\n", "\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(\"✓ 特征标准化完成\")\n", "\n", "# 7. XGBoost模型训练\n", "print(\"\\n7. XGBoost模型训练\")\n", "print(\"-\" * 50)\n", "\n", "# 模型参数 - 针对时间序列优化\n", "xgb_params = {\n", "    'n_estimators': 1000,\n", "    'max_depth': 6,\n", "    'learning_rate': 0.05,\n", "    'subsample': 0.8,\n", "    'colsample_bytree': 0.8,\n", "    'random_state': 42,\n", "    'n_jobs': -1,\n", "    'early_stopping_rounds': 50,\n", "    'eval_metric': 'rmse'\n", "}\n", "\n", "# 创建模型\n", "model = XGBRegressor(**xgb_params)\n", "\n", "# 训练模型\n", "print(\"✓ 开始训练XGBoost模型...\")\n", "model.fit(\n", "    X_train_scaled, y_train,\n", "    eval_set=[(X_train_scaled, y_train), (X_test_scaled, y_test)],\n", "    verbose=False\n", ")\n", "\n", "print(\"✓ 模型训练完成\")\n", "\n", "# 8. 预测\n", "print(\"\\n8. 模型预测\")\n", "print(\"-\" * 50)\n", "\n", "y_train_pred = model.predict(X_train_scaled)\n", "y_test_pred = model.predict(X_test_scaled)\n", "\n", "print(\"✓ 预测完成\")\n", "\n", "# 9. 模型评估\n", "print(\"\\n9. 模型评估\")\n", "print(\"-\" * 50)\n", "\n", "def calculate_metrics(y_true, y_pred, dataset_name):\n", "    mse = mean_squared_error(y_true, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    mae = mean_absolute_error(y_true, y_pred)\n", "    r2 = r2_score(y_true, y_pred)\n", "    \n", "    # MAPE计算\n", "    mape = np.mean(np.abs((y_true - y_pred) / np.maximum(y_true, 1e-8))) * 100\n", "    \n", "    print(f\"\\n📊 {dataset_name}评估结果:\")\n", "    print(f\"   MSE:  {mse:.4f}\")\n", "    print(f\"   RMSE: {rmse:.4f}\")\n", "    print(f\"   MAE:  {mae:.4f}\")\n", "    print(f\"   MAPE: {mape:.2f}%\")\n", "    print(f\"   R²:   {r2:.4f}\")\n", "    \n", "    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'MAPE': mape, 'R2': r2}\n", "\n", "train_metrics = calculate_metrics(y_train, y_train_pred, \"训练集\")\n", "test_metrics = calculate_metrics(y_test, y_test_pred, \"测试集\")\n", "\n", "# 过拟合检查\n", "overfitting_ratio = test_metrics['MAPE'] / train_metrics['MAPE']\n", "print(f\"\\n🔍 过拟合分析:\")\n", "print(f\"   过拟合比率: {overfitting_ratio:.2f}\")\n", "if overfitting_ratio > 2:\n", "    print(\"   ⚠️ 存在过拟合\")\n", "elif overfitting_ratio > 1.5:\n", "    print(\"   ⚠️ 轻微过拟合\")\n", "else:\n", "    print(\"   ✓ 过拟合控制良好\")\n", "\n", "# 10. 特征重要性分析\n", "print(\"\\n10. 特征重要性分析\")\n", "print(\"-\" * 50)\n", "\n", "# 获取特征重要性\n", "feature_importance = pd.DataFrame({\n", "    'feature': feature_cols,\n", "    'importance': model.feature_importances_\n", "}).sort_values('importance', ascending=False)\n", "\n", "print(\"📊 前10个最重要特征:\")\n", "for i, (_, row) in enumerate(feature_importance.head(10).iterrows()):\n", "    print(f\"   {i+1:2d}. {row['feature']:<25} {row['importance']:.4f}\")\n", "\n", "# 检查汇率相关特征的重要性\n", "exchange_features = [f for f in feature_cols if 'exchange' in f.lower()]\n", "exchange_importance = feature_importance[feature_importance['feature'].isin(exchange_features)]\n", "print(f\"\\n💱 汇率相关特征重要性:\")\n", "for _, row in exchange_importance.iterrows():\n", "    print(f\"   {row['feature']:<30} {row['importance']:.4f}\")\n", "\n", "total_exchange_importance = exchange_importance['importance'].sum()\n", "print(f\"   汇率特征总重要性: {total_exchange_importance:.4f} ({total_exchange_importance*100:.1f}%)\")\n", "\n", "# 11. 结果可视化\n", "print(\"\\n11. 结果可视化\")\n", "print(\"-\" * 50)\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "fig.suptitle('XGBoost Trend预测结果分析', fontsize=16, fontweight='bold')\n", "\n", "# 1. 预测vs实际值\n", "ax1 = axes[0, 0]\n", "train_dates = data['date'].iloc[:split_idx]\n", "test_dates = data['date'].iloc[split_idx:]\n", "\n", "ax1.plot(train_dates, y_train, 'b-', label='训练集真实值', linewidth=2)\n", "ax1.plot(train_dates, y_train_pred, 'b--', label='训练集预测值', linewidth=1, alpha=0.7)\n", "ax1.plot(test_dates, y_test, 'r-', label='测试集真实值', linewidth=2)\n", "ax1.plot(test_dates, y_test_pred, 'g-', label='测试集预测值', linewidth=2)\n", "\n", "ax1.set_title('Trend预测结果')\n", "ax1.set_xlabel('时间')\n", "ax1.set_ylabel('Trend值')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# 2. 预测准确性散点图\n", "ax2 = axes[0, 1]\n", "ax2.scatter(y_test, y_test_pred, alpha=0.6, s=50)\n", "ax2.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "ax2.set_xlabel('真实值')\n", "ax2.set_ylabel('预测值')\n", "ax2.set_title(f'测试集预测准确性 (R²={test_metrics[\"R2\"]:.3f})')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# 3. 特征重要性图\n", "ax3 = axes[1, 0]\n", "top_features = feature_importance.head(15)\n", "bars = ax3.barh(range(len(top_features)), top_features['importance'])\n", "ax3.set_yticks(range(len(top_features)))\n", "ax3.set_yticklabels(top_features['feature'])\n", "ax3.set_xlabel('重要性')\n", "ax3.set_title('前15个特征重要性')\n", "\n", "# 高亮汇率相关特征\n", "for i, feature in enumerate(top_features['feature']):\n", "    if 'exchange' in feature.lower():\n", "        bars[i].set_color('red')\n", "\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# 4. 预测误差分析\n", "ax4 = axes[1, 1]\n", "test_errors = y_test - y_test_pred\n", "ax4.plot(test_dates, test_errors, 'o-', markersize=4, color='red')\n", "ax4.axhline(y=0, color='black', linestyle='--')\n", "ax4.set_title('测试集预测误差')\n", "ax4.set_xlabel('时间')\n", "ax4.set_ylabel('预测误差')\n", "ax4.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 12. 未来预测\n", "print(\"\\n12. 未来预测\")\n", "print(\"-\" * 50)\n", "\n", "# 创建未来6个月的预测\n", "future_periods = 6\n", "last_date = data['date'].iloc[-1]\n", "future_dates = pd.date_range(start=last_date + pd.DateOffset(months=1),\n", "                            periods=future_periods, freq='M')\n", "\n", "print(f\"✓ 生成未来{future_periods}个月预测...\")\n", "\n", "# 简单假设：外生变量保持最后观测值\n", "last_sales_price = data['sales_price'].iloc[-1]\n", "last_exchange_rate = data['exchange_rate'].iloc[-1]\n", "\n", "# 创建未来特征\n", "future_features = []\n", "for i in range(future_periods):\n", "    future_date = future_dates[i]\n", "\n", "    # 基础时间特征\n", "    features = {\n", "        'year': future_date.year,\n", "        'month': future_date.month,\n", "        'quarter': future_date.quarter,\n", "        'time_index': len(data) + i,\n", "        'time_squared': (len(data) + i) ** 2,\n", "\n", "        # 外生变量（假设保持不变）\n", "        'sales_price': last_sales_price,\n", "        'exchange_rate': last_exchange_rate,\n", "        'exchange_rate_weighted': last_exchange_rate * 2.0,\n", "        'exchange_rate_squared': last_exchange_rate ** 2,\n", "        'exchange_rate_log': np.log(last_exchange_rate),\n", "\n", "        # 交互项\n", "        'exchange_sales_interaction': last_exchange_rate * last_sales_price,\n", "        'exchange_time_interaction': last_exchange_rate * (len(data) + i),\n", "\n", "        # 其他特征设为最后观测值或0\n", "        'sales_price_pct_change': 0,\n", "        'exchange_rate_pct_change': 0,\n", "        'exchange_rate_cumsum': data['exchange_rate_cumsum'].iloc[-1]\n", "    }\n", "\n", "    # 滞后特征（使用历史数据）\n", "    for lag in [1, 2, 3, 6, 12]:\n", "        if i < lag:\n", "            # 使用历史数据\n", "            features[f'trend_lag_{lag}'] = data['trend'].iloc[-(lag-i)]\n", "            features[f'sales_price_lag_{lag}'] = data['sales_price'].iloc[-(lag-i)]\n", "            features[f'exchange_rate_lag_{lag}'] = data['exchange_rate'].iloc[-(lag-i)]\n", "        else:\n", "            # 使用预测值（简化处理）\n", "            features[f'trend_lag_{lag}'] = data['trend'].iloc[-1]\n", "            features[f'sales_price_lag_{lag}'] = last_sales_price\n", "            features[f'exchange_rate_lag_{lag}'] = last_exchange_rate\n", "\n", "    # 移动平均（简化处理）\n", "    for window in [3, 6, 12]:\n", "        features[f'trend_ma_{window}'] = data['trend'].tail(window).mean()\n", "        features[f'sales_price_ma_{window}'] = last_sales_price\n", "        features[f'exchange_rate_ma_{window}'] = last_exchange_rate\n", "\n", "    future_features.append(features)\n", "\n", "# 转换为DataFrame\n", "future_df = pd.DataFrame(future_features)\n", "\n", "# 确保特征顺序一致\n", "future_X = future_df[feature_cols].fillna(0)\n", "\n", "# 标准化\n", "future_X_scaled = scaler.transform(future_X)\n", "\n", "# 预测\n", "future_pred = model.predict(future_X_scaled)\n", "\n", "print(f\"\\n🔮 未来{future_periods}个月Trend预测:\")\n", "for i, (date, pred) in enumerate(zip(future_dates, future_pred)):\n", "    print(f\"   {date.strftime('%Y-%m')}: {pred:.2f}\")\n", "\n", "# 13. 模型保存\n", "print(\"\\n13. 模型保存\")\n", "print(\"-\" * 50)\n", "\n", "# 保存模型和预处理器\n", "import joblib\n", "\n", "model_save_path = r\"D:\\埃塞俄比亚咖啡\\xgboost_trend_model.pkl\"\n", "scaler_save_path = r\"D:\\埃塞俄比亚咖啡\\trend_scaler.pkl\"\n", "\n", "joblib.dump(model, model_save_path)\n", "joblib.dump(scaler, scaler_save_path)\n", "\n", "print(f\"✓ 模型已保存: {model_save_path}\")\n", "print(f\"✓ 预处理器已保存: {scaler_save_path}\")\n", "\n", "# 保存预测结果\n", "results_df = pd.DataFrame({\n", "    'date': data['date'],\n", "    'actual_trend': data['trend'],\n", "    'predicted_trend': np.concatenate([y_train_pred, y_test_pred])\n", "})\n", "\n", "# 添加未来预测\n", "future_results = pd.DataFrame({\n", "    'date': future_dates,\n", "    'actual_trend': np.nan,\n", "    'predicted_trend': future_pred\n", "})\n", "\n", "all_results = pd.concat([results_df, future_results], ignore_index=True)\n", "results_save_path = r\"D:\\埃塞俄比亚咖啡\\xgboost_trend_predictions.csv\"\n", "all_results.to_csv(results_save_path, index=False)\n", "\n", "print(f\"✓ 预测结果已保存: {results_save_path}\")\n", "\n", "# 14. 总结报告\n", "print(\"\\n14. XGBoost Trend预测总结报告\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n🎯 模型性能:\")\n", "print(f\"   测试集MAPE: {test_metrics['MAPE']:.2f}%\")\n", "print(f\"   测试集R²: {test_metrics['R2']:.4f}\")\n", "print(f\"   测试集RMSE: {test_metrics['RMSE']:.4f}\")\n", "\n", "print(f\"\\n💱 汇率影响分析:\")\n", "print(f\"   汇率特征总重要性: {total_exchange_importance:.1%}\")\n", "print(f\"   权重放大效果: {'显著' if total_exchange_importance > 0.2 else '一般'}\")\n", "\n", "print(f\"\\n📈 模型特点:\")\n", "print(f\"   ✅ 使用{len(feature_cols)}个特征\")\n", "print(f\"   ✅ 汇率权重适当放大\")\n", "print(f\"   ✅ 包含滞后和交互特征\")\n", "print(f\"   ✅ 时间序列分割验证\")\n", "\n", "print(f\"\\n🚀 实际应用建议:\")\n", "print(f\"   • 定期更新外生变量数据\")\n", "print(f\"   • 监控汇率变化对预测的影响\")\n", "print(f\"   • 每季度重新训练模型\")\n", "print(f\"   • 结合业务知识调整预测\")\n", "\n", "print(f\"\\n✅ XGBoost Trend预测模型完成!\")\n", "print(f\"   模型已保存，可用于实际预测任务\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "781c14e5-1af8-43ef-afb4-438df7c7dd83", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}