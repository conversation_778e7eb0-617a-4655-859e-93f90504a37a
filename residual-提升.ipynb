{"cells": [{"cell_type": "code", "execution_count": 3, "id": "aa1506db-53b2-425e-b5ee-8867f92ae8fa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ XGBoost导入成功\n", "=== 梯度提升改进 vs XGBoost改进 - 防过拟合对比 ===\n", "目标: 对比两种改进模型的防过拟合效果\n", "\n", "1. 数据加载和预处理\n", "--------------------------------------------------\n", "数据量: 156 样本\n", "时间范围: 2012-07-01 00:00:00 - 2025-06-01 00:00:00\n", "\n", "2. 特征工程\n", "--------------------------------------------------\n", "特征工程完成，最终数据量: 150\n", "使用特征数量: 7\n", "特征列表: ['residual_lag_6', 'residual_volatility', 'residual_lag_1', 'seasonal_abs', 'sales_price_lag_1', 'trend_change', 'month']\n", "\n", "3. 数据分割\n", "--------------------------------------------------\n", "训练集: 120 样本 (2013-01-01 00:00:00 - 2022-12-01 00:00:00)\n", "测试集: 30 样本 (2023-01-01 00:00:00 - 2025-06-01 00:00:00)\n", "验证集: 24 样本 (用于XGBoost早停)\n", "\n", "4. 模型定义\n", "--------------------------------------------------\n", "✓ 梯度提升改进模型配置完成\n", "✓ XGBoost改进模型配置完成\n", "\n", "5. 模型训练\n", "--------------------------------------------------\n", "训练梯度提升改进模型...\n", "梯度提升改进结果:\n", "  训练RMSE: 6.2846\n", "  测试RMSE: 38.7448\n", "  测试R²: 0.2059\n", "  测试MAE: 25.7766\n", "  过拟合比率: 6.1650\n", "\n", "训练XGBoost改进模型...\n", "XGBoost改进结果:\n", "  实际停止轮数: 100\n", "  最佳验证RMSE: 15.3530\n", "  训练RMSE: 7.8095\n", "  测试RMSE: 40.9431\n", "  测试R²: 0.1133\n", "  测试MAE: 27.1492\n", "  过拟合比率: 5.2428\n", "\n", "6. 性能对比分析\n", "--------------------------------------------------\n", "模型性能对比:\n", "================================================================================\n", "\n", "梯度提升改进:\n", "  训练RMSE: 6.2846\n", "  测试RMSE: 38.7448\n", "  测试R²: 0.2059\n", "  测试MAE: 25.7766\n", "  相对误差: 82.7%\n", "  方向准确性: 48.3%\n", "  过拟合比率: 6.1650\n", "\n", "XGBoost改进:\n", "  训练RMSE: 7.8095\n", "  测试RMSE: 40.9431\n", "  测试R²: 0.1133\n", "  测试MAE: 27.1492\n", "  相对误差: 87.1%\n", "  方向准确性: 55.2%\n", "  过拟合比率: 5.2428\n", "\n", "7. 综合可视化对比\n", "--------------------------------------------------\n"]}, {"data": {"image/png": "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*******************************************+9e/eGQqHAf//7X3h6emLhwoUAgHfffRerV6+GTqfD7NmzsX//fuzcuVN+7vjx4/HLL78gIiICffr0QcuWLY0SD507d8bzzz+Pf//73/jyyy8BABqNBkOHDoWvry/++OMPubebo6MjZs6cieDgYKxbt65SnJIkoaysDKNHjzZqAZadnS1fmXnmzBmsXbsWEyZMkGeCEBERVUWn0+Hs2bMAgL59+zIZ3gQwqUFERERERGT7mNRoArZt21brOnfccQf+/PNPFBYWYvr06XB3dwegb/90+PBhtG7dGtevX0dJSYmcPAgICMDly5fh6OgoVzBUpXv37ujevTsA4F//+hfmzZsHBwcHAICfnx/eeustAMDHH38MNzc3o4SCj4+P3OPN09Ozyu2/+OKLeOqpp+THXV1dcfDgQfj5+Rmt5+joiEuXLlUbpyAISE5ORlBQULUnnvr164fPP/+82m0QEREZSJKEy5cvAwD69Olj5WjIHMq3n+JMDSIiIiIiItvEpEYz4+bmZnRfoVDI7aAGDx6MwYMHGz1uygG9s7NzlctbtmxZ720ZVEx4VExo1FV1A9SJiIjqS6FQoEePHvJtsn+s1CAiIiIiIrJ9TGoQERERmUChUKBnz57WDoPMiEkNIiIiIiIi28fLComIiIiIwPZTRERERERE9oCVGn+ragA2UWPg7x4RkX2SJAllZWUAAAcHBwiCYOWIqKFYqUFERERERGT7mn2lhmGgtUajsXIk1FwZrgqtbng5ERHZJp1Oh2+//RbffvstdDqdtcMhM2BSg4iIiIiIyPY1+0oNpVIJT09PZGRkAABcXV15pWUNJEmCVquFSqXiz8kMRFFEZmYmXF1doVI1+z9HIiIiqyqf1GD7KSIiIiIiItvEs6gAAgMDAUBObFD1JEmCKIpQKBRMapiJQqFA69at+fMkIrIzSqUSM2bMAAC+hzcR5WdqsFKDiIiIiIjINjGpAf2JiKCgIPj7+8u9salqoigiOzsbPj4+UCiaffcys3B0dOTPkojIDgmCwGRGE1O+UsPQopSIiIiIiIhsC5Ma5SiVSs41qIUoinBwcICzszNPxBMREVGTYkhqODk5MWFFRERERERko5jUICIiIjKBKIqIiYkBAPTq1YvJ/ibA0H6K8zSIiIiIiIhsF4++iYiIiEwgiiIuXLiACxcuQBRFa4dDZlC+UoOIiIiIiIhsEys1iIiIiEygUCjQpUsX+TbZPyY1iIiIiIiIbB+TGkREREQmUCgU6NOnj7XDIDNi+ykiIiIiIiLbx8sKiYiIiIjASg0iIiIiIiJ7wEoNIiIiIhNIkgRJkgAAgiBAEAQrR0QNxaQGERERERGR7WNSg4iIiMgEOp0OO3bsAADcd999UKn4scqeSZLE9lNERERERER2gO2niIiIiKjZ02q1cuUNKzWIiIiIiIhsFy8pJCIiIjKBUqnEPffcI98m+2ZoPQUwqUFERERERGTLmNQgIiIiMoEgCGxT1IQwqUFERERERGQf2H6KiIiIiJo9wzwNgDM1iIiIiIiIbBkrNYiIiIhMIIoizp8/DwDo3r07FApeK2LPWKlBRERERERkH5jUICIiIjKBKIqIi4sDAHTt2pVJDTvHpAYREREREZF9YFKDiIiIyASCIKBDhw7ybbJvbD9FRERERERkH5jUICIiIjKBUqnEgAEDrB0GmQkrNYiIiIiIiOwD+yQQERERUbPHpAYREREREZF9YFKDiIiIiJo9tp8iIiIiIiKyD2w/RURERGQCrVaLb775BgBw7733QqXixyp7xkoNIiIiIiIi+8CjbyIiIiITSZJk7RDITJjUICIiIiIisg9MahARERGZQKlU4q677pJvk30r336KSQ0iIiIiIiLbxaQGERERkQkEQYCrq6u1wyAzKV+pwZkaREREREREtouDwomIiIio2WP7KSIiIiIiIvtgc0mNrVu3QhCESl9bt27F1KlTjZaNHj3a2uESERFRMyWKIhISEpCQkABRFK0dDjUQkxpERERERET2weaSGjNnzkRubq78lZKSAl9fXwwdOhSnT59GbGys/NiuXbusHS4RERE1U6IoIjo6GtHR0UxqNAHlZ2qw/RQREREREZHtsrmZGo6OjkYHkh988AGmTZsGZ2dnSJKEHj16WDE6IiIiIj1BEBAWFibfJvvGSg0iIiIiIiL7YHNJjfKKi4vx3nvv4cSJEzh58iR0Oh1CQ0ORm5uLKVOm4MMPP4SXl5e1wyQiIqJmSKlUIiIiwtphkJkwqUFERERERGQfbDqp8eWXX2LQoEFo27YtvvrqK/Tq1Qtvv/02FAoF5s6di6VLl+Kjjz6q9LySkhKjA1O1Wg1A3yaC7SEaRhRFSJLEn6ON4X6xTdwvtof7xDZxv9im5rZfyn92VKlUNvt9N7f9QkREREREVJFNJzU++ugjLF++HACwdOlSLF26VH7srbfewvTp06tMaqxatQorVqyotDwzMxPFxcUWi7c5EEUR+fn5kCQJCoXNjWRptrhfbBP3i+3hPrFN3C+2qbntl9zcXPm2RqNBRkaGFaOpnmG/NId9QkREREREVBWbTWpcvnwZly9fxpgxY6p83N/fH9nZ2SgpKanUImDp0qVYvHixfF+tVqNVq1bw8/ODh4eHReNu6kRRhCAI8PPz48G0DeF+sU3cL7aH+8Q22et+0Wq12LVrFwDgrrvugkplsx+rTGKv+8VUSqVSvh0YGAh/f38rRlM9w35hiywiIiIiImqubPbo++uvv8bkyZPh4OAAAJgxYwYWLFiAO+64AwBw7NgxBAQEVHlA5+TkVOVyhULRLA7KLU0QBP4sbRD3i23ifrE93Ce2yR73i0KhgFarlW/bU+x1ZY/7xVRlZWXybRcXF5v+ng37hYiIiIiIqDmy2aTG/v378fDDD8v3e/bsiUWLFmHt2rXIysrC0qVL8eSTT1ovQCIiImrWlEolJk+eLN8m+1Z+poajo6MVIyEiIiIiIqKa2GRSo6ioCCdOnMDHH38sL3v++edx9epVjB8/Hu7u7njqqafw4osvWjFKIiIias4EQYC7u7u1wyAzKZ/UYGsnIiIiIiIi22WTSQ0XFxejA0sAcHBwwObNm7F582YrRUVERERETVVpaal8m0mNxpGXl4fExER06tQJXl5e1g6HiIiIiIjsBJvxEhEREZlAFEVcvHgRFy9ehCiK1g6HGojtpxrXjh070LZtW8ydOxehoaHYsWMHACAuLg4DBgyAl5cXlixZAkmSrBwpERERERHZGiY1iIiIiEwgiiLOnDmDM2fOMKnRBLD9VOPJz8/HU089hcOHDyM2NhYbNmzAkiVLUFJSgilTpqBfv344ffo04uPjsXXrVmuHS0RERERENoZJDSIiIiITCIKAVq1aoVWrVhAEwdrhUAMxqdF41Go13n33XYSHhwMA+vbti+zsbOzbtw/5+flYs2YN2rdvj5UrV7L1LBERERFRHZha8Xz06FF07ty50vLw8HAIgiB/zZ07V37sm2++QZs2bRAcHIyvvvrKbN9DfdjkTA0iIiIiW6dUKnHHHXdYOwwyk/IzNRwcHKwYSdPXqlUrzJo1CwBQVlaGtWvXYtq0aYiJiUFERARcXV0B6A+k4uPjq91OSUmJUTKqoKAAgL6KyhrVUwLYKqu50/8OSJAkAAJ/H5o7iVWcdovv5yRAgiRJrMYmq6rP75+h4nncuHHYvn07Fi5ciK1bt2LOnDk1Pu/MmTOYNm0aXFxcjJZrNBokJSUhIyNDPjYyXPgVFxeHWbNmYcOGDRg0aBCmT5+Ovn37VpkYsSQmNYiIiIio2TOcHHdwcIBCwWLmxhATE4NRo0bB0dERCQkJePXVVxEWFiY/LggClEolcnNzqxwkvmrVKqxYsaLS8uzsbKvMRfF3KKl9JWrSBEgoK9PK96h5y8vIsHYIZCK+n5MACXl5eZAkiZ8LyWqys7PrvG75imdXV1esXLkS8+bNqzGpUVhYiOnTp2P+/PmVqqOjoqIQHh4OPz+/Ss/btGkTRo4cKVduzJ8/H5999hlee+21OsdrDkxqEBEREVGzZ0hqsPVU4wkPD8eBAwewaNEizJ07F+3bt6/083d2doZGo6kyqbF06VIsXrxYvp+WloZu3brBx8cH/v7+Fo+/ooyy5EZ/TbItAiQ4OKjg6OgEdiUka7wPkXnw/ZwESPD09ISfnx+TGmQ1hkrygoICqNVqebmTk1Olz8z1rXgG9BdzHT16FJcuXaqU1Dh58iRSU1Ph5+eHsrIyREZG4t1334WTkxNiYmIwYcIEed2BAwfilVdeadD3agomNYiIiIhMoNVqsWfPHgDA5MmToVLxY5U9Mxw0MKnReARBQL9+/bBt2za0b98eq1atQlxcnNE6BQUF1VZdVDygMxzsKRQKq5yAkHhlPgEABAgCIPD3odkTeCLUbvH9nAD95xRrfaYgAiD/7nXr1s1o+csvv4zly5cbLVOr1fWqeAYAR0dHhISE4NKlS5UeS0xMxB133IHly5cjLy8Ps2bNwtq1a/HCCy9Uei0PDw9cv37d1G/TZDz6JiIiIjJRUVGRtUMgMzFUalijbVFz8/vvv2PPnj146623AOh/5oIgoGvXrti4caO83tWrV1FSUgJvb29rhUpEREREZFXx8fEICQmR71d1EZZKpapXxXNtPvroI6P7L730EtatW4cXXnih0msZXqexMd1IREREZAKlUonx48dj/PjxUCqV1g6HGojtpxpPp06d8PHHH+Pjjz9GSkoKXnzxRYwdOxYTJ06EWq3Gli1bAAArV67E6NGj+fdFRERERM2Wu7s7PDw85K+qjle8vb2RmZlptKymiuf68vf3R1paWpWvZc7XqQ8mNYiIiIhMIAgCvLy84OXlBYHN0+0e2081nqCgIHzzzTd477330L17d2g0Gnz66adQqVTYtGkT5s+fD19fX+zatQtvvvmmtcMlIiIiIrJpAwYMwLFjx+T7Da14Hjx4MFJSUuT7x44dQ5s2bap8raioKKNKksbCpAYRERERNXtsP9W4xowZg/Pnz0OtVmPHjh3w8/MDAEydOhVJSUnYtm0bEhISKvUQJiIiIiIiY8OGDau24jkvLw86na5e2+vevTsef/xxnDhxAtu2bcM777yDJ598EgBwzz33YPv27YiNjcWtW7ewbt06jBs3zuzfU204U4OIiIjIBKIo4tq1awCAtm3bcoignWP7KdsRGBiISZMmWTsMIiIiIiK7YKh4joyMxJIlS6BQKHDo0CEAgJeXF6KiotC7d+86b+/tt9/GnDlzMHLkSPj7++Ott97C7NmzAQC9evXC008/jf79+8PZ2RkdO3bEU089ZYHvqmZMahARERGZQBRFnDhxAgDQunVrJjXsmE6ngyiKAJjUICIiIiIi+2OoeD5z5gwiIiLg4+MDAJAkqcbnjRgxQr5Yz8DT0xM7d+6s9jmvv/46Zs2ahbS0NAwfPtwq1e5MahARERGZQBAEBAUFybfJfhmqNAC2nyIiIiIiIvvUmBXP3bp1s2qrWCY1iIiIiEygVCoxYsQIa4dBZlA+qcFKDSIiIiIiItvGPglERERE1KwxqUFERERERGQ/mNQgIiIiomattLRUvs2kBhERERERkW1j+ykiIiIiE2i1Wuzbtw8AMGHCBKhU/FhlrzhTg4iIiIiIyH7w6JuIiIjIRLdu3bJ2CGQGbD9FRERERERkP5jUICIiIjKBUqnE6NGj5dtkv9h+ioiIiIiIyH4wqUFERERkAkEQ4OfnZ+0wyAzYfoqIiIiIiMh+cFA4ERERETVrbD9FRERERERkP1ipQURERGQCURSRmpoKAAgNDYVCwWtF7BXbTxEREREREdkPHn0TERERmUAURfz555/4888/IYqitcOhBmD7KSIiIiIiIvvBSg0iIiIiE5SfqSEIgpWjoYZg+ykiIiIiIiL7waQGERERkQmUSiVGjx5t7TDIDNh+ioiIiIiIyH6w/RQRERERNWus1CAiIiIiIrIfTGoQERERUbPGmRpERERERET2g+2niIiIiEyg0+lw4MABAMDYsWOhVCqtHBGZipUaRERERERE9oNJDSIiIiITSJKEvLw8+TbZL87UICIiIiIish9MahARERGZQKlUYuTIkfJtsl9sP0VERERERGQ/mNQgIiIiMoEgCAgMDLR2GGQGbD9FRERERERkPzgonIiIiIiaNbafIiIiIiIish+s1CAiIiIygSiKSE9PBwAEBQVBoeC1IvaK7aeIiIiIiIjsB4++iYiIiEwgiiIOHz6Mw4cPQxRFa4dDDcD2U0RERERERPaDlRpEREREJhAEAd7e3vJtsl9sP0VERERERGQ/mNQgIiIiMoFSqcS4ceOsHQaZASs1iIiIiIiI7AfbTxERERFRs8aZGkRERERERPaDSQ0iIiIiatbYfoqIiIiIiMh+sP0UERERkQl0Oh0OHjwIABg1ahSUSqWVIyJTsf0UERERERGR/WBSg4iIiMgEkiQhKytLvk32i+2niIiIiIiI7AeTGkREREQmUCgUGDp0qHyb7BcrNYiIiIiIiOwHkxpEREREJlAoFAgNDbV2GGQGhpkaKpWKCSoiIiIiIiIbx6M2IiIiImrWDJUabD1FRERERERk+1ipQURERGQCSZKQkZEBAPD394cgCFaOiExlSGqw9RQREREREZHtY6UGERERkQl0Oh0OHjyIgwcPQqfTWTscagBD+ykmNYiIiIiIiGwfKzWIiIiITOTh4WHtEMgMWKlBRERERERkP5jUICIiIjKBSqXCpEmTrB0GmQFnahAREREREdkPtp8iIiIiomaN7aeIiIiIiIjsB5MaRERERNRsSZKE4uJiAExqEBERERER2QO2nyIiIiIygU6nw++//w4AGD58OJRKpZUjIlMUFxfLg97d3d2tHA0RERERERHVhkkNIiIiIhNIkoSbN2/Kt8k+qdVq+TYHvxMREREREdk+JjWIiIiITKBQKDB48GD5NtknJjWIiIiIiIjsC5MaRERERCZQKBRo27attcOgBmJSg4iIiIiIyL7Y3GWFCxcuhCAI8leHDh0AAHFxcRgwYAC8vLywZMkStnkgIiIiogZjUoOIiIiIiMi+2FxS4/Tp09i7dy9yc3ORm5uLqKgolJSUYMqUKejXrx9Onz6N+Ph4bN261dqhEhERUTMmSRKys7ORnZ3Niy3sWEFBgXybSQ0iIiIiIiLbZ1NJDa1Wi/Pnz2PYsGHw9PSEp6cn3N3dsW/fPuTn52PNmjVo3749Vq5cic2bN1s7XCIiImrGdDodDhw4gAMHDkCn01k7HDIRKzWIiIiIiIjsi03N1IiNjYUoiujduzfS0tIwfPhwfPzxx4iJiUFERARcXV0BAOHh4YiPj692OyUlJSgpKZHvGw5WRVGEKIqW/SaaOFEUIUkSf442hvvFNnG/2B7uE9tkr/tFFEX5s0lT/Ixhr/ulvvLz8+XbLVq0sPnvt7nsFyIiIiIiourYVFIjPj4enTt3xvr16+Hr64tFixbhscceQ/fu3REWFiavJwgClEolcnNz4eXlVWk7q1atwooVKyotz8zMRHFxsUW/h6ZOFEXk5+dDkiQoFDZV6NOscb/YJu4X28N9Ypvseb8MGjQIAJCTk2PlSMzPnvdLfVy/fl2+LYoiMjIyrBhN7Qz7xZ73ya5du7Bo0SIkJyejR48e+Oqrr9C1a1csXLgQ69evl9dr3749Ll++bMVIiYiIiIjIFtlUUmPWrFmYNWuWfP+DDz5AWFgYunbtCicnJ6N1nZ2dodFoqkxqLF26FIsXL5bvq9VqtGrVCn5+fmwr0ECiKEIQBPj5+dn1wXRTw/1im7hfbA/3iW3ifrFNzWW/lK94aNWqFfz9/a0YTe0M+6XiZ2N7kZSUhDlz5uCjjz7C8OHDsWDBAsydOxd//vmnPFtvyJAhAAClUmnlaImIiIiIyBbZVFKjIn9/f4iiiMDAQMTFxRk9VlBQAEdHxyqf5+TkVOWBnkKhaNIH5Y1FEAT+LG0Q94tt4n6xPdwnton7xTY1h/1SflC4p6enXXyvhv1ijxISEvDGG2/g/vvvBwA8+eSTmDRpktFsvRYtWlg5SiIiIiIi+xIXF4c5c+bg8uXLmDt3LlavXg1BEGp93tGjRzFnzhwkJiYaLV+xYgXee+89FBYWYuLEifj000/h7u4OQD8aIjY2Vl73kUcewaZNm8z7DdXCpo6GlixZgi+//FK+f+zYMSgUCvTs2RPHjh2Tl1+9ehUlJSXw9va2RphERERE0Ol0OHz4MA4fPsxB4XaMg8Ib1+TJk/HYY4/J9xMTE9GxY0ej2XouLi4YP348kpOTrRgpEREREZF9KCkpwZQpU9CvXz+cPn0a8fHx2Lp1a63PO3PmDKZNm2Y0mxoAvvjiC3zxxRfYv38/zp8/L1+YBAAajQZJSUnIyMhAbm4ucnNzjVrINhabqtTo1asXli1bhoCAAOh0OixYsAAPPfQQxo4dC7VajS1btmDOnDlYuXIlRo8ezZJ0IiIishpJkpCWlibfJvvEpIb1lJaW4p133sHixYurna23f//+ap9fUlJidABmqLoRRdEqg9QF8H2gudP/DkiQJAACfx+aO8kK70NkHnw/JwESJEmyyucJIoP6/P7t27cP+fn5WLNmDVxdXbFy5UrMmzcPc+bMqfY5hYWFmD59OubPn4/NmzcbPZaSkoJt27Zh4MCBAIAZM2bg1KlTAICoqCiEh4fDz8/PhO/KfGwqqfHggw/i/PnzuOeee6BUKvHggw9i5cqVUKlU2LRpEyIjI7FkyRIoFAocOnTI2uESERFRM6ZQKDBgwAD5NtknJjWs5+WXX4abmxvmzp0LBweHKmfrqdXqavfLqlWrsGLFikrLs7Ozq21Ta0n+DiW1r0RNmgAJZWVa+R41b3kZGdYOgUzE93MSICEvLw+SJPFzPllNdnY2AP2FO+WPWaoauxATE4OIiAi4uroC0LeHio+Pr3H7Dg4OOHr0KC5dulQpqfHCCy8Y3TdUVwPAyZMnkZqaCj8/P5SVlSEyMhLvvvtuo8/8s6mkBqA/OFm1alWl5VOnTkVSUhLOnDmDiIgI+Pj4WCE6IiIiIj2FQoEOHTpYOwxqIMMBglKphIuLi5WjaT4OHjyIDRs24Pjx43BwcKj0uGG2Xnp6erVJjaVLl2Lx4sXy/bS0NHTr1g0+Pj5WGfieUcZ2Wc2dAAkODio4OjqhDi2sqYmzxvsQmQffz0mABE9PT/j5+TGpQVZTWloKAOjWrZvR8pdffhnLly83WqZWqxEWFibfFwQBSqUSubm58PLyqnL7jo6OCAkJwaVLl2qM4+LFi9i5cyfOnj0LQJ/guOOOO7B8+XLk5eVh1qxZWLt2baVEiKXZXFKjJoGBgZg0aZK1wyAiIiKiJsKQ1PDw8KjTID1quKtXryIyMhIbNmyQD9KWLFmCPn36YObMmQBuz9Zr1apVtdupeJWaYV9aa7i9xCvzCQAgQBAAgb8PzZ7AE6F2i+/nBOhPClvrMwURcLsbQHx8PEJCQuTlVVVEqFSqSsudnZ2h0WiqTWrUhSiK+Oc//4m5c+eie/fuAICPPvrIaJ2XXnoJ69atY1KDiIiIyB5IksQT4k1A+X1IlldUVITJkyfjrrvuwrRp03Dr1i0A+hL5qmbrGUroiYiIiIiaI3d391qPVby9vREXF2e0rKCgoMEtWV999VXk5OTgrbfeqnYdf39/edZkY2K6kYiIiJqc7OxsPP/880ZDhM1Np9Phxx9/xI8//gidTmex1yHLYlKjcR04cADx8fHYuHEj3N3d5a+hQ4dixowZuOeeexAZGYnx48fj/ffft3a4REREREQ2b8CAATh27Jh8/+rVqygpKYG3t7fJ2/zhhx+wZs0afPvtt0YXGg0ePBgpKSny/WPHjqFNmzYmv46pmNQgIiKiJichIQG//vor/vrrL4u+TlVD2sh+aLVaFBUVAWBSo7HcddddkCSp0lfbtm2xatUq5OXlITs7G++99x7c3NysHS4RERERkc0bNmwY1Go1tmzZAgBYuXIlRo8eDaVSiby8vHpfhJeQkIDIyEisX78erVq1wq1bt6DRaAAA3bt3x+OPP44TJ05g27ZteOedd/Dkk0+a/XuqDdtPERERUZNjOFGdnZ1tsddQqVSYPn26xbZPlldQUCDfdnd3t2IkREREREREplGpVNi0aRMiIyOxZMkSKBQKHDp0CADg5eWFqKgo9O7du87b+/jjj1FYWIjZs2dj9uzZAIA2bdrg2rVrePvttzFnzhyMHDkS/v7+eOutt+R1GhOTGkRERNTkGJIaOTk5Vo6EbJmh9RTASg0iIiIiIrJfU6dORVJSEs6cOYOIiAj4+PgA0M+CrMmIESNw7do1o2Vr167F2rVrq1zf09MTO3fuNEvMDcGkBhERETU5jVGpQfaPSQ0iIiIiImoqAgMDMWnSJGuH0SjqPFPjo48+giiKNa5TWlqKDh06NDgoIiIiooZojKSGTqfD0aNHcfToUQ4Kt1NMahAREREREdmfOldqrFy5Eo8++ii+++47qNVqKBSV8yGSJEGr1Zo1QCIiIqL6Mgwxs2RSQ5IkeRD5wIEDLfY6ZDlMahAREREREdmfOic1VCoVlEolVq1ahf79+2P79u144IEHsGPHDtx3333yv1UlO4iIiIgaU3FxMQDLztRQKBTo06ePfJvsD5MaRERERERE9qdOR+CGEwMAIAgCPvzwQ/j6+uLDDz9EUFCQ0b+1DR8hIiIisjRD+6msrCyLvYZCoUCXLl3QpUsXJjXsFJMaRERERETmkZycjNOnT6O0tNTaoVAzUOsReGFhIby9vZGamoohQ4bg0qVLAPTJjar+JSIiIrI2Q/spS1ZqkP1jUoOIiIiImqq4uDgMGDAAXl5eWLJkSZ0vRD969Cg6d+5cr8cWL16Mvn37YubMmQgLC8OFCxcaFDtRbWpNajg6OmL37t3w9fXFvHnz4Onp2QhhEREREZnOUKmRl5dnsXlfkiShsLAQhYWFrFS1U0xqEBEREVFTVFJSgilTpqBfv344ffo04uPjsXXr1lqfd+bMGUybNg0lJSV1fuzQoUPYs2cPrly5gosXL2Ls2LF44403zPWtEFWp1qSGg4MDRo8eDWdnZ8yaNQu+vr74+OOPoVar8fHHHyM3N9foX1ZsEBERkbUVFxfDxcUFAJCbm2uR19DpdNi9ezd2794NnU5nkdcgy2JSg4iIiIiaon379iE/Px9r1qxB+/btsXLlSmzevLnG5xQWFmL69OmYP39+vR5zcnLCxo0b5c/Tffr0QXZ2tnm+EaJq1LkBtFarhSiKGDt2LP78809MmDABx44dw+jRo43+5ZWKREREZG1FRUUIDQ0FYNkWVEqlEkql0mLbJ8tiUoOIiIiImqKYmBhERETA1dUVABAeHo74+Pgan+Pg4ICjR49i6NCh9Xps8ODBGD58OAD9TMNPPvkE06ZNM8N3QVQ9VV1W+uGHH/DKK68AAF5//fVq19NqtWjVqpV5IiMiIiIykUajQevWrXHp0iWLXSWkUqlw//33W2Tb1DgKCgrk2+7u7laMhIiIiIjIfNRqNcLCwuT7giBAqVQiNzcXXl5eVT7H0dERISEh8jzluj5msHHjRjz99NMYNmwY/vnPfzb8myCqQa1JjdzcXMyePRsBAQHQaDQIDAysdt3S0lI88cQTZg2QiIiIqL6KiooQHBwMACx9pmqxUoOIiIiImiKVSgUnJyejZc7OztBoNNUmNRrqoYceQlBQEJ588km8//77VbaqIjKXWpMaXl5eSE9Px65du/D6668jLi4OAQEB6N+/f6VWUzqdDmVlZRYLloiIiKguiouL4eHhAQ8PDyY1qFrlkxqs1CAiIiKipsLb2xtxcXFGywoKCuDo6Gix13RycsLkyZORmZmJdevWMalBFlWn9lNOTk64//77cf/992Pz5s149tln4enpiY8++kjuzUZERERkKzQaDVxcXODj42OxmRo6nQ6nT58GAPTv35+zNeyQIanh5ubG/UdERERETcaAAQOwceNG+f7Vq1dRUlICb29vs7/We++9Bz8/P8ycOROAvlUVP1uTpdV5ULjBI488gujoaPTr148JDSIiIrI5oiiiuLgYLi4u8Pb2RlZWlkVeR5IkXLlyBVeuXKlUvUr2wZDUYOspIiIiImpKhg0bBrVajS1btgAAVq5cidGjR0OpVCIvLw86nc5sr9WuXTs888wz+O2335CYmIi33noL9913n9m2T1SVOlVqVNSmTRs8/fTT5o6FiIiIqMFKSkoAwOKVGgqFAuHh4fJtsj9MahARERFRU6RSqbBp0yZERkZiyZIlUCgUOHToEAD9qIGoqCj07t3bLK81ZcoUvPDCC5g1axbKysowd+5cLFmyxCzbJqqOSUkNIiIiIltVVFQEAHB1dYWPjw8uXbpkkddRKBTo3r27RbZNlidJEpMaRERERNRkTZ06FUlJSThz5gwiIiLg4+MDALVWmY8YMQLXrl2r12OLFy/G4sWLGxoyUZ0xqUFERERNiiGp4ezsDB8fHw4KpyoVFhbKB3RMahARERFRUxQYGIhJkyZZOwwis6tzUmPkyJEQBKHW9Q4ePNiggIiIiIgawpDUMMzUUKvVKCsrg4ODg1lfR5IkudWVk5NTnT4nke0wVGkATGoQERERERHZkzonNR5++GELhkFERERkHhqNBoC+/ZSvry8AIDc3F/7+/mZ9HZ1Oh507dwIA7rvvPqhULIC1J0xqEBERERER2ac6H33Pnj3bknEQERERmUVxcTGA25UaAJCdnW32pAbZt/JJDXd3dytGQkRERERERPXBSwqJiIioSSk/U8PQcsoSczVUKhUiIyPNvl1qHKzUICIiIiIisk9MahAREVGTYkhquLq6yi2hcnJyrBkS2aCCggL5NpMa5pGRAbAgioiIiJoCz5iHIDg5AuDcvGZtxA/WjoCqYVJSQ6PR4IMPPkBiYiJ0Oh0A/bDM6OhoREVFmTVAIiIiovrQaDQQBEEe3u3p6YmsrCxrh0U2hpUa9RMeDhw7Bri56e+vXg088QRg+NHl5wNBQcDfhwZEREREREQWozDlSQ899BD27NmD+Ph4JCUlwc/PD99++y1Gjhxp7viIiIiI6qW4uBjOzs4QBP1VVd7e3hap1NDpdDhz5gzOnDkjX+RB9oNJjfqJizNOWLzxBpCXZ7yOJDVqSERERERE1EyZVKnx888/Iz4+HhcuXMAbb7yBN998E7169cK2bdvMHR8RERFRvRQVFcHFxUW+7+PjY5GZGpIk4eLFiwCAXr16mX37ZFlMajRMVQkMgd0ZiIiIiIioEZiU1PD09MSFCxcQERGBmJgYiKKIkSNH4oknnjB3fERERET1otFoKiU1MjIyzP46CoUC3bp1k2+TfWFSo34EwThpUfE+ERERERFRlQqT676uoAJcg2tdzaSkxrJlyzBhwgTcuHED//jHPzB27FiIoogePXqYsjkiIiIis6lYqeHt7Y2EhASzv45CoWCFhh1jUqN+JAm44w5AqdTfV6uBCRMAR0f9fa3WerEREREREZEN+30yEP4agJr61Qr6x8+vBMadqHWTJiU1Hn30UfzjH/+Am5sbtmzZgnfffRe3bt3CM888Y8rmiIiIiMymuLjYKKnh6+trkZkaZN+Y1KifLVusHQEREREREdklRx8gdGrd1r3wbp1WMympAUBut+Dk5ITly5ebuhkiIiIis6rYfsrb2xsFBQUoLS2Fo+GycjOQJEkeEK5UKuXB5GQfmNSon9mzq16emqofGN6lC6Ay+ciCiIiIiIiarPocK9dxXZMOPcLCwqo9cL9y5YopmyQiIiIyi6oGhQNATk4OAgMDzfY6Op0OO3bsAADcd999UPGMrl1hUqN+0tOBBQsAnQ7YuVO/7Pnngbff1iczgoOB3buBnj2tGycRERERETV9Jh19b926Vb6t0Whw+vRpfPzxx3jppZfMFRcRERGRSYqKiuDv7y/ft1RSg+ybIanh4OAAJycnK0dj+2bPBlq0AF5+WX//88+B998HDh4Ehg3TL58/H/j9d+vGSURERERETZ9JSY3hw4cb3Z8wYQIiIyPxyCOP4NFHHzVLYERERESmKC4uhqurq3zfkNTIysoy6+solUrcd9998m2yL4akBqs06ub4cSAhAQgJ0VdrvPSSvlLDcFjwxBPAu+9aNUQiIiIiImomFObaUNu2bZGWlmauzRERERGZRKPRwNnZWb7v6ekJQRDMPixcEASoVCqoVCrO07BDTGrUT6dOwK+/6m+/8QaQmwssWnT78SNHgKAg68RGRERERETNi0mVGnPmzDE6eBdFEWfOnEGHDh3MFhgRERGRKSrO1FAqlfDy8kJ2drYVoyJbw6RG/bz/PnD33cDjjwMKBfDpp4C7O3DlCjB3LnDsGLBli7WjJCIiIiKi5sCkpEbbtm2N7guCgKFDhyIyMtIcMRERERGZrKioyKj9FAB4e3ubPakhiiJiY2MBAD179oRCYbYCWLKwkpISlJaWAmBSo64iIoCrV/UtqFq1Avz89MtdXICpU4F164AePawbIxERERERNQ8mJTVeNkwIJCIiIrIxFSs1AP1cDUskNeLj4wEA3bt3Z1LDjhQUFMi3mdSom08/vX07Ls74MW9v4OxZ/ddDDzVuXEREREREZOMkyezr1jmpMXLkyDr1iz548GBdN0lERERkVjqdDmVlZUYzNQB9UiM9Pd2sryUIAjp16iTfJvthaD0FMKlRVw8/DAgCoFQC/frpKzQMDMcdgsCkBhERERERVRA8HrjyKQAJgOHY2ZC8EMrdBtDqnjptss5JjYcffli+HRUVhc8//xyPPPII2rdvj+TkZGzcuBF33313XTdHREREZHZFRUUAUKn9lI+PD+IqXl7eQEqlEv369TPrNqlxMKlRf++/D8TH66s0LlwAPD2B8HCgVy/9v+HhQJs21o6SiIiIiIhsTrfn9f+KOuDWZUCrATy6ACrjDgu4cRAIHFWnTdY5qTF79mz59qpVq7Bnzx5ERETIy6ZMmYI5c+bUdXNEREREZqfRaACgUvspS8zUIPvFpEb9PfWU8f30dOCHH4CvvwZeekm/zMMDyM1t/NiIiIiIiMjG5UYDh6cDmhR9ibeDBzD4MyB4gj6ZEbscUMcD92TVaXMmzdTIzc2FTqczWiaKIk8WEBERkVUVFxcDqJzU8PHxQWFhIUpKSuDk5GSN0MiGMKlRf3v36is1zp/Xf127BoSF6Ss01qy5XbVBRERERERUyemFQMBIoP96QOEIXFwPHJ8DuHcC8mKAzk8DI/bUeXMmJTXmz5+Pu+66CzNnzkTr1q1x/fp1fPHFF3j88cdN2RwRERGRWRjaT1WV1ACA7OxsBAcHm+W1tFotduzYAQC47777oFKZ9LGKrIBJjfqbMkV/QZVCAfTvD0yYoJ+vIYpATIz+67PPgE8+sXakRERERERkc3LOAEM+A1R/t4rusgiIWQZ49wWG7QScfOq1OZOOvv/zn/+gV69e+P777/Hrr7/C398fH3zwAe65p26DPIiIiIgsoab2U4B5kxpkv8onNdzd3a0Yif14+WWgrAy4fBkoKgKCgoAKo2vw66/WiY2IiIiIiGycWAw4ehsvUzoBXRbXO6EBmJjUAICpU6di6tSppj6diIiIyOyqq9Tw9fUFAGRl1a0/Z10olUpMmzZNvk32g5Ua9XfXXcC0aUBKir5a488/gU8/BSZO1CczVqzQt6ciIiIiIiKqRJKAn+8AhHLHzmVq4NBEfTsqgwln67Q59kkgIiKiJqO6mRqenp5wdnZGenq62V5LEAQ4OzubbXvUeJjUqL+nnwbuvBNYtw5wctL/+89/Ap06AefO6R/fU/cWuERERERE1JxEbDHr5pjUICIioiajukoNQRAQHByM1NRUa4RFNoZJjfo7fVpfmWFoObVoEbBsGdC3L7BzJ+BT/4pxIiIiIiJqLtrNNuvm6pzUGDVqFPbs2QNXV1eMHDkSgiBUud7BgwfNFhwRERFRfWg0GiiVSjg4OFR6LDQ0FGlpaWZ7LVEUkZCQAADo2rUrFAqF2bZNlsWkRv0VFwPeFVrgOjkBixczoUFERERERI2rzkmN2bNnw9FR39/q4YcftlQ82LVrFxYtWoTk5GT06NEDX331Fbp27YqFCxdi/fr18nrt27fH5cuXLRYHERER2Z+ioqJKVRoGISEhOH78uNleSxRFnDt3DgDQuXNnJjXsSEFBgXybSY26kSTgjjuA8uNj1Gr9TA3Hci1wz9ahBW51n/fj4uIwZ84cXL58GXPnzsXq1aurvZCKiIiIiIiar3olNaq6bU5JSUmYM2cOPvroIwwfPhwLFizA3Llz8eeff+L06dPYu3cvhgwZAoADOYmIiKiy4uJiuBr641QQHByMtLQ0iKJolgSEIAho166dfJvsBys16m+LmVrgVvd5/+DBg5gyZQrGjRuH7du3Y+HChdi6dSvmzJljnhcmIiIiImrCTL1A6OjRo5gzZw4SExONln/zzTf417/+hbKyMrzzzjuIjIyUH9uwYQNeeeUVuLm5YdOmTRg1apTZv5/a2NRMjYSEBLzxxhu4//77AQBPPvkkJk2aBK1Wi/Pnz2PYsGFo0aKFlaMkIiIiW6XRaKod3h0SEoLS0lJkZ2fDz8+vwa+lVCoxaNCgBm+nuUtISMClS5eQnJyM5ORkpKWloX///njmmWcsliwyJDUEQYCbm5tFXqOpMdc1TdV93t+3bx/y8/OxZs0auLq6YuXKlZg3bx6TGkREREREtSgpKTHpAqEzZ85g2rRplbodxMXFYdasWdiwYQMGDRqE6dOno2/fvujcuTN++uknPPvss9i+fTv8/Pzw4IMP4tSpU/Bp5J60NpXUmDx5stH9xMREdOzYEbGxsRBFEb1790ZaWhqGDx+Ojz/+GK1bt7ZSpERERGSLamo/FRoaCgC4fv26WZIa1HCLFy/G2rVrKy3/8ssvceeddyI8PNwir2tIari7u7NtWCOr7vN+TEwMIiIi5Eqr8PBwxMfH17itkpISlJSUyPcNbcVEUYQoimaOvHYCpEZ/TbIt+t8BCZIEQODvQ3MnWeF9iMyD7+fE93MysOZ7eX0+z5pygVBhYSGmT5+O+fPnY/PmzUaPbdq0CSNHjsTcuXMBAPPnz8dnn32G1157DR9++CFmz56Nu+66CwBw1113YefOnfK6jcWkpEZ2djbWrFmDV155BcnJyVi0aBG0Wi3eeustdO3a1SyBlZaW4p133sHixYsRHx+Pzp07Y/369fD19cWiRYvw2GOPYf/+/VU+t+IBjuHA1VoHOE2JKIqQJIk/RxvD/WKbuF9sD/eJbTLnftFoNHBxcalyW4GBgQCAlJQU9OzZs8Gv1dRZ+u9FkiR88skn1T6empqKHj16WOS1b926BQBo0aKF3b0fNKX3sfKf9y9fvoywsDD5MUEQoFQqkZubCy8vryqfv2rVKqxYsaLS8uzsbHkWYGPydyipfSVq0gRIKCvTyveoecvLyLB2CGQivp8T38/JwJrv5dnZ2XVe15QLhBwcHHD06FFcunSpUlIjJiYGEyZMkO8PHDgQr7zyivzYzJkzjR47fPiwfSQ1Zs2aBQcHBwiCgIULF8rlJY888giOHj1qlsBefvlluLm5Ye7cuXBwcMCsWbPkxz744AOEhYVBrVZX2Qe5ugOczMxMFBcXmyW+5koUReTn50OSJF7ZaEO4X2wT94vt4T6xTebcL4YPfhnVfPh0c3NDQkIC+vfv36DXAQCtVouDBw8CAEaNGgWVyqYKYBvM0n8vKSkpyM/PBwB07doVjz32GE6dOoUvv/wSgL6iprr92FCGz4NKpdJir2Ephv3SFN7Dyn/eX7ZsGZycnIwed3Z2hkajqTapsXTpUixevFi+n5aWhm7dusHHxwf+/v4Wjb0qGWXJjf6aZFsESHBwUMHR0QkctUTWeB8i8+D7OfH9nAys+V5eWloKQF+NXH4moJOTU6XPzWq1ut4XCDk6OiIkJASXLl2q9FjF7Xl4eOD69eu1PtaYTDr6/uOPPxAfHw+tVos//vgDN2/eRFZWFjp27GiWoA4ePIgNGzbg+PHjcHBwqPS4v78/RFFEenp6lUmNigc4arUarVq1gp+fH4dBNpAoihAEAX5+fk3iYLqp4H6xTdwvtof7xDaZc78IggAvL69qP3y2a9cOBQUFZvlwqtVq5USGv79/k0xqWPLv5dixY/Ltu+++GwsXLsR///tfOakhCILFDiK0Wv2Vdy4uLnZ30smwXyoeyNibip/3vb29ERcXZ7ROQUFBjRUXFQ/oDAd7CoXCKu/xEq/kJACAAEEABP4+NHsCP2vaLb6fkx7fz8m67+WGz7PdunUzWv7yyy9j+fLlRstUKlW9LxCqScXtGbZV22ONyaSjb39/f5w4cQIlJSXo0aMHHB0dERsbi4CAgAYHdPXqVURGRmLDhg3yTluyZAn69Okjl7YcO3YMCoUCrVq1qnIbVWWsAOsd4DQ1giDwZ2mDuF9sE/eL7eE+sU3m2i/FxcU1noQPDQ1Fenq6Wfa/g4OD3EfUUMHa1Fjy7yU2Nla+3bt3bygUCqOLTzQajcX+TsvKygDo95s9vhcY9ou9qurz/oABA7Bx40ajdUpKSuDt7W2tMImIiIiIrC4+Ph4hISHy/arOeZtygVBNvL29kZmZWeW2anqsMZl0NPT666/jwQcfxBNPPIHnn38eJ0+exLRp0/Dcc881KJiioiJMnjwZd911F6ZNm4Zbt27h1q1bCA8Px7Jly/Drr7/iwIEDeOKJJ/DQQw/JfcKIiIiIgJoHhQNAcHAw0tLSzPJagiDAzc0Nbm5uTTKhYWkxMTHy7V69egHQz7gwMMy9sARDKbc1Pnw3d9V93h86dCjUajW2bNkCAFi5ciVGjx4NpVJp5YiJiIiIiKzH3d0dHh4e8ldVSY0BAwYYVcI39AKhituLioqSEys1PdaYTEpqREZGIjs7G1lZWZg8eTI6duyIqKgoPPHEEw0K5sCBA4iPj8fGjRvh7u4ufw0dOhQzZszAPffcg8jISIwfPx7vv/9+g16LiIiImp7akhohISHIyMiQT2qTZanV6mqTE4akhouLCzp06ACgcZIakiQZVWpQ46ru835aWho2bdqE+fPnw9fXF7t27cKbb75p7XCJiIiIiGzesGHDqr1AKC8vDzqdrl7bu+eee7B9+3bExsbi1q1bWLduHcaNGwcAuPfee/HBBx8gLS0NN2/exObNm+XHGpPJzZ8NB52lpaXw9PREy5YtGxzMXXfdBUmSqnxs1apVWLVqVYNfg4iIiJqu2pIaoaGhAID09HS0adOmQa8liiIuXrwIAOjUqZPF2gFptVpcv34drVu3tsj2LSUlJQU9e/ZEaWkpzpw5g65du8qPFRQUICkpCQDQs2dP+Wr8xkhqGOZpAKzUsIaaPu+3bdsWSUlJOHPmDCIiIuDj49PI0RERERER2R+VSoVNmzYhMjISS5YsgUKhwKFDhwAAXl5eiIqKQu/eveu8vV69euHpp59G//794ezsjI4dO+Kpp54CAEyZMgU7duyQZ2vfeeedmD59urm/pVqZdPRdUFCAxx57DAEBAXB1dUVsbCxCQ0Nx5swZc8dHREREVGd1aT8FwCwtqERRRFRUFKKioiCKYoO3VxVJkjB06FC0adMG7777rkVew1J++ukn5Ofno6ioCJs2bTJ6rOI8DQN3d3f5tqWSGuWrdJjUsD2BgYGYNGkSExpERERERPUwdepUJCUlYdu2bUhISJBn10mSVGNCY8SIEbh27Vql5a+//jqioqLwzTff4OjRo/JxtiAI+Oyzz/D777/jxx9/xO7du63SjtmkpMacOXNw7do1bNu2DW5ubmjZsiUWLVqEefPmmTs+IiIiojqRJKnWpIa/vz9UKhVSU1Mb/HqCIKBNmzZo06aNxT7Epaen4/jx4wCADz/8sNr1UlJSUFBQYJEYTJWRkSHfPnDggNFj0dHR8m3DPA3AuFLDUt+PofUUwPZTRERERETUdJj7AqFu3bphzJgxVV4MNmDAAIwYMcJq8yVNSmr88ssv2Lx5M8aPHw+FQgFBEDB79mycP3/e3PEREdmFc+fOYfr06dBoNNYOhajZKisrgyiKNSY1FAqF2YaFK5VKDBkyBEOGDLHYMOPyiYGLFy8iOTm50jq7du1C69at0blzZ5t6Dyofe1xcnNHPvKoh4UDjtJ9ipQYREREREZF9Mymp0aVLF2zduhWA/ipFQRBw+PBhdO/e3ZyxEVEd7dq1yywn6Mh0x48fR3JyMk6fPm3tUIiaraKiIgCoMakB6IeFX79+vTFCarDMzEyj+z///HOldQxtqdLT023qPaim2MsnNcLDw+XbjZHUYKUGERERERGRfTMpqbF+/XqsW7cOISEhKCgowP3334+nn34aGzZsMHd8RFSLrKwsrFy5Er/88ou1Q2nWEhMTAUBuE0NEjc9QpVBbUsNclRqNoXy1A1C5jVNmZiYOHz4s38/Ozm6UuOqiYuw//fQTAECn08kzNdq1a2c0R8PR0VFONLBSg4iIiIiIiKqiqs/KOp0Oly9fhkqlQkxMDA4dOoTU1FSEhoZi0qRJHBROZAWHDx+GJEmVroilxnXx4kUIgsCkBpEV1adSY9++fZAkqUH9P7VaLXbv3g1AP5RNparXx6o6qfje/uuvv0IURSgU+utSdu/ebTSk3JaSGlVVaoiiiKSkJDkBVdXAOnd3d+Tk5LBSg4iIiIiIiKpU50qN6OhodOjQAd27d8egQYMQHh4OLy8vPPfccwgMDMSUKVNw//33WzJWIqrCb7/9BqDyySNqPGq1Gunp6Rg5ciSSk5Ptpq0NUVNTXFwMoPakRmhoKDQaDfLz8xv8miUlJSgpKWnwdqpTsdohOzsbUVFR8v3vvvvO6PGcnByLxVJfVcV+9uzZaudpGBhaUFlqUDgrNYiIiIiIiOxbnZMaCxcuxKhRo6BWq6HRaPDvf/8bc+bMwbBhwzBt2jSMGDECV65csWSsRFTBrVu3cOrUKbRo0YJJDSu6ePEiAODBBx+EQqHAiRMnrBwRUfNUn/ZTABqcgFQqlZg4cSImTpxosUHhVb23G2ZT5OfnV2o9aCuVGtVVEB44cADR0dHy/ZqSGqzUICIiIiIioqrUOalx5swZvPTSS3B1dYVKpcKiRYtQUFCAvn374sqVK3jllVfg4eFhyViJqII///wTWq0WU6ZMQVZWFiRJsnZIFhcbG2t0lbItuHjxIhwdHdG9e3f06NHD7pMaoiha9MpzIkupT/spAEhNTW3Q6wmCgJYtW6Jly5YNamNVk+oSAwDw448/GlUdALaT1MjLy4NWqwUAdOzYUV7+008/1blSo7Cw0Ki1lrmwUoOIiIiIiMi+1TmpUVxcDG9vb6NlTk5OWLx4MXx8fMweGBHV7rfffkPXrl0RHh6OkpISi7XqsCVr167Fhx9+aO0wjCQmJqJjx45QKpWIiIjAyZMnLXIirrHs2bMHd999N3Q6nbVDIaoXQ1LD1dW1xvXc3d3h4eFhF8PCy7dwCg0NBaBPaGs0mkqtpwDbaT9VPu5BgwahQ4cOAICjR4/i5MmTAICWLVuiTZs2lZ5rSGpIkiTvU3NipQYREREREZF9q3NSQ5Ik3HHHHejbt6/8pVarMXHiRKNlRNQ4SktLcfToUYwYMQIBAQEAgBs3blg5KssqLS1FQkICsrKyrB2KkcTERHTu3BkAEBERAbVajYSEBCtHZbqLFy8iMzMTiYmJ1g6FqF4MJ8CdnZ1rXTc4OLjB7adEUcTly5dx+fJliyUyDZUaXl5emDBhAgD9e+FPP/2EH3/8EQDg6ekpr28rlRrlK0z8/Pwwbtw4APrh6obHevXqVWWFi7u7u3zbEi2oWKlBRERERERk31R1XXHLli2WjKNJ++CDD5CYmIj33nvP2qFQE3Ly5EloNBqMHDlSvir55s2b6NKli5Ujs5wLFy6grKzMZk7aAfqTY1evXsW9994LAOjevTvc3Nxw/PhxdO3a1crRmSY9PR0AcPr0aXTr1s3K0RDVXVFRERwdHaFQ1H7NRkhISIMrNURRxKlTpwAAbdu2rdPr1peh4sHPzw9jxozBxo0bAQAvvviiPEPknnvuwZdffomioiKbeX8sX6nh7++Pbt26YcOGDUbrVNV6CrhdqQHoh4UbEvfmwqQGERERERGRfatzUmP27NmWjKNJc3JyQmxsLCRJsljP7cby9ddfo7CwEHPmzLF2KM3eb7/9htatWyMsLAw6nQ4KhcLoJFJTZBguW1hYiOLi4jpdjW1pV65cgU6nkys1lEolBg4ciOPHj9vt30n5pMZDDz1k5WioKVi8eDH69u2LBx980KKvU1RUVGvrKYOQkJBKQ7brSxAEeT6HJf5/LykpgVqtBqBPDNx5550QBAGSJOHChQvyetOnT8f+/fuRlpZmM+2nKlZqjBw5EiqVSp6zAdQtqWGJSg22nyIiIiIiIrJv5r+kkCrp2LEj1Gp1lcM+7c0333yDgwcPWjuMZk8URRw+fBgjRoyAIAhQKBTw8fFp8kmNc+fOyVfV2sqJu4sXL0IQBLlfPKBvQXXu3DkUFhZaMTLT3bhxA56enoiKijI6AUlkqoSEBFy+fNnir1NUVFTrkHCD0NBQ3Lhxo0GzY5RKJYYNG4Zhw4ZBqVSavJ3qVEwMeHt7o3///kbruLu7484775Tnm2VnZ0OSJLPHUl8VKzXc3d0xZMgQo3WsldRgpQYREREREZF9Y1KjERhOdjbGCR1LKioqwl9//WVz8wyao5iYGOTm5mLEiBHyMj8/vyY9U0OSJMTExMgnxWzl9/DChQto3bq10YnUQYMGQafT4cyZM1aMzDQajQZqtRoTJkxAUVER4uPjrR0S2TlJkpCTkyNXHFhSfZIawcHBEEURN2/etHBUpquY1ACAMWPGGK0zefJkODk5yUmN0tJSm0ioVkxqAJDnagCAQqFA9+7dq3wuKzWIiIiIiIioJkxqNIKgoCC4urri0qVL1g6lQS5dugRJkpCVlWWxgahUN4cOHYKPjw969OghL/Pz82sSlRrJyclyn/jyUlJSkJubi1GjRgGwnWG4Fy9elFtPGYSGhiIkJAQnTpywUlSmM7SeMsxqOX36tJUjIntXUFAAnU6HgoICi79WfZIahrZRqamp1a6j0+nw66+/Wq3yoarEwNixY43WmT59OgDA29tbXmYLlWxVJWTKx96lS5dq9xUHhRMREREREVFNmNRoBIbWNPae1DD079ZqtTZxwqS5kiQJv/32G4YPH240lNbX19emrziuC41Gg//7v//Du+++W+mxc+fOAQDuuOMOKJVKm0hqiKJYZVID0LegOn78uBWiapjr168D0Cdm+vbty6QGNVhubi4A2FylRmBgIBQKRY3Dwk+cOIHnn38eKSkpVT6u1Wqxa9cu7Nq1yyKt2qpKDAwePBhubm4AAGdnZ4wfPx5aLZCTMxFABADbSPpWlZDp27cv+vTpA0A/3Lw6FQeFmxsrNYiIiIiIiOwbkxqNpGPHjnaf1EhMTISTkxMA2P3Jc3uWlJSE69evY+TIkUbLDZUattBL3VS//PILCgsLsWfPHuTl5Rk9FhMTg3bt2sHDwwM+Pj42cdIuLS0NGo0GnTp1qvRYREQEUlJS7K4lWHp6OlQqFXx9fdG/f39ER0cbXdVMVF+GJLitVWo4ODjA399fTuRVxfD3W1PsGo2myuoycyif1DAkBhwdHfGf//wH7u7ueOmll9CiRQs8+ijw22//BHAQQIhNvD8aYnd2dpaTMAqFAkeOHEFUVBRWrFhR7XM5U4OIiIiIiIhqwqRGI+nYsSOuXbtmdHWgvblw4QIGDhwIgEkNazp9+jQcHBzQr18/o+V+fn4oLS1Ffn6+lSJruO+//15uqfXdd98ZPRYTE4PevXsDgM0kNS5evAgAVVZqDBgwAIIgyBUm9uLGjRsICAiAQqFA//79UVpairi4OGuHZTaiKOLUqVN2nfyzN7ZaqQHo52rUVKlhODFf3Yl1pVKJsWPHYuzYsRYZFF6+2sFQqQEAzz//PPLz87F06VJs2wZs3Wp4xAXAYJt4fzTE7u/vD0EQ5OVubm7o3bu30bKKOFODiIiIiIiIasKkRiPp2LEjdDodrl27Zu1QTFJWVoakpCQMHDgQjo6Odnf1OQAUFxdbOwSziIqKQvfu3StdXWo44WWvCacrV67g3Llz+L//+z9MmjQJX3/9tXziSa1W48qVKwgPDwegb7VlC4PCExMT4evra9TL3qBFixbw9fWt8SpwW5Seno6goCAAQKdOneDu7t6kWlAdO3YMy5Ytw+XLl60dSrNhSGoUFxdbPLFf36RGUFBQjf+fGd5PqzuxLggCfHx84OPjU+NJelNV1X6q/GsnJABPPVXxWd2s3iJSFEX5Pbpi3HXBSg0iIiIiIiKqCZMajaR9+/YAYLctqK5cuQKtVosuXbrAz8/P6ESLPbhx4wZGjRpld1fNVyRJEqKjo+WKhfJ8fX0B2G9S4/vvv4enpyeGDRuGyMhIZGVl4eeffwYAuVKgV69eAPSVGraS1Kiq9ZRBaGioXSc1FApFk5urYXgPqG5GAplf+RPslm5BpdFo6pXUCAwMrDGpUVulhqVVNZfCQKMB7r9f/6+xrlav1MjJyYEoigAqx10Xlh4UzkoNIiIiIiIi+8akRiNp0aIFgoKC7Pbq4MTERAiCgI4dO8LPz8/uTpxHRUWhtLQUv//+u7VDaZDU1FRkZ2fLg1bL8/LyglKptLt9A+ivmt27dy8mT54MBwcHtGvXDoMHD8aXX34pJ3K8vb0RGhoKwLbaT3Xp0qXax0NDQ5Gent6IETVc+aQGAPTv3x+xsbEoKSmxYlTmY0iQ2VuyyZ4ZKjUAyyc1iouL653UyMzMrHbId22VGqIo4tq1a7h27Zp8Et+cyl9A4OPjY/TYM88Ahs5w3bsDKpXh9btZ/f2xpgqTurD0oHBWahAREREREdk3JjUakT0PC79w4QLatGkDV1dXu0xqGE5kHj9+3MqRNEx0dDQEQZDbMJWnUCjsct8AwKFDh5Cfn4+77rpLXhYZGYkLFy4gOjoaMTExCA8Pl9u7GJIa1pyLkJOTg8zMzBorNUJCQuwqqVFaWors7GyjpMaAAQNQVlZm91VOgP4E9Pnz5wHArvaLvcvJyZFPyFt6roYp7ackSTKqiCjPsLympMaxY8dw7NgxiyQ1DK/v7e1tVFGwfTuwcaP+tqsr8PXXQNu2hhP1nZGVlWf2WOqjpgqTumD7KSIiIiIiIqoJkxqNqEOHDnZbqXHhwgV5GLKvr6/dzdQ4f/483NzckJiYaPVe4w0RFRWFDh06GLXmKC8gIKDak3O27Pvvv0fv3r0RFhYmLxs8eDDCwsLw2Wef4fz583LrKUD/O6jVai1+1XdNDEPCa2s/VVBQ0CgDks3B8HddPqnRrl07eHp6NokWVNeuXYNGo0GLFi1YqdGIcnNz0aZNGwC22X4KQJX/p2k0GvmEek0zNQICAhAQEGDRmRoVqx2WLbt9e8MGoFs3/ZeeE1JSrNtSqboB53XFQeFERERERERUEyY1GlHHjh2RmZmJvLw8a4dSL6IoGrXZ8fPzQ3Z2NnQ6nZUjq5vS0lIkJiZixowZAICTJ09aOSLTRUdHV9l6ysDf39/uEk7Xr1/HyZMncffddxstFwQBkZGROHz4MIqLi42SGoarvq05VyMxMRGurq5yS6yqGB5LTU1trLAaxFC9UD6poVAo0K9fvyaR1IiLi4NCocCQIUOY1GhE5ZMalkzwiaKIkpKSeiU1AgICAFSd1DAkFBQKRbUn1pVKJUaNGoVRo0ZBqVSaEHX1iouL5SRQ+WqHjAwgKUl/OyICePhh/e3w8NsVB+npXmaNpb7Kt59ipQYRERERERGZG5MajahDhw4AYHfVGsnJySguLpYrNfz8/CCKYp16dpeVleG///0vNJUnmTaaS5cuoaysDMOHD0enTp1w7Ngxq8XSEDk5OUhOTq5ySLhBYGCg3bWf2rVrF9zc3HDnnXdWemzixInw8PCAg4OD0ewKQ1LDmn3jY2Nj0bVrVygU1b+N2mNSQxCESich+/fvj/Pnz6OoqMhKkZlHbGwswsLC0LZtW6Snp1u1fVlzkpOTg6CgIDg4OFg0qVFcXAwAcHV1rfNzXFxc0LJlyyqTGoZqg5CQEKsMCq9uLsWZM7fXGTz49u0ePW6/F2VnB1g0tto0tP2Uo6OjnGxorEqNmBhg8WLgqadufy1YABw4YPaXJyIiIiIiogZiUqMRtW7dGo6OjnY3V+PChQsAYFSpAVR9ZWtF58+fx8aNG/Hrr79aLsBaxMXFwcHBAR07dkRERASOHz9ulyczo6OjAaDGpIa/vz8yMjLs5vvT6XTYvXs3xo8fX+XV1c7Oznj00UcxYcIEo6tpvb29AVgvqSFJEmJiYmrcFwDg4eEBd3d3u0pq+Pn5VWrH0r9/f2i1WsTGxlopMj21Wo01a9YYXWVdH7GxsejZsycCAwNRXFxsNMC6vJ9//hm7du2yu6o6W6TT6ZCfnw9vb2+4u7tbtP2UIanh7Oxcr+cFBgbWmNQICwuzqaRG+aKp/v1v377dfgooKKi+gqwxNHRQOHC7WqOxBoVPnw6sXQt8+OHtr/ffB8aNA/7zH8BO/lslIiIiIiJqFpjUaERKpRLt2rWzu6RGYmIigoKC4OHhAeD2CYq6VARcuXIFAHDixAnLBViL8+fPo1OnTnB0dERERASys7ORZOjdYUeio6MRHBxc41Wv/v7+KCsrq/Zkra05f/48MjMzMXny5GrXiYyMxEsvvWS0zNXVFa6urlZLaqSkpCA3N7fWpAagb+WUlpZm+aDMID093aj1lEGbNm3QokULxMXFWSGq2w4ePIgvv/xSnmdSHxqNBleuXEGPHj3kOQpV7RdJkvD666/j1VdfxdixY/HEE0/g66+/tutZPNaUn58PSZLg5eUFd3d3i1ZqGCoC69N+CtD/jVaV1Lh58yZatmwJHx+fapMaWq0We/fuxd69e6HVausfdA2qq3aoLqmhH++jbwtZWtreIoPL66qhlRrA7aRGY1RqFBUBf39cqdJrrwEzZwJ/582IiIiIiIjIypjUaGQdO3a0u/ZTFy5cMGr94+bmBldX1zolNa5evQpAn9Sw1gmWuLg49OjRA4C+ysHR0RHHjx+3SiwNERUVVetJdEN/eHtpQfXXX38BqHnYdnV8fHysNlMjOjoagiCgZ8+eta4bFBRkN5UaN27cqDKpoVAo0K1bN8THx1shqttOnToF4Pbsj/qIj4+HKIpGSY2q5mrk5OTg1q1bePHFF7F06VI4ODjgnXfewYIFCxoWfDNlSLB6eXnBw8PDopUahvZo9Wk/BVRfqZGZmQl/f3+0aNGixhPrarXaIsma2tpPeXgAf3e1BAA4OQGuroa/ja7Izc03e0x1Zc5KjcaYqVH+OoARI4CzZ/Vfb70FGOa/b98O3HknUO5bIyIiIiIiIithUqORdejQAUlJSVa9grI+JElCYmKiPE8D0A9w9vPzq3Olhre3N3Jzc62SzFGr1UhOTpaTGo6OjujXr5/dJTU0Gg0SExPrnNQof5WsLUtJSYG/vz+cnJzq/VwfHx+rVWpER0ejY8eORsNsqxMcHGw3SY3qKjUAWD2pIUmSPKzclKRGbGws3Nzc0LZtW7i5ucHDw6PKSo1r164BAPr27Ytp06Zh/fr1mDt3rlWH0tszQ4WLof2UJSs1DEmN+lZqGJIaFdv23bx5s9akhiUHhVdV7ZCeDhh+bfv1AyqO9PHyMiRnXHHunPWSGobYDRdBmMLw/lpYWGj2z0wVKzXKJzXatgX69NF/Pfss8P33gOFbOHpUP8ekinwoERERERERNSImNRpZx44dUVJSYlcnOdVqtVGlBqA/eV7XSo2JEyfCycnJKokEQ7uc7t27y8sGDx6Ms2fPoqSkpNHjMVVsbCxEUUSfPn1qXM/LywsqlcpuKjVSU1PlYdr15evra9VKjV69etVp3aCgIGRlZZk8B6Kx6HQ63Lx5s8akRkZGhtV+5teuXUN2djYEQajTPJ+KYmNj0b17d3mwe1BQUJWVGteuXYNSqTT6vWzRooVFKwyaMkOlhre3Nzw8PBolqWHKTA2NRlNpH2dmZiIgIKDGpIYgCAgICEBAQAAEwyX9ZlJVtUP5IeHlW08ZBAbebpMWFWW9/+MMSQ1TqzQAwN3dHYA+oWnYt+ZSU6WGl5fxulOnAocPA4a3xqQkYPJkwApjVoiIiIiIiOhvTGo0sg5/94qwl7kaiYmJAGBUqQHULalRWFiIjIwMdO7c2WrVEXFxcfDw8ECrVq3kZRERESgtLUVUVFSjx2Oq6OhotGzZEm3btq1xPYVCAX9/f5NO+lpDamqq0b6pD2tVauTk5CA5OblO8zQA/clzSZJsfq5GRkYGRFGUWzNV1O3vKcTWqtY4deoUlEol+vbtW+9KDUmSjNrQAfoKmuqSGqGhoVCpVPIyd3d3lJaWGl3dTXWTk5MDR0dHuLi42HT7KQCV3jdv3rwJPz8/tGjRAmVlZY2amMzLA1JTbyeADJUa1c3TMGjd+vaZ9vPnrVMRqtVq5QodU+dpADCqhDP3703FSo3yI3MqJjUAfVXMiRP6Kg4AiIoCZswAzDxGhYiIiIiIiOqISY1G5u3tDW9vb7uZq3HhwgV4e3vD19fXaHldkhqGeRrt2rXDoEGDEB0d3ejVEefPn0ePHj2MrqANCwuDn5+fXbWgMszTqMuVwAEBAXbVfsrekhrnzp0DgDonNYKDgwHA5quzDImC6io1AgIC4O3tjfPnzzdmWLLTp0+jZ8+eaNeuXZXJiJqkp6cjJyfHaAZKSEhItUmNislDS/b2tweHDx+WKy7qKycnB97e3hAEAe7u7o2S1DCl/RRgnNQoKytDbm6uXKkBVL3/RVFEamoqUlNTzdYi6fJlIDQU2L79HQD6KklDxUNtSY327W//H3v5soNZ4qmv7OxsuZVXQyo1yic1zP23V1Olhrd31c9p1Qr48UfA01N//8cfgQULgApdy4iIiIiIiKgRMKlhBR07drSrSo0uXbpUOpkeEBCA7OzsGq9cvnLlCgRBQJs2beTqiOjoaAtHfFtVV2cD+nYhERERdpPUKCsrQ2xsbK2tpwzq2hrM2tRqNQoKChrUfiovLw/aRr5UNjo6Wm43Uxfe3t5wdHS0+aSG4YRudZUagiBYba6GKIo4ffo0BgwYgKCgIKSnp1eaf1ATQxu68u8FQUFBuHHjRqUT0VevXmVSo5yysjI8++yzWLNmjUnPz8vLg9ffl743RvspQRDg6OhYr+d5e3vDwcHBKKmRlZUFSZLkSg2g+qTGkSNHcOTIEbMlNb7/HigsBHQ6FwCLAeiTuJJ0u/2UlxcQFlb5uV27CgD0cfz1l5tZ4qmv8m2zzFWp0ZhJjaoqNQy6dgW++w5w+Dtf9NFHwDvvmDU0IiIiIiIiqgMmNaygQ4cOdpPUuHDhQqV5GoD+xLkkSUYnLyq6evUqgoOD4ezsjHbt2sHX17dREwlpaWnIz883mqdhEBERgcuXL9vF8N/ExESUlJTUuTLAXpIaKSkpANCgSg3g9iDixhITE1PnfQHoW4JZc1i4RqPBggULkJycXON66enp8PLyqvEq927duuH8+fP1SiiYw+XLl6FWq+WkRlXzD2oSGxuLkJAQ+eQ6oK/U0Gq1RlVNRUVFuHHjBsIqnC02nFxtjnM1DG3J9u/fjytXrtT7+Tk5OfLP3d3dHRqNBjqdztxhAtDvcbbeVAABAABJREFUPxcXl3rPtlAoFAgICDBKahh+L2qr1BAEAb6+vvD19TXbTI2LF8vfewDe3q2hUqlw/TpgCLFfP6CqlwsO9gTwFwAgPd3LKlUEVQ04N4Xh564EELhsGTBkCGCmz071bT9V3siRwCef3L6/ZAmwY4dZwiIiIiIiIqI6YlLDCjp27Ii0tDRoNBprh1KjnJwcZGVlVZqnAdw+UVHTyfMrV66gXbt2APQnfgYOHIgTJ05YJtgqVDUk3GDQoEEQBAFHjx5ttHhMde7cOTg6OlaZXKqKof2Uua4athRDUsPUSg1DUqMxW1AVFxcjISGhzkPCDUJDQ62W1IiOjsaxY8fwSfmzcFVIT0+vtvWUQffu3aFWq+vd/qmhTp06BUdHR/To0UOOsT5zY+Li4oxaTwG324KV/14MiZ+KlRqGgcXNsVLD0JbM1dUVH330Ub2fb2g/BegrNQDLJYcMSQ1TBAYGVpnU8Pf3rzGpoVQqMWbMGIwZMwZKpdKk167I+Ly9OxwdHwRQe+spwPC+qK+mKi11hjVG+VQ14NwUhr+7+wAE7t0LHDsG/Pe/DQ0PwO1KDZVKBUEQ6tR+qrwHHwReeeX2/QULABv/SEdERERERNSkMKlhBSEhIQBg83MPqhsSDtxuUVPT93D16lWjK54jIiJw8eLFRruy/vz58wgNDYWnoQF2OZ6enujXrx/efPNNfP755xa7ctgcLl68iA4dOhgNLq5JQEAAtFqtyT3wG0tqaiq8vLzg5mZaixRrJDXi4+Oh1WrrVakBWDepYZiBsW/fvhr/XtPT06ttPWVgGBbe2HM1Tp06hd69e8PR0VFOatR1WHhpaSkuXLhQbVKj/AD3a9euAQDatGljtG5zbj9lSPosWLAABw8elP9fqKvc3Fw5qWE4SW2pFlQajaZBSY3yv1MZGRlwcXGBm5tbo+9/40oNQKN5AEDdkhr6n/XtFnFW6BZn9kqNReUX/p0MbyhDpYbD332k6tp+qrxly4A5c4DWrYFffwXqOZ+eiIiIiIiIGoBJDSto2bIlAMud2DGXixcvwtXVVT75V56rqytatGhRbaVGUVER0tPTjZIagwYNAgCcPHnSMgFXUNU8jfLWrFmDe+65B++99x7mzJmDixXPJNmIixcvolOnTnVe3zDrwdZbUDVkSDgAeHl5QRCERm0hFh0dDTc3N3To0KFezzMMpbZG9Ux8fDx69eoFV1dXfPHFF9WuV5dKDS8vLwQFBVlsrsbBgwfx119/GS3T6XQ4e/Ys+v99FtfLywuOjo51rha5ePEiysrKKiU1HB0d4evra7Sda9euwcfHRz75bmBIvDXHpMaNGzfg4+ODadOmoXXr1vWu1qjYfgqwXKVGcXGxWSs1/P39IQhCo+7/W7eAir/aanVPxMfXktTIygIWLkSrF1/EB9iMt/AsXsZyiJ99DjRy0r58UqOhg8KHABhYfmE9KrRqYqjUMMxfMSWpIQj6wpGTJ4EqCkKJiIiIiIjIgpjUsAJDUiM/P9/KkdQsMTERnTp1gkJR9a9JxR7k5f3111+QJEluPwXor6zv0KFDo8zVKCsrQ2JiYpWtpwxcXV2xePFibNmyBSUlJXjwwQfx6aefWjy2+tBqtbh69Wq9khp1aQ1mC1JSUkxuPQXo24Z4eno2aqVGdHQ0wsPDq/2bqE5oaCjKysoavTpLkiScP38eAwYMwH333YedO3dWmUwVRRE3btyoNakBwKLDwlevXo0FCxYYxZiQkACNRoMBAwYA0M8/qHhVfU0M7ds6duxY6bHg4OBKlRoVW08B+t81Z2fnZpnUMCS7lEolHnvsMRw5ckRu7Veb0tJSFBYWGg0KByyX0G9oUiMrK0u+gt+Q1ABq3v86nQ4//fQTfvrpJ7NU/Bm3niqUb23efDup4eurrw6Q5eUBY8YA69fD8euv8SQS8SzewXKswPjP/w94//0Gx1Uf5hwU/kzFhWb6f61ipUZ9ZmqU5+AA/H0dARERERERETUiJjWswHC1qq0nNWqrEKhpILVhoGzFgbsRERE4ceKExQcNX7p0CaWlpTVWahj06NEDn3/+OaZMmYKPPvrIpmZR/PXXXygrK6tXZYCXlxccHBxsPqmRmpraoKQGoE+UNVZSQxRFnDt3rt6tp4Dbc0PSGrnB/Y0bN5CTk4Pu3bvjgQcegFarxTfffFNpvdzcXJSWltYpqdG9e3ckJCRY5O9Eo9Hg+vXrWL58ubz9U6dOwdXVVW59BQBBQUF1mqmRmZmJrVu3YsiQIfLJy/KCg4MrVWpUldQA9CdYm2NS4/r16/LvxdixY9GuXTt8+OGHdXpuXl4eADTaTI3i4mI4Ozub9NygoCBIkiSfkL9586bRCfnq9r8kScjJyUFOTo5Z/l8zLhj8AEAJAH1FgKEorX//ckPCNRpgyhQgOrr6jf73v2jMieHmqtTwKyzE9IoLLVyp4e4O1LHTIxEREREREVkRkxpW4OjoCBcXF5tuP1VUVIS//vqrynkaBjUlNa5evYqAgAC4VmgyPWjQIGRmZuLq1asNiu/ixYs1th2Ki4uDSqWqMf7yHBwcMHbsWJSWljb6ieeaGFpiVXWVeXUEQYC/v79Nz2zRaDTIyclpUPspQJ/UaKz2U1euXMGtW7fqPSQc0J8wFQSh0edqGGZfdO/eHd7e3pg8eTK2b98un9AzMFQ91CWp0bVrVxQVFcnzJ8xFkiQUFRVh2LBhOHz4MD7//HMA+qRGnz59jIYwBwUF1VqpodVqsXTpUqhUKvz73/+ucp3ySQ1RFPHXX39Vm9Rwd3e3uaRGVlYWnnnmGYv+X1K+LZlCocATTzyBEydO4OzZs7U+1zA/yVCp4eLiAoVCYbF4i4qKTE5qGObJGJJlmZmZcis/oPqkhkKhwLBhwzBs2LB6V3BVxTipcQzATgBA4e2ijdutp0pLgXvuAf74Q3/f1xc4fBjT2rTBUHyDk9BXNyEhAThzpsGx1ZW5BoV3+flnVBq9np8PFBebvE2D6mZq1KdKg4iIiIiIiKyHSQ0r8fDwsOlKjaSkJEiSZHJS48qVK0atpwz69OkDhUKB6JquKq2DZcuW4f0aWmqcO3cOXbp0ka/CrAtDvIYqE1tw6dIlBAYGVurxX5uaWoPZAsPJ/YYmNXx9fRutUiMmJgZKpbLGlmbVcXR0REBAgFWSGkFBQfKV8g8++CByc3OxZ88eo/Xqm9QwbNucSktLIUkSxowZg9mzZ+P999/HyZMnERMTI7eeMqhLUmPDhg2IjY3FqlWr5JPqFYWEhCAzMxOlpaVIT09HaWmpXVVq/PDDD/jjjz8sNg9IFEXcvHnT6PdixIgR6NSpE7Zt21br8w1JDcPvnyAI8PDwsGj7KVOTGoYERnp6OkRRREZGhtEJeTc3t2qTGiEhIQgJCbFAUuMSgM2V1unfH/o5GQ8+COzfr1/o4QH89BMwdCgyQkLwB9zxMR67/aRGbK1oSKi7u7ubvD9QUIDgffsAAMUALpef7WWGhH35Sg1Jut1+yuitIisL+P57wIY+ExAREREREZEekxpW0rJlS5uu1EhMTIRSqawyMWEQEBAgt62p6OrVq5VaTwGAs7MzPD095ZNdpsrOzkZMTEy1j8fGxlYaDFwbX19fuLu7IykpqUGxmdOlS5fqNU/DoOLQW1uTkpICAHbVfio6OhqdO3c2uWd/aGioVZIa5ZMwrVu3xqhRo/DZZ58ZtY9KT0+Hm5tbnZJnLVq0QJs2bZCQkGDWWIuKigDor+Z/6qmn0Lt3byxatAglJSVVJjXy8vLk51T022+/4bPPPsPTTz9dY2VNcHAwJEnCjRs35MoTe0lqSJKEfX+f9LVUq7msrCzodDqjpIZCocC4ceNw9uzZWmdI5P59+Xv5pJKHh4fJ7adqe15DkhqG/5tu3LiBvLw86HS6OlVqmJvxTI3LAH5FQIDx73m/fgD+/W9gxw79AmdnYM8eoG9fAIYkUjx24D4Uw0m/zldfAX9XJ1iaoVKjIfM0sGULVH+Xp3wGIPnv1mUAzNKCqnylRmEhoNXqlwe3LAS+/BKYNAkICgKmTQMGDTJLIoWIiIiIiIjMh0kNK2nZsqVNV2okJiYiLCysxkoHwwmfim2OSktLkZqaWmVSAwA8PT0blNARRRFqtRopKSlVJkdycnKQlpZW76SGIAho166dzVVq1GeehkGrVq2QnJxsgYjMIzU1FS1atEDLli0btJ3GrtQwZZ6GQUhISKMmNURRREJCgtEsCgCYPXs2UlJSMHPmTMyePRtz5szBV199VacqDYPu3bubvVJDo9EAAFxdXaFUKrFy5Uq4ubnBw8OjUvs1Q6ugqqo1UlNTsXz5cowaNQqRkZE1vmZISAgA/dyIa9euwdnZ2ehEdnktWrSw2CwIU1y6dEl+r7JUUsPQmqvi70afPn1QVFSExMTEGp+fm5sLV1dXo/9H3N3dTXr/z8/Px7hx42qs8isqKjI56QjcTgYbfp7lKzVqmqlx48YN3Lhxo8EzNSQJMPxI3dyyoK9RkHDvvbd/7wICgJBgST8nA9APgPj2W2DoUHkdHx8fAPFQoyV24S79wqys21UdFlRaWions+qc1CgpAQ4fBg4d0rfJunABeO89+eF3AVwrDry9vhl+38tXahhaT83Henz7ZwAwaxbw44+3Mx1ZWcC77zb4NYmIiIiIiMh8mNSwEltvP1XbkHDg9onFiifUkpOTIYpitVUeHh4e8gBZU+Tn58snj2JjYys9blgWHh5e7223b9/eZpIaubm5yMrKMqlSo127dsjLy5NPLtmalJQUtGrVCoI87dY0Pj4+KCoqkk+IW0pOTg6uX79e70RZeY1dqXH16lUUFRXh/9m78/gm6vx/4K9J0rtN75Pe3AUKKCCKcniigCKogLhe4LmIv3WX/arreq6g7qrruV6ILh64iycKnqCIC3Ijpdz0ogdt6ZGeaZOZ3x9hpkmbtEmas309ffAwmUySz2SSSfp5z/v9HjlypMXynJwc3HvvvRg1ahQGDRqErKwsTJw4Ebfeeqvdj52Tk4MjR44oZzu7QuuZOvnymfZxcXF47bXXsGLFii5lfVLOlKKxlo30zDPPICYmBo888kiP76/ExESoVColqJGRkWGzhJCv9dTYsGEDoqKikJ2d7bb+ObbKkg0fPhyBgYHYs2dPt/evqalRSk/JnM3UKC0tRVtbW7fb2ptMDaCjAb38HPZkahiNRmzatAmbNm3qMXOlJ6dPA/JXY0hIibJ80SIV5JYykyYBQunJjhUvuQS44gqLxzEFNUzfg//GjR03eKAElXmPI7v7adx4IzBlCjBtmqm21vDhSsmnbwDk41JsKzQLUPYyU0OSJIughuncCAlP4X6EGM2al6SmAnJA7uWXOxpvEBERERERkddpvD2A/ioyMlI5C9bXiKKIo0eP4pJLLul2PfkszM5BDTkoYCtTo7dZKuYT9fv27cOUKVMsbt+/fz/i4uJsnnHdnezsbHz55ZcQRdEl9dF74+iZOiSONAmXya99QUGBzX4C3lRSUtLr0lOAPHlnKkfWuSm9K+Xl5QFAr4MaDQ0N0Ol00JqXUnGTAwcOQKVSYdiwYV1u+93vfterx87JyUF7ezuOHTum9NjoLfPyU7Ls7GyrwdH4+HioVKoumRptbW3YuXMnlixZgrCwsB6fU61WIykpCaWlpSgsLLRZegrwrfJToiji66+/xqWXXory8nK3BjW0Wm2Xz1ZAQABGjRqFPXv2YOHChTbvX1tb2yWoERER4VSwVQ5g2So5BvQ+qJGUlIRt27ahsrISGo0GUVFRym229r8gCMp6vQ3SmvfTUKmOKY+ZmxuNDz4wJVo8+CCA337rWNFK8N50XPwVwE58i0txCglIRCXwxRemiXk3fieYNwm3K1Pj4EHgP/+xefPzCAewCqewo2NhLzM1zINPAQEBqK0FolCHMJwJjg8dasqEueAC4K67gDfeABoaTIGNv/61V89NRERERERErsFMDS/x5UyN4uJi6PX6HjMEgoODodVqu5wtXVBQgNjYWJsTt73tJyJneQwZMsRqX43ffvsNubm5Tk0wZWdnK+WzvO3o0aMICgpyqpl2WloaVCoVCgoK3DCy3jt58mSvm4QDHUEN87OD3WH//v2IjY11KlAmk4M4nnpv5eXlISsryy3BnqFDh0KtViM/P99lj2lefqonarUaiYmJXYIa+/fvR1tbW5ceHN1JSUlBWVkZCgoKfC6oodPprB4rd+3ahaqqKkyfPh0JCQk2y09JkoQ5c+bghx9+cOr5KyoqbJYlO+uss7Bnzx6L3iyd1dTUdAmqOtsoXN7G7rKyXBHUkINEcuBMZmv/q9VqXH755bj88suhltMpnGQe1DAYTJ+t2NhYqNVqXHcd8PbbwKBBAMwzFK0EWk2BJAnAn2BAAD7A9aYb2to6+nC4iXmAzZ5MDenFl5TLn+NKHLn4blPmxty5wAsv4DvhTQApqIBZ+aleZmqY9wGTy08lwCwwOH68KXNEpQL+7/+gpMn885+AjwQ2iYiIiIiI+jsGNbzElxuFHzkzszJ06NAe101KSupylvCJEye6bTDe20wNOagxZcoUHDx40GKCwmg0Ij8/v0vJHXsNHDgQAHyiWfiRI0cwaNAgpzJGAgICkJaW5pNBDb1ej1OnTrk8U8Od8vLyMGrUqF6die3poEbnJuGuFBQUhIEDB+LQoUMue0xrmRrdSU5O7pLttmPHDkRFRSmfY3ukpKQgPz8fdXV1dgU1ets3wREPP/wwbrnlli4T+Rs2bMCAAQMwatQoJCYm2gxq1NXVobi4GOvXr3fq+cvKymwGNcaOHYv6+nqlwbo1tjI1nCk/JW9jd5karuip0draiqNHj3bJMvBEUMs8qNHUZArYWw0M2JWpAQA/ISfnqEdLUDmUqVFXB+mddwEAjQjDjfg3cja9gk9nvwusXYs1CUshSvMBAKdgFlDuZaaGedk8OVMjHh3jhvlrnp0NXH8mKFRTA7z2Wq+emyxVV1cjKyvL4jiydOlSCIKg/HOmrxgREREREfV9DGp4SWRkJBobG3tdg9sdDh8+jKSkJLtK5FibUCsoKLBZegpwTVBDpVLh/PPPR1tbm0Wz2mPHjqG1tdWpfhqA6QxXrVbrE301jh496lTpKZmvNT2XlZaWAoBLghoREREICAhwa1BDFEUcOHCgV6WnANNYtVqtR4Iaer0ex44dc1tQAwAyMjJQVFTkssfr3FOjJ3JTZ3Pbt2/HuHHjHAoEDhgwQHlP9hTUaG9vtwiiult+fj6KioqwfPlyJZjS1taGH374AZdffjkEQUBiYiLq6uqsjkt+fbZv3+7UuMvLy5X+JZ2NHDkSKpWq274atbW1XTI1nG0ULgfP5fdJZwaDAUajsdeZGoAp289aUKOtrc2lfWQ6O1NxEACg15sCF1YDA3KmRkCAqVRSJx1BDWDSpHXYrxqDPJw5FvzyC+DGoL1DmRpvvw1Vqylg9w5uhg6RMBqBefNMWSl3392x6imYvY96GdTonKlRU9MpU6Pza/7AA4Ac0H72WaCbwBrZr7q6GjNnzuwSGN25cye++uor1NbWora2tsfePURERERE1D8xqOElcsDAF7M1Dh8+bHdz6s5BDYPBgOLi4m6DGnKj2O7KlnSnrq4OkZGRGDp0KIKCgvCb2Vmr+/fvh1qtdrrOvyAIGDhwoEczNZYvX47t27dbLDMYDCgoKHCqSbis89mPvkKe1HdF+SlBEBAbG+vWoEZBQQGam5udzv4x56lm4YcPH4bRaHRrUCMtLQ0lJSU9r2gnORvB3knp5ORki/JTzc3NOHDggEOlp4COpuOCICA9Pd3meuHh4QDgsRJUNTU1qKmpwSWXXIKvv/4an376KQDg559/RlNTEy6//HIAHZPe1vpqmPeh2LVrl0PPL0kSysvLlYn+zkJDQzF8+HCbE46SJNksP9XY2Ojw8b+nnhqOBsWskbdVp9NZDWoAXfe/0WjE999/j++//77XJynImRoajQSgEICVwEBbGyBnSA0b1tHI2oxldswh3HmX4LFsDfNMjW6DGkYj8FJH6amXcA9mzjRdbm8HFi0y78u9Bm2q31AP0+8msax35accytQATI3L5841Xa6oMEVcqNfmz5+P6+UsmDMMBgMOHDiAyZMnIyoqClFRUYiIiPDSCImIiIiI/EteXh7Gjx+P6OhoLFu2zK5KE2vXrkVGRgZSUlLw4YcfKsunTp1qkUEt/wNM1VI6L3/vvffctl22MKjhJb4a1JAkCYcPH7ar9BQApa69PKl88uRJGAyGbstPRUVFQRRFpycH6+rqEBUVBY1GgxEjRlj01fjtt9+UYIezPJnh0NbWhk8++QQvv/yyxcGmqKgI7e3tvSq7kJWVhcrKSp9pbiw7efIkgoKCEBcX55LHi42NdWtPjf3790OlUrmkIXZaWhqKi4tdMKruHThwAIGBgW4t25GRkYGqqqpuexw4oqWlBcHBwXZnWaSkpKC6ulqZoNy7dy+MRqPDQY0BAwYAMAVJujtueDqoceyYqVH0XXfdhblz5+If//gHjhw5gg0bNmD48OHIyMgAAKXPi62gRmBgIJKSkvDzzz879Py1tbVoa2uzWX4KMJWg2r17t9UfSi0tLdDr9V3KT8nffY6+jj311HC0fJk10dHRCDwTJOjcP0fe/01NTRbLJUlCVVUVqqqqelWaTBQ7MjUGDGgFYAqQdMnUOHQIMBhMl21kJJpnapw+fRqPPAJ8Eb4QxjM/+QwvvmJqfO0GcnlIoHNwpZMvvwTOBN03YDo0OUPx2WfAdddZrhYQUAXg9xDFEqWvhuTiTI0uQQ1r2TEPPthx+emnAWYP9Nqbb76JpUuXWizbv38/RFHEmDFjEBISgunTp3vkO5OIiIiIyN/p9XrMmjULZ599Nnbu3In8/Hy888473d4nLy8PCxcuxF//+ld88803ePjhh5VqOF9++aWSPV1bW4vXXnsN06ZNA2Dq83nBBRdY3D5v3jx3b2IXDGp4SVRUFAD4XLPw06dPo7a21u4MgREjRqCpqQnTp0/H4sWL8faZMxh7ytQAnN9285Imo0ePxr59+5TJJLn3QW8MHDgQhYWFMMgTR24kn32cn5+PvXv3Ksvlvia9KT8l7wNfy9YoKSlBampqr/pTmHN3psb+/fsxcOBAlzTczsjI8FhQY9iwYdBoNG57DjnTxlWZJ472Q0hOToYkScpk944dO5CQkOBwBpCcqdFd6SnAO0GNoKAgpKam4o9//COysrKwbNkybNmyRcnSADrOhrfWV6OiogJJSUm44IILsGXLFocm3eUsmO6CGmeddRYqKyu7NGwHTMdpAFbLTwGOBfRFUVQyANyZqaFSqZRgRucsA1v7X6VSYdKkSZg0aZJT/Y9kpaUdVY0SEztemy7ZDub9NGx815kHNWpqahAfD9zy0ACsgak/habutEWWhCuZ79duS1i++GLHRSzFjBmmftzvvQfMmtWxWk7OcwBqAJQpfTXUjbpelYDqnKnRpfyUtQyTsWOBGTNMl0tKgLPOAs47D3j/fUCvd3os/Zm134n5+fkYOnQoVq9ejd9++w0ajQa33357t4+j1+uh0+mUf3LPHlEUvfJPgMR//AdAgiQBEv/r9/9561jEfzye859r/vF4zv8keP9Ybq8NGzagvr4ezz33HAYOHIjly5dj5cqV3d7nrbfewrRp07B48WKMGjUKS5YswerVqwGY/gaWs6e1Wi3++c9/4m9/+xsA0/zLpEmTlNujoqIQEBBg91hdxX0zXtSt3k7su4sjTcIBYMKECfjuu++wefNmbNy4Ed999x0SEhK6TGSZi4yMBGDadmdKEMmZGgCQm5uLVatWoby8HKGhoSguLu7xD+CeDBw4EAaDASUlJd0GZ1xBruUfExOD999/H2PHjgVg6qeRlJTUq7ILmZmZEAQBJ06ccEnpJFc5efKkS0pPyeLi4pCfn++yx+ssLy8Po0ePdsljpaeno6amBk1NTQgLC3PJY1pz4MABTJo0yW2PD0Ap1VRcXNyrMmkyR4MacqmgsrIypKamYseOHRg3bpzDwbKYmBgEBgb2+Fn3dFDj6NGjGDhwIFQqFQIDA/H0009j4cKFEEURl112mbJeSEgItFqt1aDGqVOnkJSUhPPPPx///e9/UVBQ0G0WnTk5UGGrpwYA5XOxd+/eLuvV1NQA6Hq2vjNZitXV1RBFERqNxmZPDVcENQDT+6qkpMRmpoa1oEZ3ZcvsZd4kPDq6mxJOcj8NwGamRmhoKIKDg9Ha2qoEfO+9Fzj38b9ifvMaqCFC+sc/IPz+98CZ72NXsSuosX8/sHEjAOAIBuMbXIYHzpSeCggA1q4FVq8G0tOBV145CFMyZlnXZuE9BCJt6ZypUVnZQ/kp2T/+Aezc2dHTY+tW07/77gNuuw14/HGgF4EtAhYuXIiFCxcq11999VVkZWVBp9PZfD+tWLECjz32WJflp0+fVjKvPCkhgEGu/k6AhPZ2g3KN+rc6K5m05B94PCcez0nmzWO5/PdcQ0ODxd9aQUFBXSpN7Nu3DxMnTlROyM3Nze1xrmzfvn0WJ01OmDABjz/+eJf1Pv74Y6SkpOC8884DYOrbeejQIbz00kuIjo7Gn//8Z9xzzz3ObWQvMKjhJb5afurw4cMIDw/v9uzcziIjIzFr1izMmjULTU1NaG1t7XZiUQ5qOLvttbW1yvjkhuD79u1TAgDONgmXyZN+J06ccHtQo6ysDCqVCnfccQeeeuoplJSUIC0tDUePHu31RHFwcDCSk5N9MlNDTllzhbi4OLdlajQ1NeHEiRMWkyy9YR4IcEU5K2t0Oh1KSkrc2k8DMH2OIyIiXJZ54mxQo6KiAjqdDocPH3Yq3VGlUuH//u//egxcyceXBjeV7ens6NGjFsHl1NRUPPvsszh+/LjFmfiAqUSRrfJT2dnZGDduHIKCgrBlyxaHghqhoaHdBlYjIyMxcOBA7N69G1dccYXFbXKmRueghjOvoxywSUtLc2umBtDxvrI3U8NVzIMaWm1HgEoO4CvsyNQATK97WVmZcmxsa9Mh31CJ1fgdbsa7EGprgX/+E3jkEReMvoNdQY1OvTQio1Q489sYgKlNyKJFpsvvvht+ZmmZUn4KQK+CGtZ6anTbKFw2bBhw7JgpneSVV4C8PNPyykpg+3YGNNwgISEBoiiivLzc5vvpgQcewH333adcLy0tRU5ODmJjY7uWb/OAynaWy+rvBEgICNAgMDAILkpKJj/mjeMQuQaP58TjOcm8eSyXT8jKycmxWP7II4/g0UcftVim0+ks5jAFQYBarbaodtNZ5/totVqUlZV1We+ll17C//t//0+5fuzYMdx8881YuHAhfv31V9x4440YP348Jk6c6Ogm9opfBTXy8vJwyy234NixY1i8eDGeeeYZl5Ww8bTAwEAEBwf7ZKbGkCFDnH5dw8LCejwDXQ5qmNfedoR5pkZkZCQyMzPx22+/ISIiAjExMQ4FZKyJjo5GdHQ0jh8/josuuqhXj9WT0tJSJCUlYebMmXjttdfw4Ycf4s9//jOOHDmC2bNn9/rxs7KyPNYfxB4Gg0E5s95V5PJToij2qvyLNQcOHIAkSS7LdPFEUEOOxLs7qCE31nZVs3BHgxqBgYGIjY1FeXk5du3aBUmSMG7cOKee+6qrrupxHfm45olMDaPRiBMnTmCGXO7mjHHjxlndxsTERJvlp8477zwEBQVhwoQJ2LJlC2688cYu61kjNwnv6btg7Nix2LFjR5flclAjslMmgDMBfXnbMjMzrQZvANf01ACgbHPnnj+2ghqSJCk9feLi4pz+7pT7aZieq+NHZJeJXDlTIzoaONMPxprY2FiUlZUpGTMvvvgi2tqMeBwP4wa8Bw2MwHPPAffcA3TX+8JBcrBKpVJZ3xf19aagAAAdIvAObsbM6YCtSnny627K1DA7Dlc43yy8c6ZGTY1ZpkZQEKA8p9UBAXfeCdxxB/Dzz6bgxiefAL//vdPjoQ7Lli3D2LFjlQbiW7duhUql6ja7s/NZavKxRaVSufw3gT0knslJAAABggAIfD/0ewID3n6Lx3My4fGcvHssl3/P5ufnK/1AAVjtB6rRaLosDw4ORnNzs82gRuf7yOubO3DgAI4dO2Yxb3LgwAHlcnp6OjZs2IC1a9d6PKjhN9+yzjQ88XWRkZE+malhb+kpZ8l/gDq77eZBDcCUmbFv3z7s378fo0aNckmgy1PNwsvLy5GSkoKgoCBce+21+OKLL1BUVITTp0/3qp+GLCsrCwUFBS4YqWuUl5dDFEWXlp8aOnQoRFHEr7/+6rLHlOXl5SEsLKzHfgv2Cg8PR0xMDIqKilzyeNbs378fWq3Wpa+xLenp6S7N1HC0b0lycjLKy8uxY8cOpKam9jqg2R2VSoXQ0FCPBDVKSkrQ1tZm9zEgMTGxy2R/W1sbqqurlcyDCy64AHv37rX7uFteXm7X6zl27FgUFRUpk+eympoaREZGQq1WWywPDQ2FSqVyOFMjODgYCQkJbs/UuOiii3Dbbbd1qQcaEBCAwMDALvvfaDTi+++/x/fffw+j0ej085pnagQHd3ymLDJlTp82Nd8ATFka3XzXydk8ra2tqKiowHPPPQdgKwqQjVW4xbSSTgc8+6zTY7ZGfn9ptVrr38X/+Y/SD+PfuBGNiMDMmbYfr2P7rWRqOKnbRuEJCd2+rgpBACZPBj76CCguRrcbQXYbPXo0HnroIfzwww/49ttvceedd+LGG290SU8rIiIiIiJ/FRERAa1Wq/yzFtSIiYlRelHKGhoaui3J2vk+1tb/8MMPMWfOnC5/25tLSEhQyut7kt8ENZxpeOLrIiMjfSpTo7m5GSUlJS6pj98TZ7e9tbUVra2tFlHG0aNH49ixY0pQwxUGDhzokaBGaWmpUov+mmuugSiKePrppwHAJfshOzsbZWVl0PtII1P5rH5XZmqMGDECgwYNwieffOKyx5Tl5eVh5MiRLj3b05WBAGv27duH3Nxcj2SxpaWluTSo4eiEdEpKCsrLy7Fz506MHz/eJePoTnh4uEeCGkfPnLY/aNAgu9ZPSEjokqkh/zCRgxqTJk2CKIrYtm2bXY8pB1x7IvcB2rt3r8Xy2traLqWnAFNwKDw83OGgRmJiIkJDQ93eU2PQoEE2+zLZ2v/h4eFmGQXOkYMaoaGAJHX8GLQIatjRT0Nm/to/8sgjZzJnTPv+b3gI7cKZoM0LLwBnMk1cwTyoYdW77yoXV+EWqFTA9Om2H6/jdS237KnRi0wN8/JTGk0g6mvFjqCGrX4a3UlONnU5p1674YYbMG/ePMydOxcLFizA9OnT8fLLL3t7WEREREREPm/8+PHYunWrcr2goAB6vd7q3+W27rNnzx6LjBAA+M9//oM5c+Yo14uKijBp0iRIkqQs27p1KzIyMlyxGQ7xm/JTzjQ86cxoNPbqTEpXCw8PR21trc+M6e2334Yoihg2bJjNMYmiqLyO5m9gR0VERDi17adPn4YkSYiIiFDuO2LECBiNRrS0tCiXeysjIwNr165Fa2trlzN2Xam0tBQXXHABjEYjIiMjcdlll+GLL75AUFAQkpOT7d4WW/slLS0NoijixIkTHglW2XLkyBF89tln2LBhA6KiohAfH+/S9/3s2bPx7LPPoqKiomtjXSdJkoTffvsNV199tdNjtbZfUlNTcezYMbd87o1GI/bt24dbbrnFI8eVAQMGoKamBvX19b2e1G1qakJsbKxD405ISMD//vc/NDQ02L3NvTmGhYWFob6+3u2v7eHDhxEbG2txnOuOXIKtpaVFOauitLQUkiQpn7W4uDgMGjQImzdv7rGsniRJKCsrw/Tp03t8/tjYWCQnJ2Pnzp2YMmWKsry6uhqRkZFW7x8eHo66ujqL27rbL2VlZYiPj0dQUBCam5utPmZTUxMkSUJAQIDb9k9YWBh0Op3F4wuCYNFPxJnnbm8Hjh83XR48GNDpOgL+oaGhHY9p6phtMnIk0M1zmQf+33jjjTOX6gH8hmKMwBvSrbgTrwNNTcBTTwFngumdOfp5kYMaVt+7R48Cv/wCANiPEdiN0ThvohFRUbY3peMM/WbUBUbCKCdZlJd3u/3dMc/2MRpDESGdhgAjjAAQF+f043qS+X7xd53fVytWrMCKFSu8NBoiIiIiIv80efJk6HQ6rFq1CrfccguWL1+Oiy++GGq1GnV1dYiIiOiSbTF37lxMmjQJ9957L7KysvDiiy/ihhtuUG4/ceIEioqKlAbhgOlE3crKSjz44IO45ppr8PHHH2Pbtm146623PLatMr8JajjS8ESv11ucmS7/kf3111/7VAp7dXU1SktL8eWXX3p7KPj222/xzTffYObMmcjPz7cZMJIkCfX19YiMjOzVmeB1dXXYtWuXw9t+8uRJ1NfXY9++fUoDVEmS0N7ejtbWVhQWFlptauOo0tJS1NTU4N1331XOdHY1vV6PoqIilJSUKK9DUlIS6uvrkZaWhvXr19v9WLb2S0tLC+rr67FmzRqcddZZLt+Gnhw6dAhff/01SkpKEBERgXPOOQcTJ050aNvsIQgCmpqa8MQTT+DSSy91yWPW1NSgsLAQOp3O6c+otf1SW1uLvXv3Yt26dS7PpigtLUV5eXmvxuyIkpIS1NfXY/Xq1b0ud3X06FE0Nzc7NO6ysjKcPHkSgGl/2XPf3hzDamtrsWfPHre/tt999x00Go3dz3Ps2DHlcy6fhbFz507U19dj165d+O1Mc+moqCh88cUXGDduXLfb3tzcjLKyMhQWFto1hrCwMHz11VcWpQv37t2LkJAQq/fX6XTYuXOnxW3d7Zfdu3cjKSkJ+fn5KCsrs/qY//vf/9DU1OTyY4s5Z7+3elJWBoiiKRssPFzCsWPHlNt+/fVXHDmTxiF89ZVSzVesrwe6GYftnlVvALgMD2MiUtUroTIaIL38MqTzzrOabeDI50U+uQAw9U/q/DoJ77+vjP9tTACwAQMHit1thkXGZEXIPqw/E9SQ9u2D5OR+MO8Bc+RIMcKwFvK7Rmprc/pxPUneL+486YKIiIiIiPyHRqPBW2+9hQULFmDZsmVQqVT48ccfAZhOetuzZw/GjBljcZ/Ro0fj3nvvxbhx4xAcHIzBgwfj7rvvVm7ftGkTxo4da1ERQRAEfPzxx7j11lvx3HPPYcSIEfj2229dUkLfUX4T1HCk4cmKFSvw2GOPdXkMnU5nUXbA21QqFerq6rxegmrz5s344YcfcNFFF+Gss87qdjyiKKK5uRmSJPWqJI9Go0Ftba3D237q1Cnl7ETz+yYnJ6OhoQEtLS02a647Qj479vjx471uPGuLvC2BgYHKtoSEhGDUqFGIjY116LXpbr+EhISgsLAQAwcOdOn4e1JdXY2VK1ciOTkZ1157LYYOHaqMzR3v+WHDhmHLli0YP368S8pFHThwAEajEVFRUU6P19p+CQ0NRVNTE8rKynqd3dCZ3KxJq9V65LginxVfWFhou9yMnZqamrp8ru19/oSEBLvv25tjmKeO2cXFxRgxYoTdz6NWq2E0GlFSUqKceVFWVtalyVdqaioaGhqQl5enNK23pry8vMuxqTtJSUn49ddfsXPnTuWHTG1tLcLDw63eXz4hwfy27vZLdXU10tLSlEnz2traLuvU19dDpVK5dd/Ij+/q5zh6NACA6VgQE9OKY8c6SnO1tbUpzxd+/DjkKWxdVBSkbsah6dR5W6VS4eyzz8aOHYcAXIYaxOJk4likl+2AoNdDV1AA0UqWmyOfl6amJuVyQECA5eskitD+8APUAIxQ4UeYsnqGDm1Efb3tbAPzs/hbgjq2qe1UNZqd3A/m42ppUUOLjuv6oCC0+FBZUFvk/eKJMoNEREREROQfrrzyShw/fhy7du3CxIkTlV6L3WXdP/nkk1i4cCFKS0sxZcoUi54aixYtwqJFi7rcJzc3Fzt37nT9BjjIb4IaMTExyMvLs1hmq+HJAw88gPvuu0+5rtPpkJaWhmuuuabXE2+uVFtbi++++w4LFizwyPMVFRXh+PHjSE1NRWpqKkJDQ/Hee+9hz549WLZsGW677bYeH0MURVRVVSE+Pr5XE8eFhYU4fPiww9u+YcMGfPXVV7jlllssIoXTpk2DXq93aQ23tWvXIiMjw237Z/PmzYiJicGtt95qUTLJmefrbr/88ssvCA8P99j7DDDVLF+0aBFGjhyJd999t9d17u0xevRo3HrrrcjIyMD555/f68crLy/HqFGjsHjxYqcfw9p+OXHiBL799luMHz++S5S8tw4cOIBJkybhd7/7nUsftzv/+c9/kJ2d3ev310cffYTzzjvPocc5fvw4vv76a8yZM8fu+/XmGJafn4/q6mq3fpYaGxvxwgsv4Nprr7Uoa9Sd5uZmvP/++zjrrLNw2WWXATAdY5ubmy3GKooiNm3aBK1W2+02/PTTT8qxSf4R1J25c+dCFEV88803GDt2LGbNmoX33nsPU6dOtfo8+/fvh06n6zI2a/vFYDDgxRdfxIwZMxAaGootW7bg6quv7pJ12djYiOLiYrfum3379nV5TY1GI7Zs2QIAOP/887ttnmaLqR2KaZtnzxbx6qvPKLfdfPPNpolrUYRw110QAEhZWZBuuaXbx2xtbcVHH32kXL/hhhuQm5uLHTveAGB6XyUF/YArYMpaEEePBszKh8kc+bwUFRUpl4cOHWq5L374AaozzeQ34DLU4QakpUlYtkzqti93WFgYXn/9dQBAXPK5OLcyEtGoh75dRICT+1qtVit9GjIzpyIMKsifNPGCCwAPflc6S94vQUFBFmdSERERERFR/5aUlIQZM2Y4dJ+cnBzk5OS4aUTu4zdBjfHjx+PNN99UrnfX8CQoKMhqJ/iAgACfStWPjo5GY2Ojx8b03HPPYfv27RbPX1tbi0WLFuGuu+6y64w/URSh0WgQEBDQq6BGTEwMGhoaHN72xsZGBAcHWzZPBXpd+saawYMHo7Cw0G37R56QSEpK6nVmQXf7ZeDAgdixY4dH3/uvvfYaCgoK8M4773TZV+6Sm5uLYcOG4YsvvsC0adN6/XgHDx7E6NGje/W6WdsvWVlZUKlUKC8vd2lza7kHyOWXX+7RfZ2RkYHy8vJeP2draysiIiIcepzMzEwkJibikksusft+vTmGRUZGoqSkxK2vb3FxMQRBwPDhw+1+nsjISERERKCmpka5T3V1NQYMGNDlMSZOnIjdu3d3+9jysSkhIcGu1yggIADPP/88nnrqKSxfvlzJwkhISLD6PNHR0aioqLC4zdZ+qT7TxDo1NRUGgwGCIMBoNHZ53Pb2doSGhrp130RGRuL06dMWzyEIgtKUXaPRdMmQ6ElrK/D22x3XR45UK83ItVptx4kbx48DctbN6NFAD9uZmNjRVFutVuPhhx8+cybNUZh6a8Rgd2UW5srrlJdbfcwePy+7dwOvvALcdhtazTLPoqKiLPfFBx8oF9/FLQDUmD0bsHJeigXzTNyQEB2qkYQ41COw+hQ0Tu5rURSVy0ZjJBJRCDkUpU5O7vG19QXm+4WIiIiIiKg/6n2NFg8xb3gCwKLhib+KjIxEY2Ojxxo9lpSU4Nprr8WqVavw+OOP49prr8VDDz1kd0DDlSIjI5VeJ46oq6vrUm7MXc4++2xs2rQJzzzzDFpbW13++GVlZS4JaPQkOzsbxcXFMBgMbn0e2a5du/Dvf/8bd911F4YNG+aR5wRMk4tz587FL7/8glOmU5+dZjAYcPjwYYwcOdJFo+sQGBiI5ORki7OaXaG8vBxVVVUuz/7oSXp6OoqLi3v9OC0tLQ6XegsODsb69es91i8mIiICDQ0NPa/YC8eOHYNarUZmZqZD90tISLB431dUVFjtB5STk4OjR49aTOx2Vl5e7vCxSa1W48EHH8Sdd96Jf/3rXzAajVZPOgBMr6O9x//KykoAprNN5IwvayUGW1tb3Z4RFh4ergQcZCqVCueccw7OOeccp47lDz8MHDpkunz22cC4cVDeYxYB4TN9UQAAo0b1+LjmPchuvPFGDBw4EMnJyQAkAFsBAPlNZiXInPkMG43AnDmmqMz06Wg+09+my9gbGoCPPwYANGqisA6zAADz5vX8FOYl+gICqlAB03ta09JoanLuBPMypHp9KOJR1XGjlRJcRERERERE5Hv8JlOju4Yn/kouhdXQ0ICoqCi3PpfRaMSpU6cwePBgjBo1CqPsmBRxJ/OAjiOBqbq6Ore/VrJbb70VERERePHFF7Ft2zY8+uijyM3Nddnjl5WVYcCAAS57PFuysrKUevvmE13u0NDQgIcffhhjx471aAkk2fTp0/HPf/4Tn332Ge644w6nH6e0tBTt7e1u60OSlpbmkkCAuX379gEwleHypPT0dGzevLlXjyGKItra2tzWv8ZVrE1qu9rRo0eRmZlptbRidxITE5UAgCRJNoMaQ4YMgV6vR1FRkc3jQUVFxZkJcMcIgoDFixcjISEBzz77rM3H12q1dgeH5EBNYmKi0h/EvE+IzJtBjezsbKce73//A/7xD9PlwEDg3XcBlcpGUGP//o7LdnwP5ebm4plnnkFhYSGWL18OAGb7dCuAGShGL4MamzYBcnC2vh77f7cBwPsATuL06YSO9dauVbJM3jMugB7BSE0Fzj2356cwD2qo1adwCh0ZKDh1CnDitW9ra1Mu6/WhSEBlx40JCVbuQURERERERL7GbzI1gI6GJ++++y4OHjzol/W+zEVGRgKAUxkLjjp16hREUURKSorbn8se8rY72nDVk5kaKpUK8+bNw4cffgitVovFixfjX//6V7cNdhxRVlbmkf0hTywWFBS4/bn+/ve/o6mpCY8//rjbM1CsCQ0NxeWXX47PPvusVxlQcsChu2bKvZGRkWE1U+Prr792Ohizd+9eZGVlKZ8tT0lPT4dOp+tV82T5zHt/CWq44hig0+mwfPnyLq/b0aNHMWjQIIcfzzxTo6GhAc3NzVaDGkOHDgUAHDlyxOZjlZeXOxXUkF155ZX48ccfbQY15IyX7rJFZBUVFQgNDUVYWJjy/rCWqdHS0uKVoIazmpuBm28G5LfS448DI0aYAlLyc9jM1LAzuL5s2TK88soryjGh4/3wPwCwDGo4kTlW9tS7FtcXVL+JZEwF8Ge8996N2L37zA3vvKOs87Z0MwDguutMAZyemAc1BKG8a1DDCeaZGq2tIczUICIiIiIi8kN+FdQAOhqe2NO81NfJEw11dXVuf66ysjIA8Jmghpyl4mhAx5OZGrL09HSsXLkSv/vd77By5UqUlJT0+jElSUJpaalHMjWio6Oh1Wp7HdRoamrqtqxTaWkp1q9fj6VLl1qdTPWUmTNnoqqqCvn5+U4/RnFxMYKDgy0auLtSeno6Tp482WVS94MPPsDu3budCsjs3bvX46WngI7AT2/KaflTUEMURZeUo/v222/xySef4IUXXlCWSZKEY8eOYfDgwQ4/nnmmRkVFBQBY/RxqtVokJSXh8OHDNh+rrKysV0ENAN2WNNRqtZAkCU12lA86deqUsh3y+8Pa69/a2ur29094eDhaW1stSvlJkoTa2lrU1tY6FOz6y1+Ao0dNl885B/jjH02Xm5qalMdRghqS1BHUCAkBnMwgi4iIQFhYGIDtAIyoRhxahDOvmYOZGuVHGhD5wycWy0LRgr/iiTNDVuGJJwDk5QFnMrmKQ4dhB0x9hOwpPSWPWSaKJ5XyUwCcDmqYZ2q0tDCoQURERERE5I/8LqjRlzg7sW9NZWUl3nzzTZuTKmVlZRAEwauTzeZ6k6nh6aAGYKoXP3euqaVqaWlprx+voaEBTU1NvZ44tIcgCMjOzsaJEyd69Thvv/02fve731lMCJn7/PPPERYWhiuuuKJXz9NbcqCotrbW6ccoKipCenq623rNpKeno62tzSJIVFhYiPz8fGWS1BE6nQ7Hjx/3SlAjNTUVAHoV7JPLCfl6UEOeYHXF2fqbNm1CWFgYvvjiC+zatQuAKRjR1NTkdFDj9OnTMBgM3QY1AFMJKltBjZaWFtTX17v12GReerEnp06dUppe+0JPDcCy/JXRaMTXX3+Nr7/+2u5g5ObNgBzLCgoyJTLI/cXNfw9ERESYalRNndoRARkxAuhFLzHTfm2CSnUAgIAi6Uy2RnFxR9pID4xG4N0rP0YYTK/DlqRrIJ35bNyG1zAIPwMAdn1WjLZLZyj3e7XlZgACsrKA8ePtG68pCGNiMJRYZmqceZ87yjKoEaSUnxKDggGzzBAiIiIiIiLyXQxqeJEry0/9+OOPeP3111FVVWX19tLSUsTHxztcp91dnA1q1NbWeqz8VGcJCQlQqVQoLy/v9WPJmTOeyNQATCWoepupUVlZiZqaGqxfv77LbUajEevWrcPll1/u9onFnsgTpr3JgCouLnZb6SnAVH5Kfh6Z+et6+vRphx5v/5l6+57upwGYSn7FxcX1qkeIfOa9rwc15Ent3jYL1+l02LlzJ37/+99j9OjRePLJJ9HW1oajZyaunQlqJCQkQJIkVFVVoaKiAhqNxmaj7mHDhuHw4cNWg+CFhYUA3Htskl9He777KisrlaBGaGgoAO/21AC6BrVCQkIceu/ed19H/ODJJ4Fhwzpuk99bowA8unMnMGmSkukAAJg925mhK+RglShuAWBWgqqpCbAzmPrEE8CEw/9Wro9adR+EM6kmGgCPYzEScArf4RIElpuOC9UDcvGKdDcAU+kpe+PFgYGByu+WpqbTaArvfaaGefmppqagjkyN+Hj7B0ZERERERERexaCGFwUGBiI4OLhXtehl8pm58oRUZ72tke5q8sSzI9suiiLq6+u9kqkBmJrVx8fHK6+1NR999BE2bNjQ42N5uhxYampqr4MxcvbA+++/32Uy9JdffkFVVRWuvvrqXj2HK2g0GoSHh/fqc+XuoEZycjI0Go1SskkURaxfvx6TJk0C4HhQY+/evYiNjfVYkKyz9PT0XmVq+FP5KaD3mRpbtmyB0WjE1KlT8Ze//AVlZWV45513cOzYMURERDhV9kye+K+srERFRYUShLVmyJAhqKursxoE37FjB4KDgzF8+HCHx2AvRzI1KioqlG0LCgqCIAg2e2p4ovwUYLn/NRoNZs+ejdmzZ0Mjp1t0o7YWOJOYg5wc4P/9P8vbGxoaMBnADgBjzLMChwwBPvoIePDBXm1Dx+8AK3017AhMbtwIvPNYES7EJgBAS+pgRF42EbjvPjSeef0X4Ah+UZ2PoTD1bWnLGIzb0r9FI0zZHPaWnpKZv+5SYkemhlTe+0yN5kYN4lANABASWHqKiIiIiIjIXzCo4WVardalQQ1bde091b/BXgEBAQgNDXUoS0VuLOutoAZgmhDqKajx97//3eqZxOZKS0sRGhrqsabOycnJaGho6NUZ5rW1tRg0aBAKCgqwdetWi9s+++wzDB8+XGlC7G2RkZFOZ2o0NzejsrJSyaZwB5VKhbS0NCW7Yc+ePaioqMDvfvc7AEB1dbVDjyf303BXuayepKen96qnhr+Un3JVUOPHH3/EiBEjkJCQgOzsbNx0001YtWoVfvzxRwwePNip/ZiQkADAVK7JvA+FNd01C9+2bRvOOusst2b12RvUaGtrQ01NjRLUEAQBwcHBNntqeCtTwxE7dnRcvuiirpWkmisr8Q6AIHlBairw1lvAgQOOpTjY0PG+MB3DHQ1qLF0K3IDVyvWQO28yjSkiAl+alb8bJB478/hpWJTxPT7fZtqHgwcDjlbJM3/dAwZ0BDXaT/Y+U0PT0AgNTGXDhDOfISIiIiIiIvJ9DGp4WWRkpEvKT/lbpgZg2nZHAjryJLW3gxq2Mh6MRiPKysqg0+nw8ccfd/s48v7w1CS0vO97k61RW1uLKVOmICcnB++9956yvLKyElu2bPGJLA2Zo+8tcydPngQAt2ZqALAIanz11VcYMGAAzj77bERGRjqUqdHW1oYDBw54pZ+GTM7UcKRRsjn5zHu5vJCvckVPDb1ej//973+YNm2asuzWW29FUlIS8vPzMWjQIKceNywsDKGhoUqmRndBjaSkJGi12i59NfR6Pfbu3YuJEyc6NQZ72Vt+Ss4kSTQ7Oz8kJMTrPTV6s/+3beu4fM45XW9Pe+UVZJ25XJyRYeqlsWhRR9ONXur4HXACkZGtDgU1amqAAwck3IiO0lO44Qbl4pcDBsA8tFkpJOBifI/3Nqcr5bbmzXM8LmP+uQvJ6Ag8tJf2vlF4SGNdxw1sEk5EREREROQ3GNTwMk9karS1taGqqsqnMjUAx8+m9/WgxqlTp2AwGJCeno7Vq1dDr9fbfBxPZ87IZa6cDWpIkoS6ujpER0fjhhtuwPbt25WzvNetW4fAwEBcdtllLhtvb0VFRTn9uZI/Q+7M1JAfv6ioCHq9Hj/88AOuuOIKCIKA2NhYh4Iahw4dQltbm1f6acjS0tLQ3NyMmpoap+4vn3nv7X4sPQkJCYFKperlpPY2tLa2WgQ1goKC8MADDwCA09lOgiAgMTHRrqCGIAhWm4Xv2bMHbW1tbg9qqFQqhIeH9xjUOHWmZ0LnoIa1TLiWlhavBDWMRiO2bNmilBTriXlQo8vL/OOPyDrTW6cZwKYbbwRcvE3mJzdkZJQ6FNTYuxeYiG0YgjNNy6dNA8yOkzVNTfh/AIwAxLg4fHbXtziKIRaP4WjpKaDjdW9qakJ8aiBqYOqrJZxyrvxUR6aGCmHNZscsZmoQERERERH5DQY1vMwVmRoGgwHV1dWIioqyGtSoqKiAJEke699gL61W69C2yz0dvBnUSEpKQmVlpdXJK/kM/2XLlqGurg6ff/65zccpKyvz6P6IiYlBYGCg00GNlpYWtLW1ITo6GhdddBGSkpLw/vvvQxRFfP7557j00ksRFhbm4lE7LyoqyunyUyUlJYiMjFRK5LhLeno6ysvL8cMPP6CpqQlXXHEFACAuLs6h8lN79+5FSEiIV0t/WWt87ojm5maoVCoEBAS4clguJwgCwsLCehXU2LRpE7KysroEzSZMmIC3334bl19+udOPnZCQgNLSUlRVVXUb1ABgNajx66+/Ij4+HllZWTbu5TparbbH8lNyUCPBbLLZWqaGJElobW11e/mywMBABAQEWOx/SZJQUlJiV6aSJAG//mq6HBcHZGeb3djYCNx6q3L1fgCiG/aDeVAjMTHfMqjRQwm5PXuAm/Bux4KbbrK4vaGhAZ8BSANgOHgQ1z05GuaH0REjgJEjHR+zHNSQJAlxcW2ogOm9HVDT20yNqI4m4QAzNYiIiIiIiPwIgxpe1psyObKqqiqIoogJEyagvLy8S71xTzeltpejZ9PX1dVBEASP9aGwJjk5GaIoorKyssttJSUlUKvVmDBhAi699FK8++67FrW7ZZIkeTyoIQgCkpOTlfeCo+QAQXR0NNRqNebPn49vvvkGGzZsQFlZGWbPnu26wbpAbz5XRUVFbs/SAEyBAFEU8dZbbyE3NxdpaWkA4HCmxr59+zBq1CioOxfn96DU1FQIguB0s/CWlhaEhoZ6rSeII8LDw50OahiNRmzevNkiS8Ncbm5ur3pZJCYm4sCBAxBFscegxtChQ1FaWmqxLb/++ismTJjgkf0QERFhV6ZGRESERVmykJCQLt9xBoMBoih6JNOn8/5XqVQ4++yzcfbZZ9tszC47dsxUwgkwlZ6yeJkfeAAoKAAAbAbwMuCWwKr5+yI8fDtOIrXjxh6Cknv2AFfiCwCAMTgUmDPH4nZ5f9YEBSEwLg5RUcA993Tc7kyWhmmc4crlqKhmnIIpcydA32QKBjmo43s5mkENIiIiIiIiP8Wghpe5ovyUfPa9XDKk89nSZWVlUKlUFiU8fIGj215XV4eIiAivTt7KE0LWMh5KSkqQkpICtVqNW2+9FadOncKGDRu6rFdbWwu9Xu/xIFN3pbN60rn01+zZsxEYGIgnn3wS2dnZGDVqlItG6Rq9aRReXFzs9n4aQEfPjuLiYsyYMUNZHhcX51BQo6yszCNBmO4EBgYiKSnJZk+fnrS0tPh8k3BZb4Iau3fvhk6nw9SpU107qDMSEhKULB97ghpAR7PwmpoaHDlyxO2lp2T2Zmp0/t6ylqnhyfJl1oIaQ4YMwZAhQ3oMashZGsCZ0lOnTgH//jewYAHw8ssAgDaNBrcCkNDRS8KVzDM1Wlp2ISoxGOVnMh+kHoIaR3fWIwWm7xBh/Dig0/jkoIZ5MOb++02bd801wB/+4NyYzYMaWm2jkqkBwPQaOqgjUyMaCTA7QYHlp4iIiIiIiPwGgxpe5oryU3I/jXPOdB3tXIKqrKwMSUlJXg0GWOPo2fS1tbWIjo5244h6Jk8Uyq+5uZKSEuVs++zsbEybNg2rVq2CKIoW65WWlgLwfOZMSkqK05kanUt/hYeHY/bs2Whra8Ps2bN97gx7+b3lTOPqoqIijwQ1YmNjERoaioCAAFxyySUWyx0pP9XQ0OCWyU9HZWZm9ougRkREhNNBjU2bNiExMRHDhw938ahMzMs09RTEzszMRGBgoBLU+PXMjPs51rpXu0FERESPgUdbQY3OPTXkIIcn3kO9CWrJ/TQuxne4Z/V4ICnJVMJpzRplnS/POw/Hz1x2x+c6NjYWmjNNxysqynHhhegoQVVeDpg10TbX3AxIR44q11VDh3RZx1pQIzwc+OAD4L//NV12hvnrEB6uUzI1ADgV1OjI1IhhpgYREREREZGfYlDDy+SzVTtPfDuioqICkZGRSExMtNpXo6yszOLsTF/haECnrq7Oq/00ANOkWVRUlM1MjdTUjlIet956K0pKSvDdd99ZrOetcmC9ydSw1s/kd7/7HS655BLMnDnTFcNzqaioKBiNRjQ1NTl0v/r6euh0Oo8ENQRBwMCBAzF58mSLScDY2Fg0Nzd3ORvdFp1O5/b+H/bIysrqF0GN8PDwHjMMrJEkCT/++COmTp3qtiCgHACIiIjosceNRqPBwIEDLYIagwcPRkxMjFvG1tnw4cPx22+/dfsZ9YdMDUmS0NDQgIaGhh6DqNu2ARq04z3cgMgjOy1v1GqB++/H+oEDlUXuCGqoVCqLjEPzoIYgScCZoHtnv/0GDJKOdCwYYhnUkF8HwPVls8wzNYKD6yyDGlZOMOgJMzWIiIiIiIj8H4MaXib3h+hNtkZFRYUySZGRkdFlYrG0tBQDBgxw+vHdJTIyUmlAbY/a2lqvBzUA68EBURRRWlpqEdQYPnw4zjvvPLz22msWk3BlZWXQarUWEzWekJKSAp1O5/BEP2B67UNDQy3q/cfHx2PFihU+MaHemfy5crS0mxwQ9ERQAwCeeeYZPPTQQxbLYmNjAZjKAfXEaDSiubnZZzI1Tp48affn2Zy/BTWcOVP/4MGDqKystNlPwxXkAEBPpadkQ4YMwaFDhyBJEn799VePlZ4CgBkzZqCtra1L0NfcqVOnumyLtZ4a3gxqGI1GfPnll/jyyy9hNBpt3q+lBdi3D5iBr5AoT6RnZpp6afz0E1BdDaxYAZ3ZY7vrcy2f5FBZWYnJkw2WzcJtlKDaswcYjI5MDQwebHG7Xq9XMiBcPW7z78rW1gY0R7iu/BQzNYiIiIiIiPwTgxpe5uqgRmZmZpdMjfLycp/N1ADsn3iuq6vzevkpwHpQo7q6Gnq9Xik/JfvjH/+IqqoqPP/888oyTzcJl8nvAWeyNXzltbeXHPyy9d5699138cILL3RZLvej6bwf3SU+Pr7LBGBcXBwA2NVXQ55c9YWgRlZWFkRRdKpZeF8NarS2tuLnn3/GU089hT/96U+IiorC2LFj3TY2ufyUvUGNoUOH4sSJEzh69Ciqqqo8VnoKMI114sSJ+OKLL6zefvDgQdTV1SE7O9tiua9lagBAQEAAAgICur3f7t2AwQDcjHc6Fv7rX8Dy5cDkycCZ+5tnAbnrcy2/PyRJQkREFRpjOoIabcetBzX27hUwBLYzNcx/w7gzU6OhoQFifEemhlTueKaGtfJTxsBgoIfsJiIiIiIiIvIdDGp4mfzHf2+ahXfO1CgqKlLKYLS0tKCmpsYnMzXkbbc3oOML5acA04RQ58DAyZMnAXSdDM/IyMB9992HTz75BJs3bwZgypzxZlDDmb4avtDPxBFywMxWzf5t27bhP//5T5fJ0eLiYiQkJHh1gl3O1LCnr4Y8AeoLQY3MzEwAQEFBgcP37YtBjWeffRYXXngh/vCHP2Dr1q2YNm0aXn31Vbf2NoqIiEBwcLBDQQ2DwYAPP/wQgYGBbg24WHPllVfit99+s/qeWbVqFdLS0jB58mSL5b4W1NBoNLjmmmtwzTXXKL0qrNm2DYhHJWbgK9OClBTArJeOzBNBDfOTHMrLyxE7piOoUfKLraAGlKCGJAiAWZkswHNBjcbGRgjJHe/v1iLXNApvj04AfKw3FBEREREREdnGoIaXOTqx35kkSV0yNZqbm5UJUXny3RuT6D3p6Wz6znwlqJGcnIyKigqL+uklJSUQBMHq63z11VdjypQpePzxx3H69GmvZWrExsYiICDAapPznvhbpkZPQY3Tp09Dr9fjl19+sVheXFyMjIwMdw+vWxEREdBoNHZlalhrzOst0dHRiIyMdKqvhr8FNezpqfHtt9/i/PPPxyeffILPPvsMy5Ytw5AhXZsru5IgCLj22msxZcoUu9YfPHgwBEHAhg0bMGbMGAQFBbl1fJ3J/WTWrVtnsfzEiRPYuHEjbr75ZqhUlj9TrDUK93amRk+OHz+ON9/8DQvxPgJgMC288UbASoBL/kwHBwf3mP3hrM5BjYHTOoIaNbuLuqzf3g7s/01Syk8JGRlAp/eKO4Mx5o/X2NiIwLSOTI22YsczD+VMDQGRiIPpt5IYy9JTRERERERE/oRBDS9ztva/rLGxEc3NzRaZGgCUiUVvNaW2hyNZKm1tbWhubvaJifXk5GS0tbUpzbMBU1AjKSnJoueETBAEPPTQQ1Cr1XjkkUdQUVHhlf2hUqmQnJzsVKaGrwSU7BUcHIygoCCb7y056PfDDz9YLC8uLvZYPw1bVCoVYmJi/K78FGAqQeVMpkZzc7NfBTWampogiqLNdeSmyePGjUN6errbGoNbc++999rdGyM0NBRpaWkwGAwe7achCwwMxBVXXIEvv/wSBoNBWf7OO+8gISEBV1xxRZf7+FpPDXssWbIEhw9H4has6lh4881W1/VE9lXnoMbYqzqOecaCrpkax45poG2rRhTOHE+tBOc8makRnp2AJoQCAMK2fg9UVdm6q1VypkYMgqCG6XOsSmBQg4iIiIiIyJ8wqOFlQUFB3U6+9kQ+614OagwYMABqtVrpq1FWVgaNRqPU6fclWq0WgiDYte3yGfe+MLFurTfFyZMnLZqEdxYdHY1HH30U27Ztg8Fg8Fo5MGeDGv5WfgowBQytvbfa2tqg0+mQkZGBn3/+WZkQFUXRJzI1AFNfDXvKT8kTif4e1GhtbUVoaKgbRuR64eHhkCSpSwkkc3q9Hm1tbT6zX7ojZ494sp+GuSuvvBI1NTX43//+B8BUnu/rr7/GjTfeaDVTISQkBG1tbRZBJXlfeCqo0dzcrDy/0WjEtm3bsG3bNpuNwnfsKMVYnEYu9psWnHsuMHSo1XU9EdQwL09WXl6OpJFxaBFMQUVtfTE6JyLt36/ptp8G4LmgRklJCZLSArASiwAAGn0z8NxzDj2enKmRYLZMk5JgfWUiIiIiIiLySQxq+IDIyEiny0/JE+vyRLtGo0FqaqpFUCM5OblLCQ9foFKpEBERYde2+3pQo6SkpMfm0ueeey4WLFgAAN0GQNzJWpNze9TW1vrEa++IqKgoq+WnampqAADXXXcdWltblcnU6upqtLa2ej1TAzCVCrMnU6OhoQGCICDMRxrcZmVloaioqNssBmtaWlo8MiHtCvJkc3dn68vHNDkTz5edddZZSElJweDBg73y/EOGDMHQoUOVElSrV69GZGQkZs+ebXV9OaPHPKjU2tqKwMBAj3zPyRPsTU1NAExZOQUFBSgoKLAoSShramrC6dMDLbM0brnF5uN7OlOjoqICEATUR5qOe+koxs+bLbdj//4ApfQUAMDKe8WdQY34+I4sin/961/YsGElnsGfoceZzMiXXwbOHNftIWdqJAgdQSh1EjM1iIiIiIiI/InvzXT3Q1qttleZGhqNBjExMcqyjIwMi/JTvlh6SqbVam32PTAnr+ML2QIREREIDQ1VggOSJKGkpMSuQMXSpUvxwgsveG3iPCUlxeGgRnt7OxobG/0yqGHtcyUHC8aOHYshQ4bg+++/BwAlEOgLQY24uDi7gxrh4eE+E7TMzMyEXq93uG+Lv5WfAuwLavhDpsY111yDtWvXevU9dNVVV2HLli04fvw41q1bh+uvv95mkEt+n5j31WhtbfVYUKzz/lepVBgzZgzGjBlj9TUsLCxEIMbhenwAAGiGGtK111p97La2NmXC3ZPlpwBAyDAd98LRhK3ray3Wz8sL6DFTw509NUaOHIk777xTuf7FF/9CKVLxNm41LWhsRPn9L+DgQcCe80NMmRoxiJM6MoGERGZqEBERERER+RPfmAnr52yVybFHRUUFEhMTLSZTMjMzLTI1vFXqyB72brvcv8IXJtYFQUBSUpIyGVRbW4vm5uYeMzUAICAgAJMmTfJojX1zycnJqKur69Jotzvy/vGFgJIjbL235LJOcXFxuOiii7Blyxbo9XoUFxdDpVL5RBAwNjbWrvJTDQ0NPjVxnpmZCQAONQuXSzn5U/kpAN02C/elBu49UalUVnsBedL06dOhUqnw17/+FUFBQbjWxqQ/YD1Tw5OZPtaCGsOHD8fw4cNtBjVmQUQsTJkEnyAAvxV1bcYNuDcwYC4xsaPRtvw9FjmqI5h75PuOvhqiCBw4oLHM1PBw+SlBEPDqq6/itddeO1OSzFRC8Sncj3ZoAAAhb76AiTn1iIwEEhKAKee24bUL3sfGRzd3eTxT4Oj/IR5mn+F4ZmoQERERERH5EwY1fEBvyk9VVFRY1McGTJka5eXl0Ov1SvkpX2XvttfV1SEwMNBnzuZOTk5WzkY/efIkANgV1PA2ecLekTPp5YCSPwY1rGUBnT59GiqVClFRUbj44ovR3NyMrVu3oqioCKmpqdBoNJ4fbCexsbGoqanpsYyTrwU1kpKSEBwc7FBfjfb2doii6DOf7Z7Yk6khT077Q1DDF2i1WkydOhWnT5/Gtddea9FDoTP5fWLeLLy1tdVj7x979r+5vXtP4WZsU66/g1Z88cUXVtc1D2q4870TGBiI2NhYAB1BjeDBHUGNliPFkOMuBQVAQ4OqI1MjIACwks3mzqAGYAps3HHHHfjxxx+RmCgAKEIxMvAubgIARKEe9+AlAEBoVSH+vu183LnlBkx+7EJ8/8IBi8fS60MBLEUCKjsWMqhBRERERETkVxjU8AG9LT/VOaiRmZkJSZJw8OBB6HS6PpGpUVdXh6ioKK9lOHRm3ptCDmr48usskwNcjjQL96UsGUfY6qlRXV2NmJgYqFQqZGRkYODAgfj+++9RUlLiE6WnAFNQw2AwdJsNAJgmEn0pqCG/po4ENTzZ5NkV7JnUlo9pvrRvfN28efOQnp6O+fPnd7uerZ4annr/yP1r5P0vSRKam5vR3NxstafG9q/VuAzfAgBKoMVGAJ9//rnVxzYPDLj7vWPeG0qSJItARTqKcfXVQGMjsGcPIEDsyNQYOBCwEvj1VEDmvPPOw+7dOwBcCuAvWBk/FEZBDQC4P/A5PDHsfewVxmICdgAANDDiwIPvw7yan053C4BIxKOqY2ECy08RERERERH5EwY1fIBWq3V5pgYApQGyL5TTscWR8lO+NKluHtQoKSlBXFycX5xpHhcXB41G41BQw5f6mTjC1nvr9OnTylnKAHDxxRdj8+bNOHbsmM8ENeLi4gCgx74ajY2NPpcNkJWV5VRQw1/KTwUHB0OtVveYqREaGuoTWT/+Ijc3F2+++WaPxxlf66lhNBrx+eef4/PPP4fRaOyyfsaeYgTAAABYF6yFBGDXrl1KMNycp8pPAR1Bjba2NtMxvlNQY88e4PrrgV27BAxAKUJwJjPGSukpwLMBmZSUFISHlwFYjvq4VVDfeAMAILytFg8dugFRUp3F+jOa/4N7lpgCTnV1QGvrHQCAeJzqWImZGkRERERERH6FQQ0fEBUV5VRQw2AwoLq6uktQIyoqClqtFtu2mUpe+HJQw94sFTlTw1ckJyejsbERjY2NKCkp8YvSU4DpTPqkpCSHyk/V1dUhICDAbyadZZGRkdDr9dDr9RbLq6urLYIaF110EZqbm1FeXq4EBL1NHl9PfTV0Ol23pXq8ITMz06GeGnJQwx+CgoCpDE54eHiPmRq+FmzqK7zdUyMoKKhLUEsQBKtZhGVlwIymnzvWm3excvnLL7/ssr43ghrAmRJUZkGNQQGm2lPr1gH//Ccsm4QPHmz18dxdfqozi+DSgw8CnfuZXHst2sZPAgAMwnEcWrMHn34KvPQSAEQBAFI0hzrWZ6YGERERERGRX2FQwwdotVo0NDTAYDA4dL+qqiqIomi1Z0ZmZiYOHjyIoKAgnz7DPioqCvX19VbLdpjztaCGHEgqLy/3q6AGYApyOVp+Kjo62mdKf9lLfr90DpqdPn1ayYQAgOzsbGRnZwOAz2RqxMTEAOg5U6OhocHnJs+zsrJQX1+vlC3rib8FNQDThGp3pcF8cb/0Fd7uqSEIAmJiYpSAo0ajwfz58zF//vwumTlfvVuNi7ARAFAkxGDi0nuU26z11fBqUMPsO+yiQcVKham2NsEyqGFHpoYn3vvy69PQ0GAa0403mm4IDAReeQX46CMELr5RWf86/Ad33gk8/7y8xIBk9ZnGISEhwJmyYkREREREROQfGNTwAVlZWZAkCceOHXPofvLZ9p0zNQBTCSpJkpCSkuLTk9FarRbt7e0WE1TW+GL5KaAjqJGamurlEdkvKSnJ4aCGL7329pLH3LmvRuegBmDK1gB8J6gREhKC0NBQu4Iavta3ISsrCwDsLkHlr0ENZmp4R0BAAFQqldd6agCm79ciuZN2N06/+V9oYCpJ9VNSCsaMHat8V/zwww9dAmOeDGqY/24oLy8HgoKAM8si64vxr391rKv00wBsBjXMx+6J7DHzTA1JkoBXXwXeew/Yvx+4+25AEICrr4akNvXbuA7/QWWlBDnWOgZPYpD+zJgzM90+XiIiIiIiInItBjV8wPDhw6HRaPDbb785dD+5p0NiYmKX2zLP/JHuy6WnAFOJIKDr2fSd1dXV+VTGSWxsLAICAnDkyBHodDq/y9SQ3zv28LUsGXvJ7y3zoIYkSV3KTwGmJsXLli1Dgg+VIImLi+s2qCFJkk8GNdLS0qBSqewuQeWPQY2IiIgee2owqOEegiAgJCTEoqeGJ8tPAfb1jSktBcYXrFWuF47PhiAIuPLKKwGYell8++23FvfxaqYG0FGCqrwci29sw5//bLrqSPmp8PBwqDqXgnID+fUxGAymEoMhIcDChZZBl/h4CBdeCADIRgHOxq4zNxjxKP7esd5dd7l9vERERERERORaDGr4gMDAQAwbNszhoEZFRQUiIyOtTgbKvQEGDBjgkjG6izzx3F1PEUmSfG5iXaVSITExETt27AAAv8rUSElJQW1tbY/ZMTK5/JS/sRYwa2hoQHt7e5egRlRUFObNm+dTWU2xsbHd9tTQ6/Vob2/3uaBGQEAAUlNT7c7UkCen/Smo0VOmhk6nY1DDjUJCQrpkanjy/ZOVlYXi4mIYjUYYjUbs2LEDO3bssGgUvn7VKUzFjwCAY4iGatw4AFCCGkDXElTmQQ13v3/MgxpKjyU5qCFJwMmTWLECeOYZEeO0h03LQ0MBGydKyN/hnnrfm2eDdPdZxHXXdVzEfwAAZ+FJXIUm08IBA4DbbnPLGImIiIiIiMh9GNTwEbm5uU4FNayVngI6ghrW+m34Emtn03fW2NgIo9HocxPrycnJyj7zp6CGeekse/hr+amwsDCo1WqL95ac+dC5/JQvio2N7TZTQ54A9cXJc0eahcvBNX8LanTXU4NBDfcKCQnp0lPDk5kamZmZaG9vR2lpqVI68tixYxa9oXSrPoYaIgDgP1Ah60zfnqlTpyoT8ps2bbJ4XPPgvlcyNQYO7Fjhs8+gUgF/XNqOxOZC07LBg01lnazw2aDG1VcDZ0pQ3RXzH9y2uBmP4omO2x98EPDge4eIiIiIiIhcg0ENHzFq1CiUlZX1WEPfXHdBjbS0NOTm5uKss85y1RDdwlamhsFgwO7du/HSSy/htjNnUXY+u97bkpOT0d7ejqioKJ87W747jgY1fK30l70EQUBkZKRFpoac+eBr7yVr4uLius3UkCfVffG9Z095HllzczMCAwM9UrLGVZip4V3WMjU8HdQAgMLCQqhUKowcORIjR45U3sMnTwJnn/iPsv5HOK30mgkKCsLQoUMBAGVlZTAYDMp6Xu2pAQA33NARtHj6aaCpCSgshCCP0UY/DVEUlc+Dp45H5s/TXYARsbHAxReb7lNThL9nPo9ZMG1PVUgIsGiRW8dJRERERERE7uE/s0h93OjRowHAoWyN7oIaGo0Gb7/9NkaMGOGS8blLWFgYVCqVxcTz2rVrceGFF+L222/HunXrMHToUKxYsUJ5jXyF/Nr7Uz8NAIiPj4darbarWbgoin4b1ABMZaXM31ty0NAfghr2Zmr4alCjoqLCou+BLS0tLX6VpQF0H9QQRZE9Ndysc08NTwc14uPjERoaqgQ1Ro0ahVGjRilBjQ0ryzAZmwEAhxGD3wAlqAF0lIU0Go04deqUstyTQY2IiAiEhYUBMAtqjBwJXHut6XJlJfDaa8DRnpuENzU1KVkqPpepAViUoNI+8ohy+bOcHFODdCIiIiIiIvI7DGr4iISEBCQkJGD//v12rS9JEioqKny+vFRPOp9N/9///hdPPfUULrnkErz33nv4+uuv8dhjj+GSSy7xuTO55dfe34IaarUaiYmJdmVqNDY2QhRFvyw/BaBLpsbp06cREhKC0NBQL47KPrGxsaivr0d7e7vV2+XsJl8NagBAUVFRj+u2tLT4xf4w112j8ObmZoii6JP7pa8wz9SQJMnjPTUEQeg2G6nxnbVQwTTJ/xEEBAcHW5yAYF6u8OTJk8plTwY1gI7vsLKyso7jzF//2pGt8cwzwN69HXfooUk44Lmght2ZGgAwezag0QAAhDN9TwoBbLGxPUREREREROT7fGuWuJ8bPXo09u3bZ9e6jY2NaG5utpmp4U+0Wi3q6+vxySef4Omnn8aCBQvw0EMPYdiwYT4XyDAnTwj5Uz8NWUpKil1BjdraWgDw20yNyMjILj01/KGfBtDR90PeB535ek8NAHb11fDXTA05eNGZPMErl9Yj1zPvqdHe3g5RFD2aqQGY3uMFBQWQJAltbW1oa2uDJEkoKQHGF3aUnvoPTiMjIwOCWS8K8++M0tJS5bKngxpyxkhDQwMmTpxoyhTtlK0h/OMfHXewkanhjaCGQ5kaMTFKCSrZ3wCo/ey4Q0RERERERB18d8a4Hxo1ahQOHjxo88xscxUVFQDQJ4IakZGR2LhxI5YvX47rrrsO9913n8UEkK/y56BGcnKyUn5KkiSsXbsWF110EXbv3m2xnhwQ8NdMjaioKIugRnV1tV+UngI6SmTZ6qvR0NCAwMBABAYGenJYdgkLC0N8fLxdfTVaWlo8PiHdW3KQz1rAyZczaPoK80wNObjh6fdQVlYWCgsLYTAY8PHHH+Pjjz+G0WjEsU0lOB+/AAAOIA4HYFl6Cug5U0OlUnkke+m2225TThzYvXs3xo0bhyeeeALtDzygZGsIZplutoIang7GAA4GNQCLElQnALwL+OSxk4iIiIiIiOzDoIYPyc3NRVtbGw4fPtzjun0tqFFeXo65c+di2bJlfhHQAEzZDvfffz+mTp3q7aE4TA5qVFRU4Pe//z2eeuopNDc3Y/PmzRbryZO2/hrUsNYo3N+CGrb6ajQ0NPj0xLk86dsTfyw/FR8fDwCoqqrqchszNdzPV4IajY2NXT6f02J/g3SmV8VHaFfWNddTUCMiIsIj34MLFy7Etm3bkJOTA8CU9fLwww/joqVLYbzmGot1pehoU9NtK3y+/BQAXHMNMHw4xIAALAVgABAQEOC28REREREREZF7MajhQ4YOHYrAwEC7moWXl5dDo9EgJibGAyNzr4suugg333wz/u///s9vAhqAqa76Nddc43cTsoApIFNTU4N58+ahsLAQL7/8Mi688ELsNa+fDlNQQxCEPhPU8KfyU9HR0RAEwWZQQ6fT+XRQY8SIEfj555/xzjvvwHimjr01/lh+yp6ghi/vG39n3ijcm0ENACguLsa8efMwb948qNVqYMYMCJWV2HLvvfg36i3WldkKanjjvTN+/Hjs3r0bDz74oGn8AH7++WdsnDSpo7cGYDNLA/CD8lMAEBEB7NuH7V9+ia/OLGKmBhERERERkf9iUMOHBAQEYPjw4XYFNQoLC5GcnOzTPSfsNXPmTCxZsqRPbIu/GDhwIABg6tSp+OijjzBx4kSMGTMGBw8eVCYJAVP5Ka1W67f7JioqCo2NjTAYDAD8K1NDrVYjOjraZlCjsbHRJ/tpyG677TYsWLAAr776KhYtWmSzabg/BjViYmKgUqlsBjVUKhXCzpytT65n3lPDW0GNAQMGQKPRoLCwECqVCiqVqiMoHxqKzQkJkN/xnYMaci8LwHamhicFBQXhySefxJo1a5Rl7+7YAcyb17FSN021zTMlfDZTAwACAtBqFshgpgYREREREZH/8s+Zyj5s9OjRPQY1RFHEDz/8gPPPP99Do6K+Zvjw4fjuu+/w2GOPKZNDY8aMgdFoxIEDB5T1amtr/bZJONBRNkun06G9vR319fV+k6kBmEpQ2eqpodPpLM5W9jWBgYFYunQp3nrrLeh0OixYsABr167tsp4/BjVUKpXNfSPvF38NBPoD8/JT8v89HdRQq9VIT0+3WWLNvJ9M56BGaGiokmUpBzVEUVQyDryV5XPllVcqx8x169ah7f77IZ35bErdlFk0z9Tw2Z4aZ7S1tSmXmalBRERERETkvzjr4mNGjRqFyspKnDp1yuY6O3bswOnTp3H55Zd7cGTU13QOVgwcOBDh4eHYt2+fsqyurs5vS08BHX0N6urqlP4g/pKpAZjG2l1PDV/O1JDl5ubigw8+wCWXXIKnn35aKRska25u9rugBmAqQWUrU8Mf9os/s9ZTwxvvoczMTBQWFmLPnj3Ys2cPRFFUbusuqAF0lKAqLS2FKIpoampSbvNWUCMwMBCzZs0CYHofbywvh7R9O2rWrAFuvtnm/bzdU8ORoEZ7e7tymZkaRERERERE/otBDR+Tm5sLAN1ma6xfvx7p6ekYPny4p4ZF/YBKpUJubq5FXw1/z9QwD2rIZ9X7U6ZGXFyczUwNX28Ubi44OBjXXnstJElCcXGxxW2tra1+2ZcmLi7OalDDX4JN/iwkJAQGgwHt7e1eKz8FdAQ1Dh06hEOHDlkNami1WqvHUDmo0dbWhurqaq+UcLJmzpw5yuVPPvkEyMlB25Qplv01OvF2Tw27y0+BmRpERERERER9BYMaPiY2NhYpKSnYv3+/1dtbWlqwadMmXHHFFX7VVJv8w5gxY7Bv3z5lcq6urq5PBDXq6+uV4EBfytTwl6AGAGRkZABAl94aLS0tXpmQ7i1bmRr19fUMariZnJXR0tLitfJTgCkDo6qqCpmZmRg2bJhScsxoNCrBu6ysLKvf1Z37aphPzHvzc33ppZcqQcbPPvsMRqOxx/t4IyDjbPkpZmr4lurqamRlZVmUccvLy8P48eMRHR2NZcuWQZIk7w2QiIiIiIh8FoMaPig3N9eiBJC5zZs3o7m5maWnyC3GjBmDpqYmHDt2DIApU8Ofy09ptVoIgoD6+nqcPn0agiD4VZBGDmpYm9TR6XR+FdSIiIhATExMl6BGXys/xUwN95PfL62trUqmRlBQkMfHkZWVBUmSoNVqMXbsWCWoUVpaCoPBAMCUzWGNnKkBmIIa3uhLYU1oaKjy+6Kqqgq//PJLj/fxxtidahQOZmr4kurqasycOdMioKHX6zFr1iycffbZ2LlzJ/Lz8/HOO+94bYxERERERP7EmROE1q5di4yMDKSkpODDDz9UlkuShKioKAiCoPz729/+ptz+yiuvIDExEdnZ2di4caNbtqcnDGr4oNzcXBw+fBh6vb7LbevXr0dubq7FWZ5ErpKTkwONRqME1fw9qKFWqxEREaEENaKjo6FWq709LLvFxcWhtbVVORtdJtfg97fJc7lcj0wURej1er8sPxUfH4/a2lpl8lrGTA33M8/UaG1tRVBQkFcas8vZR+b9Mzpft9ZPA+ga1PCVTA3AsgTVp59+2uP63ig/FRgYCI1GA4CZGv5q/vz5uP766y2WbdiwAfX19XjuuecwcOBALF++HCtXrvTSCImIiIiI/IczJwjl5eVh4cKF+Otf/4pvvvkGDz/8MA4fPgwAOHr0KKKiolBbW6v8W7ZsGQDgm2++wZ/+9Ce88cYbeO+997B48WKbVUbciUENH5SbmwuDwdClr0ZNTQ22bt2KK664wksjo74uODgYw4cPx969e9Ha2gq9Xu9XmQ3WREZGKj01/KmfBgAkJCQAACoqKiyWy5N43p78dFRGRoZFpoY3mzz3Vnx8PCRJ6vLFzUwN95PfL83NzWhtbfVa+bKQkBAkJSXhxIkTEEVROQvG0aBGaWmpTwU1ZsyYoUz4f/rppz2e3eONoIYgCMrrxEwN//Tmm29i6dKlFsv27duHiRMnKoHu3Nxc5Ofne2N4RERERER+xZkThN566y1MmzYNixcvxqhRo7BkyRKsXr0aALBjxw6ce+65iIqKUv7JFRL+9a9/4aabbsJVV12F8847D1dddZVdJ8S5GoMaPmjw4MHIzs7GY489hsrKSmX5d999B0EQcMkll3hxdNTXjRkzBnv37kVtbS0A+HWmBmAav5yp4U/9NAAgLS0NALo015Yn8bw9+emojIwMFBcXK5Ok3uyH0FtygKxzCSpmarhf554a3nz/ZGVlobKyEh999JHSf8LfMzUiIyNx8cUXAwBKSkpslsOUyWNXq9Ue3RdyXw1HMjUY1PAd1j4bOp3OYrkgCFCr1crvEWv0ej10Op3yT34/iqLolX8CJP7jPwASJAmQ+F+//89bxyL+4/Gc/1zzj8dz/ifB+8dyezlzgtC+fftw4YUXKtcnTJiAXbt2AQC2b9+O7du3IyoqCgkJCXjooYeUuZzu7udJGo8/I/VIrVbj5Zdfxq233oolS5bgzTffRGRkJNavX49JkyYpzY+J3GH06NFYvXo1Dh06BAB9JlOjrq4O6enp3h6OQ2JiYhAaGtolqCGfGe3tyU9HZWRkoKWlBVVVVUhISFCCGv5afgqwDGoYjUY0NzczqOFmnXtqeDPTJzMzE6WlpRbL/D2oAZhKUG3YsAGAqezlpZdeanNd+Xgk9zDyFGeCGiw/5ds0Gk2X/jjBwcFobm62+VtkxYoVeOyxx7osP336tFcCVwkBXUvHUv8iQEJ7u0G5Rv1bndkJiuRfeDwnHs9J5s1juVwZoqGhwSJDPigoqMvv5u5OELL1W7rzfbRaLcrKygAAR44cwaxZs3Dvvffi+PHjmD9/PkaOHIn58+d3ez9PYlDDRyUkJOCVV17BokWLcO+99+KBBx7AgQMH8NRTT3l7aNTHjR49GgCwadMmAH0jqFFSUoLq6mqcddZZ3h6OQwRBQHp6us1MDX+bPJd7EBQWFloENfyx/FRUVBQ0Go1FUMNf94u/6dxTw5uZGpmZmVi7di2eeOIJpV+PeVDDVqNwrVaLiIgINDQ0dAlq+ML758orr8Qdd9wBURSxfv16PP/88zbXNQ9qeJIc/GlsbIQoinb1VWGmhm+LiYlBXl6exbKGhoZu99UDDzyA++67T7leWlqKnJwcxMbGKiUcPamyvbjnlahPEyAhIECDwMAgeDDOSz7KG8chcg0ez4nHc5J581gu//2Sk5NjsfyRRx7Bo48+arHMmROEOt9HXh+AcpIbYDpZb+nSpVi7di3mz5/f7f08iUENH5aRkYGXXnoJd9xxB26//XaEhYXhggsu8PawqI+Ljo5GRkYGtmzZAsD/y09FRkYiLy8P1dXVfld+CoDVoIZ8ZrJ8prK/GDBgADQaDQoLCzFhwgS/DmoIgoD4+HhUV1cry7w1udvfmPfU8IXyU+3t7Th16hSys7MBmIJ2gCmbp7vPaGpqKg4ePIiTJ09anHXjC5kaCQkJuOCCC/DTTz/h+PHjyM/Px6hRo6yu6633vflr29zcbNfxkJkavm38+PF48803lesFBQXQ6/WIiYmxeZ/OZ6nJ70eVSmVXoMvVJJ7JSQAAAYIACHw/9HuCF45D5Bo8npMJj+fk3WO5/Hs2Pz8fAwYMUJZ3Dl4Azp0gFBMT0+VETVvrJyQkKFUKHLmfO/Fb1scNHz4czz33HNra2nDxxRdbfeMSudqYMWOg0+kQEhLi9++5qKgolJeXo62tzS+DGmlpaSgpKbFYptPpIAiC3wU11Go1UlNTlWbh/hzUAEyT1uZf5AxqeIZGo0FAQIBPZGrIKbdydoZer1d+6NkqPSWTS1A1NzdbBC59IagBmEpQya699lps3769yzrt7e1obW0F4Plxmz+fvc3Cmanh2yZPngydTodVq1YBAJYvX46LL75YyYIiIiIiIuqPIiIioNVqlX/W5unGjx+PrVu3KtftOUGo83327NmDAQMGoKWlBaNGjVLmbABg69atSvUNW/fzNAY1/MC4cePw0Ucf4Y9//KO3h0L9xJgxYwD4f+kpwBTUkCey5ObO/iQjIwNVVVUWqXwNDQ0ICwvzylmovZWRkdFnghpxcXEManhJSEiIT/TUiIyMxKBBg5Cfnw9RFFFcXKw0T7M3qAEABw8eVC77SlBj/vz5SiD48OHDOPfcc/HQQw9ZBAa8WTbLPKhrb18NZmr4No1Gg7feegtLlixBXFwcPv/8czz99NPeHhYRERERkc/r7gShuro6GI3GLveZO3cu1qxZg/3796OxsREvvvgiLrvsMoSEhCAxMRF33303du7cieeffx4ffPAB7rrrLgDANddcg1dffRWlpaU4deoUVq5cicsuu8yj2wswqOE30tPT/bKZLvknOajh76WnANOko8wfgxppaWkATM2EZQ0NDX47cW4e1JADNf4a1GCmhvcEBwf7RKaGKIrIzMxEdXU1RFGEwWDAZZddhiFDhmDo0KHd3tf8TJZDhw4pl30lqJGQkICffvpJ6bMkiiKefPJJTJgwAUePHgUAi7JZ/hDUYKaG75EkyaL3zJVXXonjx4/j3XffxcGDB7vUDyYiIiIioq66O0EoOjoa+/fv73Kf0aNH495778W4ceMwYMAAqNVq3H333QCAt99+G0VFRTj//PPx+uuvY82aNZgyZQoAYNasWbjoooswePBgZGVlYezYsRaZ/p7CnhpE1EVqaipiYmL6XFDDH8tPpaenAwCKi4sxZMgQAKaghr+VnpJlZmaioqICer0era2tUKlUfju5aC2oERAQ4Pcl2/xBSEgImpubvR7UEAQBF1xwAYKDgyEIAoYPH46vv/7arvuaZ2qYT8r7SlADMJXA/PLLL7Fq1So8/vjjMBgM2LdvHxYsWICdO3d6NVPDmfJTzNTwD0lJSZgxY4a3h0FERERE5FfkE4R27dqFiRMnKnNgcjUBa5588kksXLgQpaWlmDJlijI/k56ejo0bN1q9jyAIWL16NZYuXYqmpiZMmTIFguD53jM+lanx+eefIzs7GxqNBmPGjLEox7B06VIIgqD8GzRokBdHStS3CYKAefPmYfLkyd4eSq/JgZmgoCC/zHaKjIyEVqu1qLmv0+n8NhsgIyMDkiShuLhYafLsjS8/V4iPj4dOp1PO/tbpdIiIiPDb7fEnoaGhSqaGNzN91Go1ZsyYgYsuusjhuv/mQQ1zvhTUAExn/PzlL3/B9u3blTPqd+3ahcOHD3u1wTkzNYiIiIiIiCzJJwg5clJvTk4OLrnkEof/Rho/fjymTp3qtTkQnwlqHD9+HLfccgueeuoplJaWYsiQIVi8eLFy+86dO/HVV1+htrYWtbW12LNnjxdHS9T3LVq0CNdcc423h9FrcqZGXFyc3042p6WlWQQ1GhoafG7i015yY6mioiI0Nzf7bekpwBTUAIDq6moApqCGeWYQuY95Tw1vZmr0hrWgRkhICDQa30yiHTt2LJYsWaJc//TTT71afoqZGkRERERERP2XzwQ1Dh48iKeeegrXXXcdEhMTcddddymBC4PBgAMHDmDy5MmIiopCVFSU307oEZFnyZPM/lh6SpaRkdFnghpRUVHQarUoKipCS0uLX2bPyOQeLXIJKjlTg9xP7qkhZ/v4I2tBDV9//1x99dXK5U8++YQ9NYiIiIiIiMgrfCaoMXPmTNx+++3K9cOHD2Pw4MEAgP3790MURYwZMwYhISGYPn26xQQfEZEtgYGBCAkJ8csm4bK0tDSUlJQo1/05qAGY+moUFRV5vXRQb8mZGuZBDWZqeEZISIhPNAo3GAxYs2YN1qxZA4PB4NB9Y2Jiuozd1z/X2dnZGDNmDABgx44dOHDggHKbNzM1GNQgIiIiIiLqXzxe42D27Nn48ccfuyz/29/+ppQ1aGtrw7PPPov77rsPAJCfn4+hQ4fipZdeQlxcHP7whz/g9ttvt9mMU6/XQ6/XK9flMwlFUYQoii7eov5FFEVIksTX0cdwv3QvKioKsbGxHn99XLVfUlNTUVtbi/r6ekRERKC+vh7h4eF+u7/T09Nx/PhxZGdnIygoyKPb4crPSmhoKIKCgnDq1CmIooj6+nqkpKT47X7xJkf3S0hICCorK9Ha2orAwECvvebmvyuc+Y2RmpqKY8eOKdcjIiJ86v1jbb/MmTMHe/fuBQD8+9//VpaHhYV5dOzmWV46nc6u5zYPaqjVap96rR3B73wiIiIiIurvPB7UeP3119HS0tJleUxMjHL5kUceQVhYmNJTY+HChVi4cKFy+6uvvoqsrCybzXJXrFiBxx57rMvyqqoqtLa2umIz+i154k6SJKhUPpPo0+9xv3Tv9ttvR3JyMiorKz36vK7aL6GhoWhvb8fevXsxdOhQ1NXVQZIkj2+Pq0RHR+PYsWOIjIyEIAge3Q5Xf1YiIiJQUFCAyspKVFVVIS0tzW/3izc5ul+MRiMqKyvR1tYGvV7vtddckiScd955AIDTp0873LcnISHBIqgRFBTkU+8fa/tl8uTJyu1FRUUW63py7OaZMZWVlXY9t3lGR319vcPN3X2FvF/4fU9ERERERP2Vx4MaiYmJ3d6+ceNGvPLKK9i2bZvNJo4JCQkQRRHl5eVWgxoPPPCAkuUBmM7gS0tLQ3x8vMfLI/Q1oihCEATEx8fzj2kfwv3SvRkzZnjleV21X0JDQxEQEICmpiZERkZCkiQMGDAACQkJLhyt54wcORLt7e2oqalBSkqKR7fD1Z+V1NRUtLa2IiEhAW1tbUhKSvLb/eJNju6XuLg4tLS0ICAgAImJiX77mmdlZeF///ufcj0mJsantsXafomPj8fQoUNx+PBhi3XT09M9Ova0tDTlstFotOu5zYNOKSkpfvubUN4vQUFB3h4KERERERGRV3g8qNGdgoICLFiwAK+88gpycnKU5cuWLcPYsWNx/fXXAwC2bt0KlUpl8QetuaCgIKt/6KlUKk74uoAgCHwtfRD3i29yxX7RarWIiYnByZMn0dTUBMDUAN1f93V2djYA4MSJExg0aJDHt8OVn5WEhARUV1dDpVJBp9MhKirKb/eLtzmyX0JDQ5XSkmFhYX77mnduFu6Ln2tr+2XOnDlYsWKFxXqeHrt5QKKpqcmu525vb1cuBwcH+9xr7Qh5vxAREREREfVHPvPXUEtLC2bOnImrrroKV199NRobG9HY2AhJkjB69Gg89NBD+OGHH/Dtt9/izjvvxI033mhRT5mIqC+Tm4XLE7n+eoYxYJrIValUMBgMft0oHDBlDFRVVUGv16Otrc2v94s/Mf/+92ajcFEUcfDgQRw8eNCp/gadgxq+3ihcNmfOnC7L/KFRuHlQw1Y2MBEREREREfk+n8nU+Pbbb5Gfn4/8/Hy8+eabyvKCggLccMMNOHDgAObOnQu1Wo0bbrgBy5cv9+JoiYg8S26uLU/ehYeHe3lEzgsICMCAAQNQUlLi90GN+Ph4VFVV9Ylgkz8xf994O6ghN80ePHiww2fO+2tQ4+yzz1YCrTJPv/fNj4ENDQ123UduFK5Sqfy2nwYRERERERH5UKbGVVddBUmSuvzLzMwEYGr+XVdXh9OnT+OFF15AWFiYdwdMRORB6enpfSZTAwAyMjIAoE8ENZqbm1FRUQHA//eLvzAPZHgzqCEIArKyspCVleVwk3DAf4MagiB0ydbw9NjNfwc6mqnBLA0iIiIiIiL/5jNBDSIisi09PR0NDQ3KmdH+MvlpS18KagCm/iAAgxqeYv6+8eZ7SK1WY+LEiZg4caJTZ/77a1ADsCxBFRwc7PFAgUajUfa9o5kagYGBbhsXERERERERuR+DGkREfiAtLQ0AcODAAQQGBiIoKMjLI+qdvhbUOH78OAAGNTzFV3pq9FZCQgI0mo5KoP4U1Jg0aRISExMBAElJSV4Zg1yCipkaRERERERE/QuDGkREfsA8qOHP/TRkcmlBfw9qxMXFAWCmhqf5Sk+N3lKpVBgwYIBy3Z/eP2q1GitXrsTUqVPx7LPPemUMchCImRpERERERET9i880CiciIttCQkIQHx+PkpISJSDgzzIzM6FSqRAdHe3tofRKaGgoQkNDcfz4cYSEhFicdU/uYx7I8OYEtcFgwGeffQYAmD17tlP7PzU1FUVFRQD8K1MDAGbMmIEZM2Z47fkdzdRgUIOIiIiIiKhvYKYGEZGfSE9PB+B/E5/WxMTEYM2aNTj33HO9PZRei4+PR1VVlV+dZe/v5EyN4OBgqFTe/SnT3t6ulDVyhnlfjb7w2fYkOaih1+vt2gcsP0VERERERNQ3MKhBROQn+lJQAwCys7O9PiHtCgkJCQD8q3SQv5N7ani79JRarcbMmTMxc+ZMpxqFA8BZZ52lPNbAgQNdObw+z/xYaE+2BjM1iIiIiIiI+gbWySAi8hNyX42+EtToK+Rm4QxqeI55poY3CYLQ68/j73//ewiCgOHDh1v016CemfcXamxs7LGcHTM1iIiIiIiI+gYGNYiI/IScqcHJc98iNwvnfvEcOZjh743mASAsLAzLli3z9jD8knlAyZ5m4czUICIiIiIi6hsY1CAi8hN9rfxUXyFnanC/eI5KpUJgYKDXMzVEUcSxY8cAAIMGDeoT5dT8SedMje4YjUaIogiAmRpERERERET+jkENIiI/kZqa6pJyN+RaclAjMjLSyyPpX0JDQ30iqLFr1y4AfadHjD9xpKeGeSNxZmoQERERERH5NwY1iIj8RGBgIB577DGMGzfO20MhM8zU8I6QkBCvBzUEQVB63QiC4NWx9EfmmRo9lZ8yD2owU4OIiIiIiMi/MahBRORHrrjiCm8PgTphpoZ3hISEeL2nhlqtxvnnn+/VMfRnjpSfkvtpAMzUICIiIiIi8nesk0BERNQLcXFxSElJQXZ2treH0q/4QqYGeZcjjcKZqUFERERERNR3MFODiIioFwIDA/HFF194exj9zh133MHsmH6OmRpERERERET9E4MaRERE5HfOO+88bw8BBoMBX375JQBg5syZ0Gj4s8qTHGkUzqAGERERERFR38G/vomIiIic1NLS4u0h9FtsFE5ERERERNQ/MahBRERE5AS1Wo3p06crl8mzWH6KiIiIiIiof2JQg4iIiMgJgiAgOjra28Pot9gonIiIiIiIqH9SeXsARERERESOYqYGERERERFR/8RMDSIiIiIniKKIwsJCAEBmZiZUKp4r4knM1CAiIiIiIuqfGNQgIiIicoIoivj1118BAOnp6QxqeFhISAgEQYAkSczUICIiIiIi6kcY1CAiIiJygiAISE5OVi6TZwmCgPDwcDQ0NPQY1GCmBhERERERUd/BoAYRERGRE9RqNaZOnertYfRrERERaGho6LH8FDM1iIiIiIiI+g7WSSAiIiIivyQ3C2emBhERERERUf/BoAYRERER+SW5WXhDQwMkSbK5HjM1iIiIiIiI+g4GNYiIiIicYDAYsG7dOqxbtw4Gg8Hbw+mX5EwNURTR2tpqcz0GNYiIiIiIiPoO9tQgIiIiclJPZY/IveRMDcC0L0JCQqyux/JTREREREREfQeDGkREREROUKvVuPjii5XL5HlypgZgKkEVHx9vdT1mahAREREREfUdDGoQEREROUEQBJuT6OQZ5kGN7rJmmKlBRERERETUd7CnBhERERH5JfPyUw0NDTbXY6YGERERERFR38FMDSIiIiIniKKIkydPAgBSU1OhUvFcEU9jpgYREREREVH/w7++iYiIiJwgiiJ++eUX/PLLLxBF0dvD6Zc6Nwq3hZkaREREREREfQczNYiIiIicYN5TQxAEL4+mf+rcKNwWZmoQERERERH1HQxqEBERETlBrVbj4osv9vYw+jV7y08xU4OIiIiIiKjvYPkpIiIiIvJL9jYKZ6YGERERERFR38GgBhERERH5JWZqEBERERER9T8MahARERE5wWg0YsOGDdiwYQOMRqO3h9MvsVF437N06VIIgqD8GzRokLeHREREREREPoZBDSIiIiInSJKEuro61NXVQZIkbw+nX2Kj8L5n586d+Oqrr1BbW4va2lrs2bPH20MiIiIiIvJ5eXl5GD9+PKKjo7Fs2TK7/kZdu3YtMjIykJKSgg8//FBZbjQacdddd0Gr1SI0NBS33XYbDAYDANPfwVFRURYnIv3tb39z23bZwkbhRERERE5Qq9WYNm2acpk8j+Wn+haDwYADBw5g8uTJFvuWiIiIiIhs0+v1mDVrFi677DKsWbMGS5cuxTvvvINbbrnF5n3y8vKwcOFCvPLKKzjnnHMwZ84cnHXWWRg6dCieeuop7NmzB9u2bUNbWxsuvfRSTJgwAbfddhuOHj2KqKgoFBYWKo8VEhLiga20xEwNIiIiIicIgoCkpCQkJSVBEARvD6dfYqPwvmX//v0QRRFjxoxBSEgIpk+fjuLiYm8Pi4iIiIjIp23YsAH19fV47rnnMHDgQCxfvhwrV67s9j5vvfUWpk2bhsWLF2PUqFFYsmQJVq9eDQCorq7GBx98gJycHIwZMwaXX365kkG9Y8cOnHvuuYiKilL+BQUFuX0bO2OmBhERERH5JWZq9C35+fkYOnQoXnrpJcTFxeEPf/gDbr/9dnz99ddW19fr9dDr9cp1ObAliiJEUfTImM0JYBm6/s70HpAgSQAEvh/6O8kLxyFyDR7PicdzknnzWC7/nm1oaIBOp1OWBwUFdQki7Nu3DxMnTkRoaCgAIDc3F/n5+d0+/r59+3D55Zcr1ydMmIDHH38cAPD8889brHv48GHMmzcPALB9+3Zs374dUVFRCAwMxO23344nnnjC4yf6MahBRERE5ARRFFFeXg4ASE5OhkrFBFhPCwwMRGBgINra2pip0QcsXLgQCxcuVK6/+uqryMrKgk6ng1ar7bL+ihUr8Nhjj3VZfvr0aa8ErxIC9D2vRH2aAAnt7QblGvVvdZWV3h4COYnHc+LxnGTePJafPn0aAJCTk2Ox/JFHHsGjjz5qsUyn0yErK0u5LggC1Go1amtrER0dbfXxO99Hq9WirKysy3qbNm1CXl4e1q1bBwA4cuQIZs2ahXvvvRfHjx/H/PnzMXLkSMyfP9+p7XQWgxpEREREThBFEZs3bwYAXHvttQxqeEl4eDhqamqYqdEHJSQkKMFDa0GNBx54APfdd59yvbS0FDk5OYiNjUVCQoInhwoAqGxnqaz+ToCEgAANAgODwKqE5I3jELkGj+fE4znJvHksl/+Gyc/Px4ABA5Tl1ko9aTSaLsuDg4PR3NxsM6jR+T7y+uaamppw22234ZFHHkF8fDwAU6krWVZWFpYuXYq1a9cyqEFERETkDwRBQExMjHKZvCMiIqLHoAYzNfzDsmXLMHbsWFx//fUAgK1bt0KlUiEtLc3q+p1T7+W0fJVK5ZUgo8QzOQkAIEAQAIHvh35P4MkOfovHczLh8Zy8eyyXf89GRERYPcHHXExMDPLy8iyWNTQ0dHtCV0xMDKqqqrpdf8mSJUhPT8cf//hHm4+TkJCA0tLSbsfnDgxqEBERETlBrVbjsssu8/Yw+j25r0Z35afks5zkNGzyTaNHj8ZDDz2ExMREGI1G3HPPPbjxxhuV2sBERERERNTV+PHj8eabbyrXCwoKoNfrlZPwbN1n69atWLRoEQBgz549Fhkhr776Kr799lvs3r1bCbC0tLRgwoQJ2L59O0JCQgCYTkTKyMhwx2Z1i6cOEBEREZHfkoMazc3NMBqNVteRMzUCAwOZVePDbrjhBsybNw9z587FggULMH36dLz88sveHhYRERERkU+bPHkydDodVq1aBQBYvnw5Lr74YqjVatTV1Vn9O2nu3LlYs2YN9u/fj8bGRrz44ovKSXubNm3Cfffdh3fffRdhYWFobGxES0sLQkJCkJiYiLvvvhs7d+7E888/jw8++AB33XWXR7cXYFCDiIiIiPxYRESEctlWCSo5U4Olp3zfihUrUFdXh9OnT+OFF15AWFiYt4dEREREROTTNBoN3nrrLSxZsgRxcXH4/PPP8fTTTwMAoqOjsX///i73GT16NO69916MGzcOAwYMgFqtxt133w0AePHFF6HX63HJJZcgIiICERERuPzyywEAb7/9NoqKinD++efj9ddfx5o1azBlyhTPbewZLD9FRERE5ASj0YiNGzcCAC688EKWNfKSpKQk5XJJSQkiIyO7rCMHNdgknIiIiIiI+qIrr7wSx48fx65duzBx4kTExsYCACRJsnmfJ598EgsXLkRpaSmmTJmi/L306aef2rxPenq68newNzGoQUREROQESZJQXV2tXCbvGDRokHL52LFjGDlyZJd15PJTzNQgIiIiIqK+KikpCTNmzHDoPjk5OcjJyXHTiNyHQQ0iIiIiJ6hUKlxwwQXKZfKOzkENa5ipQURERERE1HcwqEFERETkBJVKhdTUVG8Po98bPHiwctlWUIOZGkRERERERH0HTyskIiIiIr9lnqlx9OhRq+swU4OIiIiIiKjvYFCDiIiIyAmSJOHUqVM4deoUe2p4UUxMDKKjowEwU4OIiIiIiKg/YFCDiIiIyAlGoxEbN27Exo0bYTQavT2cfk3O1igpKUFra2uX25mpQURERERE1Hf4VFBj6dKlEARB+WdeTiAvLw/jx49HdHQ0li1bxjMiiYiIyOu0Wi20Wq23h9HvyX01JElCQUGBxW2iKCpBJ2ZqEBERERER+T+fCmrs3LkTX331FWpra1FbW4s9e/YAAPR6PWbNmoWzzz4bO3fuRH5+Pt555x3vDpaIiIj6NY1GgxkzZmDGjBnQaDTeHk6/1l1fDbn0FMBMDSIiIiIior7AZ4IaBoMBBw4cwOTJkxEVFYWoqChEREQAADZs2ID6+no899xzGDhwIJYvX46VK1d6ecRERERE5AvMgxqd+2owqEFERERERNS3+ExQY//+/RBFEWPGjEFISAimT5+O4uJiAMC+ffswceJEhIaGAgByc3ORn5/vzeESERERkY+Qy08BXYMacj8NgOWniIiIiIiI+gKP10qYPXs2fvzxxy7L//CHP2Do0KF46aWXEBcXhz/84Q+4/fbb8fXXX0On0yErK0tZVxAEqNVq1NbWIjo6ustj6fV66PV65bpOpwNgqqksiqLrN6ofEUURkiTxdfQx3C++ifvF93Cf+CZ/3S9GoxGbN28GAEyePBlqtdrLI3Itf9ov2dnZyuWjR49ajNm8cXhAQIBfbE93/Gm/EBERERERuYPHgxqvv/46WlpauiyPiYnBI488olx/9dVXkZWVBZ1OB41Gg6CgIIv1g4OD0dzcbDWosWLFCjz22GNdlldVVVn8YUuOE0UR9fX1kCQJKpXPJPr0e9wvvon7xfdwn/gmf90vBoNBySo9depUn+ur4U/7RZIkaLVa6HQ6HD58GJWVlcpt5eXlymVRFC1u80fyfvH1fUJEREREROQuHv/rOzEx0a71EhISIIoiysvLERMTg7y8PIvbGxoabNZFfuCBB3Dfffcp13U6HdLS0hAfHw+tVuv84AmiKEIQBMTHx/OPaR/C/eKbuF98D/eJb/LX/SKKIqZMmQIASEpK8qux28Pf9svgwYOxa9culJaWIjIyUjkhpqGhQVknIiICCQkJ3hqiS8j7pfMJP0RERERERP2Fz5xSuGzZMowdOxbXX389AGDr1q1QqVRIS0vD+PHj8eabbyrrFhQUQK/XIyYmxupjBQUFWf1DT6VS+cUf5b5OEAS+lj6I+8U3cb/4Hu4T3+SP+0WlUlmUPeqL/Gm/yEENURRRXFyMoUOHAjCVCZMFBgb6xbb0RN4vRERERERE/ZHP/DU0evRoPPTQQ/jhhx/w7bff4s4778SNN96I0NBQTJ48GTqdDqtWrQIALF++HBdffHGfq11NRERERM4ZNGiQctm8Wbh5o3BbWb5ERERERETkP3wmU+OGG27AgQMHMHfuXKjVatxwww1Yvnw5AECj0eCtt97CggULsGzZMqhUKqvNxomIiIg8RZIk1NTUADD1BhMEwcsj6t/MgxpHjx5VLpuXnwoICPDomIiIiIiIiMj1fCaoAZgafK9YscLqbVdeeSWOHz+OXbt2YeLEiYiNjfXw6IiIiIg6GI1GfPvttwCAa6+9ts81Cvc3gwcPVi6bZ2qsX79euZyTk+PRMREREREREZHr+dVf30lJSZgxY4a3h0FEREQEAAgNDfX2EOgMa+WnJEnCmjVrAJh6oMydO9crYyMiIiIiIiLX8augBhEREZGv0Gg0uOqqq7w9DDojPj4eERERaGhoUIIaO3fuxIkTJwAA06ZNQ1JSkjeHSERERERERC7gM43CiYiIiIicJQiCkq1RWFiI9vZ2JUsDAObPn++toREREREREZELMahBRERERH2C3FfDaDTixIkT+OijjwCYsmrmzJnjzaERERERERGRi7D8FBEREZETjEYjfvnlFwDApEmToFarvTwiMu+r8e6776K0tBQAcNlllyEmJsZbwyIiIiIiIiIXYlCDiIiIyAmSJCmT5pIkeXk0BFgGNV5++WXlMktPERERERER9R0MahARERE5QaVSYfz48cpl8j65/BQANDQ0AACCg4PZ0J2IiIiIiKgPYVCDiIiIyAkqlcoiM4C8z9r+mDlzJiIiIrwwGiIiIiIiInIHnlZIRERERH1CYmIiwsLCLJax9BQREREREVHfwqAGERERkRMkSUJ9fT3q6+vZU8NHCIJgka0RHh6OK664wosjIiIiIiIiIldjUIOIiIjICUajEevXr8f69ethNBq9PRw6w7yvxuzZsxESEuLF0RAREREREZGrMahBRERE5KSgoCAEBQV5exhkZuTIkcpllp4iIiIiIiLqe9gonIiIiMgJGo0Gc+bM8fYwqJO77roLhw4dQnZ2NktPERERERER9UEMahARERFRn5GQkIAPP/zQ28MgIiIiIiIiN2H5KSIiIiIiIiIiIiIi8gvM1CAiIiJygtFoxK+//goAOOecc6BWq708IiIiIiIiIqK+j5kaRERERE6QJAlFRUUoKiqCJEneHg4RERERERFRv8BMDSIiIiInqFQqjB07VrlMRERERERERO7HoAYRERGRE1QqFYYNG+btYRARERERERH1KzytkIiIiIiIiIiIiIiI/AIzNYiIiIicIEkSmpubAQChoaEQBMHLIyIiIiIiIiLq+5ipQUREROQEo9GIL774Al988QWMRqO3h0NERERERETULzBTg4iIiMhJarXa20MgIiIiIiIi6lcY1CAiIiJygkajwXXXXeftYRARERERERH1Kyw/RUREREREREREREREfoFBDSIiIiIiIiIiIiIi8gssP0VERETkBKPRiJ07dwIAxo0bx/4aRERERERERB7ATA0iIiIiJ0iShBMnTuDEiROQJMnbwyEiIiIiIiLqF5ipQUREROQElUqF3Nxc5TIRERERERERuR+DGkREREROUKlUGDFihLeHQURERERERNSv8LRCIiIiIiLyCXl5eRg/fjyio6OxbNkylnYjIiIiIrKDM7+j165di4yMDKSkpODDDz+0uO2VV15BYmIisrOzsXHjRovb/vKXvyA6Ohq5ubn47bffXLod9mJQg4iIiMgJkiShtbUVra2tnHglcgG9Xo9Zs2bh7LPPxs6dO5Gfn4933nnH28MiIiIiIvJpzvyOzsvLw8KFC/HXv/4V33zzDR5++GEcPnwYAPDNN9/gT3/6E9544w289957WLx4MU6fPg0AeP311/H666/jiy++wN/+9jfMnz8fbW1t7t7ELhjUICIiInKC0WjEp59+ik8//RRGo9HbwyHyexs2bEB9fT2ee+45DBw4EMuXL8fKlSu9PSwiIiIiIp/mzO/ot956C9OmTcPixYsxatQoLFmyBKtXrwYA/Otf/8JNN92Eq666Cueddx6uuuoqfPrpp8ptf/rTn3DBBRfgyiuvxNChQ7F582a3b2Nn/aKnhnz2pE6n8/JI/J8oimhoaEBwcDCbovoQ7hffxP3ie7hPfJO/7heDwYDm5mYApt8YGk3f+lnlr/ulr5P3i3w2VF/KEtq3bx8mTpyI0NBQAEBubi7y8/Ntrq/X66HX65Xr9fX1AIDS0lKIoujewVrRVFPh8eck3yIAqAlrRWCACEHw9mjI25qKi709BHISj+fE4znJvHksLy8vB2D6javVapXlQUFBCAoKsljX0d/R8n0uv/xy5fqECRPw+OOPK7ddf/31Frdt3rwZixYtwv79+/HGG29Y3LZr1y5cfPHFTm6pc/rWX982NDQ0AADS0tK8PBIiIiLqixYvXuztIVA/1dDQgMjISG8PwyV0Oh2ysrKU64IgQK1Wo7a2FtHR0V3WX7FiBR577LEuyydOnOjWcRJ1Z423B0A+JMPbAyCiXuDxnEy8fywfOXKkxfVHHnkEjz76qMUyR39HW7uPVqtFWVlZt7c1NjZCFMUutx05csTp7XNWvwhqpKSkoKSkBBERERAYYu0VnU6HtLQ0lJSUWEQJybu4X3wT94vv4T7xTdwvvon7xTfJ+6W4uBiCICAlJcXbQ3IZjUbT5ayz4OBgNDc3W/1j7IEHHsB9992nXDcYDDh48CDS0tKYXURe0dDQgJycHOTn5yMiIsLbwyEiIifxeE6+QBRFFBcXIycnx6IqQOffy4Djv6Ot3Udev7vb5HHYup8n9YughkqlQmpqqreH0adotVpOcPgg7hffxP3ie7hPfBP3i2/ifvFNkZGRfW6/xMTEIC8vz2JZQ0MDAgMDra5vLfV+0qRJbhsfUU/kcscDBgzoc59PIqL+hMdz8hXp6el2refo72j5PlVVVVbXt3VbSEgIQkJCUFVVpXw2enoed+EpTERERERE5HXjx4/H1q1blesFBQXQ6/WIiYnx4qiIiIiIiHybM7+jO99nz549GDBgQI+3jRs3zuZtnsSgBhERERERed3kyZOh0+mwatUqAMDy5ctx8cUXQ61We3lkRERERES+q7vf0XV1dTAajV3uM3fuXKxZswb79+9HY2MjXnzxRVx22WUAgGuuuQavvvoqSktLcerUKaxcudLitqeffho6nQ5HjhzB2rVrlds8qV+UnyLXCQoKwiOPPGK1fht5D/eLb+J+8T3cJ76J+8U3cb/4pr68XzQaDd566y0sWLAAy5Ytg0qlwo8//ujtYRHZrS9/PomI+hMez8nfdPc7Ojo6Gnv27MGYMWMs7jN69Gjce++9GDduHIKDgzF48GDcfffdAIBZs2bhv//9LwYPHgwAuOiiizBnzhwAwB133IHPP/8cqamp0Ov1WLx4Mc4++2yPbatMkCRJ8vizEhERERERWVFRUYFdu3Zh4sSJiI2N9fZwiIiIiIj8gjO/o/Pz81FaWoopU6Z06Y2xY8cONDU1YcqUKRAEQVkuiiJ++eUXBAUFYcKECS7dBnsxqEFERERERERERERERH6BPTWIiIiIiIiIiIiIiMgvMKhBRERERERERERERER+gUENIiIiIiIi8hu1tbXQ6XTK9erqahw6dAgA0Nraip9++gk9VVnOy8vD5s2b3TpOc6z6TETU/xw7dgxTp05FU1OT1dvffvttrFu3zmLZ1Vdfjffffx8AMGjQIGzZssXt4yTyRwxqED7//HNkZ2dDo9FgzJgxOHjwIADTD/3x48cjOjoay5Yt6/JD/NixY4iJibH5uO3t7Rg1ahR+/PFHdw6/T3LXPpk3bx7uuecet469L3P1fnniiSeQmJiI8PBwXHnllaiurvbIdvQ1zuyXN954A8nJyQgICMCUKVNQXl6u3PbTTz9h+PDhiIuLw3PPPefx7ekrXL1furuN7Ofq/SLjd37vuGu/8Huf+qr7778fK1euVK5/8803uOmmmwAAP//8M+6++260trZ2+xgbN27Ebbfd1uNzjRkzBt988w0AYPPmzdBoNIiKilL+RUZGIiEhweb9t2/fjptuugnnnHMOGhoa8Oc//xnz58+3ZzMV//73v7F06VKrt4miiGXLlqGgoMBieXx8PAoLC3Hy5EloNBqHno+IyJO+/fZbREREoL6+HgDw9ddfIzo6GjqdDiUlJZg2bRrCwsJw4YUX4uTJkwCAd955B4IgQBAEBAUFYfz48dixY4dXxr97924sXrwYv//977FkyRIsWbIE//vf/wAAn376KWJiYhAWFmb1vpGRkbjnnnvQ3t4OvV4PSZIgSZLSrDkoKAhBQUEwGAwwGAwe2yYif8CgRj93/Phx3HLLLXjqqadQWlqKIUOGYPHixdDr9Zg1axbOPvts7Ny5E/n5+XjnnXeU+504cQJXXHEFamtrbT72M888g7y8PA9sRd/irn2yfv16/Pjjj3jiiSc8tCV9i6v3y+bNm/HRRx9h8+bN2Lt3L4xGI+677z4Pb5X/c2a/bNmyBX/961+xevVqFBQUQJIk/OlPfwIAVFVV4corr8SCBQuwdetWvP/++9i0aZMXt9A/uXq/dHcb2c/V+8Ucv/Od5679wu996ssCAwMRGRkJADAYDJg7dy4uvfRS6PV6fPDBB3jmmWcQEhKirL9161bExsYiMzNT+ff3v/8dp06dUq6npqYiKiqqS/ZGcHAwgoKCAAABAQEYN24c6urqlH+HDx9GcHCwsr4oili9ejXuvPNOZGdn45xzzkFZWRn+/Oc/K48hP565Dz74ALfffrsyIbZkyRJUVVUBAD766CMMGjTI6muhUqnQ2tqKhx56CJIkQa/XAzAFm8PCwpQJMQBoa2uDKIpOveZERO5y6aWXYtiwYXj77bcBAC+88ALuueceaLVaXH/99cjKysL+/fuRnZ2NRYsWKfcbOXIkamtrcfLkScyYMQPXXHON28cqCAIKCwstliUmJuLyyy/H9OnTMX36dBw6dAhHjhyBKIp444038Pvf/15ZVxRF5TgNAHPmzMGUKVNQWVmJ0aNHIygoCF999RUWLVqEuLg4HD58WAnqvPLKK27fPiK/IlG/tm7dOun1119Xrm/cuFEKCQmRPv30Uyk6OlpqamqSJEmS9u7dK02aNElZLycnR/r73/8u2XoLHTlyRIqKipIyMzOlTZs2uXUb+hp37JPGxkYpIyNDWrlypfs3oI9y9X75+9//Li1btky5/t5770nnnnuum7ei73Fmv7z99tvSp59+qtzn7bffloYPHy5JkiQ9//zz0rBhwyRRFCVJkqTPPvtMWrhwoYe2pu9w9X7p7jayn6v3i4zf+b3jjv3C733q65YuXSqtXr1akiRJevPNN6UhQ4ZII0aMkGJjY6XU1FRpxIgRUnh4uLRhwwZJkiRp27Zt0qhRo5T7f/7551JJSYly/cMPP5QKCgosnuPw4cPS559/Lg0dOlR68sknpfXr10vbt2+XNBqNFBsbq/yLiYmRMjIyLO77wgsvSA8++KD0448/Sjk5OdLnn3+u3PaXv/xFuummm7ps0549e6SPP/5YWrdunbRu3TopISFBKigokE6cOCFptVqpvr5eWbe9vV1qa2tTrh8/flxavHixVFZWJgGQQkJCJABSTEyMFBMTIwGQwsPDJY1GI+3du9fh15uIyN0+/fRTKTs7Wzpw4IAUEREhVVdXSwcOHJACAgIknU4nSZIkFRYWSgCkuro6adWqVdLo0aOV+5eWlkoApMrKSreOE0CX74vObrrpJun999+XPvjgAykkJETKyMiQMjIypKCgICkhIUFavHixJEmS1NraKun1eqm9vV2SJEk5rk+ZMkX6/vvvJUmSpNGjR0s7duxw3wYR+TFmavRzM2fOxO23365cP3z4MAYPHox9+/Zh4sSJCA0NBQDk5uYiPz9fWe/LL7/sNgp+xx134P7770dGRob7Bt9HuWOfPPbYY2hra4NGo8F3333HM7Sc4Or9MmLECHz66ac4ceIEKisrsXLlSlxyySXu35A+xpn9csstt2D27Nld7gMA+/btw7Rp0yAIAgBgwoQJ2LVrl4e2pu9w9X7p7jayn6v3i4zf+b3jjv3C733qq/bs2YOLLroIH3zwAZ599lksWLAAixcvxuHDh5GXl4cbbrgBf//735GXl4dJkyYp5TsA09m1er0eBoMBf/vb33DkyBHltueeew5HjhxBW1ubUt6jsLAQ3333Herr67Fjxw789NNPMBgMOPvss1FdXa38O3DgQJfP2NKlS/Hkk09iypQpEAQBarW6x20bM2YM5syZg5kzZ2LmzJkICQlBYGAgnnrqKajVauTm5iIzMxOCICA7OxurV68GALS0tCAtLQ2vvfYakpKS0N7ejqamJqhUKpw6dQrHjx9HWFgYGhoa0N7ejtGjR7tiVxARudRVV12F0NBQXHfddbjjjjsQGxuL7du3Y+DAgYiIiAAApKam4v/+7//Q3t7e5f7//e9/MWDAAKXs8+bNmzFmzBhER0fj+uuvR11dnbJud7d9+OGHyMrKQlhYGC677DKlRPSwYcOUvxGzsrIgCALWrFljc3vq6upw//3347///S8KCwtRWFiIrKws/PDDD3jzzTcBAP/85z8xceJEBAQEoKamRvmuaGhoQHx8vMXjSZLE8lNEnTCoQYq2tjY8++yzuPPOO6HT6ZCVlaXcJv8Yl0vomN/W2apVq1BfX8/SIC7gin1SVFSEF154AVlZWf+fvfsOi+Jq2wB+L7t0BJSmggVFjRWMNTbU2Evs3dh7L7FFY+89GnsPolijUbHGHnsDBTWCgCAqRaq0Lef7g5f53FBEA67g/buuvXTOtGfOLDAzz5xz8Pz5c0yZMgXt27fnA47/ICfOS8uWLVG6dGmULl0adnZ2iI+Px9SpUz9L/PnVx5yXNG/fvsWmTZswbNgwAEi3nrm5OUJDQz/PAeRTOXFesjuPsi+nzgv/5uesnDgv/LtP+VnVqlVx+vRpaDQaTJw4EXv37sX9+/dRoUIFVKpUCbt378akSZNQqVIl3LhxQ+riQwgBuVyOKVOmwMXFBT4+PhgyZAhcXFyk6eHDh8PFxUXqdrJZs2ZYu3YtSpQogbFjx0rJhdevX2t1Y1W7dm2th09v375FfHy89ElMTIRKpZKmlUqlNB0bG4vIyMhMj/fWrVs4fPgwvL29ERgYiJs3b8LKygovXrzAgAEDAAB9+vRBxYoV8e2330rHGR8fDwsLi3RjaajVav4uIKIvkkwmw4QJE+Dr64vx48cDAN68eQMrKytpGblcjsWLF8Pa2hoA8PDhQ1haWsLU1BRTpkyBu7s75HI5goOD0apVK4wcORJ3795FfHw8+vXrBwBZzouLi0Pfvn2xaNEi+Pj4QKFQYMWKFQCA27dvS9dgXl5eiIqKQqdOnaTYGjZsKP1dOHjwIHx8fFC+fHls2rQJQGpXiS9fvtTqSnDKlCnS2BtxcXEwMjJCgQIFcP/+fdSvXx+WlpZ4+PAhGjRoAENDQ3Ts2DEXap4o72JSgySzZs2CqakpBg0aBIVCka6vVyMjIyQkJGS5jfDwcEybNg3bt2/P1htJlLWcOCe7du2CnZ0d/vrrL8yePRuXLl3C1atXce7cudwMPV/LifNy8OBBvHjxAr6+vggLC0PFihXRu3fv3Aw73/uU8zJy5EjUqVMHLVu2BIB062XnXFLWcuK8ZHceZV9OnBf+zc95OXFe+Hef8rtDhw4hOjoahw4dgoeHBwIDA1GtWrV0LTWio6Oln4ukpCTo6+tj9erVePToESpWrIg5c+bg4MGDOHjwIMqWLYstW7bA19c3w5azb9++xaVLl1ClShX4+/tLb92mfW7fvi0lUMqVKwd7e3s4ODjAwcEBAQEB6NWrlzT966+/Yv/+/bC3t4e9vT2KFCkClUqVLlkSEhKCe/fuoWLFitixYwcA4J9//kH58uW1Yjtw4ADWrFkDc3Nz7N27F8bGxihSpAhiYmJgaWmJEiVK4N27d9L4GqtWrcrlM0RE9GmuXbsGIQRu3LgBIHVsID291MeWEyZMgKWlpfSgH0j9ffvgwQPcvn0bAwcOxMCBA/Hu3Tvs3r0bderUweDBg1GqVCls2LABR48exevXr7Ocp1AooFAokJKSgiJFiuDPP//E3LlzAQAFChSApaUlgNQX3ywtLaGvry/FHhkZiYMHDyIwMBDx8fFYt24dPD094e/vj5s3b+Lu3buoVKmS1hhM7ytRogRSUlLw7NkzGBkZITIyEtHR0ahcuTIuX76MlJQU/Pnnn7lV9UR5kuLDi9DX4Pz581i3bh1u3LgBfX19FCpUKN2An3FxcVpNuDMybtw4DBw4kM2ac0BOnZOQkBA0adJE+uNZoEABlClTBn5+fmjWrFmuxZ9f5dR5cXd3x/Dhw6Ub09WrV8PS0hLR0dHSxRJl36ecl127duHChQvw8vKSygoVKiQNypnROvRxcuq8ZGceZV9OnRf+zc9ZOXVe+Hef8jOVSoU5c+agZcuWcHJywqhRo/DTTz/B09MTlSpVwuvXr3Ho0CHMnz8fQGpri5UrV+Ldu3dab/t27NgR169fx/Xr1wEAdevWhZ2dXbr9eXt7IywsDCNHjsS0adPQqVMnGBkZQaFQ4PXr17CwsICxsTHUajXMzc3h4+OjdR2RlJQEMzMzeHl5oXTp0gCAGTNmICQkBDt37tTal76+PiIiIhAfH69V/uLFC3z33XeYMGECrl27hvr162dYN3K5HL169UKvXr2wb98+bNmyBefOnUN0dDQcHBzSbZeI6EsSHByMvXv3Yt68eViwYAE6duwo3R8DwPTp0zFmzBhUqVIFarUaAGBgYICSJUsCAH777TdYWFjg7NmzCA4ORqlSpaRt29vbw9DQEC9evMhyXs2aNeHh4YGlS5di9OjRqFevHtauXau1fGYyesFHT08Pc+bMwbRp01C5cmV06dLlg9s5c+YMatSoka6lHRGlx5YahICAAPTo0QPr1q1DhQoVAAA1atSQLvLTlklOTpb6J8zMnj17sHbtWimDfvXqVbRp0waLFy/O1WPIb3LynDg4OCAxMVGa1mg0CAkJgb29fe4En4/l5HnRaDQICwuTpl+/fg0A0gUaZd+nnJc7d+5g9OjR8PDw0HqI8e/17t+/z5+VT5ST5+VD8yj7cvK88G9+zsnJ88K/+5Sf7dy5E+XKlYOTkxOcnZ1x8uRJTJ06FW/evIGenh4cHR2llhqLFy+W+kJ//fo1ihYtimPHjsHR0REbN27ElStXcPXqVakl05gxY7T6aZ8/fz5mzpwJjUaD3bt3Y8yYMbh8+TKmTJmCwMBA1K5dG7t27UJgYCCCg4Ph4+OTLt5jx47Bzs5OSmhkJbMWb8WLF0eHDh2wbNky7N27N1sPxU6fPo26det+cDkioi/F0qVL0aFDB0yePBmvXr2Cp6cnnJ2d8fTpU8TFxcHKykrqtjkzQgio1WoUL14cz58/l8pDQ0ORnJyMEiVKZDnv7du3sLOzw9WrV/HmzRtYW1tj3LhxWvuQyWQQQmR5LH5+fvD39wcAdO7cGWq1Gu7u7ujfv3+W68XHx2PhwoXo27evVKbRaNhtIFFmdDlKOeleQkKCqFChghg8eLCIi4uTPikpKcLGxkZs375dCCHEoEGDRJs2bbTWDQgIEP/+CgUEBGh9atWqJfbu3SuioqI+1yHleTl9Tnx9fYWJiYk4ePCgCA4OFpMnTxY2NjYiPj7+sx1TfpDT52XZsmXC1tZWbNiwQezcuVO4uLiIOnXqfLbjyS8+5by8efNGFC5cWMybN09rHSGECA8PF0ZGRuLs2bMiJSVFtGjRQowaNUpnx5dX5fR5yWoeZV9Onxf+zc8ZOX1e+Hef8rPY2Fjx7NkzMXbsWOHm5iaVjx07VnTq1EmMHDlS7N27VwghxLFjx0Tfvn2FEEJMnTpVzJo1S1p+zJgxYtq0aUIIISIjI0Xp0qXFlStXMtxnrVq1xIULF4QQQmzbtk00bdpUCCGEq6ursLW1FUWLFhVFixZNt15MTIxwcnISa9eu1SqfPn26FNf7oqKihKmpqTR95swZoVKphBCp1yfW1tbC1dU1wxhPnjwpzXv06JEwMTERT58+lY7P2Ng4w/WIiL4Er1+/FsbGxuL+/ftCCCEWLlwo6tSpI9RqtahUqZLo0aOHCAgIEFOnThUAxP3798WOHTtEpUqVRFRUlHjx4oWYOnWq0NfXFyEhIeLFixfC1NRUbN68WTx//lz88MMPon379kIIkeW8x48fCyMjI3Hy5Enx8uVL0adPH9GyZUutWJ2cnMS6detESEiIuHTpklTu7Owsrl69KhYtWiRsbW3FmTNnhBBCXL9+Xdja2goLCwtx8eLFdMd+/vx5AUC4ubmJOXPmiOrVqwulUqm1v/f3Q0T/j0mNr9yRI0cEgHSfgIAAcfToUWFiYiKsrKyEjY2N8PHx0Vo3owe1/+bq6irdBFD25MY5OXr0qKhSpYowMjISlSpVEteuXftch5Nv5PR5SUpKEmPGjBFFixYVBgYGwtXVVTx//vxzHlK+8CnnZfXq1Rmuk2bDhg1CX19fFCxYUDg6OorXr1/r6vDyrJw+Lx86Z5Q9ufHz8j7+zf80uXFe+Hef8ru0pIZKpRITJkwQpUqVEhEREVJS49SpU6JNmzZi9OjRQgghatasKTw9PaX13717J6pXry5++eUXUaNGDbFu3bpM9/V+UqNLly6iXr16IiUlRbi6uoqTJ09muE50dLRo0KCBaNSokdbDKSE+nNR4/fq16Nevn3B0dBShoaFCCCE2btwoTE1NRZkyZcTLly/TrTtz5kxhZ2cnbt++LRo1aiTGjRsnzQsJCRF6enqZHh8Rka5NmjRJNG7cWJp++/atMDU1FX/99Zfw9/cXdevWFWZmZqJTp06iWLFiUlIj7frH0NBQfPvtt+LYsWPSNi5evCicnZ2FhYWF6N69u9ZLN1nNW7dunShZsqQwMjIStWrVEo8ePdKK9ezZs6JUqVJCX19f9OzZUwghRGJiorC2thaWlpaiS5cuIiQkRISHh4tJkyYJGxsbcfz4cXH8+HFhYWEhlixZIv1d2L17tzAxMRGtWrUSVlZWolatWmLx4sXi7t274uXLlyI+Pl48f/5chISEiIcPH4oTJ07kQu0T5V0yIT7Qboq+aq9fv8bdu3dRu3ZtrX5oSXd4Tr5MPC9fpk89LwEBAXjy5Anq168PMzOzXIzw68Sfly8Tz8uXieeFKL3Ro0fDxcUFO3bsQExMDE6ePAkHBwcMGzYM9erVQ+HCheHp6YlJkyYhLCwMrq6uCA0NhYmJCYQQePDgAdavX49t27bBzs4OM2fOxHfffQcHBwdYWVlBJpNJ+3JxcZHGPqtXrx6+++47xMbGIiIiArNmzUK7du2QlJSEiIgIVKhQATdv3sTgwYNhbm6OU6dOoUCBAlqxT5s2DS9fvsTvv/+uVX7nzh3Url0blpaWGDFiBKZNm4bAwEBMmzYNz549w5EjR+Dm5obNmzdjy5YtaNu2LTQaDcaOHYs9e/agVq1auHjxIho2bIhevXrBxcUFtra2MDQ0xJs3b2BmZoaQkBCo1WrUrl37s5wnIqKvQXx8PDp06IBRo0ahXbt2WLRoERYsWIC2bdti+fLlUhegd+7cQbdu3WBra4srV65g1apVKF++PNq0aYPExEQcOXIEp06dwr179xAUFIS4uDit/bRo0QInT57UxSESfZGY1CAiIiIiIqI8Y9CgQahduzYqVKgAZ2dnmJqaAgAGDhyI7777DoMGDZKW9fT0xJ07d9CjRw/07t0br169wjfffIOuXbuiU6dOePDgAY4cOYKrV6/i0aNHqFChAu7evSv1yx4fH49z586hXbt2mDZtGkaPHo2tW7di27ZtePz4MeLj4yGEQLVq1XDnzh3s27cP165dw7Jly2BgYJAu9okTJyI4OBj79+/XKn/48CHGjBmDtWvXonz58ujatSvOnDmDcePGYfr06TAyMgIA/P777xg5ciSGDRuGRYsWYcSIEZgyZQpKly6N169f48CBA7hw4QK8vLzw6tUrrTF2AGDx4sWYMmVKTp8SIiL6n8DAQLx79w4VK1ZMNy8+Ph7+/v5wdnb+4HZUKhVSUlKgVquh0WhgYGAAY2Pj3AiZKE9iUoOIiIiIiIjyvaCgIBQvXlyrJcb7VCoV4uLiULBgQQDAgwcP4OjoCAsLC9y4ceOztnC4desWHB0dYWNjk26ev78/TExMUKRIkQ9uJyUlBUqlEmq1GkIImJiYQF9fPzdCJiIiIvpsmNQgIiIiIiIiIiIiIqI8QU/XARAREREREREREREREWUHkxpERERERERERERERJQnMKlBRERERERERET/iRACKSkpug6DiIi+AkxqEBERERERERF9xVavXo2uXbsCAJRKJQCgVatWOHfuHJ4+fQoXFxcAkJIWo0aNQtGiRSGXy1GyZElYWVmhQIECaNu2LVQqFaKjowEAP/74IzZu3PjZj4eIiPI3JjWIiIiIiIiIiL5ihoaGMDY2BgCMGDEC1apVw/Xr1zFq1Ch07twZT58+RfXq1dGsWTMAwG+//YbHjx/D1tYWgYGBWLBgATp37ozTp0/j7t27aNSoEQDA2NgYhoaGOjsuIiLKnxS6DoCIiIiIiIiIiD6/hIQE3L59G8+fP0dYWBjOnTuHX3/9FSYmJti4cSOaNm0KMzMzLF26FCtWrNBaV6lUQl9fP902DQ0NpUSGnp4eZDLZZzkWIiL6ejCpQURERERERET0FYqPj8fevXtx//59xMXF4cCBA6hYsSLMzc3h4uKCrVu3Ssva2dlh7dq1KF++PPbs2YOUlBTExsZi9uzZuHPnDkJCQvDzzz/j+++/1+ERERHR14BJDSIiIiIiIiKir5CtrS02btyIZs2aoXz58ti0aRMAwNzcHHfu3NFadty4cdDX10dKSgqioqIQGhoKMzMzREdHIzExUSpXKpUQQujicIiI6CvBMTWIiIiIiIiIiL5SL168wF9//YVnz57ht99+A5DagqN69epan71790Imk6FatWrYsGEDjIyMMGjQIKxevRqdO3dG9erVsWHDBhQoUADJyckAgJiYGMjlcl0eHhER5UNsqUFERERERERE9JWaN28e7O3t4eDggM2bN6NKlSrQ19fHsGHDtJbbt28fVCoVoqOj0a1bN8TFxWHXrl0AgJSUFDx+/Bjh4eGoW7cuHjx4gGrVquHx48eYOnWqLg6LiIjyMZlgm0AiIiIiIiIioq/OiRMnMGTIEIwdOxa+vr6YOXMm7OzscPv2bQDAli1bULBgQXTu3BkAUKFCBdja2uLSpUuoXr06TE1NAaS27Gjfvj06dOiAkSNHAkhtAWJmZoZChQrp5uCIiCjfYlKDiIiIiIiIiOgrFBcXBx8fHzx48AA3btzAzp070bdvXzx8+BB6enp48eIFTExMYG1tDQBQqVQ4ffo07OzsUKhQISQmJsLQ0BAAkJCQAIVCAQMDAwBAUlISGjduDE9PT50dHxER5U/sfoqIiIiIiIiI6CtUoEAB1K5dGw8ePJDKtmzZAoVCgcePH6Np06ZwdXWFEAI7duxAcnIyjIyMAABv377V2lbv3r1Rr169dN1WERER5TQOFE5ERERERERERAAAPT09uLu7o3Xr1tixYwe2bNmC0NBQtG/fHpGRkZDJZNBoNNnenlqtBjsJISKinMSkBhERERERERHRV0ypVCI5ORkhISEoVqwYjhw5gnPnzqF58+YwMDCAp6cnypYti8qVK2P9+vUwNDSEubk5LC0tpc+ff/6JKVOmaJUVKFAAhoaG8Pf31/UhEhFRPsIxNYiIiIiIiIiICEDqoN9mZmYZznv37p00ODgREZGuMKlBRERERERERERERER5ArufIiIiIiIiIiIiIiKiPIFJDSIiIiIiIiIiIiIiyhOY1CAiIiIiIiIiIiIiojyBSQ0iIiIiIiIiIiIiIsoTmNQgIiIiIiIiIiIiIqI8gUkNIiIiIiIiIiIiIiLKE5jUICIiIiIiIiIiIiKiPIFJDSIiIiIiIiIiIiIiyhOY1CAiIiIiIiIiIiIiojyBSQ0iIiIiIiIiIiIiIsoTmNQgIiIiIiIiIiIiIqI8gUkNIiIiIiIiIiIiIiLKE5jUICIiIiIiIiIiIiKiPIFJDSIion959uwZwsLCdB0GEREREVG+plarERgYiJSUFAghEBgYiMTERK35arX6o7ap0Wjw4MEDREZG5nS4RET0hWBSg4jyrPv37+P58+dZLrNjxw4oFAqtC+P3BQYGYsaMGYiLi0s3LyEhQfr/x1xIx8fH4/Lly9lePjOHDh3C8ePHP2ndxMREzJkzB7du3frPcbxv8+bNqFq16kets2PHjhypj4w8ffoUSqUyXfmKFStw8uTJdOUPHz5E//79kZiYiIMHD2a63cWLF8PV1RUajSbD+W/fvsX+/fvx66+/ZrqN48ePY/Xq1enKlUolfvzxR/j5+cHDwyPD+AHg3r17+Pnnnz/6ZszX1xdCiI9ah4iIiIj+u6SkJCQnJ6e7FhNCICkpCSkpKQAg/Ztdb968wZ07dz643MyZM7FkyRIAwOzZszFnzpyP2k8atVqNZs2aYf/+/R9c1s/PD3/88Uem8+/cuYOoqKhM50dFRcHR0RFPnjxBcnIyHB0dce3aNWn+zZs3YWtri/v372c7/pSUFFStWhVHjhzJ1vLR0dEf/fnYc0hERDmLSQ0iypNSUlLQqVMn9OjRI8uEQ4ECBaBWq2FoaJjh/JcvX2LBggXpHizv2LED3333nXRDMm3aNNSoUSNbsS1ZsgSurq7w8fH54LI3b96ETCaDr69vunkbN27E7t27P7iNd+/epYvf2NgYv//+OzZv3qxVLoRAcnKyVsLmY6jVagQFBaUr37dvH2QyWYbbXbhwITw9PT9pf1kJDg7G999/j5s3b6abd/bsWfzzzz/pymNiYnDmzBlERERg9uzZmDp1arplhBA4c+YMhgwZguDgYMyaNQs//fQT+vbtiyZNmqBkyZKwsrLCgAEDsGvXrkxbdDx69AiXLl1KV66vrw8PDw8kJyfj0KFDaNGiBd69e5duuZs3b2LTpk0oUKAAgNRkyJMnTxAYGJjhJy1xN27cOMyYMSPryiMiIiKiT1a1alUULVoUJUuWlD7jx4/HqFGjYGRkBD09PchkMumjp6cHY2NjrF+/HiqVCrVq1cKkSZOgUqnw4sULnDp1Kt3n/YfmJ06cwA8//CBNnzx5Env27NGKSaVSYd26dXj8+DEAwNnZGfPmzcPOnTszPAZ/f388e/ZMupb09/eXrvP37NmDy5cvo1q1ah+si7/++gtDhw7VKnv/2rZmzZq4cOFChusmJSXBwMAAAGBkZCT9X6FQIDk5GUDqNbGFhQVcXFw+GEuatHu/tO29Lzk5GUlJSVplhQoVQsGCBT/qs2XLlmzHQ0REOU+h6wCIiD6FgYEBNm3ahGbNmmHp0qWYOnUqkpOTYWhoCJlMprVcmrQbg/fLFIrUX4MmJiYAID00bty4MYYMGYIDBw6ga9euUCgUMDY2/mBc3t7eWL58OWrUqIFx48bh5MmT0j4yYmRkpPXv+/T19TNNxryvdevWuHLlCszMzKCvrw8g9aZGoVDgyJEjWm8opSU0fvzxR+kG59SpU7h37x709fW16q558+aoXLmy1r6MjY2hp5c+H54Tx/ExNBoNOnfujPHjx6NevXoAgNjYWPzyyy8wNDSEv78/jh07hsDAQDRs2BDt2rUDkJrk0tfXR7FixXDp0qUMkw4XL17Ey5cv0a1bN4SEhGDevHmYPn06nJ2dUblyZUyaNAlPnjxBuXLlMoxt/vz5iImJwb179xASEoIJEyagaNGi+Omnn6RlzMzMYGpqin379mHnzp3S9+99T548wQ8//CB9X0NDQ+Hi4pLuO572RuDJkyfRokULuLu7o1atWqhTpw5at2796ZVMRERERBny8PAAkHpfcfz4cUyfPh3t2rVD7dq18dtvv8HQ0BDdunWDnZ0d1q5dK7XUkMvlUCgU+PnnnzFw4EAEBASgRYsWmDBhAipUqAAg9Xr9wYMHiI2NRVxcnPSw//17mKdPn2Ly5MkwMzOTkh0eHh54+/Ytxo4dCwDo0KEDNm7ciPr160MIIb0ElbadoUOH4tatW0hISIBcLoeBgQG+//57bN++HVOmTIFCoUC1atUghEBsbCxmz56NWbNmpauLf8cGAA4ODti+fTs6dOgAIyOjDO8RAKBJkyb4+++/AUDr2rphw4bo1KkTDh48iPPnz6NLly7S9a9Go0FycnKW92Zpy/bp0wd9+vRJN79Xr15aL48pFAocPnwYbdq0kcoWLFiAzp07Z3jN7+TklGHChIiIPh8mNYgoz2ratCn69OkDb29vREZGwsbGJtNl5XI5AGDRokWYOnUqQkJCEBkZidDQUABAQEAAjI2NcfToUbi5ueHRo0fo0qUL5s+fjy5dukBfX/+DF67R0dHo0qULBg4ciBUrVqBhw4bo168ffv/99wwTAe/HldF8AwMDKUmRZv/+/YiJicHgwYOlsjNnziAyMhJFihSRyjp16gQDAwO4u7trbV+j0eDGjRtarU4iIiLw9OlTKBQK6QZgx44dqFixIsqUKZPuIXradpKSkqSH8R97HKtWrUK5cuXQqlWrDOvlQ7Zu3QpDQ0NMnDhRKpPL5XB0dISxsTHMzMxgY2MDR0dHFCxYEB06dMCdO3cgl8sREhICR0dHKJVKxMTEoHTp0nB2dtbatq2tLYoWLYo3b95AT08P8+bNAwCEhYVh0qRJKFiwYKaxOTg4wNraGi9evEB0dDRKlSqFggULYsWKFVi2bBnMzMwQHx+P+vXrQyaTIT4+HvHx8RgzZgyA1O+RoaEhbt26hb59+0pvktnb26d7q+z27dto27Yt6tatKyV3bGxssHnzZgwfPhxNmjTJ8YQSERER0deuXLlyWLhwIUqWLIk5c+bg0KFDaNiwodYyz549Q/PmzQGkPmR//yF8ly5dUKVKFURHR+Off/7Bt99+i4sXLwJI7R7X0dER+vr6aNasGTp37gw7OzutbY8bNw4JCQno2rUrTpw4gcaNG2PZsmUAgG+//TbTuKdPn4758+cDAM6dOwcAKFGiBKZPn44hQ4ZACIH27dvD3t4e/v7+MDY2hoeHB8aPH6/1gg6Q2oI7JSUF+vr6UCgU2Lx5M1q2bAm1Wo3o6GiplYeenp50j6BSqbRa0Z87dw4ajQampqZ49uwZnJycIJPJcOnSJdSoUQMJCQk4d+4cjh8/jqVLl0r7Ll26NPz8/D54njZs2IDOnTtL00IIJCYmpruv+/e9yqlTpzBjxgwULFhQq+5NTU2lZdPuf4iISEcEEVEelpiYKIQQQqVSCR8fH/H8+XMREBAgfbZt2yYAiOfPn4unT5+KsLAwIYQQK1asEGZmZsLc3FwAEGZmZqJr165i1apVonbt2kIIIW7duiUACE9PTzFz5kzRuHHjTOOIi4sTtWvXFi1bthQpKSlCCCHevHkjypUrJ7p16yYSEhIyXO/hw4cCgAgICEg3r127dmLgwIFCCCHevXsnxo8fL2QymXBxcdHanru7uzA1NRU7d+4UQgixa9cuUaJECfH48WMREBAgnJycxLlz50RiYqLo0aOHsLCwEP7+/pkeS0hIiAAgNm7cKABk+YmKihJCCHHs2DGR2Z8UZ2dnMW/ePCGEEBEREaJnz54CgGjZsqXQaDSZxpEVJycncf369Uznt2vXTqxdu1aabtOmjXBzcxNxcXFCoVBkut/nz58LhUIh7OzshBBC3L9/X8jlcml+bGysACACAwM/GOOqVatEp06dpOnly5dL57NatWriypUrGa5namqaYV2/fzxCCLFp0yZhamoq5s6dm+F2mjdvLjw8PD4YJxERERF9vH379gkTExPRtWtXqez169fCyspKWFlZCZlMJszNzaXptHsMIYRISkqS/u/m5iZcXV3FxYsXRd26dcWDBw8EAJGYmCgaNGgg1q5dK9zc3ESJEiXSxTBq1Chx6NAhsWvXLgFAFCtWTERFRYmoqCjh4eEhXa+HhYWJly9fiujoaK31g4ODBQDx7NkzIYQQc+fOFVZWViIoKEgIIYRarRYVKlQQq1evTrfvu3fvCgMDA7Fz505RokQJ0aFDBzFt2jRx+PDhLO8fhg4dKm07ISFBJCQkaMUAQFy6dEkkJSWJnTt3CoVCIXbs2CHc3NyEvb29GDFihNi7d+8Hzw8A4ebm9sHlhBDCwsJCHDt2TAiRel/n5OSULm5jY2OpXkqXLi127NiRrW0TEVHu4JgaRJQnvX37Fmq1WmrKLJfLUaFCBVy/fh2FChWS+ra1tbUFADg6OqJs2bJSa44JEyYgLi4Oly5dglwuR1xcHPbt2weFQiG9OVSjRg24u7ujUaNGkMlkmQ4aHRoaigYNGsDS0hKHDh2S3t6xtbXFX3/9BV9fX7i4uEhvX2XE398fT548waNHj3Dv3j28ffsWQOobULt27UL58uVx+PBhbNu2DXfv3tV606tnz55Yv349du7cidWrV6N///5o1KgRPDw80KxZM7i4uOC7777DnDlzcOfOHdy5cwelSpWS1g8PD8fkyZOlsUkePHiA4sWLo0ePHnj+/DkiIiIQFRWFqKgorFu3DoUKFUJERARCQkJgbm6udRxPnjzBkydP8PDhQ9y9exfx8fEAUrtI+vXXX1GuXDl4eXnhjz/+gKenZ7oWINnh7e0NtVqN2rVra5UnJycjJSVFGgdF/G/8kLSm/kBqt08GBgaIioqCu7t7urE+5s6dm2VT9rSWKWn9BCuVSq0+g1UqFZKSkrS+K2nLvP82l52dHcLCwuDl5SW9VZfmxYsXOH78OMzMzBAXF4f4+HgYGRmhYsWK0jLBwcEYOnQoVq5ciV9++SXDWHv06IEDBw5keixERERE9PGEEEhJSUHXrl1x+vRpfPPNN9I8fX19REZG4ubNm4iOjsalS5cQFBSEpUuXSmPPHT9+HBUqVICXl5fWdvX09PD333/D1NRUKvvQtfLatWtRq1YtTJw4Eebm5tDT04OlpSUsLS2l7VhYWKBQoUKwtLSEhYWF1vp//PEHqlSpAicnJwDA+PHjsX37dpQoUQLW1tYoVKgQnjx5gnnz5sHa2hpyuRwHDx4EADx//hzFixeXrnF79eoFd3d3nD17Fp06dcKrV6/w6tUrmJiYYM+ePXj16hWCg4OlFtD//PMPTExMpOvrMmXKSMfr6uqKTp06Yd26dZDJZOjbty969+4NuVyOli1bonv37lrHoVQq0403B6S2SP93eVZjC2o0GvTv3x9+fn6YOXMmhBCIiopCyZIlsXjxYhQvXjzL80FERJ8PkxpElCcVL15c6i4prUnxixcv0KdPn3SDY2dX2sPw9/Xs2RNGRkZQqVQZ3lQcOHAALi4uKFWqFJYsWYLXr19rXTQrlUps27YNZcuWRaNGjdC4cWPs2bMn3b46dOiA2rVro1atWqhVqxbOnj0LANi1axcWLFiA6dOn49mzZ+jfv3+GXTz16dMH58+fR4UKFdC8eXNYWlpi6dKlqFq1Knbt2gUTExPMnDkT58+fl25a0kRFRWHXrl0YPXo0gNTurOrVqwdzc3NoNBpoNBrp5sjExAQymQxWVlawt7dPF0vt2rW1jiNtsPSFCxdi165dWLduHby9vdG+ffvsn5h/8fb2Rp06ddKVT5o0CYUKFUKhQoXw119/YcKECbC1tcXAgQO1lnNwcEBoaCjc3d3x9OlTqfzChQvYtWsXRowYobW8Wq2WBnlMGx+lYsWKkMlkMDAw0GqSfvToURQsWBCFChWSuiKwtbVF+fLlM4zh9OnTuHr1qta8QoUK4caNG2jYsCHMzMwQFBSE5ORkrcER05J1VapUybSe6tatC29v70znExEREdHHe/PmDQwNDSGXy9GwYUMsWLAACoUCCoUCly9fBpD6wpWfnx+qVq2K8PBw6OnpSS8+NW7cGDVq1EDdunW1rgMVCgWMjIyyHI8vI4GBgbC0tMTYsWMRFBQkXbe2bdsWQGqyRKFQwNTUVHrhCEh9gL9hwwYkJSWhd+/e6N27N65du4ZatWpBLpcjIiICx48fR7FixRAREYGIiAgUKVJE6rrp8ePHWgmdNm3aYN26dTh48CA6duyIwoULo3DhwpDJZChYsCAKFy4MBwcH6SWzsmXLIiEhARqNBkIIrU9SUhJ69eoFb29vqFQqvHnzBgAQGRmZLjEDAH5+fnB0dNT6AKlJmn+X//ulpvd5eHjg8uXLOH36NDZt2oRJkyahZcuWaNq0qdRVLBERfRmY1CCiPOnWrVvw8/NDy5YtpdYaxYsXR6dOnbBmzRqp1UF2LVu2TGt8hn9LSUlJNzZBv379MGTIECxfvhzdu3eHs7MzSpUqle7CuWbNmhg7dixOnTqF6OhonDt3Ll2CxNvbG9HR0Xj37h2USiW6desGIDXZ8eTJEwwZMiRdX6/vCw8PR5cuXVCzZk0cPHgQjx8/Rrt27eDm5obGjRvj119/xblz51C/fn1pML40ZcuWxZkzZ+Du7o5Fixbh2LFjUv+/vXr1kvrdzY7o6GhER0cjISEBKpUKtWrVAgCMHTsW9+7dQ7du3TIdXyS7Xr16pTV+SJo1a9YgPj4eUVFR2LBhA6pVq4aYmBhpXJE0JUuWxL1793Djxg0MGDBAKlcoFBg0aBCqV6+utbxcLpdaqkRFRaF69er47bffEBUVhdevX8PX11datlOnTkhMTER0dDQePnwoxfvixYt0MTx//hyHDx/GqFGj0h3LuXPnpHNw5coVlClTBlFRUbh37x58fX3x/PlzAEBQUJDUMub+/fta2yhSpIg0ZgwRERER5Qw7OzvEx8dDpVKhffv2mDFjBu7evQsAWi2JExMToa+vjxIlSmitn9ZyYdKkSahcubLWvMwG1M5MYGCg9CKLra0tihUrJl2zpg1m/vbtW4SHhyMoKEirFci6devw+PFj1K9fHy1atMCDBw8QEBDwwX2mJTVu376NSpUqSeWGhoYwNzeHEALt2rX74Hb09PRgbGyM9u3bw87ODg4ODnBwcECRIkVgZ2cHGxsbbNy4EUWLFoWPjw/Cw8Px7t07FC1aNNOYLly4kC5Bkvb590DpGenZsycePnwIV1dX9OjRA8uXL4evr690b0ZERF8ODhRORHlShQoVAKTeFLz/NtOgQYOwf/9+nD17Fi1atMh0faVSiZMnT2LDhg1Qq9X4+++/MW3aNNy+fVtaJiEhAZGRkdLNgaWlpdY2Vq5ciYULF6Jo0aJQq9VQKpWQy+XpEhbJyclQKBSQy+Vo3rw5EhMTs32cBQsWzDIJoNFosGPHDvz8889o2rQpLl68iBkzZkClUqFZs2aYNWsW3r17h6lTp+L27dvo3LkzXF1dsWbNGq0WCc7Ozti/fz/atGkDuVyOH374AQAwbdo0dO3aFRMnTvxPza2trKw+ed1/09PTy7QrsE2bNqFRo0aZrhsaGooKFSpgzpw5aN26tdabXvXr10f9+vWlJvXve//cly5dGuHh4em+D2lOnjwJfX19rTfXMoph/PjxUKvV+P7777XmBwYG4ubNm9i/fz8A4PTp02jevDmWLVuGbdu2wcDAQPrODxo0CHK5HCqVCpaWlggJCZG2o1arOYAhERERUQ6TyWRayQEg9QWj7t27w8zMTCpLTEyEnZ1dhq299fT0MHnyZMjlcum6NiIiQuvaNLPr3TRCCNSvXx/Lli2TumNK634KgBRjwYIF061769YtTJkyBSYmJqhduzZ69+6NnTt3wtDQEGq1Gmq1GpaWllCr1Xj37p20zdjYWGkbs2fPhpmZGW7duiWVNWjQAM+fP8eTJ0+kFt5CCISEhODx48dITk6Gg4MDrK2tpXWUSiVGjx6NGTNmAACOHDmCDh06oE6dOjAxMYG7uzuuXLkCtVoNPT092NvbpzuerBIV/5bVi2InTpzAmTNnsH//fpQoUQJnz57FzZs30alTJ9ja2sLV1RVDhw7N9r6IiCj3sKUGEeUrjRs3hpubm/SWe2aWLFmCnj17wsLCAnK5HEeOHJFaFQCpNxG9e/eWWik8ffoUJUuW1NpGoUKFULRoUSxcuBAKhQL6+vrQ09OTmnynfdKakaeNb5DVmA0fS6lUYvPmzZg7dy52794NuVyOwoULo2XLlli0aBGCg4MxZ84cVK1aFRcuXMCyZcuwdetWuLq6pttW8+bNUalSJSgUCkRFRQEA2rVrB0dHx3TjPuhSVi0Qtm3bJrWcePDgAYoVK4Zdu3ZBCIENGzagW7duqFmzJp4/f47Bgwd/8GYxI+XLl8ejR48ynX/hwgUcP35cmi5XrhyGDRsGIQRu3boFZ2dnVKhQAQEBAem6xgKAu3fvQiaTYc+ePYiMjMSpU6fQsWNHbNiwASkpKYiPj0d0dDQA4OzZs4iOjkZ8fLxWQgPIvEULEREREeWsJk2aYPfu3VpdzIaEhGi1Kvh397M7duxAgwYNkJSUBCD12u395ZOTk7Pc59WrVxESEoJq1apJZRqNRmo5nTbuW1RUFCIjIxEaGirFUKpUKSxduhT169dPt9208eiio6Nx8uRJFC9eXNrm+/F9++23KFu2bLr19fX18d1330nd0iYkJGDcuHFSF7UnTpzQWt7AwACzZs2SuvDq1KmTVA6kdql64MABnDx5EhUqVMiwNUvaNf2/uwJ+/xMUFAQgdQy8zDx69Ahv377F77//jhEjRqBfv35ISUnB5cuXMXnyZISGhqa7JyQiIt1gSw0iylfkcjl69+4tTWd20TpixAgMGTIEoaGhGb6ZP2bMGNy6dQsXL15EYmIiHjx4gJEjR2a4LQMDA7i4uODChQuZxlWwYMF03VdlJu3BdXYIIfDXX39JiZK2bdtK/eeePHkSLi4u6NixIzp06CC9Jda3b18olUoolUqtN5X27duH58+fo0aNGujbty8uXrwIPT09TJgwQWqu/TGyewwfq1q1apg8eTKEEOnefIuIiICtrS3i4+Ph4uKCS5cuAQB++eUXWFpa4siRI1i0aJG07Lx581C1alWpZUpW3rx5AwMDA1SvXh2bNm3KdLmIiAitcUt8fHykrq1CQkKwb98+6Y22iIgInDlzBleuXMGCBQsApHZh9ccff2DgwIH49ddfUbx4cTRs2PCj6ggALl68mK4rLSIiIiLKPe/fe9y6dUur5W5KSorWslu2bEGVKlVQrVo1GBsb46+//kLx4sVhZWWFDRs2wMjICGq1Ot16769fr149lClTRioLCQmRHrqnxVKyZEmo1WokJycjKioKZmZmsLa2xqhRo7RexEkTFxcHjUaDNm3aICoqCmFhYWjTpg2A1DEtPsTIyEgrZjMzMxw8eDDLVvRz5sxJ11IjTfv27TFnzhw8e/Ys01YSaYmhHj16fDC+rJJFU6ZMAZBadyqVChqNBitWrMD169dx5swZDBo06IPbJyKiz4MtNYgoXwoMDMTly5fxxx9/aDUDT1OoUCFpsOX3xcXF4fr169i/fz/OnTsHJycnbNu2DXFxceneKkojl8shl8ulwbQz+gD44FgSQgjs3bsX5cuXl/rAzcjr16+l/48bNw4FChSQBk1///P06VNMmjQJMplMqwWJnp4eDA0NsW3bNmk7L1++xMiRI7Fo0SLs3r0bDx48wNq1awEAQ4YMyTShkxGlUon169fDyclJGizx39LGnfgU5cqVQ8GCBXH+/Pl02wwKCsLIkSOlOjI0NIShoSFWrlyJa9eu4cKFC7h16xbc3NzQr18/rFy5MlsJm7i4OLRp0wYHDhxA/fr1ERERkW5skjSPHz/GsmXLcPPmTQD/P+jj8OHDcePGDVhZWWHatGk4fvw43NzcMHDgQK2m/ADwww8/4NixY3j16hWSk5MRGBj40fW0e/dudOnS5aPXIyIiIqJPo1KpUKJECSiVShw+fBhNmzYFABQtWlTrZZPbt2/Dy8sLkyZNQrVq1dCrVy+cPXsWDRo0wKFDhzBs2DAYGxsjOTk5w6RGUFAQPDw8tMaHA6DVqiLtfiImJgbx8fFQKpUZ3hcNHjwYMpkMf/31F4DUFiPW1tY4fvw4lixZAltbWxw/fhzHjx/P0S5ls8vFxQXfffcdlEplpkmNlJQUqFSqDMfS+PdA5NkZ72Pu3LmoVKkSHj16hM2bN2d5b0ZERLrBpAYR5VleXl64e/cuvL290z309ff3h6urK3bv3o1hw4Zle5t37tyBoaEhTp06hW+++QY3btzA5MmTMWDAAOzfvx9bt25Nt05G/eRmV9pbRYcOHUKVKlXQt29fNG7cWHob6t+ePHmCEiVKSBfWq1evxrt37zK8iC9XrhyWLVuWrlytViMpKUnq+igqKgqtW7dG/fr1MWLECNjb22P27NmYOXNmtltbpB3H5s2bUbZsWUyePBk//vgjatasmeHyly5dgr29Pa5cufJR9ZVmxowZmDhxotaA8A8ePEClSpXQtm1bTJkyBcnJyVJT9M6dOyM0NFQaCL13795YuHAhunTpgo4dO2a6n7Q6a9OmDZRKJTp06ABzc3O0bt0aS5cuBZDaCiRtMPKUlBQ8fvwYq1evRp8+fQCkjs0CpDadL1GiBAYMGICNGzeidevWOHr0KJydnTFz5kyt/cbExGDEiBHo1q0bypUrh/r16+PZs2fZrp8DBw5IiRgiIiIi+jxsbGwQGBiI9evXw8DAAA0bNkSzZs1ga2uLHTt2SMstWLAArVq1kgba3rNnD968eYMCBQqgf//+2LJlC4DU1h5Tp05Nt5958+bBxMQk2wNYCyGgUqmk69L3bdmyBUIIfP/991Aqlbh79y6+/fbbTzn8TyKEwC+//CK9gPV+Kw0gdTy6yMhIyGQyHDx4MF33sWFhYfj222+xfPlyqNVq/Pjjj9LLT//88w8qV66MsLAwAKlddpUtWzZdi5P37ykAYPTo0Zg+fTq8vLxQr169DO8XPqUbWyIiyjlMahBRnrRmzRo0atQIGzZswKxZs9CiRQu0bNkSS5cuxbFjx6BQKLB582acP38ePXr0wI0bN3Du3Dl4enpqbeffzY83b96M06dP49tvv4WHhwe+//57tG/fHlu2bMH06dMxfvx4BAcHa60jhJDGQcjsA2R84ZvWZdW0adPw3Xff4Z9//sGWLVtQvHhxGBsb49atW/Dy8sKTJ0/w5MkTrFq1CkqlUrrRMDIygomJyUcNCJ3WUkNfXx8vX75EnTp1YGxsLD2YB1Iv5P/dykWj0SAgICDDbrTSjmP8+PHo0qULAgICsGzZMlhbW8PY2BiXLl2Cj48Pnjx5Al9fX6xfvx5CCFSpUiXbcb+vW7duKFKkCCZNmiSV7d+/H61atcLcuXNx6NAhxMXFwdHREaNHj4abmxvCw8Nx8OBBqFQqPH/+HN9//z0GDRoEb29vrW2r1WrpXD179gwajQZxcXHo0KEDNm/eDACYPn06jh8/jt27d+PMmTN4+vQpgNQuv8qXL4++ffvi5s2bcHV1haOjI3r37o2NGzfi77//xtatW1GuXDkEBASgcOHCmDdvHnx8fKT9BwQEoHHjxrCwsMDvv/+OI0eOoHjx4pg2bZq0TFrLjoxa//j5+WHs2LFYv349BwonIiIiygUvXrzA9evX8eTJE62BuFNSUjBmzBhs27YNe/fuRdGiRdGxY0c0atQI69atAwD89ddfOHr0KMaOHQsAePfuHebMmYMBAwbgxx9/xE8//YTx48fD399f2u77D901Gg2ePn2K/v37w8TERCoXQiAoKEi6/0jrkjatpba+vj4KFSqkdRzvb7dBgwawtbXFrl270Llz5wyPO6P7mbSXgIDUF51CQkIQGRkptRhJO8bo6GhEREQgODhYq/VJSkoK5s2bByEE3r59i3nz5kkx37t3Dw0aNMC3336LI0eO4LfffkP9+vVx7tw5af3NmzfD0NAQgwcPhlwux8uXL/Hrr78CAEqUKIHY2FjMmTMHAFClShUkJSVh8uTJWsegUqnw6tUr+Pn5wc/PDzExMahfvz42bNiA06dPY8iQIdI8Pz8/KJXKLMfmICKiz0AQEeUxERERolatWmLfvn1SWWJioli7dq1o3LixsLe3F6ampkJPT08A0PqMGjVKWmfhwoXCzs5OlCxZUmv7/v7+olOnTgKAGD58uFAqlUIIIZKSkkTx4sXFokWLtJZfvny5cHZ2FuHh4Zl+AIj9+/enO5Y3b96IDh06iH/++SfdvFOnTgkbGxut+A0NDcUvv/ySrXpycnISS5YsyXIZjUYjfvvtNxEdHZ3lcufPn5fqc9CgQenm+/j4iO7du4vQ0NB083bs2CHMzc21jsPMzExs3LgxW8eRmaioKFGhQgVx4cIFIYQQzZs3F8+ePZPmq9Vqcfr0aTFq1ChRt25d4eDgIAoUKCD09fW1vhsHDhzQ2q6bm5uwtLQUQqSeg8qVK4uIiAjh6ekp5HK5OHHihBBCiLlz5woAQiaTiUePHgkhhFiwYIHYtm2b1vbu378vZsyYIZo1ayYcHR2FpaWlMDAwEHK5XMhkMgFAjBw5UgghhJeXl7CwsBBNmjQRMTEx0jbCwsJETEyMSExMFO3btxflypUTBgYGIjIyMl29uLq6/ue6JSIiIqLMnTlzRpiamorGjRuLV69eCSGEuHHjhihevLgoX7688PLy0lr+xo0bwsrKSixfvlzMmTNHODk5CY1GIzQajejQoYOwtLQUL1++FEKk3nOUKVNGjB07VgghxMyZM0X58uVFlSpVpO2p1WoRGxurtY9Vq1YJBwcH8erVq3Sf0NBQERwcLPz9/bXWadq0qdi5c6c0PWzYMFGiRAmRmJgohBDiypUrokSJEiIhIUHMmjVLyGQycfHiRa1tbNmyRRQuXFgIIcSFCxfS3X9l9Hn48KG0fuPGjcW8efOEEEL4+fkJAwMDMXLkSHH69GlRuHBhsXXrVqHRaIQQQrx69Ur06NFDDB06VCQmJorIyEhhZWUlJk6cKG3Pzc1NyOVy8eLFCyGEEOvXrxf6+voiMDBQCCHEtm3bhEwmEzdu3JDW0dfXz1bc73/WrVuXybeDiIg+B5kQ/0upExHlIcnJydkeeDsz58+fx61bt9C7d284ODhI5cePH8eoUaOwevVqtG/fXmudf/75B2XKlNHqcmrhwoU4fPgw7ty585/iyWnFihXD4MGD03Vt9CmEENi2bRtq1aqFSpUq/acut3LS27dvpTfO1Gp1jrRM2LFjB0aNGoV3794BSH3jzMjICAAwefJk1KtXTxpY/OzZszAwMICrq6u0/n+NY+fOnejVq5fWIO7vGzVqFBISEjBw4EDUrVs33fz364SIiIiIPp8TJ06gadOmMDAwSDfP398f9vb2MDIywuvXr1G4cGEkJydj6dKlcHZ2lq4vgdQx2pycnKCvr48lS5bg4cOHGDdunNa4HP+2ZMkSbNiw4ZPGYktz8+ZNJCUlSde2iYmJCA0NRenSpdGzZ0+UK1cOM2bM0LrW3bBhA2bMmIHIyEipm1tDQ0MoFIp020+bb2xsnOl4g+9fS6ekpGRYl2kiIyOxfPlyjBs3DnZ2dlLZ3r170b9/f5iamiIhIQHbt2/HkCFDYGBgAJVKhcmTJ2PChAnSPaCenh7+/PPPbHfd6uTkhDFjxmDMmDHZWp6IiHIekxpERBnQaDQfHNibiIiIiIiI8jY/Pz8ULVpUqzsvIiL6sjGpQUREREREREREREREeQJfQyYiIiIiIiIiIiIiojyBSQ0iIiIiIiIiIiIiIsoTmNQgIiIiIiIiIiIiIqI8gUkNIiIiIiIiIiIiIiLKExS6DuBz0Gg0CA0NRYECBSCTyXQdDhERERHRfyKEQFxcHIoWLQo9Pb6nBPCan4iIiIjyD17vZ+2rSGqEhoaiWLFiug6DiIiIiChHBQcHw8HBQddhfBF4zU9ERERE+Q2v9zP2VSQ1ChQoACD1S2Bubq7jaIg+TKPRIDw8HDY2NszGEhF9JvzdS3lJbGwsihUrJl3nUv6/5ufvqNzF+s19rOPcxzrOXazf3Mc6zn2s49yVk/XL6/2sfRVJjbTm5+bm5vnyBofyH41Gg6SkJJibm/OPDBHRZ8LfvZQXsZul/5ffr/n5Oyp3sX5zH+s497GOcxfrN/exjnMf6zh35Ub98no/Y/z2EhERERERERERERFRnsCkBhERERERERERERER5QlMahARERERERERERERUZ7wVYypQURERPQlUavVUCqVug6DvmByuRwKhYJ96OawvPyzp9FooFQqkZSUxD6w/yP+fBERERHlbUxqEH1GU6ZMga+vL44dO5at5f38/FC7dm28fftWKps9ezbmzJmTbtkLFy6gYcOGORUqERHlkvj4eISEhEAIoetQ6AtnYmKCIkWKwMDAQNeh5At5/WdPCAGNRoO4uDg+jM8B/PkiIiIiyruY1CD6TLy9vbF+/Xp4eXlla/mgoCD8+OOPiIqK0iqfOnUqxo0bp7Vc06ZNUbVq1ZwMl4iIcoFarUZISAhMTExgY2PDB5OUISEEUlJSEB4ejoCAAJQpUyZPvpkfERGBGjVq4MKFCyhZsiQA4OjRoxg/fjxevHiBSpUqYe/evShfvjwAYMyYMVi7dq20funSpeHn55cjseSHnz0hBFQqFVsY/Ef55eeLiIiI6GvGpAbRZ6DRaDBkyBCMHz8epUqVytY6ffr0weDBgzF58mStciMjIxgZGUnTkydPxrhx42BhYZGjMRMRUc5TKpUQQsDGxgbGxsa6Doe+YMbGxtDX10dQUBBSUlK0/vbnBREREWjTpg0CAwOlMn9/f/Tv3x8bN26Eq6srRo8ejUGDBuHvv/8GANy5cwcnTpxAnTp1AKR2EZRT8sPPHpMaOSev/3wRERERfe34SgrRZ7Bx40Y8fPgQJUuWxJ9//omUlJQPruPm5oZOnTpluUxoaCj++OMPjBkzJqdCJSKiz4APJCk78vLb4927d0fPnj21yh4/fozFixeja9eusLOzw/Dhw3H//n0AgEqlgo+PDxo0aABLS0tYWlqiQIECOR4Xf/YoTV7++SIiIiL62vFKjiiXxcfHY9asWShVqhSCgoKwatUq1KtXD4mJiVmuV7x48Q9ue+PGjejRowfMzMxyKlwiIiKde/XqFdq3b4/o6GgAQHJyMjQaTbbXf38sKgBITExEQkJCttZNTk7G8uXLs72vjxEaGopdu3blyra/NFu2bEn30kWbNm0wZMgQafrp06coU6YMAODhw4fQaDRwcXGBsbExWrRogRcvXnzWmImIiIiIKG9g91NEuezw4cN49+4dLly4AGtra6hUKlSuXBlubm5aN/YfS61WY8uWLfjrr79yMFoiIqLsuXbtGho2bIiiRYsiPj4eiYmJsLGxQVxcHJo0aYJ9+/YBAKKjo1GwYEGkpKRAX18fY8eOxa5du2BpaSltKygoCJGRkShUqBAAwMbGBoGBgfj555+xfPlyVKpUCfr6+tDX19eKISwsDJMmTcKkSZOkspcvX8LR0RE3b96Uxptyd3fH/PnzERAQ8ME39YcPHy6N/5DTLCwssGbNGpQoUQINGzbMlX18KRwdHbOcn5KSghUrVmDChAkAAF9fX5QrVw5r166FtbU1xo8fjyFDhuDUqVOZbiM5ORnJycnSdGxsLIDUbj//nQTTaDQQQkifvCQqKgpyuRzm5uYQQiAiIgIRERH45ptvkJSUhJs3b6JBgwZZfrcfPXqEt2/fokGDBp8lZiHEF98qJu27kPZ9SfuOfEwClT4O6zj3sY5zF+s397GOcx/rOHflZP3yHGWNSQ2iXBYSEoLatWvD2toaAKBQKFClSpX/PPDlhQsXYGVlhQoVKuREmERERJlasmQJ5s2bB4Ui9dIxMTERv//+O8qWLYtHjx5h586dOHXqFDw8PLBx40Zcu3YNYWFh+OWXX6RtjBkzBjVq1IC+vj7GjRuH2bNnS/NkMpm0bSD1b+XSpUtx4sQJmJiY4Pnz5wgNDcXly5dRtWpVbN++HUuWLMHs2bNhYGCgFau7uzsaNGgAT09PtGvXDnp6eoiPj0d8fLz0oF2pVGLTpk1o06aN1rrnz59HUFAQtm/fDiC1teWAAQNw/PhxKBQKjBkzBvPnz/9gfSmVSgwfPhwHDhxA5cqVsWfPHhQvXhympqbw8PBAp06dcPfu3XRJmq/JrFmzYGpqikGDBgEAevXqhV69eknz169fD0dHR8TGxsLc3DzDbSxatAhz5sxJVx4eHo6kpCStMqVSCY1GA5VKBZVK9Z9iVyqBxETA2Bj4HKdwypQpKFeuHMaMGQO1Wg1PT0+sW7cOf//9Ny5evIiJEyfixo0bWY4Vcu7cOWzYsAE+Pj5Z7qt69epYuHAhmjVrhitXrqBZs2ZaLYKFEDA0NMTLly8zXP/27dvYsGEDnjx5gtOnT2PBggUIDg6Gu7t7to/Xzc0N9+7dw6pVq9LN02g0+PnnnzF06FCtxFnRokVx7do1KBQKlClT5oMtooHULs80Gg0iIyOhr68PjUaDmJgYCCHYNVUuYR3nPtZx7mL95j7Wce5jHeeunKzfuLi4HIoqf2JSgyiXOTg4pLuxCgoKkgbB/FT79+9Hx44d/9M2iIiIsmPixImYNGkSBgwYgCZNmqBr16549OhRpsvr6enB3NwcAwYMgKenJwBgyJAhMDc3x5MnT7B+/XocPHhQa520QaGTkpLQvXt37N+/H82aNZPmx8bG4qeffsK9e/eQkJAAtVqttR4AvHv3DitXrsTWrVvRvHlzTJ06FXK5HPPnz8ejR4/g4eEBILWVQEaDUM+ePRtr1qyRpsePH4/ExET4+PggMDAQrVq1gqurK5o2bZplfc2cORMXL17E5cuXcfPmTfz444+4dOkSAKBMmTJo1KgR9u7diz59+mS5nfzq/PnzWLduHW7cuJFpYsfW1hYajQavXr3KNKkxbdo0qaUHkPodKVasGGxsbNKtk5SUhLi4OCgUCq0E2sfw8wPOngUuXgSSkgAjI6BhQ6BZM6B06U/aZLYYGhqiYMGC0NfXh0qlQteuXfHs2TOo1Wrs378fS5cu1Rp/5Pr162jbtq1WmVqtRlxcnNTdl0qlQnx8PI4eParVesPY2BgmJiZQKBQwMjJC9erVcf36dWn+69evUatWLakONRoN3N3d8ffff+Ps2bMICAhAkyZNMHnyZCgUChgaGsLY2Dhdne/ZswcXL17USkrOmjULNjY2OHToEJo1a5bpeUpOTsacOXPg5uaGlJQUGBoaQqlUwsLCQqovhUKBlJQUKBSKTB8opM2zsrKCkZERNBoNZDIZbGxs+JAnl7COcx/rOHexfnMf6zj3sY5zV07Wr5GRUQ5FlT8xqUGUy1q3bo3Ro0dj48aNaNOmDQ4fPgwvLy8cOHAAsbGxMDY2/qQ3NU+dOoWdO3fmfMBERET/8u+HiwYGBtDT04Ofnx8qVaqE6OhoxMfHo1KlSoiKikLTpk2lB6IDBgwAADx//hyWlpbQ09PDiBEj0rXUSEsyGBkZ4dixYzh+/DjGjx8vvX2uVqvx5s0bNGjQABqNBn379oWTk5NWXIsXL8abN29QuHBh6Ovro3///nj48CFCQkKgVqtRvXp1AMCaNWvSvVzw5s0bREdHw8XFBUDqDYlSqYS7uzvMzc3h6OiImjVr4v79+1kmNTQaDTZt2oT169fD2dkZzs7O2LBhA/z8/KR4e/fujblz536VSY2AgAD06NED69at02ptOmnSJFStWlUaXPz69evQ09NDsWLFMt2WoaEhDA0N05Xr6emlu4nU09ODTCaTPh/r0iVg1SogPBwoWDA1oZGQALi7A2fOABMmALnVs5NcLpeO083NDStXroS+vj42btwIY2Nj3L59Gz169MCBAwfQokULyOVy2Nvbw9vbGwDw559/4ttvv4WDgwMAwMPDA7Vr19bqZu2ff/7BkydPEB0djevXryMpKQnW1ta4e/cubGxspOWEEChQoIBUh3K5HNHR0bC2tsaOHTswYsQIjB49Gj/88AMA7Xp/X8WKFWFsbCwlNQYOHIhJkyYhMDAQV69exd69e6V1VCoVhBDS9fKECROwaNEivHnzBkWLFoWxsTESExNRvnx5AEBCQgLMzc2RlJSEO3fuwNnZOcN6TYvr/e/Lv6cp57GOcx/rOHexfnMf6zj3sY5zV07VL89P1pjUIMplVlZW8PT0xE8//YQJEyagSJEi2L9/P4oVK4aSJUti9erVaN++/Udt09/fH6GhoahZs2buBE1ERJ9Vkiop03l6Mj0YyA1ydFkjxce99ZOcnIy4uDgolUokJiYiIiICQgg4OTll2P1U2pvda9askbqusbS0xKBBg9CuXTts3749XUuN98c50NfXR8eOHbVaJL579w5OTk64d+8ebt++DVdXV63EyO3bt7Fy5UoULFhQKvP398f8+fPRokULqaxFixbpuiYCUsccqFGjhjStp6en9fKAWq2Gn5+f9KZ7ZoKDgxEVFYXGjRtLZTVr1sTdu3elpEb16tXh6+ub5Xbyo8TERLRp0wbt2rVDhw4dEB8fDwAwNTWFs7MzZsyYATs7O6jVaowePRp9+vSBiYmJjqNObaGxahUQHw9UrAi8/3y+aFHA3x9YuRKwt8/ZFhv379/HTz/9BG9vb1y+fBknTpzA77//jiFDhkAmk2HcuHGoXbs2unfvjhYtWmi1epDJZEhOTpZaKi1evFhKaqxcuRLz589H0aJFoaenB4VCgcDAQJw9exYxMTG4ffs2YmNj0a5dO1SrVg03btyQtvv69et015/vDwj/foIyKy4uLlICEYCU4JgzZw7kcjmqVKkCILV1c7FixTB79mwMGDAAiYmJKFasGDZu3Ag9PT0olUrI5XIoFAq8efMG8fHxcHBwYHcNRERERPkckxpEn0HdunW1mu6nCQwMzHK9kiVLZjiYZenSpf9zf9BERPTl6HKgS6bzqhepjlkNZ0nTvQ/3RrI6OcNlK9lUwqImi6TpgX8ORGxybLrljvU49lHxXbx4Eb169cK7d+9w7NgxrF27Vhp3IiNqtRqXLl3CvHnzcPr0adSsWROurq7YtWsXjh07lmFLjX8PhBcWFgYnJycUL14cr169wp49e6BSqXD37l107doVz58/11re3d0d48ePx5EjR6QyPT09DBgwQOvB+KtXrzB16tR0MYeFhcHOzi7TY/r9998hl8vRqlWrTJcBUrtAMjExga2trVRmbm6O0NBQreNN6z7ra3LmzBn4+vrC19cXW7ZskcoDAgLQu3dv+Pj4oFOnTpDL5ejduzcWLlyow2j/39mzqS00/p3QAFKnS5cGfHxSW2wMH55z+61atSpOnz4NOzs7TJw4Eb169cLt27fRr18/6Onp4fXr1zh06BDmz5+PkJAQadB0IQTkcjmmTJmCc+fOISAgAEOGDJESjM+ePcPw4cNhaGiIX3/9FU2bNkWzZs3QrFkz3L59G2PHjkXDhg1x69YtvH79WqtFBwCtlhtv377VSqYkJiZKXVsBqWOZpE2ntX6ysrLK8Hhv3bqFw4cPw9vbGw4ODnjz5g0qVqyIFy9eSMv06dMHXl5eMDY2xv379yGXyxEfHw8LC4t0LcrUarX0piQRERER5S9MahARERFRlpo3b46IiAj069cPTZo0Qe/evXHnzh3ExMSgZMmSiI+PR2JiopSMb9myJb755hu4u7uj9P9eXU9JSUGDBg1w+PBhbN68GcePH9faR0pKCkxNTaVpAwMD2Nra4tGjR2jRogWsrKzg7OyMZcuWoV+/flrLAqmDmevp6WklNQBg+/bt6VpqZMTU1FR6EPtvr169wk8//YSNGzdm2N3R+9LGEXifkZEREhIStMo+pQukvOj9lzPatWuX4csaaRYtWoRFixZlOl8XlErgwoXULqcyO2UyWer8CxeAQYNydvDwQ4cOITo6GocOHZJaJFSrVg1ubm5aLTXel5SUBH19faxevRpAakuhsWPHolatWgCALl26YMWKFVqtid739u1bXLp0CbVq1YK/v3+6lhcajQbJyckwNDREuXLlkJKSIn2fY2Nj0atXLynBkJSUBI1Gg2PHjknrJSQkICIiArVr15a2GRISgnv37qFixYrYsWMHfvnlF/zzzz9Sl1JpDhw4gFOnTmHBggXYu3cvBg4cCIVCgcTERFhaWkIIgXfv3sHU1BTJyclYsmQJJk6c+OkngIiIiIi+SExqEBEREenYgS4HMp2nJ9N+y3h3x93ZXnbbD9v+W2BZqF69OoKDgwFAq/up97Vs2RIAEBcXJ7WWWL16NebOnZvp4M9plEolgoODUalSJQQFBQEA2rZti3HjxmHJkiXpls8s2TBs2DDpDXUA0rb+rUSJEvD3909XrtFo8OOPP6Jt27bo0iXzFjVpChUqhJiYGCiVSmkMgLi4OK3jDQsL0+omi75ciYn/Pyh4VoyMgOTk1OVzKqmhUqkwZ84ctGzZEk5OThg9ejTGjx8PT09PVKpUSaulBgA0a9YMK1euxLt377RaQ3Ts2BHXr1+XWg3XrVs3w1ZJ3t7eCAsLw8iRIzFt2jR06tQJRkZGUCgUeP36NSwsLGBsbAy1Wg1zc3P4+PggPDxcWj8pKQlmZmbw8vKSkpkzZsxASEhIunHg9PX1ERERkS6R+OLFC3z33XeYMGECrl27hvr162dYN3K5HL169UKvXr2wb98+bNmyBefOnUN0dDQcHBwyTVASERERUf7ApAYRERGRjn3MGBe5tWx2RUREYOnSpUhOTsbmzZul7l/SWmoAQPHixXH58mUAwJQpUzB27Fhs2rQJNjY2KFeuHHr27AlfX18YGhpizpw5sLS0xNixY7X2Y2Njg+TkZISEhMDLywtyuRwxMTHQ19eHQqHAqVOnshXvxo0bs9VSo0qVKnjy5Ani4+O1kiBTp05FWFgY/vzzz2ztz87ODg4ODrh58ybq1asHIHVshPfHITh27NgHu7GiL4Ox8f8PCp6VpCTAxCR1+Zyyc+dOlCtXDiVKlICzszM8PT1RtWpVTJ48GdWqVYOjoyMmTpyI7t274/jx49i/fz+A1HEvihYtimPHjmHMmDEQQsDCwkJqTZGUlITHjx/j1KlTUuJt/vz5uHPnDjQaDXbv3o3vv/8eTZo0wV9//YXRo0ejYcOGmDp1aqY/P0Dq99rOzk5KaGQls3E3ihcvjg4dOmDZsmU4cuQIduzY8cFtnT59GnXr1v3gckRERESUf7CDUSIiIiLKUnJyMo4ePYrr169j9uzZCA0NxYABAxAQEIDAwEAsX74cbdu2RWBgIAICAnD27FkAqX33r169Gubm5ggICMCrV69Qq1Yt2Nvb4/z581CpVGjSpAmmTZuGc+fOSfsTQmDChAkoVKgQWrdujaNHj+Lq1atYu3YtZs+ejZo1a2LWrFmZhatl2LBhqFSpkvT5+++/M1xOJpPhxx9/xIoVK6Sy33//HZs2bYKbmxs0Gg3i4+OlcQtSUlIyHYy4U6dOmDNnDlJSUnDlyhXcuHFD6upHqVTi119/Rf/+/bMVP+mWvj7QqBEQFQVk1nOWEKnzGzXK2a6nunXrhmXLlknTaQPZ//TTTyhbtqzUnVSatLEj/Pz84ODggLZt2yIgIADt2rVD69at8eDBA+nnbs6cOVJCA0htUXHkyBEULlxYSjjcuHEDx479//g7ffv2hb29Pezt7dPFGhsbi59//hnTpk37pGM9e/asNM7M7NmzsW7dOlhaWqJq1apZrufj44N9+/ahV69eAFJbVv17fB4iIiKij5WSkoLDhw/rOgzKApMaRERERJSlCxcuYNCgQRg0aBCCg4OxevVq2NvbpxuYF0hNDqR1BXXw4EE0btwYZmZmMDIyglKpBACcP38eLVu2xMyZM3Hx4kUsWLAAw4YNg1qthhACSqUSo0ePhp+fH7y8vLB582bcvHkTv/32G37++WesXr0ay5cvR1BQULpup9RqNZKTk6FSqQCkttR49OiR9Klbty6EEFJy4n2TJ0/G/v37cefOHQDAqlWrEBsbCxcXFxQoUAAFChTA0KFDAQB79uzJtGuc6dOnSwOPN27cGPPmzUPhwoWleW3atMnW2+z0ZWjaFLCxAZ4/T5/YEALw9wdsbYFmzXJ2vwUKFICTk5M0rVarMXnyZBw/fhybNm2Syk+fPo1NmzZJXZydP39eK+GxaNEinD17FjNnzkSLFi0wYcIEqRVRVk6dOoXExETp53bXrl14+fIlXr58qbVcTEwM2rZti2LFimHYsGEfdYxv3rxB//79MXToUISFhQFIHUckMTERoaGhCA0NTbfO9evX8eTJE9y5cwejR4/GkCFDULZsWQCpA5Vn9LNNRERE9DHUajVmzZqFgwcP6joUygS7nyIiIiKiLDVr1gz//POPNA7E27dvUb58eRj/r6+df3c/pVKpsHjxYjx48ABdu3YFAFSrVg3Dhw/H3r17pW5wwsPDcfDgQdStWxft2rWDXC5HXFwchBBwdHTUimH37v8fS6RZs2YoXbo0ypUrh5kzZ2otl5KSgnPnzqFLly7Q19fP8CFr3759UbJkSamLrDQmJibw8PDA5MmTcfLkSdy/fz/TOunXrx/69euX4TwrKyvcvn0bly9fRuHChVGpUiUAqS1XvL290w2STl82JydgwgRg5UrAxyd1UHAjo9Qup6KiUhMa48cDuZWnSkvUNWzYENHR0bh48SKsrKygUqmgUqkgl8tRpkwZTJo0CV5eXnj69ClcXV0BpLZ6evr0KVxcXDB//nzY2dlBJpPhwYMHcHBwgJWVldag9UlJSQCABw8ewNPTE9999x3q1auHiIgIhIWFISYmBklJSYiIiECFChVw8+ZNDB48GObm5jh+/Hi6RKdarc6w5YSfnx+SkpJQsWJFjBgxAj4+PggMDMTw4cPx7Nkz3L9/H25ubvj222+xZcsWtG3bFhqNBmPHjsWePXtQq1YtNGjQAA0bNkT16tXh4+MDW1tbFChQAE+ePMGrV68QEhICtVqtNSA5ERERUXYYGxvDw8MDbdq0QevWraX7HvpyMKnxmbRtq+sIKC+RyYBixYDg4My7OiB633u9QxAR5Tg9PT2tga0LFSqEoKAgGGUyenJaNzK9e/eWynr06IEePXpkuo9SpUoBSH07XXzgj1+RIkUQFRWV4eDgaYN9Z7d7qn+rXLkyTpw48Unrvs/AwABNmjTRKitTpgw8PT2lboIo72jQALC3B86cAS5cSB0U3MQEaN06tYVGbja8SUxMhBACS5YsQcWKFWFhYQEgtSuzpKQkNGnSRPqu3b9/HxMmTMDLly/Ru3dvvHr1Ct988w26du2KpUuX4sGDBzhy5Ai2bt2KR48eoUKFCrh79y7UajWKFy+O+Ph4GBsbo0WLFliwYAFGjx6NrVu3Ytu2bRg1ahT69esHIQSqVauGO3fuICgoCI0bN8ayZctgYGCQLvaUlBQpUfI+Q0ND1K9fH2vXrkX58uXRtWtXnDlzBuPGjYOHhweMjIwwd+5cODk5oWfPnhg2bBgWLVqE5ORk3Lp1C6VLl8br169x4MABHDp0CDNnzsSrV6+QmJiotZ/FixczqUFEREQfFBcXh6ioKBQvXlwqq1ixIu7fv8+ExhdKJj5015gPxMbGwsLCAjExMVKz7M+NSQ36GDKZBsWKhSE42BZC8MEHfRiTGkT/nUajQVhYGGxtbXPtoXNSUhICAgLg6OiYaUKAKE1W35cv4fr2S5NVneTkz55SCSQmpg4KnpNjaHyIEAIqlQoKhUKrdUVmgoKCULx48UyXValUiIuLkxKWDx48gKOjIywsLHDjxo3Pmgy4desWHB0dYWNjk26ev78/TExMUKRIkQ9uJyUlBUqlUurKzsTERGvskPf9+zvxOf4GfO1Yx7mPdZy7WL+5j3Wc+1jH6d29exfdu3eHmZkZbty4keGLU9mVk/XL6/2ssaUGERERERFRNunrf95kxqcqUaJElvMVCoVWCywXFxfp/5+7dUPNmjUznfcx488YGBhk2GKEiIiI6N80Gg1WrVqFadOmSWOIzZw5E0uWLNFxZJQdTGoQERERERERERER0VfhzZs36Nu3L06fPi2V1axZE0OHDtVhVPQx2M6IiIiI6DP7Cnr/pBzA7wkRERERUc46ffo0qlSpIiU0ZDIZpk6diqtXr0rj/NGXj0kNIiIios9ELpcDSO33nehDEhISACDTMQGIiIiIiCh7UlJSMGnSJLRo0QJhYWEAgMKFC+PMmTNYtGgRr7nzGHY/RURERPSZKBQKmJiYIDw8HPr6+hycjzIkhEBCQgLCwsJgaWkpJcOIiIiIiOjjKZVK1K9fH7du3ZLKWrVqhZ07d8LGxkaHkdGnYlKDiIiI6DORyWQoUqQIAgICEBQUpOtw6AtnaWmJwoUL6zoMIiIiIqI8TV9fH02bNsWtW7dgYGCApUuXYsyYMZDJZLoOjT5RnkxqTJkyBb6+vjh27JiuQyEiIiL6KAYGBihTpgy7oKIs6evrs4UG5Vt+fn4YNGgQTpw4AVNT03Tzt2/fDhsbG7Rt21Yq69ChAzp37oxevXrByckJO3fuRL169T5n2ERERJSHzZo1C8HBwRg3bhyqVq2q63DoP8pzSQ1vb2+sX78eXl5eug6FiIiI6JPo6enByMhI12EQ0adQKoHERMDYGMjFvpfPnDmDTp06ISQkBBYWFjh16hR69qqrsCcAAQAASURBVOyJwMBAxMbGok+fPrh16xZq1aqF33//HQ4ODti5cyf69+8PIDWBWqVKFaxfvx41atTItTgzc+/ePaxfvx6GhobSW5A9e/ZEnTp18Mcff6BQoUIZJjQAwMLCAqNHj0aLFi2g0WhgYGAAIQQMDAwAAIaGhjA0NIRKpQKQ2rUfERERUZqbN2/C19dXui4CUl8a2rVrlw6jopyUpzpy1mg0GDJkCMaPH8/R6ImIiIiI6PPx8wM2bAB69QJ69079d8MGwN8/V3bXrFkzfPPNN9i+fTsAYM2aNRgxYgTMzc3Rs2dPODo64uHDhyhVqhQGDhworVepUiVERUUhJCQErVu3RufOnXMlvvfJZDIEBgZqldnZ2aFly5Zo0aIFWrRogSdPnuCff/6BRqPB5s2bMXLkSGlZjUaD5ORkabpjx45wdXVFWFgYnJ2dYWhoiBMnTmDgwIGwtrbG06dP0ahRI5iammLdunW5fnxERESUN2g0GixZsgT16tXD0KFDcffuXV2HRLkkTyU1Nm7ciIcPH6JkyZL4888/2W0DERERERHlvkuXgJ9+AnbvBhISAAOD1H937wYmTgQuX86V3U6fPh2//fYbfH198ffff2P06NHw9fXFzZs38euvv6JUqVL45ZdfcObMGcTExAAA5HI5LC0tYWNjgyFDhuDFixcIDw/PlfiyYm9vj06dOqFt27Zo06YNHBwcYGBggH379uHly5cYOHAgSpYsCSMjIxQpUgSjRo0CACQnJ0OpVGLbtm2wt7fHw4cPkZKSgrp16+KPP/5AREQEKlWqhIsXLyI5ORljx4797MdGREREX55Xr16hefPmmDp1KlQqFZRKJVatWqXrsCiX5Jl2uvHx8Zg1axZKlSqFoKAguLm5Yf78+bh06RKMjY21lk1OTtZ60yc2NhZAarZOo9F81rjTcNwZ+hgymQYymYBMppvvK+U9OvrVRpSvaDQaCCF0dq1A9DH4Pf2M/PyAVauA+HigYkXtC/uiRVNbaqxcCdjbA6VL5+iu27Vrh19++QVdu3bFkCFDYGVlhZMnT6J06dIoUKAAAMDBwQFTpkyBUqlMt/6BAwdgb2+PQoUKAQAuX76MMWPGICgoCC1btsT69ethaWn5wXl79+7Fzz//jLCwMNSrVw/u7u6wtrbGN998g6dPnwIAHB0dpWW7d++e4fFER0djyZIlOHDgAFq3bg0AKF++PA4cOIBKlSoBAFavXo19+/bh/v37iIyMlGKIi4uDjY2N1vaEEFCr1ex+ioiI6Cvn6emJvn37IiIiAkBqK9Jp06Zh9uzZug2Mck2eufo7fPgw3r17hwsXLsDa2hoqlQqVK1eGm5sbhgwZorXsokWLMGfOnHTbCA8PR1JS0ucKWUuxYjrZLeVZGlhbx0AIgTzWoIp0JCxM1xEQ5X0ajQYxMam/e/X0+LuXvmxxcXG6DuHrcfYsEB6ePqEBpE6XLg34+ABnzgDDh+formUyGSZMmICBAwfi9OnTAIA3b97AyspKWkYul2Px4sXS9MOHD2FpaQmlUgm1Wo3Tp09DLpcjODgYrVq1wqpVq/D9999j3Lhx6NevH44cOZLlvLi4OPTt2xe///47ateujZEjR2LFihVYtGgRbt++DbVajYIFC8LLywvFixfXGiejYcOGUrdUERERMDU1Rfny5bFp0ya0bt0aKpUKL1++hJOTk7TOlClTMHbsWBgbGyMuLg6FCxeGoaEh3r17h/r160MmkyEuLg4NGjSASqVCixYt8Oeff+ZovRMREVHekJycjClTpuDXX3+VyooUKYLdu3ejcePGOoyMclueSWqEhISgdu3asLa2BpA6GFyVKlXg5+eXbtlp06ZhwoQJ0nRsbCyKFSsGGxsbmJubf7aY3xccrJPdUh6V2lJDhpAQGwjBB2v0Yba2uo6AKO/TaFJ/99rY2DCpQV88DjT/mSiVwIULQMGCmTe9lslS51+4AAwalOODh1+7dg1CCNy4cQM//PADlEql9DtqwoQJ0pgbV65cAQCUK1cOnp6eSEhIwLp16zBw4EB4eXlh9+7dqFOnDgYPHgwA2LBhAxwcHPD69ess51lYWEChUCAlJQVFihTBn3/+KbUUSmstAgDm5uZSq4o0kZGROHjwIKpXry6VaTQaVK5cGTdv3gSQOgZIZt/nEiVKICUlBa9fv0apUqUQGRkJhUIBFxcXbN26VWu7RERE9HV5+vQpunfvjgcPHkhlbdq0wY4dO6Tnx5R/5ZmkhoODAxITE7XKgoKCUKdOnXTLGhoawtDQMF25np6ezh5SCKGT3VIeJoQMQugxqUHZwuevRDlDJpPp9HqBKLv4Hf1MEhOBpCTgQ0kkIyMgOTl1+RxMagQHB2Pv3r2YN28eFi5ciB9++AGWlpaIjo4GkDrmxpgxY1ClShWo1WoAgIGBAUqWLAkA+O2332BhYYGzZ88iODgYpUqVkrZtb28PQ0NDvHjxIst5NWvWhIeHB5YuXYrRo0ejXr16WLt2rdbymZHL5enK9PT0MGfOHEybNg2VK1dGly5dPridM2fOoEaNGuxmioiIiACkdkHZo0cPKaFhaGiI5cuXY+TIkZBxDICvQp65G2rdujV8fX2xceNGhISEYM2aNfDy8kLHjh11HRoREREREeVHxsapCYsPdWGblAQYGqYun4OWLl2KDh06YPLkyXj16hVOnjwJZ2dnPH36FHFxcbCysoKdnR3i4+Mz3UbauBPFixfH8+fPpfLQ0FAkJyejRIkSWc57+/Yt7OzscPXqVbx58wbW1tYYN26c1j5kMtn/uk3NnJ+fH/z9/QEAnTt3hlqthru7O/r375/levHx8Vi4cCH69u0rlelyrEQiIiLSPZlMhi1btkBfXx/ffPMNbt68iVGjRjGh8RXJM0kNKysreHp6YteuXShbtix+/fVX7N+/H8U4WAUREREREeUGfX2gUSMgKirzptdCpM5v1ChHW2m8efMG27Ztw8SJE2FgYIBRo0Zh8eLFqFu3LsqWLYuhQ4ciMDAQc+fO1UooqNVqREdHIzg4GD///DOSk5NRu3Zt9OrVC9euXcOWLVsQEBCA4cOHo3379rCzs8tyXlhYGBo2bIhTp07h7du3AACVSqUVa+nSpXHy5Em8fPkSly9f1pqXnJwsxZ2WOLlx4waePHkClUoFLy+vdMd+/fp1AMDu3buxcuVKFChQAH369JHmJyYm6mysRCIiItKNtFapaapVq4YTJ07gzp07cHZ21lFUpCt5JqkBAHXr1sX169eRkJAAf39/tG3bVtchERERERFRfta0KWBjAzx/nj6xIQTg7586uFWzZjm62xUrVuC7776Di4sLAGDYsGHw9vbGxYsXcfToUbx48QKVK1fGs2fPtF70evToEQoWLIgyZcrgzJkzOHz4MOzt7VGsWDGcOHEC69atQ9WqVWFiYoIdO3YAQJbzvvnmG6xYsQLDhw9H6dKl8fTpUyxbtkwr1g0bNmDFihVwdHTEpk2bAABJSUl4+fIl2rRpg3v37uHevXuoWrUqJk+ejB9++AHbt2+Hu7s72rVrh6VLl0qJEnd3d7Rp0watWrXCuHHj4Onpic6dO8Pb2xuhoaF49+4dzpw5g9KlS+PRo0fw9PTM0XonIiKiL4tarcbChQvRuHHjdC9WNG3aFKampjqKjHSJnZISERERERFlxskJmDABWLkS8PFJHRQ8rUuqqKjUhMb48UDp0jm626VLl2pNFyxYEFFRUVAoFJDJZLh69Wq6dVxcXNCvX79Mt+nq6qo1mGZ2540YMQIjRozIdLtNmjSRupZKo1Kp4OLiglGjRqFdu3ZYtGgRFixYgLZt2+L+/fuwt7cHAJw7dw7dunXDH3/8gStXriA0NBT79u1DmzZtkJiYiCNHjuDUqVPYvXs3goKCEBcXp7WfFi1aoFWrVpnGRkRERHnXy5cv8eOPP+LChQsAgDlz5mDevHk6joq+BExqEBERERERZaVBA8DeHjhzBrhwIXVQcBMToHXr1BYaOZzQyA/MzMxw9uxZabpHjx744YcfULFiRa3lqlevDi8vL/j7+0OhUGDSpEnSPGNjY/To0QM9evSQylQqFVJSUqBWq6HRaGBgYJD7B0NERESf3bFjx9C/f39ERkYCAPT09KBQ8FE2peI3gYiIiIiI6ENKlwaGDwcGDQISE1MHBc/BMTTyu5IlS2Y6z8zMLNt9YSsUCj7QICIiyseSkpIwefJkrF27VipzcHDA7t274erqqsPI6EvCq0EiIiIiIqLs0tdnMoOIiIgoFzx+/Bjdu3eHt7e3VNa+fXts3boVVlZWOoyMvjR5aqBwIiIiIiKiTyX+PdA3fbX4XSAiIvqybN26FdWqVZMSGkZGRli/fj0OHz7MhAalw5YaRERERESUr8nlcgBASkoKjI2NdRwNfQkSEhIAAPpsdUNERPRF8PHxQWJiIgCgYsWK8PDwQKVKlXQcFX2pmNQgIiIiIqJ8TaFQwMTEBOHh4dDX14eeXt5rsC6EgEqlgkKhgEwm03U4eZYQAgkJCQgLC4OlpaWU8CIiIiLdWrx4MS5duoRatWphxYoVMDEx0XVI9AVjUoOIiIiIiPI1mUyGIkWKICAgAEFBQboO55MIIaDRaKCnp8ekRg6wtLRE4cKFdR0GERHRV0mtVuPBgweoVq2aVGZoaIirV68ymUHZwqQGERERERHlewYGBihTpgxSUlJ0Hcon0Wg0iIyMhJWVVZ5safIl0dfXZwsNIiIiHQkJCUHv3r1x8+ZN3LlzBxUrVpTmMaFB2cWkBhERERERfRX09PRgZGSk6zA+iUajgb6+PoyMjJjUICIiojzp6NGjGDBgAN6+fQsA6NWrF+7du8drG/po/MYQERERERERERERUa5ITEzEyJEj0b59eymhUaxYMaxdu5YJDfokbKlBRERERERERERERDnOx8cH3bt3x6NHj6Syjh07YsuWLShUqJAOI6O8jKkwIiIiIiIiIiIiIsoxQghs2rQJ1atXlxIaRkZG2LhxIw4ePMiEBv0nbKlBRERERERERERERDlm7NixWLt2rTRduXJl7N27V2tgcKJPxZYaRERERERERERERJRjunbtKo2XMXLkSNy8eZMJDcoxbKlBRERERERERERERDmmXr16WL58OUqVKoV27drpOhzKZ9hSg4iIiIiIiIiIiIg+yYsXLzBp0iSo1Wqt8vHjxzOhQbmCLTWIiIiIiIiIiIiI6KMdOnQIgwYNQnR0NKysrDB16lRdh0RfAbbUICIiIiIiIiIiIqJsS0hIwNChQ9G5c2dER0cDALZu3YqkpCTdBkZfBSY1iIiIiIiIiIiIiChbvL29UaNGDWzevFkq69KlC+7cuQMjIyMdRkZfCyY1iIiIiIiIiIiIiChLQgisW7cONWvWhK+vLwDA2NgYW7Zswb59+2BpaanbAOmrwTE1iIiIiIiIiIiIiChTkZGRGDBgAP7880+pzNnZGXv37kX58uV1GBl9jdhSg4iIiIiIiIiIiIgytWTJEq2ExpgxY3Djxg0mNEgnmNQgIiIiIiIiIiIiokzNmjUL33zzDaysrPDnn3/i119/5fgZpDPsfoqIiIiIiIiIiIiIJCkpKTAwMJCmTU1NcfjwYVhYWKBo0aI6jIyILTWIiIiIiIiIiIiI6H8OHDgAJycn+Pn5aZWXL1+eCQ36IjCpQURERERERERERPSVe/fuHQYPHoyuXbsiODgYPXv2REpKiq7DIkqH3U8RERERERERERERfcW8vLzQvXt3PHnyRCpzcnJK1w0V0ZeALTWIiIiIiIiIiIiIvkJCCKxduxY1a9aUEhqmpqbYsWMH3N3dYWZmpuMIidJjSw0iIiIiIiIiIiKir0xERAQGDBiAY8eOSWVVq1aFh4cHypYtq8PIiLLGlhpERERERJTjIiIi4OjoiMDAQKns0aNHqFGjBgoWLIhJkyZBCCHNu3TpEsqXLw9ra2usXLlSBxETERERfT0uXbqEKlWqaCU0JkyYgOvXrzOhQV88JjWIiIiIiChHRUREoE2bNloJjeTkZLRt2xbVqlXDnTt34Ovri507dwIAwsPD8cMPP6BHjx64fv063N3dceHCBd0ET0RERPQVSElJwatXrwAANjY28PT0xIoVK2BoaKjjyIg+jEkNIiIiIiLKUd27d0fPnj21yk6ePImYmBisXLkSpUuXxsKFC7Ft2zYAgLu7O4oWLYpffvkFZcqUwcyZM6V5RERERJTzmjZtismTJ6Np06bw9vZGy5YtdR0SUbYxqUFERERERDlqy5YtGDNmjFaZl5cXateuDRMTEwBAlSpV4OvrK81r1KgRZDIZAKBmzZq4e/fu5w2aiIiIKB+7cOGCVtefALBgwQKcOnUKhQsX1lFURJ+GA4UTEREREVGOcnR0TFcWGxurVS6TySCXyxEVFYXY2FhUqFBBmmdubo7Q0NAs95GcnIzk5GSt7QOARqOBRqP5r4fwxdFoNBBC5Mtj+xKwfnMf6zj3sY5zF+s397GOc0d8fDzGjBmDXbt2YdWqVejWrZtUx3p6qe+7s85zRk5+h3lOssakBhERERER5TqFQpGuj2YjIyMkJCSkm5dWnpVFixZhzpw56crDw8ORlJSUM0F/QTQaDWJiYiCEkB5AUM5h/eY+1nHuYx3nLtZv7mMd5zxvb28MHz4cz58/BwBMmTIFlSpVQoUKFVjHuSAnv8NxcXE5FFX+xKQGERER5UtjxozB2rVrpenSpUvDz88vy3Xc3NywcuVKREREoE6dOvDw8ECRIkW0llEqlfj222+xdu1aNGzYMDdCJ8qXChUqhEePHmmVxcXFwcDAAIUKFUJ4eHi68qxMmzYNEyZMkKZjY2NRrFgx2NjYwNzcPGeD/wJoNBrIZDLY2NjwIUQuYP3mPtZx7mMd5y7Wb+5jHeccjUaDNWvWYOrUqVAqlQAAU1NT/Pbbb6hYsSLrOJfk5HfYyMgoh6LKn5jUICIionzpzp07OHHiBOrUqQMAkMvlWS5/9epVLF26FO7u7qhQoQJ69uyJn376Ce7u7lrLLV26NN2DWSL6sBo1amDLli3SdEBAAJKTk1GoUCHUqFEDe/bskebdv38f9vb2WW7P0NAwXcsPILUbhfx6ky6TyfL18eka6zf3sY5zH+s4d7F+cx/r+L8LCwtDv379cPLkSamsWrVq2Lt3L0qXLo2wsDDWcS7Kqe8wz0/WWDtERESU76hUKvj4+KBBgwawtLSEpaUlChQokOU6z549w9KlS9GkSRM4ODigf//+uH//frplli9fjpIlS+Zi9ET5U4MGDRAbG4sdO3YAABYuXIgmTZpALpfjhx9+wN9//41z585BqVRi6dKlaN68uY4jJiIiIspbzp49iypVqmglNH766Sdcu3YNZcqU0WFkRDmLLTWIiIgo33n48CE0Gg1cXFzw8uVLuLq6YvPmzShevHim6/Tv3x9hYWHS9NOnT9Nd+A8dOhRTp07VukkgouxRKBTYunUrevTogUmTJkFPTw8XL14EAFhbW2PVqlVo1aoVzMzMYGlpiZ07d+o0XiIiIqK8ZN++fejevbs0bWtri99//50vilC+xJYaRERElO/4+vqiXLlycHNzg7e3NxQKBYYMGZLt9d++fYtNmzZh2LBhUtmOHTsQExODn376KTdCJsqXhBBaLZt++OEH+Pv7Y9euXXj8+DEqVKggzRs2bBiePn0Kd3d3eHt7w87OTgcRExEREeVNLVu2hKOjIwCgefPm8Pb2ZkKD8i221CAiIqJ8p1evXujVq5c0vX79ejg6OiI2NjZbAwiPHDkSderUQcuWLQEA4eHhmDZtGk6fPv3BsTmIKGuFCxdG69atM5zn6Ogo3YwTERERUfaZm5tj7969uHr1KsaPH88xGShf47ebiIiI8j1bW1toNBq8evXqg8vu2rULFy5cwPbt26WycePGYeDAgXB2ds7NMImIiIiIiD4oLi4OI0aMQHBwsFZ5rVq1MHHiRCY0KN/jN5yIiIjynUmTJmHPnj3S9PXr16Gnp4dixYplud6DBw8wduxYeHh4aHV9s2fPHqxdu1YadPzq1ato06YNFi9enGvHQERERERE9G937tzBt99+iw0bNqBXr15Qq9W6Donos2P3U0RERJTvODs7Y8aMGbCzs4Narcbo0aPRp08fmJiYIDY2FsbGxtDX19daJywsDH379sWkSZNQvXp1xMfHAwDMzMwQEBCgtWz37t0xbtw4tGjR4rMdExERERERfb00Gg1WrlyJn3/+GUqlEkDqS1k+Pj6oUqWKjqMj+rzYUoOIiIjynd69e6Nbt27o1KkTevTogRYtWuC3334DAFSpUgUnTpxIt46HhwfCwsIwc+ZMFChQQPoAQMmSJbU+RkZGKFy4MCwtLT/nYRERERER0Vfo9evXaNmyJSZNmiQlNGrWrIkHDx4woUFfJbbUICIionxp0aJFWLRoUbrywMDADJcfM2YMunfvDltb2w/2QXvx4sUciJCIiIiIiChrp06dQt++fREWFgYAkMlkmDJlCubOnZuu9TnR14JJDSIiIiIiIiIiIqIvSEpKCn7++WesWLFCKitcuDDc3NzQpEkTHUZGpHvsfoqIiIiIiIiIiIjoC3L58mWthEarVq3g7e3NhAYRmNQgIiIiIiIiIiIi+qI0adIEI0eOhIGBAVavXo3jx4/DxsZG12ERfRHY/RQRERERERERERGRDiUkJMDY2BgymUwqW7ZsGYYMGcLBwIn+hS01iIiIiIiIiIiIiHTk1q1bqFy5Mnbu3KlVbmxszIQGUQaY1CAiIiIiIiIiIiL6zDQaDZYuXYq6devi+fPnGDVqFJ48eaLrsIi+eOx+ioiI8q+2bXUdAeUlMhlQrBgQHAwIoetoKC84dkzXERARERFRHvXq1Sv06dMH586dk8qqVKkCIyMjHUZFlDewpQYRERERERERERHRZ+Lp6QlnZ2cpoSGTyfDzzz/j8uXLKFmypG6DI8oD2FKDiIiIiIiIiIiIKJclJydj2rRpWLVqlVRWpEgR7N69G40bN9ZhZER5C5MaRERERERERERERLkoICAAnTp1wv3796WyNm3aYMeOHbC2ttZhZER5D7ufIiIiIiIiIiIiIspF5ubmCAsLAwAYGhpi7dq1+PPPP5nQIPoETGoQERERERERERER5SIrKyvs3r0bFStWxM2bNzFq1CjIZDJdh0WUJ7H7KSIiIiIiIiIiIqIcdOPGDTg6OsLOzk4qa9iwIby8vCCXy3UYGVHex5YaRERERERERERERDlArVZj4cKFqFevHvr27QuNRqM1nwkNov+OSQ0iIiIiIiIiIiKi/+jly5do2rQppk+fDrVajdOnT2PPnj26Doso32FSg4iIiIiIiIiIiOg/OHbsGJydnXHhwgUAgEwmwy+//ILu3bvrODKi/IdjahARERERERERERF9gqSkJEyePBlr166Vyuzt7bF79240bNhQd4ER5WNMahARERERERERERF9pMePH6N79+7w9vaWytq1a4dt27bByspKh5ER5W9MahARERERERERERF9hICAAFSrVg2JiYkAAENDQ6xcuRLDhw+HTCbTcXRE+RvH1CAiIiIiIiIiIiL6CI6OjujatSsAoEKFCrh9+zZGjBjBhAbRZ5CnkhpjxoyBTCaTPk5OTroOiYiIiIiIiIiIiL5Cv/32G3755Rfcvn0blStX1nU4RF+NPJXUuHPnDk6cOIGoqChERUXh/v37ug6JiIiIiIiIiIiI8jG1Wo358+dj//79WuVmZmaYO3cuTExMdBQZ0dcpz4ypoVKp4OPjgwYNGsDMzEzX4RAREREREREREVE+FxISgt69e+PSpUswNzdHjRo14OjoqOuwiL5qeaalxsOHD6HRaODi4gJjY2O0aNECL1680HVYRERERERERERElA8dPXoUzs7OuHTpEgAgPj4ely9f1nFURJRnWmr4+vqiXLlyWLt2LaytrTF+/HgMGTIEp06dSrdscnIykpOTpenY2FgAgEajgUaj+Wwxv49jBNHHkMk0kMkEZDLdfF8p79HRr7YvH3/50kfQyGQQMhk0/N5Qdunwl6+urmmJiIiIvgaJiYn46aefsH79eqmsWLFi2LNnD+rVq6fDyIgIyENJjV69eqFXr17S9Pr16+Ho6IjY2FiYm5trLbto0SLMmTMn3TbCw8ORlJSU67FmpFgxneyW8iwNrK1jIIRAHmpQRToUFqbrCL5Q/OVLH0EDIMbaGkII/ual7NHhL9+4uDid7ZuIiIgoP/Px8UH37t3x6NEjqaxTp07YsmULChYsqMPIiChNnklq/JutrS00Gg1evXqVLqkxbdo0TJgwQZqOjY1FsWLFYGNjk27ZzyU4WCe7pTwqtaWGDCEhNhCCj9bow2xtdR3BF4q/fOkjaGQyyGQy2ISEQE8IXYdDeYEOf/kaGRnpbN9ERERE+ZEQAps3b8a4ceOkl6KNjY2xevVqDB48GDK26Cb6YuSZpMakSZNQtWpV9OzZEwBw/fp16OnpoVgGb+EaGhrC0NAwXbmenh709HTzgJjPRuhjCSGDEHpMalC26OhX25ePv3zpI8mEgN7/PkQfpMNfvrq6piUiIiLKr6KiovDLL79ICY3KlSvDw8MDFSpU0HFkRPRveSap4ezsjBkzZsDOzg5qtRqjR49Gnz59YGJiouvQiIiIiIiIiIiIKA8rVKgQdu7cidatW2PkyJFYtmwZjI2NdR0WEWUgzyQ1evfuDR8fH3Tq1AlyuRy9e/fGwoULdR0WERERERERERER5TEqlQqJiYkoUKCAVNaqVSs8evQIFStW1GFkRPQheSapAaQOAL5o0SJdh0FERERERERERER51IsXL9CrVy/Y2Njg0KFDWuNlMKFB9OVjZ7xERERERERERET0VTh06BCcnZ1x9epV/PHHH9i0aZOuQyKij8SkBhEREREREREREeVrCQkJGDp0KDp37ozo6GgAQPHixVG5cmXdBkZEHy1PdT9FRERERERERERE9DG8vb3Ro0cP+Pr6SmVdunTB5s2bYWlpqbvAiOiTsKUGERERERERERER5TtCCKxbtw41a9aUEhrGxsbYsmUL9u3bx4QGUR7FlhpERERERERERESUryQlJaF79+44evSoVObs7Iy9e/eifPnyOoyMiP4rttQgIiIiIiIiIiKifMXQ0BD6+vrS9JgxY3Djxg0mNIjyAbbUICIiIiIiIiIionxFJpNh8+bNCAgIwKxZs9C2bVtdh0REOYRJDSIiIiIiIiIiIsrTAgMD8eLFCzRo0EAqK1iwIG7fvg2ZTKbDyIgop7H7KSIiIiIiIiIiIsqz9u/fDxcXF3Ts2BGhoaFa85jQIMp/mNQgIiIiIiIiIiKiPOfdu3cYPHgwunXrhpiYGERGRmLatGm6DouIchm7nyIiIiIiIiIiIqI8xcvLC927d8eTJ0+ksh49emDNmjU6jIqIPge21CAiIiIiIiIiIqI8QQiBNWvWoGbNmlJCw9TUFDt27IC7uzssLCx0HCER5Ta21CAiIiIiIiIiIqIvXnh4OAYMGIDjx49LZVWrVoWHhwfKli2rw8iI6HNiSw0iIvo/9u48vqkq///4+yZNky60FGhZStkKKCIUVBS3Kio4Coq4giAo4sLPlYqDuDCu4HwVcEPEFaUC4zIu46iAiuACOiiDUFwoiyzitEDpQps0afL749LQQltaaHq7vJ6PB9Z7cpp8cpLctudzz/kAAAAAAFCvlZSU6Oyzzy6X0EhLS9OKFStIaABNDEkNAAAAAAAAAPWa3W7Xgw8+KEmKj4/Xxx9/rOnTp8vpdFobGIA6x/ZTAAAAAAAAAOq9K664Qs8++6wuv/xytWnTxupwAFiElRoAAAAA6szcuXNlGMYh/+bOnauLL764XNt5551ndbgAAMAiCxcu1K233npI+6233kpCA2jiSGoAAAAAqDNXX321cnJygv+2bdumVq1a6cwzz9SqVau0du3a4G0ffPCB1eECAIA6tm/fPo0dO1YjRozQrFmzNG/ePKtDAlDPkNQAAAAAUGfCw8PVvHnz4L833nhDw4YNk8vlUiAQ0PHHHx+8LSoqyupwAQBAHVq7dq1OOukkvfbaa8G2b775xsKIANRHJDUAAAAAWMLtduvpp5/Wvffeq++//14lJSVq3769oqKiNHz4cOXk5FgdIgAAqAOBQEBPP/20hgwZot9++02SFBUVpTfeeEMvvPCCxdEBqG8oFA4AAADAEvPnz9cpp5yiTp06acGCBUpJSdGTTz4pm82mcePGafLkyZVOZHg8Hnk8nuBxXl6eJMnv98vv99dJ/HXJ7/crEAg0yudWHzC+occYhx5jHFqMb+hkZWVp7Nix+uSTT4JtJ554ot58801169aNMa9FvI9DqzbHl9eoaiQ1AAAAAFjihRde0IMPPihJmjx5siZPnhy87YknntCll15aaVJj2rRpeuihhw5pz87OltvtDkm8VvL7/crNzVUgEJDNxoL72sb4hh5jHHqMcWgxvqGxfPly3XbbbcrKygq23XzzzZo8ebLCw8PLtePo8T4Ordoc3/z8/FqKqnEiqQEAAACgzmVmZiozM1MDBw6s8PaEhATt3r1bHo9HTqfzkNsnT56stLS04HFeXp6SkpIUHx+vmJiYkMVtFb/fL8MwFB8fzyRECDC+occYhx5jHFqMb2i88cYbwcRFQkKCnnrqKV1xxRWMcYjwPg6t2hxfl8tVS1E1TiQ1AAAAANS5t956S0OGDJHD4ZAkXXXVVbrtttt0xhlnSJJWrFih1q1bV5jQkCSn01nhbTabrdH+kW4YRqN+flZjfEOPMQ49xji0GN/a9+qrryolJUUpKSl67bXXGOM6wBiHVm2NL69P1RgdAAAAAHXu008/1dlnnx087tWrlyZMmKCvv/5a77//viZPnqzx48dbFyAAAKh1B28nlZCQoBUrVujjjz9W69atLYoKQENDUgMAAABAnSoqKtJ3332n0047Ldg2adIk9e7dW3/5y180fvx4/b//9/903333WRglAACoLfn5+Ro9erT69OmjXbt2lbutU6dOXJUOoEbYfgoAAABAnYqIiJDH4ynX5nA49Morr+iVV16xKCoAABAKq1at0ogRI5SZmSlJuu666/Thhx/KMAyLIwPQUJEGBQAAAAAAAFCr/H6/nnzySZ122mnBhEZ0dLSGDx9OQgPAUWGlBgAAAAAAAIBa8+eff2rMmDFavHhxsK1fv35asGCBkpOTLYwMQGPASg0AAAAAAAAAteLTTz9VSkpKuYTGpEmT9PXXX5PQAFArWKkBAAAAAAAA4KhNmTJFjzzySPC4TZs2euONNzRw4EALowLQ2LBSAwAAAAAAAMBRK7sS48ILL9SaNWtIaACodazUAAAAAAAAAHDURo8erWXLlql379664447KAgOICRIagAAAAAAAACokby8PL333nsaM2ZMsM0wDL3yyiskMwCEFEkNAAAAAAAAANX2/fffa8SIEdq0aZNiYmI0bNiw4G0kNACEGjU1AAAAAAAAAByW3+/X//3f/+n000/Xpk2bJElpaWnyer0WRwagKWGlBgAAAAAAAIAq7dy5U6NHj9Znn30WbOvfv7/mz58vh8NhYWQAmhpWagAAAAAAAACo1Mcff6zevXsHExqGYei+++7T8uXL1blzZ4ujA9DUsFIDAAAAAAAAwCE8Ho8mT56smTNnBtvatWun9PR0DRgwwMLIADRlJDUAAAAAAAAAHCItLU3PP/988Piiiy7Sq6++qlatWlkYFYCmju2nAAAAAAAAABzi3nvvVYsWLeR0OvXss8/qgw8+IKEBwHKs1AAAAAAAAABwiMTERC1cuFAJCQlKSUmxOhwAkMRKDQAAAAAAAKDJW7Fihc455xzt3bu3XPvAgQNJaACoV0hqAAAAAAAAAE1USUmJpk6dqjPPPFNLly7VzTffrEAgYHVYAFAptp8CAAAAAAAAmqAdO3bommuu0dKlS4Nt27ZtU0FBgZo1a2ZhZABQOVZqAAAAAAAAAE3Mv/71L6WkpAQTGoZh6IEHHtCyZctIaACo11ipAQAAAAAAADQRbrdbf/3rX/Xss88G2xITE5Wenq6zzz7busAAoJpIagAAAAAAAABNwM8//6zhw4frp59+CrYNHTpUr7zyilq2bGlhZABQfWw/BQAAAAAAADQBX375ZTCh4XQ6NWvWLL333nskNAA0KKzUAAAAAAAAAJqAm2++WYsWLdKGDRu0cOFC9erVy+qQAKDGSGoAAAAAAAAAjdDWrVvVoUOH4LFhGJo7d67Cw8MVGRlpYWQAcOTYfgoAAAAAAABoREpKSvTII48oOTlZixYtKndb8+bNSWgAaNBIagAAAAAAAACNxPbt23XuuedqypQp8vl8Gj16tLKysqwOCwBqDUkNAAAAAAAAoBF4//33lZKSomXLlkmSbDabbrnlFgqBA2hUqKkBAAAAAAAANGBFRUWaOHGinn/++WBbUlKS5s+frzPOOMPCyACg9pHUAAAAAAAAABqojIwMDR8+XOvWrQu2XXbZZXrppZcUFxdnYWQAEBpsPwUAAAAAAAA0QO+//75OOumkYEIjIiJCc+bM0dtvv01CA0CjxUoNAAAAAAAAoAHq3bu3HA6H3G63evXqpYULF+q4446zOiwACCmSGgAAAAAAAEAD1KVLF82ZM0fffPONnnjiCUVERFgdEgCEHNtPAQAAAAAAAPWcz+fT008/rYKCgnLtI0aM0HPPPUdCA0CTQVIDAAAAAAAAqMe2bt2qc845R3feeafuuOMOq8MBAEuR1AAAAAAAAADqqX/+85/q06ePvvrqK0nS66+/rp9//tniqADAOiQ1AAAAAAAAgHqmsLBQN998sy677DLl5ORIkjp06KBly5apR48eFkcHANahUDgAAAAAAABQj6xbt07Dhw9XRkZGsO2KK67Qiy++qObNm1sXGADUA6zUAAAAAAAAAOqBQCCg559/Xv369QsmNCIiIvTSSy/pH//4BwkNABArNQAAAAAAAIB64Z///KduueWW4HFKSooWLFjAdlMAUAYrNQAAAAAAAIB6YNiwYRo4cKAk6fbbb9fKlStJaADAQRpsUuMvf/mL5s6da3UYAAAAAAAAwBEJBALljm02m9544w199NFHevrpp+VyuSyKDADqrwaZ1HjzzTe1aNEiq8MAAAAAAAAAjsiWLVt01llnafny5eXa27Rpo8GDB1sUFQDUfw0uqbFnzx7dddddOuaYY6wOBQAAAAAAAKixt956S3369NFXX32lkSNHas+ePVaHBAANRoMrFH7XXXdp2LBhKioqsjoUAAAAAAAAoNr27dunO++8Uy+//HKwLSwsTH/88YdatGhhYWQA0HA0qKTG0qVL9fnnnysjI0O33XZbpf08Ho88Hk/wOC8vT5Lk9/vl9/tDHmdFDMOSh0UDZRh+GUZAhmHN+xUNj0WntvqPky9qwG8YChiG/LxvUF0Wnnyt+p0WAAAcuTVr1mj48OH65Zdfgm1XXXWV5syZo9jYWAsjA4CGpcEkNdxut2666SbNnj1bzZo1q7LvtGnT9NBDDx3Snp2dLbfbHaoQq5SUZMnDosHyq1Wr3P0FwxrcLnGwQFaW1RHUU5x8UQN+SbmtWikQCHDmRfVYePLNz8+37LEBAEDNBAIBPfvss7r77rtVXFwsSYqMjNRzzz2na6+9VgYX1QBAjTSYpMYjjzyifv36VatQ0uTJk5WWlhY8zsvLU1JSkuLj4xUTExPKMCu1bZslD4sGylypYWj79ngFAkyt4fASEqyOoJ7i5Isa8BuGDMNQ/PbtsgUCVoeDhsDCk6/L5bLssQEAQPVlZ2dr7Nix+uijj4Jtffv21YIFC6gXCwBHqMEkNebPn6/s7Gw1b95cklRYWKi33npL33//vZ5//vlyfZ1Op5xO5yH3YbPZZLNZM0HM3AhqKhAwFAjYSGqgWiw6tdV/nHxRQ0YgINv+f8BhWXjytep3WgAAUDO7du3S559/HjyeMGGCpk2bVuG8FQCgehpMUuOrr76Sz+cLHk+cOFH9+/fXtddea11QAAAAAAAAQCV69OihZ555Rvfee6/mzp2rCy+80OqQAKDBazBJjfbt25c7jo6OVqtWrdSqVSuLIgIAAAAAAAAO2LJli9q0aVNuq8jrr79el156qVq0aGFhZADQeDTYdetz585llQYAAAAAAADqhYULFyolJUWTJk0q124YBgkNAKhFDTapAQAAAAAAAFitoKBAY8eO1YgRI5SXl6dnnnlGn376qdVhAUCj1WC2nwIAAAAAAADqk9WrV2v48OH67bffgm2jRo3SaaedZmFUANC4sVIDAAAAAAAAqIFAIKCnnnpK/fv3DyY0oqOj9cYbb2jevHmKiYmxOEIAaLxIagAAAACoM7fffrsMwwj+69q1qyRp3bp16tevn+Li4nT33XcrEAhYHCkAABXLysrSkCFDNGHCBBUXF0uSTjzxRP3444+65pprLI4OABo/khoAAAAA6syqVav073//Wzk5OcrJydHq1avl8Xh00UUX6cQTT9SqVau0fv16zZ071+pQAQA4xIYNG5SSkqKPP/442DZx4kR9++236tatm4WRAUDTQVIDAAAAQJ3w+XzKyMhQamqqmjdvrubNm6tZs2b65JNPlJubqxkzZig5OVlTp07VK6+8YnW4AAAconPnzkpOTpYktW7dWp9++qmeeOIJhYeHWxwZADQdFAoHAAAAUCfWrl0rv9+vPn36aMeOHTrrrLP04osvas2aNerfv78iIyMlSb1799b69eurvC+PxyOPxxM8zsvLkyT5/X75/f7QPQmL+P1+BQKBRvnc6gPGN/QY49BjjEOrdHxtNpvmzZunSZMm6emnn1br1q0Z81rCezj0GOPQqs3x5TWqGkkNAAAAAHVi/fr1OuaYY/Tss8+qVatWmjBhgm688Ub17NlTnTt3DvYzDEN2u105OTmKi4ur8L6mTZumhx566JD27Oxsud3ukD0Hq/j9fuXm5gYn1FC7GN/QY4xDjzEOjXfffVfJycnq3bt3cHwjIiL0zDPPSDLra6B28B4OPcY4tGpzfPPz82spqsaJpAYAAACAOjFy5EiNHDkyePz888+rc+fO6tGjh5xOZ7m+LpdLhYWFlSY1Jk+erLS0tOBxXl6ekpKSFB8fr5iYmNA8AQv5/X4ZhqH4+HgmIUKA8Q09xjj0GOPalZ+fr1tvvVXp6elKTk7W999/z/iGGO/h0GOMQ6s2x9flctVSVI0TSQ0AAAAAlkhISJDf71ebNm20bt26crfl5+dXuT+50+k8JBEiSTabrdH+kW4YRqN+flZjfEOPMQ49xrh2rFq1SiNGjFBmZqYkaePGjXr77bc1dOhQxjfEeA+HHmMcWrU1vrw+VWN0AAAAANSJu+++W/Pnzw8er1ixQjabTb169dKKFSuC7Zs3b5bH41GLFi2sCBMA0ET5/X49+eSTOu2004IJjejoaKWnp+uGG26wODoAQClWagAAAACoEykpKbr//vvVunVrlZSU6LbbbtPo0aM1aNAg5eXl6bXXXtN1112nqVOn6rzzzpPdbrc6ZABAE/Hnn39qzJgxWrx4cbCtX79+WrBggZKTkynaCwD1CEkNAAAAAHVi1KhRysjI0GWXXSa73a5Ro0Zp6tSpCgsL08svv6wRI0bo7rvvls1m05dffml1uACAJuLTTz/VmDFjyhX9njRpkh5++OEqt0IEAFiDpAYAAACAOjNt2jRNmzbtkPaLL75YGzdu1A8//KD+/furZcuWFkQHAGhq/vzzT11yySXyeDySpDZt2uiNN97QwIEDLY4MAFAZamoAAAAAqBfatGmjwYMHk9AAANSZNm3a6O9//7sk6cILL9SaNWtIaABAPcdKDQAAAAAAADQJgUBAgUBANtuB63xvv/12JSUladiwYTIMw8LoAADVQVIDAAAAgCTp4Ycfrla/KVOmhDgSAABqX15ensaPH6+OHTtq6tSpwXbDMHTppZdaGBkAoCZIagAAAACQJG3evPmwfbiCFQDQEH3//fcaMWKENm3aJMMwdO655+rcc8+1OiwAwBEgqQEAAABAkvTaa69ZHQIAALXK7/friSee0P333y+fzydJatasmfLz8y2ODABwpEhqAAAAAAAAoNHZuXOnRo8erc8++yzY1r9/f82fP1+dO3e2MDIAwNGwHb4LAAAAAAAA0HB8/PHH6t27dzChYRiG7rvvPi1fvpyEBgA0cCFfqVFUVKSIiIhybcXFxQoPD5fH41FaWppmzZoV6jAAAAAA1NCqVav066+/qqSkRJIUCAS0evVqPfXUU9YGBgBAJTwej+65555yP6vatWun9PR0DRgwwLrAAAC1JuRJjQ4dOmjdunV66qmnNG3aNEnSNddcoyuvvFIXX3yxvv7661CHAAAAAKCGpkyZotmzZyssLExRUVFq2bKl/vOf/2jQoEFWhwYAQKUMw9BXX30VPL7ooov06quvqlWrVhZGBQCoTSFParRs2VJFRUX68MMPFRsbq169emnDhg0aMmSIHA6HbDZ2wAIAAADqmzlz5uiLL77QL7/8orfeektvv/22pkyZoqysLKtDAwCgUuHh4VqwYIFOO+00/e1vf9Mtt9wiwzCsDgsAUItCnlGIiYlRp06dtHTpUu3Zs0cdO3bU3//+dy1btizUDw0AAADgCPl8PkVGRio1NVUrV66UJN1000165513LI4MAIADcnNztXHjxnJt3bp10+bNm3XrrbeS0ACARqjOlkl89tlnevTRR5WXl6eRI0fqo48+qquHBgAAAFBDI0aM0LnnnquoqCi1bt1aU6ZM0XPPPafmzZtbHRoAAJKklStXqk+fPrr44otVWFhY7rbo6GiLogIAhFpIkxper1d+v19+v19z5szR2LFj5fP5dOaZZ+r//b//J0lkzAEAAIB66JlnntGkSZMkSa+88oq++uorLVmyRHPmzLE4MgBAU1dSUqJp06bpjDPO0JYtW7R+/Xrde++9VocFAKgjIaupsWHDBvXv31+xsbH6/PPP9fnnn2vEiBF65513dN5552nAgAFyOBzKyspShw4d5PF49L///S9U4QAAAACoAZvNpvHjx0uSUlJStHTpUosjAgBA+uOPP3TNNdfoiy++CLaddtppmjBhgoVRAQDqUsiSGh06dFB6erruvvtu3XrrrTrllFOUmJioHTt26Oeff9b48eN18cUXa/jw4frHP/4hj8cTqlAAAAAA1NB1111X6arqV199tY6jAQBA+uijj3Tttddq9+7dkszdP+6//35NmTJFYWEhm+ICANQzIdt+yul06oILLlBkZKR++ukndevWTXPmzNGoUaMUCASUnZ2tPn36KCIiQikpKTr55JNDFQoAAACAGurUqZM6duyojh07Kj4+Xlu3btWCBQvUqlUrq0MDADQxbrdbd9xxhy666KJgQiMxMVFffPGFHn74YRIaANDE1MlZ3+l0avLkyUpMTFTPnj114403qkuXLnXx0AAAAACOwN/+9rdD2t5//3394x//sCAaAEBT5fP5dMYZZ+iHH34Itg0dOlSvvPKKWrZsaWFkAACrhLRQuCQVFBRIMpev//DDD/J6vXrggQfUvXt3SRQKBwAAABqKSy65RD/99JPVYQAAmpCwsDANGzZMknnR7KxZs/Tee++R0ACAJizkKzWys7O1c+dObdu2TR9//LEiIyM1ZMgQXXnllfrXv/5FLQ0AAACgHnrjjTfKHfv9fq1YsYKLkgAAde6ee+7Rtm3bdMstt6hXr15WhwMAsFjIkxo//vij2rZtqy+//DLYNmnSJO3evVs+n08dO3YMdQgAAAAAaui1114rd2wYhhITE/XWW29ZFBEAoCn45ptv9NNPP2n8+PHBNrvdrhdeeMHCqAAA9UnIkxpJSUmHtCUkJCghIUGS9PHHH4c6BAAAAAA1tHTpUqtDAAA0ISUlJZo6daoefPBBGYahlJQUnXbaaVaHBQCoh+qkUHhmZqa6du1a4W0ff/yxfvvtN11//fVq1qxZXYQDAAAAoAIPP/xwtfpNmTIlxJEAAJqS7du3a9SoUVq2bFmwbdasWSQ1AAAVCnlSIy8vT6eeeqqefPJJjRkz5pDbf/rpJ82aNUsLFizQd999F+pwAAAAAFRi8+bNwf/PycnRhx9+qH79+ik5OVlbt27Vt99+qwsuuMDCCAEAjc3777+v66+/Xnv27JEk2Ww2TZkyRffdd5/FkQEA6quQJzViYmL0+OOP67777tNVV10ll8tV7vZ77rlHt9xyi1q1ahXqUAAAAABUoWwdjfPOO08vvfSSrr/++mDbG2+8oblz51oQGQCgsSkqKtLEiRP1/PPPB9uSkpL05ptv6swzz7QwMgBAfWeriwe5/vrr1bt3bz399NMV3t6sWTMFAoG6CAUAAABANfznP//R2WefXa7t9NNP1+rVq60JCADQaGRkZOjkk08ul9C49NJL9d///peEBgDgsOokqSFJ06dP18yZM1VUVHTIbW63W4Zh1FUoAAAAAA5j8ODBuvrqq/Xee+9p1apV+vDDDzV69GgNHDjQ6tAAAA1YIBDQtddeq3Xr1kmSXC6XXnjhBb3zzjtq0aKFxdEBABqCkG4/ddJJJ8npdMput0uS8vPzdcopp6h58+bl+v3555/q0qVLKEMBAAAAUAMvvfSSHnjgAd11113auXOnEhISdPHFF+vRRx+1OjQAQANmGIZeffVV9evXT926ddPChQvVs2dPq8MCADQgIU1q3HnnnXI6nQoPD6+yn91u18knnxzKUAAAAADUQFRUlGbMmKEZM2ZYHQoAoIHzer1yOBzB4169emnRokU6+eSTFRERYWFkAICGKKRJjVGjRgX/v6ioSD179tSPP/4YXKnx9NNP67333tOXX34ZyjAAAAAAAABQx3w+nx599FF98skn+uqrr8pd9HrWWWdZGBkAoCGrs5oaDodDW7ZsUVjYgTzKtddeq/Xr1+uzzz6rqzAAAAAAAAAQYlu3btWAAQP00EMP6fvvv9e9995rdUgAgEYipCs1yj1QWJgMwyiXlY+NjdV9992nKVOm6LzzzqurUAAAAABU4OGHH9Y999yj8PBwPfzww5X2mzJlSh1GBQBoaN59912NGzdOe/fulWRuOx4XF2dtUACARiOkSY2XXnpJ4eHhMgxDkhQIBDRv3rxy+yg6nU6tWrVKb731lq688spQhgMAAACgCps3b5bf7w/+f0VKf7cHAOBghYWFSktL05w5c4JtHTt21Pz583XaaadZGBkAoDEJaVJj4cKFcrlcstnMXa4GDx6s999//5B+8fHx+uGHH0hqAAAAABZ67bXXKvx/AAAOZ+3atRo+fLjWr18fbLviiiv04osvBmurAgBQG0Ka1Pj888+r1c/v9wcTHwAAAAAAAGg4Zs+erQkTJsjj8UiSIiMj9cwzz2js2LGs8AMA1Lo6q6lRFRIaAAAAAAAADdPWrVuDCY2UlBQtXLhQxx57rMVRAQAaqzrNJvzwww+64oorgvv0AgAAAKi/lixZokAgILfbraeeekpPPvmkCgsLrQ4LAFDPPPzww+rfv7/uuOMOrVy5koQGACCk6nSlxksvvSSfz8fKDAAAAKCeu+222/Thhx9q06ZNSktL0zfffCNJ+v777/XWW29ZHB0AwCo+n0/ff/99ucLfDodDX375pZxOp4WRAQCaijpLavz8889KT0/Xf/7zH0nSpZdeKq/XK8MwFAgE5PF4tHjx4roKBwAAAEAV5s+fr+XLl8tms+mdd97RunXrtG/fPvXp08fq0AAAFtmyZYuuvvpqrVq1SitXrtQJJ5wQvI2EBgCgrtTJkgm3263Ro0fr3nvv1dVXXy1J+u6773T55Zfr0ksv1cqVKzVq1Ki6CAUAAABANdjtdgUCAa1evVpt2rRRQkKCcnNz5XK5rA4NAGCBt956S3369NGKFSvk9Xp1zTXXqKSkxOqwAABNUMiTGm63W0OHDtXpp5+ue++9V1u2bJEkhYeHa8yYMbr22msVHR2t0aNHhzoUAAAAANV07bXXKjU1VQMHDtTIkSP122+/aeTIkbr88sutDg0AUIf27dunG264QVdddZVyc3MlSZ06ddIrr7wiu91ucXQAgKYopNtP/fDDD7r99ts1cOBAPfjgg5IkwzAO6VdRGwAAAADr/N///Z/OPfdcRUREKDU1VZs3b9add96p6667zurQAAB1ZM2aNRo+fLh++eWXYNtVV12lOXPmKDY21sLIAABNWchWauTk5Oiss85SixYtggkNAAAAAA3H+eefr9TUVElS586ddcMNNygsrM7K8gEALBIIBPTMM8/o5JNPDiY0IiMj9eqrr2rBggUkNAAAlgpZUiMuLk5r1qxRQUGBLr74Yrnd7lA9FAAAAIAQmD9/voYPH67TTz9dGzZs0JVXXqldu3ZZHRYAIMRuv/123XHHHSouLpYk9e3bVz/++KOuu+46dtsAAFgupDU1kpOT9fnnn6tDhw4aNGiQ8vPzKSIFAAAANAD33Xef/vrXv6pz585as2aNbDbzT4ebbrrJ4sgAAKE2ZsyY4Mq8CRMmaMWKFTrmmGMsjgoAAFPI147bbDY999xzuuOOO3TllVfqzjvvlCQVFRXp9ttvlyTt3r1bd9xxh55++ulQhwMAAACgGl566SV9/vnn6tWrl1544QU5HA7NmDFDPXv2tDo0AECInXTSSXr22WfVoUMHXXjhhVaHAwBAOSFdqVHWU089Ja/XG8z0//Wvf1WHDh3UsWNHPfDAA2rTpk1dhQIAAADgMJo3b65t27aVa9uxY4dat25tUUQAgFDYvHmzbr31Vvl8vnLtN998MwkNAEC9VGdV/gzD0PPPP68TTzxRI0aMUFpaWl09NAAAAIAauv/++zVs2DBdeuml8ng8mjlzpj744AM99NBDVocGAKglCxYs0M0336y8vDy1bNmSczwAoEGos5UaktS9e3e9//776tq1a10+LAAAAIAaGj16tJYsWaJmzZrp7LPP1r59+/TGG2/ommuusTo0AMBRKigo0NixY3X11VcrLy9PkrRw4UIVFRVZHBkAAIdXZys1Sp177rmHtGVnZys+Pr6uQwEAAABQhdTUVKWmppZr27t3r5o3b25NQACAo/bjjz9qxIgR+u2334Jto0aN0qxZsxQREWFhZAAAVE+drtSoyAsvvKCuXbvqX//6l9WhAAAAAE3arl27NGrUKLVt21axsbG68MILtWXLFklmMuOBBx5Q586drQ0SAHBEAoGAZs6cqf79+wcTGtHR0XrjjTc0b948xcTEWBwhAADVE9KVGlOnTtVPP/0kh8Mhv98vm82myMhItW3bVikpKXr77be1evVqffDBBzr77LNDGQoAAACAw7jtttu0ceNGzZ07Vy6XS88//7wuueQSXXTRRXrmmWfUt29fffjhh1aHCQCooaysLF177bX65JNPgm0nnniiFixYoG7dulkYGQAANRfSpEbfvn3VqlUrhYWZD+P3+1VUVKSdO3dq9uzZ+uKLL/Tiiy+S0AAAAADqgcWLF2vp0qXq3bu3JOmUU05RbGysXC6X3n77bQ0aNMjiCAEAR+Lpp58ul9CYOHGiHnvsMYWHh1sYFQAARyakSY0LLrhARUVFevzxx3XTTTepXbt2kiSPxyOn06m1a9fqkksukcfj0fjx46t1n3v37tWvv/6q7t27Ky4uLpThAwAAAE3K3r17y20v5XK5FBkZqbfeeksdOnSwMDIAwNF44IEH9O9//1t//vmnXn/9dZ1//vlWhwQAwBELaVLj008/1c0336ykpCRdddVVwaTGJZdcou7du+vpp5/Wp59+qlNPPVW9e/fW6aefXuX9vf3227rhhhuUlJSkTZs2ae7cubriiitC+RQAAACAJiMQCOiee+6R0+kMthUVFenhhx8ut9f6jBkzjvgxPvjgA02YMEFbt27V8ccfrwULFqhHjx66/fbb9eyzzwb7JScnKzMz84gfBwCaMrfbLZfLFTx2uVx69913FR0drdatW1sYGQAARy+khcI3bNigZ555Rl999ZWOO+64YPs777yjX375RTfddJO6deumxx57TOPGjZPP56v0vnJzc/X//t//0/Lly7V27VrNmjVLd999dyjDBwAAAJqU0aNHq7CwUDk5OcF/I0aMUElJSbm2I7Vx40Zdd911evzxx7Vjxw51795d48aNkyStWrVK//73v4OPsXr16tp6WgDQpLz55pvq1KmT1q9fX649OTmZhAYAoFEI6UqN2267TY8++qheffVVRUZGlrstOjpar7zyivLz8zVv3jwVFhYGa29UJC8vT0899VRwf98TTjhBu3fvDmX4AAAAQJMyd+7ckN7/zz//rMcff1xXXnmlJGn8+PEaPHiwfD6fMjIylJqaqujo6JDGAACNVX5+vm6//Xa9/fbbkqThw4fr+++/L7diAwCAxiCkSQ1JOvvss9W1a1c5HI5Dbjv//PPlcrlkt9s1YcKEKu8nKSlJI0eOlCR5vV7NnDlTw4YNC0nMAAAAQFOyfv16Pf7441VeZFTKMAydd955GjFiRI0fZ8iQIeWOf/31V3Xr1k1r166V3+9Xnz59tGPHDp111ll68cUXqeMBANX0ww8/aPjw4eW27evbt2+VO2IAANBQhTypccYZZ6hbt27asGGDNm7cqPHjx2vx4sVHfH9r1qzROeeco/DwcP38888V9vF4PPJ4PMHjvLw8SZLf75ff7z/ixz4ahmHJw6KBMgy/DCMgw7Dm/YqGx6JTW/**************************/TttbGysTjrppHK1NCrz559/aty4cbr88ssrvHCpuoqLizV9+nSlpaVp/fr1OuaYY/Tss8+qVatWmjBhgm688UZ9+umnlX5/ffydP5T8fr8CgUCjfG71AeMbeoxxaPj9fj311FO699575fV6JZk7Yzz//PPBC0MZ89rBezj0GOPQY4xDqzbHl9eoaiHffiomJkZZWVl6+OGHFQgEtHbtWqWlpal58+bBfk6nU2PHjlV8fPxh77N3795avHixJkyYoHHjxumdd945pM+0adP00EMPHdKenZ0tt9t9VM/pSCUlWfKwaLD8atUqV4FAQCEufYNGIivL6gjqKU6+qAG/pNxWrRQIBDjzonosPPnm5+fX6v0lJibq9ttv13vvvad///vfstnKfwp8Pp+8Xq/mzZun4uJiFRUVyev1HlVS429/+5uioqI0btw4ORyO4OSbJD3//PPq3Lmz8vLyyhUoL6s+/s4fSn6/X7m55u+HB78+OHqMb+gxxrUvOztbt99+u7788stgW69evfTCCy+oS5cuyuKPhFrFezj0GOPQY4xDqzbHt7Z/329sQprUaNmypRwOh+x2e/CqryFDhuijjz7S9ddfH+z38ccfKysrS9OnTz/sfRqGoRNPPFGvv/66kpOTtXfv3nIJEkmaPHmy0tLSgsd5eXlKSkpSfHx8pX8Uhdq2bZY8LBooc6WGoe3b4xUI8EMGh5eQYHUE9RQnX9SA3zBkGIbit2+XLRCwOhw0BBaefEO1P3qHDh10+umnH/JHmN/vD25h4nA4NGXKlENq5tXEF198oVmzZmnlypUVJkYSEhLk9/u1c+fOSn9/r4+/84eS32/+fhgfH88kRAgwvqHHGNeuL774QiNHjiyXuLj77rt1yy23KDExkTEOAd7DoccYhx5jHFq1Ob7UQ6paSJMaDz74oCTpmWee0aRJkyRJRUVF6tChg6644gp16dJFktSvXz9t2LChyvtatmyZPvroIz3xxBOSpPDwcBmGUeEbxOl0Vrh03mazWfaBZW4ENRUIGAoEbCQ1UC38LlIJTr6oISMQkG3/P+CwLDz5huJ32g8//FAff/yxbDbb/tWi5gVFPp9PHo9HL7/8sn7//XeNHDlSJ598smbMmHFEj7N582aNGDFCs2bN0nHHHSfJnIjr27evrr76aknSihUrZLPZlFTFirv6+Dt/qJX+/dNYn5/VGN/QY4xrT3h4uHbt2iVJatOmjd544w2de+65ysrKYoxDiPdw6DHGoccYh1ZtjS+vT9VCXlNDkp566qng/0dEROjVV19VXFxcsC0lJUVnnnlmlffRvXt3vfjii+rWrZsuuOAC3X///Ro0aFCjvAoLAAAAqGtr166V1+tVamqqjApqy2zatElnnXWWxo0bpwceeOCIHqOoqEhDhgzR0KFDNWzYMBUUFEgyt5i9//771bp1a5WUlOi2227T6NGjj2o1CAA0ZqmpqXrggQf0n//8R6+99lpwhRsAAE1BnSQ1pkyZoksvvTR4JdVFF11U7vbVq1fr0ksvDRb3q0jbtm31zjvv6M4779TEiRN1/vnn64033ghp3AAAAEBTcvrppysyMlI33HCDYmNjFRERofj4eCUmJmr37t2aMWNGcDXFkVi8eLHWr1+v9evX66WXXgq2b968WVdddZUuu+wy2e12jRo1SlOnTq2NpwQADV4gENCnn36q888/v9yVuw888IBsNluFiWgAABqzOklqZGdnV7g0vFR8fLz27dt32PsZOHCgMjIyajM0AAAAAGWcd955Wr16tcLDw1VSUqKsrCxlZGTo3//+t/7973/r+++/16OPPqro6Oga3/fQoUODW1sdbNq0aZo2bdrRhg8AjUpeXp7Gjx+v+fPn6+9//7v++te/Bm+z2+0WRgYAgHVCmtTo1q2bXC6XCgsL1bt370Nuj4yM1DXXXKMLLrgglGEAAAAAOAybzaYJEybonnvuKdfu8/nkdrv1+++/67HHHtMtt9yiU045JZj4AACExvfff68RI0Zo06ZNkqT77rtPl19+ebA+KQAATVVIkxpPPPGEoqOjdfnll+vZZ5895PZffvlFEyZM0F/+8heWSwIAAAAWuueee3T22Wfr1FNPlWQmM8LCzD8XXn/9dUVHRys+Pl6ffPKJ3n//fRIaABAifr9fTzzxhO6//375fD5JUkxMjF544QUSGgAAKMRJjUsuuUSSFB4errPOOuuQ208++WTde++9ysnJ4Y8iAAAAwEJTpkzRRx99pB9//FGBQECXXnqpzjnnHF188cV67rnnNGnSJE2aNEk33XSTLrvsMqvDBYBGaefOnRo9erQ+++yzYFv//v01f/58de7c2cLIAACoP2yH71K7MjIyNG7cOP3444+KiIjQzp07FRERoYiIiLoOBQAAAICkwsJCvf/++3rnnXdkGIbuv/9+ZWdn64YbblCXLl30n//8R++++64+/vhjtW/fXvPmzbM6ZABodD7++GP17t07mNAwDEP33nuvli9fTkIDAIAy6jSp8frrr6t3797avHlzcCl7eHi4fvvtN7Vt27YuQwEAAACwX2RkpH766SclJydLkm644Qa99957ioqKCvY5/fTTtWTJEs2YMUPnnHOOVaECQKP0j3/8Q4MHD9auXbskSW3bttVnn32mxx57TA6Hw+LoAACoX0K6/VSp4uJiFRUVacSIETr22GN1yimnlLt90aJFGjBgQF2EAgAAAOAgRUVFGjx4sL744gtJ0o8//ii/3y+73S7J3N/d7XZr2LBhGjZsmM4++2x99NFHSkxMtDJsAGg0hgwZomOOOUa//vqrhgwZotdee02tWrWyOiwAAOqlOklq/PLLL8HtpQ5OaGzbtk3vv/++Vq1aVRehAAAAADiIy+UKXh0sSX/961912mmnletjGIYGDRqkG264QUOHDiWhAQC1KCoqSgsXLtRXX32lW2+9VYZhWB0SAAD1VsiTGo888og+/fRTffPNN5Kkrl27KjIyMnj7rl27dPXVV6t9+/ahDgUAAABABQzDCG4PK5nbUb366qvat2+fmjdvHmx/6623VFJSogcffLDugwSARiI3N1dpaWm677771KVLl2B7nz591KdPH+sCAwCggQhpUuOxxx7TSy+9pLlz5wbbsrOz9eGHHwaPly5dqrfeekuTJk1S69atQxkOAAAAgEps3LhR1157rbp27arCwkKtWbNGp5xyimJjY9WuXTv16NFDF154od58802rQwWABmvlypUaMWKEtmzZonXr1unrr7+mZgYAADUU0qTGmDFjdMMNNyghIUGStG/fPhmGobPOOivY56yzztL27ds1aNAg/fjjj8F9ewEAAADUnbi4OJ1xxhn63//+J5/Pp5NOOkklJSUqLCxUdna2vv/+e82aNUuvvPKKFi1aVK6IOACgaiUlJfr73/+uKVOmqKSkRJL066+/av369UpJSbE4OgAAGpaQJjUO3lLK5XJp9erVh/SbM2eONmzYQEIDAAAAsEBJSYmio6M1btw4SdLLL7+sc84555B+Tz31lF599VWNHTtW//jHP+o6TABokHbs2KFrrrlGS5cuDbadfvrpevPNN9WxY0cLIwMAoGGqk0Lhpex2uzp37lxh+7HHHluXoQAAAADYr7i4uFzh73nz5skwDBUVFSkmJkZ+v18ej0edO3fWzJkzdcIJJ+g///mP+vXrZ2HUAFD//etf/9J1112n3bt3S5JsNpvuv/9+PfDAA+VqGVXE65WKiqSICIkdqgAAOKBOkxoAAAAA6p+IiAj16dNHv/32m7p3764zzjhDkhQWFqaePXtqwYIFOu6444L9X3nlFZ1wwglWhQsA9Z7b7dZf//pXPfvss8G2xMREvfnmm+W25K5IZqa0ZIm0dKnkdksulzRggDRokJScHOrIAQCo/2xWBwAAAADAem+//bZSU1P16quvqri4WG63Wz169NC7776rpKSkcn1POukk2Wz8KQEAlVm5cmW5hMYll1yiNWvWHDahsWyZNHGilJ4uFRZK4eHm1/R06a67pOXLQx05AAD1H3+JAAAAAFBsbKy+/fZbffTRR0pMTNRjjz2mjRs36sorr9RZZ52lE044QSeccIJOPvlkzZkzx+pwAaBeO/vss3XXXXfJ5XLp+eef1z//+U+1bNmyyu/JzJRmzpQKCqSePaXERKllS/Nrz55Sfr40Y4a0cWMdPQkAAOoptp8CAAAAILfbrS5duigpKUnnnnuuHnzwQf3jH//QggULyvXbsWOHxo4dq5tuusmiSAGg/snPz1d0dLQMwwi2TZ06Vddff7169OhRrftYskTKzjYTGGXuRpJ5nJwsZWRIixdL48fXZvQAADQsrNQAAAAAoL1790qSpk2bpvT0dJWUlMgwDB1zzDHl/vXr10+jR4+2NlgAqEe++eYbHX/88Xr++efLtYeHh1c7oeH1mjU04uIOTWiUMgzz9qVLzf4AADRVJDUAAAAAaOfOnZKkyMhIhYWFKSwsTPPnzz+kX7NmzfTwww/XdXgAUO+UlJTokUceUWpqqrZu3aq77rpLP/zwk/Lyap50KCo6UBS8Ki6X5PGY/QEAaKrYfgoAAADAIWw2m0488USrwwCAemn79u0aNWqUli1bFmxr376f/va35rLZzOTDgAHSoEHmtlGHExFhfk9hYdX93G4pMtLsDwBAU8VKDQAAAAAAgGp6//33lZKSEkxo2Gw2HXPMg0pIWCqpg8LDzeREerp0113S8uWHv0+Hw0yC5ORIgUDFfQIB8/YBA8z+AAA0VSQ1AAAAAAAADqOoqEi33HKLhg0bpj179kiS2rZN0mmnfan27f+m448PU2Ki1LKllJhoFvzOz5dmzJA2bjz8/Q8cKMXHS5s2HZrYCATM+0hIMFd/oP7zenVEW5EBqH18Hhsftp8CAAAAAACowqZNmzR06FCtW7cu2HbppZfq1FNf0nvvtVCXLocW+DYMc+upjAxp8WJp/PiqH6NrVyktzUyCZGSYRcFdLnPLqZwcM6ExYUL1trOCdTIzpSVLzILupXVSarIVGYDaw+ex8WKlBgAAAAAAQBVatmypgoICSZLL5dILL7ygBQve0ffft1Bc3KEJjVKGYSYnli6t3hXCqanS9OnSqFFm7Qyv1/w6apT05JPm7ai/li2TJk40tx4rLNQRbUUGoHbweWzcWKkBAAAAAABQhdjYWM2fP1+33HKL5s2bp549eyov78CVv1VxuSSPRyoqql4tjORkc1XHuHHm90REUEOjIcjMlGbOlAoKzK3Hyia62rUztw+bMcPcmowrxIHQ4vPY+LFSAwAAAAAAoIyvvvpK27dvL9d26qmnatWqVerZs6ckM9lQuj1UVdxuyek0+9eEwyHFxJDQaCiWLJGys1XlVmTZ2eZWZEBD1VBqU/B5bPxIagAAAAAAAEjy+Xx68MEHdfbZZ2vUqFEqKSkpd7vNdmAaxeEw92bPyTm0sHepQMC8fcAAkhONmddrbjFWm1uRAfVJZqY0e7Y0cqS5Hd7Ikebxxo1WR3YoPo9NA0kNAAAAAABgqfpw9e/WrVs1YMAAPfTQQ/L7/Vq2bJnmzZtX5fcMHCjFx0ubNh2a2AgEzAm/hASzKC0ar6Kimm9FBjQUDa02BZ/HpoGaGgAAAAAAwBKZmeY2IUuXHpiEGjDATALU5T7n7777rsaNG6e9e/dKkux2ux566CFdc801VX5f165SWpq5N3tGhnnlb+mWVDk5ZkJjwgT2bG/sSrciKyysup/bbRZ+r+lWZIBVGmJtCj6PTQMrNQAAAAAAQJ2rD1f/FhYW6uabb9bll18eTGh07NhRy5cv13333Se73X7Y+0hNlaZPN7dkiYw0V5tERprHTz5p3o7Gja3I0Fg1xNoUfB6bBlZqAAAAAADQWHm95t4aERH1auamPlz9u3btWg0fPlzr168Ptl1xxRV68cUX1bx58xrdV3KyNH68NG5cvRxu1IGBA6VFi8ytyA6eAGYrMjRENa1NMW5c/Tnv1cbnsZ7++MR+JDUAAAAAAGhs6su+TpUovfr34ISGdODq34wM8+rf8eNr//F///13nXzyyXK73ZKkyMhIPfPMMxo7dqyMymbvqsHhYPKrqWIrMjQ2R1Kbor6c/47m81jPf3xiP5IaAAAAAAA0JsuWmcsgsrMPzOSU7uu0aJE502Phnkj14erfjh076tprr9ULL7yglJQULViwQD169KjdB0GTk5pqri5avNh873o85lZkgwczIYqGp6HXpjiSz2M9//GJMkhqAAAAAADQWNSHfZ0Os2dHfbn6d8aMGUpKSlJaWppchwvGCux90iCxFRkai9LaFOnp5o+PipLQpbUpBg+un+/zmnwe68OPT1QfSQ0AAAAAABoLK/d1qmrPjs6dg93q+upfn8+nhx9+WMnJyRozZkyZOCJ07733Ht2dhwJ7nzQKbEWGxqCx1IqpzufR6m0RUTM2qwMAAAAAAAC1oKb7Onm9tffYy5ZJEyeal/QWFkrh4Qf27LjrLumrr4JdS6/+zckxJ8UqUnr174ABRzcx/Pvvv+uss87SI488oltuuUW//fbbkd9ZXTjcOC5fbnWEAJqQ0toU0dHmhP6OHdLu3ebXjAwpJqZx1Iqx8scnjgxJDQAAAAAAGoMj2depNhy8Z0diotSypfm1Z08pP1966ilp587gtwwcKMXHm1f/HpzYqK2rf99++22lpKTo22+/lSS53W6tXLnyyO8w1KozjjNmmIMDAHUkNVWaPl0aNcpcPef1ml9HjZKefLJx1Jiw6scnjhzbTwEAAAAA0BhYVdW1Ont2rF8vrV4tpaRIOnD174wZ5tW+pQVZ3W5zhUZCwpFf/btv3z5NmDBBL730UrCtc+fOmj9/vvr37380zzS02PsEQD3V4GvFHKZGUUMvit4UkdQAAAAAAKAxsKKqa0327Fi71uzvdEoyr+5NTDTn6JcuNa9+jYw0QzvS8hFr1qzR8OHD9csvvwTbhg8frhdeeEGxsbFH8gxr5kiLe9d075Nx4xrYjCKAxqDB1YqpZo2ixlAUvakhqQEAAAAAQGNxUFXXkoChEp9kD5PsRgiqulawZ0eJXwces3TTa5frwIT//qSGVHtX/wYCAT333HO6++675fF4JEmRkZGaNWuWxowZI6OyREFtOdri3lXsfVJuPMvufVLRQPl8Ul6eFBXFrJvVjjTBBaB2LFtmbumXnX1gOWBpjaJFi8zlgmX2zmosRdGbCpIaAAAAAAA0Fvv3dSp4ZIbyP8vQH+44uQMuuQy32rly1Cw5QdG1WdW1zJ4dubnS9h3SH38cmIRv105q315q5nabE7uV7NlxtFf/5ubm6vHHHw8mNPr27asFCxbomGOOOfI7ra4aTpxVqIK9Tyoaz26RbrXqEKnog8exNKny22/m/4eFSeeeK11wQcOv4NvQHG2CC8DRO7hGUdkMRbt2ZoZixgxzueD+z2Uot0VE7aNQOAAAAAAAjciyQKomarrSjVEqDEQq3PCqMBCpdGOUJupJLVctVnXdv2dH7pYcrVwR0IYN5mIBm938umGDtPLbgHJ/z5F69QrZFevNmzfXvHnzZLPZNGHCBK1YsaJuEhq1Vdy7dO+TnBwpENAfO6WVK1V+PL0B5W3J0bztA7R8RZlxXLZMmjhRevll+bduU8nqNfJ/+6304IPSpZdKCxaEdAhQRulrkZ5uJqjCww8kuO66S1q+3OoIgaahtEbRwUsupAM1irKzzf0Py2gKRdEbC1ZqAAAAAADQSATn2APJ8p87XpsC4xTuK1JxWIRKDEdFF6cetd+7D9TO3YvUyrNJ+xLKTyBFRQQUnbVRv/gSFNW+rxJq5yHl9Xq1b98+NW/ePNh2zjnn6JdfflG3bt1q6VGqoTaLe+/f+6Rw3Sat3dFFXp+hFnH7bwsEFO/ZKHfLBH0TOUiLSl/DgPmCuzdskzsrR4XOFtqTY5MUqYjwEkVt2iJ7Wpp5HyNG1PazR1lHcGU4gBA4yhpFDb4oehPBSg0AAAAAABqJgy9O9dsccofHyG9zVHVx6lH5+LeueqNlmmwx0WqXk6Hm+3Yoyr1bzfftULucDBkxMUpveYdWbW9bK4+3efNmnXnmmbr66qvl9/vL3VanCY2aTpx5vVXf3/69T/7cF622ezLUwV5+HN3hMfq89wRF9ko+8BouWaL8jK3K3bxbnnyviuzR8jii5LG5tNsTpT8CbeXNL5Ief/zwq0VwdI7wynAAtayKGkXllK1RVAGHQ4qJIaFRX5HUAAAAAACgEajtOfaaPObWTql659Tp+q77KBU7ImUPeFXsiNR33UfpnVOf1NaOZ2rt2qN/zAULFqhPnz767rvv9Mknn+jpp58++idxpGpp4qws76mpmpk4XV91qmAc+z+pDW1Tg6/h8s+9yvtgqXb/6ZXDV6SSyGayhxkKs0uOMMnllPwBQzm+GJVs3SF98kktPXEcwooPH4CKldYocrur7ud2S05npbWeUL+x/RQAAAAAAI3AkcyxH+0VqGUfc1dMspYfN15fH3tgyyu/zXwAl88vr9fs73QedCelN1Sxx0dBQYFuv/12vfbaa8G2Ll266PTTTz+6J3A0KijuXSG329yUvRoTZ0VF0u9hydp5zHitizt0HEu5XFKgsEjZ2woV7clRINwZnEwPlP7HMEs6eIvC5C6xK+qLL6SbbuKy41Cw4sMHoGKlNYrS082t3ypKNAYCZg2jwYP5LDZQrNQAAAAAAKARsOLi1Ioes+yWV2Uf0+E46DEzM6XZs1UyYqS8w0epZMRIafbsQ7ZJ+vHHH3XiiSeWS2hcffXVWr16tU4++eSjfxJH6qDi3hUqnTgbMKBaE2dlx7OicSzldksBV4R25TgUbnjlt9lVUmLmh/btO/DP45HCVKIir0P+Ym+1VovgCHBlOJoKr1fKy6v/q40GDpTi46VNmw49PwcC5s+ZhARp0CBr4sNRI6kBAAAAAEAjEII59lp9zF69yjzmsmUqGD9RO59I13+/LdR3q8P1328LtfOJdBXcfJe0fLkCgYBmzpyp/v3767fffpMkRUVF6fXXX1d6erpiYmKO/gkcrVqeOKvJeJ5yhkOrW54re6BEAW+Jiookn0/7l2qYX4s9kuH1KNeIU0l4BJPpoWLFhw+oS/uT0Bo5Uho1yvxaQRK63thfo0jR0VJGhrRjh7R7t/k1I8MsljFhglnrBg0SSQ0AAAAAABoJKy5Ore5j9u27vzEzU7vum6mN/y3Qd/t6KsuRqMKIlspyJOq7fT218b/52jH5CQ055xylpaXJu/+K4BNPPFGrV6/W6NGjZVRWt6CuhWDirLrjeeGFUkbSX5TlSJTLkyv5A7LZVO5fjJGnooBL+7zh2t2byfSQ4spwNFbLlkkTJ5rbORUWmvvaFRaax3eZSeh6KTVVmj7dTMJERpqrSyIjzeMnnzRvr0pDWZXSRFFTAwAAAACARqJ0jn3GDHNOPS7uwK44OTnmnGptX5xance84w6pbVuzf9abS7R7fba2uXqqRWz55ERUpKFtuckK/zlDUR18wfaJEyfqscceU3h4eO0FXltSU6XERGnxYrMIdGm9hBEjpAsuqPFgV/c1PPZY6biLuurZVZM0ueguJQT+1A5/J4UHPLL7vQoPeOSRSzm2lvpfeAettQ3SyBANAWTNhw8ItcxMaeZMqaBA6tmzfH2Kdu3MZN2MGeY5sD6+t5OTpfHjpXHjDlu7KSgzU1qyxDyfl9bKGTDATEjWx+fYRJHUAAAAAACgETl4jt3jMS9OHTw4dHMyh3vMzp2lrCxJXq/yPliq3YE4xcRWvNoiJtbQ7qI43SuH/jjtND0wZYrOP//82g+6NiUnm1fql5RIn39u7gO1fLlktx/RoFf3NTz7bOlRx9UqjDI0wfd/cgY8iijZJ5/s2mOPl8cfrv85Oujj7hO0c12yrvSyWCOkrPjwAaG0ZImUnX1oQkMyj5OTzSTe4sVm8qC+cjiqd/JbtsxM4mRnH0hMlq5KWbTITFweboUH6gRJDQAAAAAAGpkjuTg1lI/p95tfvflFyvlz/5WvZewoztMf3jz1i2pvNrhc8mZ79cVnHyu8VWxoA68NIZgIq85rmJgodeok/XvzCK319dNlkV+ql+sT2X3FKgxE6PuYAcruM0h74pLl9RxYRIIQsuLDB4SC12sm5+LiDk1olDIM8/alS833fEN+rzf0VSlNDEkNAAAAAAAaqepenFpXj1mkCHnkkkuFKtzftiR3g2b872vZZOiVzpepjaOZXHLLrUi5jUjVww2nygvxRFhV4xkRYd5teLhUUtJFy6Oj9VKraxQhj+LaRahtB4diYiT3DnPBAHXC65AVHz6gNhUVHdh+qSoul7kqqaFnTRvLqpQmgkLhAAAAAADUB02gKGlEM4d+bjtAke4cFZZ4NPWPpXp051IV+r0q8Bfr5ez/SIGAIt05+rnNAEXENIAJstKJsC5dKp8Iy842J8JqmcNhbvVeXCz16CH17i2dMcChk8+L0THHmwmNQMAs6TCAOuEAaiIi4kBdmKq43ZLT2bCzpjVdldKIf043FCQ1AAAAAACwUmamNHu2NHKkNGqU+XX2bPMK/0bG4ZAiLx6or0rCdNOmt7Qob0Pwtr/EdFda69MVn79R2bYERQ4dVP8n4evBRNjAgVJ8vLRpk/lQ4eGSbf9sTyBgvo0SEsySDgBQbaVZ05wc82RSkcaSNT2SVSmwFEkNAAAAAPXCunXr1K9fP8XFxenuu+9WoLI/oIHGZNkyaeJEs/ZCYaE5I11ai+Guu8xi042I3+/XOvcH+n8F32priTkpFGWE6ZFW/fT32G7qmrtBu4tjtPi4Ceo/sgHsWV7BRFiJ31w5UeIv0y+EE2Fdu5olO6Kjpa1bpR07pN27za8ZGVJMjDRhAlvAAzgCZbOmB/9e1piypk1pVUojQU0NAAAAAJbzeDy66KKLdP7552vhwoW6/fbbNXfuXF133XVWh4YGwuttgDV5m1hR0qysLI0ePVpLliwJtiU5O+n/mvVQssOhXK9Ti6IHa1PyII24P7lhPOXSibDCQuXmStt3SH/8IZX4JHuY+TK2by/Fut0hLWqRmmo+1vLlZl3y0ocbPNica2wQYwmg/inNms6YYWZJ4+IOTP7n5JgJjcaQNS1dlZKebp5MK1p5V7oqZfDgBvSLRuNFUgMAAACA5T755BPl5uZqxowZioyM1NSpU3XLLbeQ1MBhZWaaJQ2WLj1wwfyAAfsncjvU80xHEypK6vf7deWVV+rXX38Ntt144yT16vWIPvhSChQWyYiMUOq5Dk1sSJPw+yfCcmela2V+OxV5DDmdkt0u+XzShg3S9q0B9W+Wo9hbQjsR1qWLuVpj5EhzUUh9fdsDaGBSU83k+uLF5g9bj6dxZk0HDjSzwps2HVojqTGtSmkkSGoAAAAAsNyaNWvUv39/RUZGSpJ69+6t9evXV9rf4/HI4/EEj/Py8iSZE6d+v7+yb2uw/H6/AoFAo3xuR2P5cunpp828QOnFo0VF0pcvb5Tx4mdqFvelWjXbn+k4+2xzwqJLl0Pux5Lx9XqlL7+UWrQ4UADhYIZh3v7ll9LYsQ1+hvree+/VmDFj1KZNG73++us677zzJEk33CAVFUXvn4Q3X4OG9Fbf2u087cxZrJaezdqXUH4iLDIqoOisTVrva6223QeqQwifWOn72G73Kzq6tC1kD9fkcB4OPcY49I54jDt3lm66yfxZdPDFAo3l9erSxVx18tRT0vr1Fa9KueMOcywqec61+R7mc1A1khoAAAAALJeXl6fOnTsHjw3DkN1uV05OjuLi4g7pP23aND300EOHtGdnZ8t9uP2QGyC/36/c3FwFAgHZKpsAb2J27pTefVdq2bL8Qod2e9apt/8DhRXk6hd7S7VLCFdkWLFsK1aYkxRDh0rHH1/uviwZ38JCqVUrc1KoWbPK+7lc5iX/O3aYV8Y2UH6/X/369dOTTz6p888/X61atVJWVla5PmU/uj6fWZciPFwKq+czF19sbabfT71Fg4o+UExxrjyOaPls4QrzF8vpLVBh5x5aHDFUHX+P1oUHPefaxHkitBjf0GOMQ6/WxrgR/q4lSTrmGGnKFGn1amntWvMCBIdD6tVL6ttXattWquI8Xpvv4fz8/KP6/saunv9qAAAAAKApCAsLk9PpLNfmcrlUWFhYYVJj8uTJSktLCx7n5eUpKSlJ8fHxiomJCXm8dc3v98swDMXHxzPRs99775lzDj17Srt2mW2tcjfqlO9mKayoQJvVRXtz/frff92KbS61axOhTv6fFbl9u/TEE+VWbFgyvl6vGXhhobmtR2VKkxmJiQ1mpUYgENC8efO0dOlSvfrqqzIMIzjGd9xxR5VjvHGj9Nln5uIU9+EX2VjO6zV3Kyn0nCNfbCcdt32Jum/5Us4Sj3x2p9a3PVvr4wfqp/wu+mWRuTVUqF5GzhOhxfiGHmMceoxxNSQkSCkpR1SsqzbH1+VyHdX3N3YkNQAAAABYrkWLFlq3bl25tvz8fIWHh1fY3+l0HpIEkSSbzdZo/0g3DKNRP7+a8HrNbb2bNzePAwHza49tn8mVk6X/envKVyIZCshTKPkipMwN0g5nF/XPzVDskiWH1Kio8/F1Os3Z+vR088rPyoqS7tkjXXih2b8ByMvL0/jx4zV//nxJ0hlnnKEbbrhB0uHHeNkys2562e3E9u0zh2jRIrNWbWpqnT2VavF4zDkvp1Pa1ayrlvfoqq+PGadwX5GKwyLkt5kTYc5iM0nj8RzBS1k6sRYWZi5hqWKCjfNEaDG+occYhx5jXE1O5xH97K2t8eX1qRqjAwAAAMBy/fr104oVK4LHmzdvlsfjUYsWLSyMCvVVUdGBq/hL2fxeddm6VDvdcfIHzGLNYQ6zXEVkhNQiTvL6DP2WHaeCj5aaE8VWGzhQio83i5KWZmZKlSlK6h0wSHl59SPkqnz//ffq27dvMKEhmfVyqiMz00xoFBSYq28SE82txRITzeP8fGnGDHNI6pOIiANbrpfy2xxyh8cEExqSebvTafavtsxMafZsc8u0M84wt0U54wzzePbs+jcYAADUEZIaAAAAACyXmpqqvLw8vfbaa5KkqVOn6rzzzpPdbrc4MtRHFU0kh/uK5Ctwa5/fZV7Ebph5AcOQjP1/+cbESPk+l7K37b+83mpdu5rLD6KjpYwMc6up3bvNrxkZKrDF6P3OEzRySrJGjTK3LqqPc9l+v19///vfdfrpp2vTpk2SpJiYGM2fP1/PPfdcte7j80+92rczT906eQ9ZtGIYUnKyuYJj8eLajv7oOBzSgAFmDdmD81KlAgHz9gEDarD11LJl0sSJ0qxZ0o8/mu+J4mLz6w8/mO133SUtX15rzwUAgIaC7acAAAAAWC4sLEwvv/yyRowYobvvvls2m01ffvml1WGhniqdSE5Pl9q1Mye9PbYI5RW7FGkrVNH+SfGSEik6Vio7Rx4d5tbOnEi1D4tQdeeXj2Bb7epLTTWXIyxebO6p5fFIkZH6rftgzVg7SGu/Sw5uxVRYWP+2Ytq5c6dGjx6tzz77LNjWv39/zZ8/X507dz78HWRmquTTJer196XqU+yWPdulX9sN0PqkQdoVkxzsZhjmllRLl0rjxtWv8iIDB5qvyaZNZt2PskmZMgtuNGhQNe+wdNlKVpa5dMVuN/daK73jvDxz6UpWlrl8JTHRzPoAANBEkNQAAAAAUC9cfPHF2rhxo3744Qf1799fLVu2tDok1GMHTyQX+x36JnyALt2Xrt2BdvJ6DYXZpaioMt8UCCjGl6MvWg/W8T7HYZMamZnSkiXmRHrpdlcDBpiT07U6h5ycLI0fL++YcSraU6QdeyI0eYpDBQFz66Wyk+Tt2pmT5PVhLvvjjz/WmDFjtGt/pXbDMDR58mQ9+OCDclQn67C/iEZgZ7bCiuPkc7gU4SvUKRvSddy2T/TVceP1c/vzgts4uVwHaljUp6RG6YKbGTPMBTelSSi321yhkZAgTZhQg9dqyRJzWYrNZj7ZuLjyb4KYGPOObbYDy1cOqhEDAEBjRlIDAAAAQL3Rpk0bDR482Oow0AAcPJEcGyttCB+oM/YtUtvCTdoe3kUtWhgKL538DgQUn79ROY4E/dx+0GFrG1RUtDpUKyUOJE8ccrsd2r5dys2V+vU7tH546VZMGRnWz2W/+uqrwYRG27ZtlZ6ernPOOad631ymiIbRq6dydxny+aSwsFxFePYqOes7dc7+j7a26qu1HS7S+qRB2uFOVmRkDetS1JFKFtxo8OAaJsG8XvMOYmOlX34xC3FUVETe6ZR27jTrbJQuX2G7PgBAE0FSAwAAAADQIB08kbw9oatmF6Xp7rAZOtHIkCcQJ6/bJUeJW5GeHOVHJGhe3AT1uii5yiv9Dy5aHcqVEgcnT8LDpT//NC/QX7lS6t1batu2/PfUl62YXnzxRX3//fdKSUnRa6+9platWlX/m0tXI/TsKbthqF07aU/GH+riXSuHr0heu1Ph3n1qUbDNXLmxfZFyotPU65bUerVKo6z9C240btxRbFdWVGQu8XA4zP3TKktU2O3m7Q7HgeUr0dFH/RwAAGgIGlSh8A8++EBdunRRWFiY+vTpo59//tnqkAAAAAAAFiqdSH7zTen11yXb2al66Zjp+s+xo1TsiJQ94FWxI1Iru43S9LZPame31MPWNiidby+tj2Dze+UqzpPN763VotUHJ08SE80L9B0Os4SC1yv99JNZQuFgZbdiqguBQEA7duwo19aiRQutWLFCH374Yc0SGqWrEcpsq9QhJlfd3WsVKPaq0BmnYkeUih3RcvoKtbN5DwXy8jV69wxdeEw9q5JeAYfD3CHqiJIvERHmi+v1Hkhc7OeXVOI3vwYTHl6vuWqjPi5fAQAgRBpMUmPjxo267rrr9Pjjj2vHjh3q3r27xo0bZ3VYAAAAAIB6wOGQ+vaV7rpLyk9I1uzAeD1yzJuanjJPjxzzpmYHxmtfm+TD1jYoO9+ekJep1IzZuv7zkRr7xShd//lIpWbMVnz+xuBKCa/3yGM+OHkiSWFh5j+/X2rWzExabN9+6Pe63XU3l52bm6urr75affv21c6dO8vdlpiYKKOi7ZGqUroaweUKNjXL3a44V5HcjmZyFxvy+iRvwC6/t0S5OSXKjklWj5bZ6vjrUWaS6juHwyzckptrLgvyeOTxBLRnr/THDumPP8yv+3I8Kopra/YbMKB+FRkBACDEGkxS4+eff9bjjz+uK6+8Uq1bt9b48eO1evVqq8MCAAAAANQHXq+Ul6fUU72aPl0aNUpyRjuUpxg5ox0aNUp68snD18EonW8/IX+ZLls5UadsSFe4r1A+W7jC9xexvnzFXeqbv/yoVkpUsFhBkln7ef9ctgzDTFz88YeZ5CgVCJh1outiLnvlypXq06ePFi5cqOzsbI0ePVqBQODo7rR0NYLbbR77/dIffyg82qmENoZiYyWbIdn8JQrY7erSPUz9TzUU26kWMkkNwcCBUny85PeryIhQ/h/5yt0bkH//sLuK85TrcWnLJr922RJ02KVHAAA0Mg2mpsaQIUPKHf/666/q1q1bje7D7XMr3Bd+SLvNsCncHl6uX2WOpm+J4ZFU2S9/huwB5xH19RvFCshfSV/JHnBZ3tcWcMqQsb+vVwGV1H5f+RQwfLXUN1zG/pxf7fZ1yJD9sH0Nwy9/mecdUIn8RuW/uBuBMNn2f5xr1tcvv1Ecgr4B+Q1P7fSVXbaAIwR9bbIFyn4+K/8s113fIz9HuH1lexpyhh3oW1xSLH+g8s+nK8xleV+n3Rm8ws9b4lVJoPLPfY36KhD83Pvkl8+o/A9wZ8BW7b7hAZtsIejrCNhkP4K+JQrIa1Q+vmEBQ2H7z1M16etXQMUh6BtQQJ5a6muXIUegdvr6DUNu+eQ2ShQm87Ur5Taq+Fkko076eoySKs4QkjNgP6K+xYZf/kp7S6560Lfs59Nr+FX5szvyvkd0jqjk98Bwe7hshvna+fw++fyV/25Qk74Om0N2mz3YF8B+B6psB6/+Tx4wQOMHDdK4cck1rm0QESF19GXqgl9nymUv0B9x5Ytq7I1sp/j8jbrw1xnae0KiIiKOrKhGBYsVghITpW3bzG2nwsMlw+dVWGGRFBmhEsOhjRulhBDPZZeUlOjvf/+7pkyZopL9WyA1b95cN910U81XZhysdDVCerqZwfH5gtspOR2Ss7nUPFbSHo/UvYPa9Nz/89BbZs+txrwyoWtXKS1NBY/M0K59eWrm36V4I1slvjDZ/D75bQ55IuK0295a8wITdL2SdZSlXQAAaFAaTFKjrOLiYk2fPl1paWkV3u7xeOTxHJjMzNu/Aek1/7xGjshDf/E5qe1JmnLWlODxyHdHylNS8WTo8fHHa+q5U4PHYz8YqzxPBRucSurWopumD5ouyfwdeF3H8SoOy6qwr6s4Sb23zQoer0+6U+7wbRX2DfclqM/vLwePf2k/SfucGyrsG1YSoxO2pAePf2s3RfkR6yrsaws4ddKmt4PHmW2nKjdyVYV9JenkjR8G/39T6+nKif6m0r4nbnpLtv1JkN8TntOuZp9X2rfv5nly+GMlSdtavais2E8q7Zvy+8ty+hIkSTtaztWfzd+vtO/xW59TpLeDJGln3EL90WJhpX2P2z5d0R4zaZbV/H1tazm30r7H7nhMMe5ekqRdMZ/o9/g5lfbtvvMBNS/sJ0na02ypNic8XXFHQ3L6x8kwzGTenqhvtLHN/1V6v52z7lB8/rmSpNzIVfqt7SOV9u2YfZNa5w2WJOW71uqXxPsq7Zu0+1q13XupJGmfc4PWt7+r0r7t9gxX+5yrJUlFjq1a1+HWSvu22XuJOuweK0kqDsvSmo6VbyWXkHuBOu0aL0ny2XK1uvM1lfZtlX+uumTdIUnyG2792OXKSvvGFZyubv+bFDz+MfnySvvGFp6kY3YeOEf8t8vIShMmzYqOV48/Dpwjfuo0Vj57xeeIKE839dw+PXh8NOeIy9860DchKkEvX3TgHDFpySRt2FPxOSLGGaP0YQfOEVO+mKJ12RWfI5x2p96+4sA5YuryqVq1s/JzxIfDD5wjpn87Xd9sq/wc8dblbwWTIM99/5w+31z5OWLeJfMU6zLPES/+8KI+yaz8HPGyw6sEn5ngmdtyh95v/melfZ/berw6eCMlSQvjdmphiz8q7Tt9+3Hq5jELMb7fPEtzW1Z8vpakx3Ycq17uGEnSJzG7NCf+90r7PrCzu/oVNpckLW22R08nbK6071//TNYZ+1pKkr6J2qP/a1P5/tJ3ZHXWufnxkqRVkbl6pO1vlfa9KbujBue1liStdeXrvsRfKu177e4kXbrXrFy6wblPd7VfX2nf4Xva6eqc9pKkrY4i3dqh4veZJF2yt43G7jbP11lhxRrXcU2lfS/ITdD4XZ0kSbk2n67pXPkqznPzW+mOrC6SJLfh15VdfizfwZA8rvVyOot0en6cJv3vwMUTlycf1LeMkwpjNWXnMcHjkV3+W2ly5fiiZpr6R4/g8dhOPynPXvHkdDdPlKZv7xk8Ht9xnbLCKk4uJxW7NGtb7+DxnUnrtS284gn3BF+4Xv69T/B4UvtftMG5r8K+MSVhSt9yQvB4SrvftC4iv8K+zoBNb286KXg8tW2mVkXmVthXkj7ceHLw/6e33qRvonMq7fvWphPl2p/geS7hd33ebFelfedt7qtYv/n73outtumT2IrPq5L08u8pR3eOeKvinx3TB05Xt5bm++f9n9/X3DVzK73fxwY8pl6tzd8jPtnwieb8UPnvEQ+kPqB+7czfI5ZvWV5pP6BJObjKtsslFRaak+WLFsmRlibH4ZZmHMThkC6PXaKofdnKTjyoSrgkGYaymyWr+Y4MXR67WA7H+CMKvXSxQmHhobfFxpoFwnN/yNSJfy7R2YGlavOFWx65tDJygOzJgzRiQvJRFymvzI4dO3TNNddo6dKlwbbTTz9db775pjp27Fg7DzJwoLRokbRpk9Spk1kfwnfgZ6ItP0+KipCS2h/4HrdbioxsGvUjUlP1wZmJ+mPTYv2l+F9qvm+7oopzlBfZWjlRSVrbcYjWJw7Ssm3JSlps1pUBAKCpaJBJjb/97W+KioqqtKbGtGnT9NBDDx3S7i32KmA/9Aq8ffv2KSvrwB+8Ho9HxSUVTxrsKyzf1+12q7i44r6FhYXBvklJ0oYot2y2ivtGhxcpKenA/W6KLpLsFfeN8LvL9d0aXaiSSvqGB8r33R61T95KJkTsAZXr+2fkPnkclV+ZX7ZvdmSBiqro2759lsJkTljuichXQXjlfRPbZ8sZMCeMcyMKlFdF33btdily/0ta4MrXXmfVfZv5zRiKnHna46q8b9u2u9W8xJw0LXbmKruKvm3a7FHLEnMsSsJz9b+IyvsmtM5Ra5/Z13Ds1c5K+wbUPLxAEe2zJNkU5sjRjqruN36v2jc37zc8LEdbq+jbKj5XSfsneHbb92hLFX1btspVUjOz7177bm2qqm/LPCVFm33zbbuUWUXfFrZ8JUWafQuNXfq1ir5x9gIlRZh9PUaefq6ib3N7vpKcZl+f3Mqoqm9YgZLCD7yHf6qib2zYPiWVSTZkRHhUUsmqlWaOfUqyH+j7S5RbxZX1DS8s9zk6mnNEcZkci9vmLneeKiwsVLGn4vt1B8r33Ve4r9K+sqt8331V9FX5vgUFBYftW5rUyM/Pr7Jvdna2PE7zCRfkV32/u9olSgFzEjLfVaBi594q+raTy99MkpTnLFKxa0+lfXe3bavYkuaSpFxnsYpd2ZX23dOmjbJKzORDbniJiiP+V2nfnNYJyvKZCYW9DkPFETur6NtaWV4zoZDjCFNxxI5K++6NT1BWc3NCICcsXMURWyvtmxvfSlmxSWbs9t0qjthSed9WLZXVzOy7275XxRGbKu2b17KlsqLNvrts+SqOyKy0b76thbIi9/c1ClUc8WulfQvsccqKMPvmGR4VR/xc+f3amyvLafZ1y6fiiIxytwck+cLDJQVUYG+urPCk4G3FET9Ver/7wmKVFXagryciQ8WVrMDY52imLPuBvu6oXyo9RxSGN1NWUtm+G1Rsq3jn0KLw6HJ9i6I3qdheYVe5/RHl+hZGb1WxveJ43YHwcn33RW1XcVglqwED9vJ9I/9UsaPyFXNl+xZEZqvYUfn+LVnt28u1/9fW/Ig9Kg4vqLRvdvtEefavbCuIyFVxeMWJZcn83B/VOaKS88/uPbsVu//3iNzc3CrPU3v27FGWYZ4vc/dW3TdnT46ywg70BZq8g6tsl00+tGsnbdwozZhhLnuoyey/16sTcpfql8g45eUbiok5tEtevqGIqDidmLdU8o47olUDBy9WODh3khpYprOMmTIC2WbhbMOlSBVqVCBdzQKLFK00STVL2FTHv/71L1133XXavXu3JMlms+n+++/XAw88oLCwWpxC2L8aQTNmSD//LEVFmftsSVJxsZm46NVLwRegdM+twYMb9yqN/bxe6YN1ySrsMV6b2o5TuK9IPluYwvw+FYdFyG8zxyCuwFykNG6cmRcCAKApMAJHvRlm3friiy90ySWXaOXKlTruuOMq7FPRSo2kpCTtzN6pmAp+I62L7acuuYTtpw5s/eRVoKrtNo64b+PafqpD+73asa2NAgFb095+KmCXTdXcUqomfRvZ9lNvv122J9tPBftefhXbT6mRbz8VMOSopb5+w1B2+/aK375dYQG2nypV77afkl8l1fws16TvEZ0jyp58y6iL7af27N2j+Jbxys3NrfD326YoLy9PsbGxjXZM/H6/srKylJCQIFslCc4mZ/ZsMyNwcEKjVCAgZWSYxTUOcxl7ufEtKJBGjVJ2Xrh+3NJSRfsLctvt5g5JHo85335Ch92Kb+6V5s1ThZmPasjMlCZONPMyZYuFx+dm6rKVE+XPK9CumC46+RRDUZGSPUyyGwEzYdOsmTR9es0SNodx3333aerUA6uOExMT9eabb+qss8466vuu9D28caO0eLH0r39Jq1eb9TW6dTOvDCyb0Ni40Tx+8slafc71VV6e+dYND5datqy83+7dZgJk3jwpOprzRChxHg49xjj0GOPQqs3xbey/2x6tBrVSY/PmzRoxYoRmzZpVaUJDkpxOp5xO5yHtkeGRigyPPOzjVKdPTfsGApItUPUS2bJ/RtekrxFwqaodTetD37L9DTlVxZzBUfQNlxE4tGZKQ+xrGH4ZgQIzoRGwSbIFa0Uc7n5r3rfq08CR9ZVsgao/G/StqO+RnyMiq3jbuWwVbNRcj/s6bYeev4+4byD4H4XLUHigemeqhtbXJgVrRdR237AQ9JWkyHrY128YigjYFem3yRYI1Lt4I0LU1xUwpGr+xLeu74H+ThlSNT8bNel7RJ/PavweGG4LV7iq/t3giPqGVa8f0GhVVmW7LMMwby+9jL0mRTVcLsX7C9X/VGn7dnMBQUmJFBYmdeggtW8vxea7JefRbYVUdrFCRsaBHbR6/7pE9t3ZymrZU717G4prXu6JmZP6GRlmMuDghI3XqxoXEtmvd+8DWxlecsklevnll9Wyqhn12pCcbD6HceOkzz6TXnhB2rVLys83n4vbba7QSEiQJkxoEgkNqertycpqSjtyAQBQqsEkNYqKijRkyBANHTpUw4YNU0GBue1AVFTU0RcpAwAAAAA0HFVV2S7LdQSFpcvsCxXbrp1ijzN07LFSiW//SgmbzKvWttbOVkipqeYOWYsXm/kXX5FXffcuVUynOHU83lBsRRdnVpSwqaBgugYMMKuJVzMRcNVVV+nrr7/Wcccdp5tvvrlu/9Z2OKQLLpC6dz8wGB6POWM/eHCNnkdjcLjtyaRDd+TyV75gFQCARqXBJDUWL16s9evXa/369XrppZeC7Zs3b1anTp2sCwwAAAAAULdCfRl72SLWXbrIbjMU3IW4dCukhARzor0WlF2sUPS/IkWPd8vmdElV7TZRNmHz7bdVFkxXWpqZPSlj7969WrhwoW6++eZy7c8++2ytPKcjVm4wjmzFSWNx0NuwXGIjBG9DAAAajAazedrQoUMVCAQO+UdCAwAAAACamNLL2HNyzNndipRexj5gQM0nxUv3hYqONrd52rHDLF6wY4d5HBMTkq2QHA4ppnWEbBEuMyFTFff+Yh87dpQvmJ6YaBZhSEw0j/Pzzf2tNm4Mfuu3336rPn36aPz48Zo/f36tPoda43CY49xEExqSZW9DAADqvQaT1AAAAAAAIGjgQCk+3ryM/eDERm1cxp6aahbiHjXKXO3h9ZpfR40yi1UftPKh1tQ0YfPll+YKjYMv5ZfM4+Rk8/bFi1VSUqJHH31Uqamp+v333yVJ9957r7xeb2ieC45aTd+GPp9ZZJyXFADQmDWY7acAAAAAAEfmKGpH11+VVdmuzcLSVm2FVN19hwYMkKZMKVcwvcR/UP2P/fU3fv/XvzUqfaG+/nZ58K7OPPNMpaeny9Fo3hSNU3XehqUlVX77zXzbOJ01LqkCAECDQVIDAAAAAI6Ct9Croj1FimgRIUdk/ZocroXa0fXbwVW2Q1VY2uEISTKj0mRTdRM27doFX9jcXGn7DumPPw4kNdq1k5rHSv9av0f3bFml3IB5+b5h2HTbbVM0ffp9CgtjWqChqOxtuGyZuQPZrl1S795SePhhS6oAANCg8dsLAAAAAByB3z/P1JaXl8jx9VLZvW6VOFzynjFAnW4YpI7nWJ8xKJ3orEHt6IapARaWrlayqToJG69Xcrm0a1uhftgpFe0vs2G3m9sQrV7n0/z85fqkJDP42Am2CF3WZrw860fp22/DGsd7oAnLzCxfUqVlS8nvNxf0tGtnLuqZMcN8KzWKRCYAACKpAQAAAAA19t+nl8n9+EzFFWTLHRknn8OlsOJCRX+Urp1fLtLee9OUcpt1s8UHT3SW3b2o0U50hmg1RW2rUbLpcAkbh0NZPQdo92fp8rraqUXcgRe6uFh6J3+pPinZHGwbENlODzXvqba+lfrzvxv18b1pSnw9tfG8B5qgJUvM99LBn3PpQEmVjAwzNzZ+vDUxAgBQ2ygUDgAAAAA18PvnmXI/PlMOT4H2JvaUu0WifM1ayt0iUXsTeyrck6+iqTP0+xcbLYuxdKKztByDze+VqzhPNr/34NrRqEMHJ5sSE80r6xMTzeP8fDPZtPHgt47DIcXEVJi0WaKBylK8ko2DCqbn5eqBkmIlyJBThm6POkl/SxqsQExn/RHXUy3D83X++hla+aZ171McHa/XXMRTpqTKIfaXVNHSpRQPBwA0HiQ1AAAAAKAGtry8RJEF2dqX0KXCS6MLEpIVWZCtLS9ZkzEoO9GZkJep1IzZuv7zkRr7xShd//lIpWbMVnz+RiY6LXBwsqmsI0k2eb3SBxld9fExaXI7otV2zzo137dDke7dap+boWOUq1n2BD0bfpb620448KCGoexmyYoPZKvwg8W8BxqooqID25eVMvy+YAKzlMtl7l5WVGRBkAAAhADbTwEAAABANXkLvXJ8vVTuyKovjXZHxsnx9VJ5C8fVefHw0onOE/KXadi6mWpWlK1CZ5y8dpfCfYU6ZUO6jtu+SLaOafrJnqqiogaxa1ODV9Or6seNO/zrUvpab0xM1YzIHVr5n/s0OzJeLX0exZTs1fawTnJFHK+oQIwCASngl4zSSxsNQ4WuOPX4c6mK8sbJ0ZI3QUMTEXFg+7L43Ez12LFEHZr/pmbrNslnOPVruwFanzRIO9zJiow0+wMA0BiQ1AAAAACAairaUyS71y2fw1VlP1+YS3avR0V7iuo8qRERIXX0ZeqCX2fKZS/QH3HlN9vfG9lO8fkbdeGvM7T3hERFRFBQoaa83prXJK/oqvqKlL2q/nD3HREhhYf79NNPjyoz8xEFAn7dENdTl53+vC74cLzccmmfPUYBr2SzlUlo7OeWS055FKEiSSQ1GhqHwywwv27WMl1WMFPRnl3a3be3fLZwhXsPJDBzotPU65ZUkpcAgEaDpAYAAAAAVFNEiwiV7C8K7quiX5jPLV94pCJaVP/S6COZKK+IwyFdHrtEUfuylZ1YcfXg7GbJar4jQ5fHLpbDQfXg6srMNLeQWrr0QIJiwABp0KDDF1wve1V9VdxuVfuq+p07t+q//x2pjRu/DrZ5vHnKcUbKiIqUI69QCkglJVJ0M+mQBSJut+K6RMoRwyX8DdWF3TPVd/dM+T0F+qNNT3ldLeVw+VUYHtDeyHaKztqo0Z4ZantMoiQSmACAxoGaGgAAAABQTY5Ih7xnDJCrMKd8UeayAgG5CnPkPWNAtVZpZGZKs2dLI0dKo0aZX2fPrqBYdHV5vTohd6k8kXHKy694n6O8fEOeqDidmEdRjepatkyaOFFKTzcTE+Hh5tf0dOmuu6Tly6v+/tKr6nPKvHXKFnCXzPacHLPf4RJb7777rlJSUoIJDcOw68QTH9HgwV8ozNVSmzoMUAvlqKgwoLAwKSqq/Pfn5QbU0shR7CXVeDDUWx1/W6IeLbO1q1kX5ew15PFIbo+0r1Das9dQdkyyerTMVsdfranxAwBAKLBSAwAAAABqoNO4gdr55SJFZW06tFh4IKDorI0qbJagTjcMOux9LVsmzZxpFoeOiztwJX96urRokZSWJqWm1jDAoiI1C3OrUw+X9myR9uRITqdkt5tX7Hs85iqATh1cig6r5j5HTVxmpvk6FRRIPQ9a/NKunZmAmjFDSkysesXGwIHm6+pel6lBtiU69o+lcpS45bW79Eu7Afq0ZJAS2iRrUBVvncLCQqWlpWnOnDnBttatO6hbt/ny+0/Xzp3m++i7mIHq7FykLu5N+jOsi7xeQ37//veAO6DO/o2K75mgVlcf/n2Kemp/oZbYTnHq38zQth3SNpvk90thYVKHDlL79oZi82tQqAUAgAaApAYAAAAA1EDHc7tq771pKpo6Q813ZMgdGSdfmEthPrdchTkqbJagiMkT1PGcqrd6qclEeefONQhw/z5H8f5C9T9V2r5d+uMPczL7wESnFJvvlpxUD66OJUvMxNPBr5NkHicnSxkZ0uLF0vgqdvPq2lV66JxlKpo2U5EF2XJHxmmfw6Uwd6F6r0lX1+hFirg6TcnJFWey1q5dq+HDh2v9+vXBtiuuuEJz5szRnj1xWrzYnLv2eKTc+K7KH5em076foW5/ZOgPd5zcXpdccqtdVI6aJSco+oEJh983C/VXmUItsTFSs1ipRUupRbzksEv20r05vDUo1AIAQANAUgMAAAAAaijltlT93jNRW15aLMfXS2X3euQLj1TOOYPV6YZBh01oSDWbKL/pphoEV7rPUXq6Ytu1U+xxho49VirxSfaw/ROdgYC0NUcaPJhJzsPYfzG84uIOfZ1KGYZ5+2Evhs/MVMoXM1XYpUCbjZ7K3WmopESyO6Wobu3U279RkZ/PkC6seMnHqlWrggmNiIgIPfPMM7r++utlGIbi4syEyrhxZWuzpEobE6XFi5Xw+VL5Cz2yRUbKfu7g6hUCQf1WQaEWm83cGs1Wdne8mhRqAQCgASCpAQAAAABHoOM5yep4znh5C8epaE+RIlpEVKuGhlTzifKxY2sYXOk+R5s2SV26yG4zZA/ff1sgYC4DSUhQlfscQVK5i+ElmVv7+HzmqhdbmSqVrupcDL8/kxXZs6d6GoaO7VE22WRIgaqXfFx77bVavHix1q9fr4ULF6pHjx6H9HE4Dnr85GRp/HjZx42TvTYq0aP+KJPAVLt2FZ9MSgu1kMAEADQiJDUAAAAA4Cg4Ih3VTmaUOniiXDKLRof7ilQcFiG/zby/shPlNdK1q1mQY8YMc5K8tGCH221OcCYkSBPYeqg6Si+Gz86W/vc/KWuHV2HeIvkcEUpIdKh9eykmphoXw1eQybLbdCDZJB2y5CPz99/VtWvXMjcbevHFF+VwOOQq++apjkOyHWgUyiYwD/48k8AEADRSJDUAAAAAoI6V3TUmPjdTPbYv0TFlikb/2m6A1icN0g53cnCi3O2u4YOkppoFOcoWWoiMNK/YZuuhanM4zGHcuChT5/qXKLVkqVyGW+6AS8t3DdDKTYNUcELy4S+GryiTVRGXS76iIj10332aOn26/vnPf2ro0KHBm5s1a1Z7Tw4N38EJTLtd2r3bfL+RwAQANFIkNQAAAACgjpXuGrNu1jJdVjBTzYqyVeiMk9fuUrivUKdsSNdx2xcpJzpNvW5JPfIL7PdvPXRQoYVafS6NXWamZCxfpkfcMxUfyFZBeJw8Npei/IUa7k3XgN2LNPurNEX2S636YvgK6h9UZMuePRr500/69qOPJEljx47VunXr1LZt21p8VmhUyiYwf/vNXBVEAhMA0IiR1AAAAAAAC1zYPVN9d8+U31OgPxLKVwvfG9lO0VkbNdozQ22PSZTU+egejK2Hjth3b2Zq6OaZim1WoPXunvKVGAoL+BVm+LTTaKP2JZt1U+EM/TcpUclVTR5Xo/7B2xs36obly5VbUiJJstvtuvvuu5WQkBCqp4fGIjlZuukm6Y8/pOhoKSqKzzwAoNEiqQEAAAAAFuj42xI1b5mtlfk9VbTXkNNp7hxTUiJ5PIaKYpLVPzpDsb8ulv/sm6wOt0nyeqXCD5coPpCtXa16qmNRnqJytqtF0R+yBUrkN+zaE9FObQJ79ceaxfJ6x1c9j3xQAffSxMY+r1d3fvutXv7112DXzp07a/78+erfv3+InyUalbAws8hL2Sr2AAA0MvyUAwAAAIC6tr9odGynOPU/zVC3buZcpN9vfu3WTep/qqHYTvuLRnu9VkfcJBXledVj51IVuuIUW7RTx+xZqc7eDYp0+hQeYVOk06fO3g2K9+7QKb+/paK8w7xOpfUPoqPN+gc7dmjNxo066a23yiU0hg8frtWrV5PQAAAAqAArNQAAAACgrpUpGh0bI8UeJx17rFTik+xhkr308jOvyyzwXVRkabhNVYSK5JRb8vmUWPCL7H6vCp1xwRUWJZK8YZFyFe5R0r5fFLE5Q2rZp+o7LVP/4J/z5mnEd9+p2O+XJEVFRuq5WbM0ZswYGRVsTwUAAABWagAAAABA3SstGu12B5vsNik8vExCQzJvdzrN/qhzjpgIxbVxqXnBDjl8RXI7mh1aC8Mw5JZLLptXjpVfV++O9xdw75eerqiYGElS37599ePq1br22mtJaAAAAFSBpAYAAAAA1LXSotE5OVIgUHGfQMC8fcAACv5axeFQzJBUJZTslDvgrLC4d3Gx5DKKZWvfVlq+vEZbhSV16aJXXn1VEyZM0IoVK9S9e/fajB4AAKBRIqkBAAAAAFYYOFCKjzeLRh+c2AgEpI0bpYQEadAga+KDJClh2OlyNnPIGXDL7Za8PslXYn51u6Vof55cLSLk7JxY5VZhXq9Xjz/+uHJzc8u1Dxs2TDNmzJDT6ayLpwMAANDgkdQAAAAAACtUUDRau3ebXzMypJgYacIEc6siWOf44+XsfayimxlKcOQowl8oR4lHEf5CJThyFNMyXBH9epkV3ivZKmzz5s0688wzNXnyZN18880KVLY6BwAAAIdFUgMAAAAArJKaKk2fLo0aJUVGmlsXRUaax08+ad4Oazkc0pVXKqxje0X17qqWrcMU39Kvlq3DFJXSTY4z+0tt2lS6VdiCBQvUp08ffffdd5Kkd955RxkZGVY8EwAAgEYhzOoAAAAAAKBJ2180WuPGmVsXRURQQ6O+GThQWrRIKiiQ7ZwBUkmJuTLDZqt0q7CCggLdfvvteu2114JtXbp00YIFC3T88cdb8SwAAAAaBVZqAAAAAEB94HCYW06R0Kh/ym4V9vPPUna2uTKjkq3CfvzxR5144onlEhpXX321Vq9erZNPPtmqZwEAANAosFIDAAAAAIDDSU2VEhOlxYulpUvNouCRkdLgweYKjeRkBQIBPfXUU5o0aZK8Xq8kKSoqSs8//7yuueYaGYZh8ZMAAABo+EhqAAAAAABQHYfZKuyDDz5QWlpa8PjEE0/UggUL1K1bNyuiBQAAaJTYfgoAAAAAgJqoZKuwoUOH6qKLLpIkTZw4Ud9++y0JDQAAgFrGSg0AAAAAAI6A3++XzXbgWkHDMPTqq6/qxx9/1KAyRcMBAABQe1ipAQAAAABADW3cuFGnnXaaFi9eXK69VatWJDQAAABCiKQGAAAAAAA18Oabb6pv37767rvvNHr0aGVlZVkdEgAAQJNBUgMAAAAAgGrIz8/XmDFjNGrUKOXn50uSmjVrpl27dlkcGQAAQNNBUgMAAAAAgMNYtWqVTjjhBL3xxhvBtjFjxujHH3/UcccdZ2FkAAAATQtJDQAAAAAAKuH3+zV9+nSddtppyszMlGSuzkhPT9fcuXPVrFkziyMEAABoWsKsDgAAAAAAgProf//7n8aMGaNFixYF204++WTNnz9fycnJFkYGAADQdLFSAwAAAECd+OCDD9SlSxeFhYWpT58++vnnn4O33X777TIMI/iva9euFkYKmAoKCvTtt98GjydNmqSvv/6ahAYAAICFSGoAAAAACLmNGzfquuuu0+OPP64dO3aoe/fuGjduXPD2VatW6d///rdycnKUk5Oj1atXWxgtYEpOTtbs2bPVpk0bLVmyRI8//rgcDofVYQEAADRpbD8FAAAAIOR+/vlnPf7447ryyislSePHj9fgwYMlST6fTxkZGUpNTVV0dLSVYaKJ27hxo9q1a6eoqKhg28iRI3XRRRcpJibGwsgAAABQipUaAAAAAEJuyJAhuvHGG4PHv/76q7p16yZJWrt2rfx+v/r06aOIiAj95S9/0datW60KFU3U22+/rRNOOEF33nnnIbeR0AAAAKg/WKkBAAAAoNZccskl+vLLLw9pf/TRR3XrrbdKkoqLizV9+nSlpaVJktavX69jjjlGzz77rFq1aqUJEyboxhtv1Kefflrp43g8Hnk8nuBxXl6eJMnv98vv99fiM6of/H6/AoFAo3xuVsvLy9Mtt9yi+fPnS5JefvllDR48WBdffLHFkTUuvIdDjzEOLcY39Bjj0GOMQ6s2x5fXqGokNQAAAADUmjlz5qioqOiQ9hYtWgT//29/+5uioqKCNTVGjhypkSNHBm9//vnn1blzZ+Xl5VV6hfy0adP00EMPHdKenZ0tt9t9tE+j3vH7/crNzVUgEJDNxoL72vLf//5X48eP15YtW4JtI0aMUK9evZSVlWVdYI0Q7+HQY4xDi/ENPcY49Bjj0KrN8c3Pz6+lqBonkhoAAAAAak3r1q2rvP2LL77QrFmztHLlykoLLickJMjv92vnzp2VJjUmT54cXOkhmVfbJyUlKT4+vlFuFeT3+2UYhuLj45mEqAV+v1/Tp0/X/fffL5/PJ0lq1qyZZs+erREjRlgcXePEezj0GOPQYnxDjzEOPcY4tGpzfF0uVy1F1TiR1AAAAABQJzZv3qwRI0Zo1qxZOu6444Ltd999t/r27aurr75akrRixQrZbDYlJSVVel9Op1NOp/OQdpvN1mj/SDcMo1E/v7qyc+dOjRkzRkuWLAm29e/fX08//bROOukkxjeEeA+HHmMcWoxv6DHGoccYh1ZtjS+vT9VIagAAAAAIuaKiIg0ZMkRDhw7VsGHDVFBQIEmKiopSSkqK7r//frVu3VolJSW67bbbNHr0aEVGRlocNRqbzMxMnXbaacrOzpZkTjxMnjxZU6ZMUU5OjsXRAQAAoDpIagAAAAAIucWLF2v9+vVav369XnrppWD75s2bNWrUKGVkZOiyyy6T3W7XqFGjNHXqVAujRWPVpUsXpaSk6LPPPlPbtm2Vnp6uc845h2KcAAAADQhJDQAAAAAhN3ToUAUCgUpvnzZtmqZNm1aHEaEpstlseuONN3TPPfdo+vTpatWqldUhAQAAoIbYnAsAAAAA0OgEAgG99tpr+vrrr8u1t23bVq+//joJDQAAgAaKlRoAAAAAgEYlNzdXN998sxYuXKikpCStWbNGcXFxVocFAACAWsBKDQAAAABAo7Fy5Ur16dNHCxculCRt27ZNb7/9tsVRAQAAoLaQ1AAAAAAANHglJSWaOnWqzjjjDG3ZskWSFBsbq3/84x+68cYbrQ0OAAAAtYbtpwAAAAAADdqOHTt0zTXXaOnSpcG2U089VfPnz1enTp2sCwwAAAC1jpUaAAAAAIAG61//+pdSUlKCCQ3DMPTAAw9o+fLlJDQAAAAaIVZqAAAAAAAapKysLF111VUqKiqSJCUmJio9PV1nn322tYEBAAAgZFipAQAAAABokBISEvTUU09JkoYOHao1a9aQ0AAAAGjkWKkBAAAAAGgQAoGASkpKFBZ24E/ZG264Qe3bt9cFF1wgwzAsjA4AAAB1gZUaAAAAAIB6b+/evbrqqqt09913l2s3DEMXXnghCQ0AAIAmgpUaAAAAAIB67dtvv9WIESO0detWSdJ5552nwYMHWxwVAAAArMBKDQAAAABAvVRSUqJHHnlEqampwYRGXFyc/H6/xZEBAADAKqzUAAAAAADUO9u3b9eoUaO0bNmyYNuZZ56p9PR0dejQwcLIAAAAYCVWagAAAAAA6pX3339fKSkpwYSGzWbTgw8+qC+++IKEBgAAQBPHSg0AAAAAQL3g8XiUlpam559/PtiWlJSk+fPn64wzzrAwMgAAANQXrNQAAAAAANQLdrtdq1evDh5fdtllWrNmDQkNAAAABJHUAAAAAADUC2FhYZo/f77atm2rOXPm6O2331ZcXJzVYQEAAKAeaXBJjV27dqlz587asmWL1aEAAAAAAI7Cnj179PPPP5dr69SpkzZu3Kgbb7xRhmFYFBkAAADqqwaV1Ni1a5eGDBlCQgMAAAAAGrjly5crJSVFF198sfLz88vdFhERYVFUAAAAqO8aVFJj+PDhuvrqq60OAwAAAABwhHw+nx588EENGDBA27dvV2ZmpiZNmmR1WAAAAGggwqwOoCZeeuklde7cWXfccYfVoQAAAAAAamjr1q0aOXKkvv7662DbWWedpXvvvdfCqAAAANCQNKikRufOnavVz+PxyOPxBI/z8vIkSX6/X36/PySxHQ5bwaImDMMvwwjIMKx5v6LhsejUVv9x8kUN+A1DAcOQn/cNqsvCk69Vv9MCR+Pdd9/VuHHjtHfvXkmS3W7Xgw8+qMmTJ8tut1sbHAAAABqMBpXUqK5p06bpoYceOqQ9OztbbrfbgoikpCRLHhYNll+tWuUqEAioge0SB4tkZVkdQT3FyRc14JeU26qVAoEAZ15Uj4Un34PrDwD1WWFhodLS0jRnzpxgW4cOHTR//nydfvrpFkYGAACAhqhRJjUmT56stLS04HFeXp6SkpIUHx+vmJgYS2Lats2Sh0UDZa7UMLR9e7wCAabWcHgJCVZHUE9x8kUN+A1DhmEofvt22QIBq8NBQ2Dhydflcln22EBN+Hw+nXHGGVq9enWw7YorrtCcOXMUFxdnYWQAAABoqBplUsPpdMrpdB7SbrPZZLNZM0HM3AhqKhAwFAjYSGqgWiw6tdV/nHxRQ0YgINv+f8BhWXjytep3WqCmwsLCdM0112j16tWKiIjQM888o+uvv14GW/0BAADgCDXKpAYAAAAAoH644447tHXrVt14443q0aOH1eEAAACggeMSLwAAAABArfjyyy81Y8aMcm02m00zZ84koQEAAIBa0SBXagTYEgIAAAAA6g2fz6eHHnpIjz32mCSpb9++GjBggMVRAQAAoDFipQYAAAAA4Iht2bJFZ511lh599FEFAgEFAgG98sorVocFAACARoqkBgAAAADgiLz99tvq06ePvv32W0mS3W7XtGnT9Prrr1scGQAAABqrBrn9FAAAAADAOvv27dOdd96pl19+OdjWuXNnzZ8/X/3797cwMgAAADR2JDUAAAAAANW2Zs0aDR8+XL/88kuwbfjw4XrhhRcUGxtrYWQAAABoCth+CgAAAABQLYFAQOPHjw8mNKKiovTaa69p/vz5JDQAAABQJ0hqAAAAAACqxTAMzZ07V1FRUerbt69++OEHXXvttTIMw+rQAAAA0ESw/RQAAAAAoFIej0dOpzN43L17d/1/9u47vqbzjwP4597c3Oy9RBKSoLbEjr2qKKqorahVtalZrdFatao1qjVrq6pVe+8Vq2ZUCBKJJLLnXc/vj/PL4cqwEhE+79fred3cM5/z3OM693zP83wPHDgAf39/o+lERERERG8Ce2oQERERERFRJlqtFuPGjUO1atWQmppqNK969eoMaBARERFRvmBQg4iIiIiIiIzcvXsXdevWxdSpU/Hvv/9ixIgR+V0lIiIiIiIADGoQERERERHRUzZs2AB/f3+cPn0aAKBSqVC0aFEIIfK5ZkREREREzKlBREREREREAJKTkzF48GAsW7ZMnlasWDGsXbsW1apVy8eaERERERE9waAGERERERHRe+7ixYvo1KkTgoKC5GldunTBwoULYWtrm481IyIiIiIyxuGniIiIiIiI3mPz589HQECAHNCwtrbGypUrsXr1agY0iIiIiOitw54aRERERERE77HY2FhoNBoAQOXKlbFu3TqUKFEin2tFRERERJQ1BjWIiIiIiIjeY9988w0OHjyIKlWqYMqUKVCr1fldJSIiIiKibDGoQURERERE9J7QaDQ4ceIEGjRoIE8zMTHB3r17YWpqmo81IyIiIiJ6McypQURERERE9B4IDg5G7dq10bhxY5w+fdpoHgMaRERERFRQMKhBRERERET0jluzZg0qVqyIc+fOQa/Xo3v37tDr9fldLSIiIiKil8agBhERERER0TsqMTER3bt3R9euXZGYmAgAKF68ONasWQMTE5N8rh0RERER0ctjUIOIiIiIiOgdFBgYiEqVKmHlypXytG7duuHChQuoUqVKPtaMiIiIiOjVMahBRERERET0DjEYDJg9ezZq1qyJ27dvAwCsra2xatUq/PHHH7CxscnnGhIRERERvTpVfleAiIiIiIiIcs+QIUMwf/58+X3VqlWxbt06FCtWLB9rRURERESUO9hTg4iIiIiI6B3Sp08fmJmZAQBGjx6N48ePM6BBRERERO8M9tQgIiIiIiJ6h1SoUAGLFi2Ch4cHGjdunN/VISIiIiLKVeypQUREREREVED9999/6N27NzQajdH0Hj16MKBBRERERO8k9tQgIiIiIiIqYIQQWLVqFfr374/k5GQ4OjpixowZ+V0tIiIiIqI8x54aRERERET0RgwePBgKhUIuxYsXl+ddvXoVVatWhYODA0aOHAkhRD7W9O2WkJCAzz//HN27d0dycjIAYPv27UhJScnnmhERERER5T0GNYiIiIiI6I0IDAzEjh07EBsbi9jYWFy8eBEAkJ6ejpYtW6Jy5coIDAzE9evXsWLFivyt7Fvq7NmzqFixItasWSNP69mzJ86dOwdLS8t8rBkRERER0ZvBoAYREREREeU5nU6Ha9euoW7durC3t4e9vT1sbGwAALt27UJ8fDzmzJmDYsWKYerUqVi6dGk+1/jtYjAYsGDBAtSpUwd37twBANja2mLdunVYunQprK2t87mGRERERERvBoMaRERERESU565cuQKDwQB/f39YWFigadOmuH//PgDg8uXLCAgIkHsaVKhQAdevX8/P6r5VwsPD0bRpU0yePBk6nQ4AEBAQgEuXLqFjx475XDsiIiIiojeLicKJiIiIiCjXfPrppzh8+HCm6cOGDUPJkiUxb948ODs7Y9iwYejbty92796NhIQE+Pj4yMsqFAqYmJggNjYWDg4OWe4nPT0d6enp8vuEhAQAUo8Gg8GQuweVzxYtWoQDBw4AkNpmzJgxmDBhAkxNTd+5Y80vBoMBQgi2Zx5iG+c9tnHeYvvmPbZx3mMb563cbF9+RjljUIOIiIiIiHLNb7/9htTU1EzTHR0dMWHCBPn9woUL4ePjg4SEBKhUKpiZmRktb25ujpSUlGyDGtOmTcOkSZMyTY+KikJaWtprHsXbpVevXti2bRsePnyIefPmoW7duoiNjc3var1TDAYD4uPjIYSAUskBDfIC2zjvsY3zFts377GN8x7bOG/lZvsmJibmUq3eTQxqEBERERFRrnFzc3uh5VxdXWEwGBAeHg5HR0dcvXrVaH5iYiLUanW2648dOxbDhw+X3yckJMDLywsuLi6wtbV9tcq/JZKSkjLlyNi0aRNSUlJQqlQp3oTIAwaDAQqFAi4uLmzfPMI2znts47zF9s17bOO8xzbOW7nZvubm5rlUq3cTgxpERERERJTnRo4ciYoVK6Jz584AgFOnTkGpVMLLywtVq1bF4sWL5WXv3r2L9PR0ODo6Zrs9MzOzTL07AECpVBbYH+lCCKxYsQIjRozAgQMH4O/vL8/z9vZGZGRkgT6+t51CoWD75jG2cd5jG+cttm/eYxvnPbZx3sqt9uXnkzO2DhERERER5Tk/Pz98++23OHDgAPbu3Yt+/fqhW7dusLS0RN26dZGQkIDly5cDAKZOnYoPP/wQJiYm+VzrNyc+Ph6dO3dGz549ERMTg44dOyI5OTm/q0VERERE9NZhTw0iIiIiIspzXbt2xbVr19C2bVuYmJiga9eumDp1KgBApVJhyZIl6NSpE0aOHAmlUpllsvF31enTp9GpUyeEhITI0+rUqQOFQpF/lSIiIiIieksxqEFERERERG/EtGnTMG3atCznffLJJwgODsb58+cREBAAJyenN1y7N0+v1+PHH3/E+PHjodfrAQB2dnb4/fff0b59+3yuHRERERHR24lBDSIiIiIieisUKlQIzZs3z+9qvBFhYWH4/PPPcejQIXlajRo1sHbtWnh7e+dfxYiIiIiI3nLMqUFERERERPQG7du3D35+fnJAQ6FQ4LvvvsPRo0cZ0CAiIiIieg721CAiIiIiInqD7OzsEB8fDwDw8PDA6tWrUb9+/fytFBERERFRAcGgBhERERER0RtUrVo1TJ48GadOncLSpUvfi/whRERERES5hUENIiIiIiKiPCKEwNatW9GiRQuoVE9+fo0cORIKhQIKhSIfa0dEREREVPAwpwYREREREVEeiIuLQ4cOHdC6dWtMmTLFaJ5SqWRAg4iIiIjoFTCoQURERERElMtOnjwJPz8/bNy4EQDw/fff4/bt2/lcKyIiIiKigo9BDSIiIiIiolyi1+vxww8/oG7durh//z4AwMHBARs3bkTx4sXzuXZERERERAUfc2oQERERERHlgtDQUHTt2hVHjhyRp9WpUwerV69GkSJF8rFmRERERETvDvbUICIiIiIiek1btmyBn5+fHNBQKpWYOHEiDh48yIAGEREREVEuYk8NIiIiIiKi1/Dnn3+iQ4cO8nsvLy+sXbsWtWvXzsdaERERERG9m9hTg4iIiIiI6DW0bNkS5cuXBwC0bdsWly9fZkCDiIiIiCiPsKcGERERERHRa7CwsMD69etx/Phx9OnTBwqFIr+rRERERET0zmJQg4iIiIiI6DWVKVMGZcqUye9qEBERERG98zj8FBERERERERERERERFQgMahARERERERERERERUYHAoAYRERERERERERERERUIDGoQEREREREREREREVGBwKAGEREREREREREREREVCAxqEBERERERERERERFRgcCgBhERERERERERERERFQgMahARERERERERERERUYHAoAYRERERERERERERERUIDGoQEREREREREREREVGBwKAGEREREREREREREREVCAxqEBERERERERERERFRgcCgBhERERERERERERERFQgMahARERERERERERERUYHAoAYRERERERERERERERUIDGoQEREREREREREREVGBUKCCGlevXkXVqlXh4OCAkSNHQgiR31UiIiIiIiIiIiIiIqI3pMAENdLT09GyZUtUrlwZgYGBuH79OlasWJHf1SIiIiIiIiIiIiIiojekwAQ1du3ahfj4eMyZMwfFihXD1KlTsXTp0vyuFhERERERERERERERvSEFJqhx+fJlBAQEwNLSEgBQoUIFXL9+PZ9rRUREREREREREREREb4oqvyvwohISEuDj4yO/VygUMDExQWxsLBwcHIyWTU9PR3p6uvw+Pj4eABAXFweDwfBmKvwMnS5fdksFlEJhgFabAJ1ODSEKTOyR8lFcXH7X4C3FL196CQaFAglaLdQ6HZTM20UvIh+/fBMSEgCAOeaektEWGW3zrjEYDEhMTIS5uTmUSl4f5ja2b95jG+c9tnHeYvvmPbZx3mMb563cbF9e7+eswAQ1VCoVzMzMjKaZm5sjJSUlU1Bj2rRpmDRpUqZtFC1aNE/rSESUX575GiQiojfhLfjyTUxMhJ2dXX5X462QmJgIAPDy8srnmhARERER5Q5e72etwAQ1HB0dcfXqVaNpiYmJUKvVmZYdO3Yshg8fLr83GAyIiYmBk5MTFApFnteV6HUlJCTAy8sLDx48gK2tbX5Xh4jovcDvXipIhBBITExE4cKF87sqb43ChQvjwYMHsLGxeSev+fkdlbfYvnmPbZz32MZ5i+2b99jGeY9tnLdys315vZ+zAhPUqFq1KhYvXiy/v3v3LtLT0+Ho6JhpWTMzs0y9Ouzt7fO6ikS5ztbWlv/JEBG9YfzupYKCT2wZUyqV8PT0zO9q5Dl+R+Uttm/eYxvnPbZx3mL75j22cd5jG+et3GpfXu9nr8AMnla3bl0kJCRg+fLlAICpU6fiww8/hImJST7XjIiIiIiIiIiIiIiI3oQC01NDpVJhyZIl6NSpE0aOHAmlUonDhw/nd7WIiIiIiIiIiIiIiOgNKTBBDQD45JNPEBwcjPPnzyMgIABOTk75XSWiPGFmZoYJEyZkGkaNiIjyDr97iehtxu+ovMX2zXts47zHNs5bbN+8xzbOe2zjvMX2fXMUQgiR35UgIiIiIiIiIiIiIiJ6ngKTU4OIiIiIiIiIiIiIiN5vDGoQEREREREREREREVGBwKAGvbdiY2ORkJAgv4+OjsbNmzcBAGlpaThy5AieNzrb1atXcfTo0Tyt59M4WhwR0dvv9u3bqF+/PpKTk7Ocv2zZMmzfvt1oWuvWrbFmzRoAQPHixXH8+PE8rycREQFbt26Fr68vVCoV/P39cePGDXne4MGDoVAo5FK8ePF8rCmRsezOXZ63VFCsWLHC6FzNKCtWrMAnn3xiNO3DDz/M7+oSyaKjo+Hj44OQkBB5Gq8n3jwGNei9NWbMGCxdulR+v2fPHnTv3h0AcOzYMfTv3x9paWk5buPgwYPo06fPc/fl7++PPXv2AACOHj0KlUoFe3t7udjZ2cHV1TXb9c+ePYvu3bujevXqSExMxKhRo9CxY8cXOUzZypUrMXjw4CznGQwGjBw5Enfv3jWa7uLigpCQEISGhkKlUr3U/oiIcrJ3717Y2NggPj4eALB79244ODggISEBDx48QIMGDWBlZYWGDRsiNDQUgPEPHzMzM1StWhXnzp3Ll/pfuHABvXv3xoABAzBw4EAMHDgQJ0+eBABs3rwZjo6OsLKyynJdOzs7DBo0CFqtFunp6RBCQAgBtVoNQEouZ2ZmBp1OB51O98aOiYjeTjn9EL569SqqVq0KBwcHjBw5kg/AvKTg4GB88cUXmD59OsLCwvDBBx+gd+/e8vzAwEDs2LEDsbGxiI2NxcWLF/OxtgUbz9XcldO5y/M292T3/cvzOXd07txZPk9jY2Px4MEDODs7o06dOggMDMSVK1fkeVu3bs3v6hYoWd10z+m8PXLkCEqXLg1nZ2fMmTMnH2pccERHR6NFixZGbcvrifzBoAa9t9RqNezs7AAAOp0Obdu2xUcffYT09HSsXbsWM2bMgIWFhbz8qVOn4OTkBG9vb7nMnDkTjx49kt97enrC3t4+U+8Nc3NzmJmZAQBMTU1RpUoVxMXFySUoKAjm5uby8gaDAatWrUK/fv3g6+uL6tWr4+HDhxg1apS8jYztPW3t2rXo27evfINt4MCBiIqKAgBs2LAh22iwUqlEWloavv32WwghkJ6eDgDQarWwsrKSb7ABgEajgcFgeKU2JyLK8NFHH6FUqVJYtmwZAODnn3/GoEGDYGtri86dO8PHxwdXrlyBr68vevXqJa9Xrlw5xMbGIjQ0FM2bN8dnn32W53VVKBRGF60A4ObmhmbNmqFp06Zo2rQpbt68iVu3bsFgMOD333/HgAED5GUNBoP8vQoAbdq0Qb169RAZGQk/Pz+YmZlhx44d6NWrF5ydnREUFCQHdRYsWJDnx0dEb7fsfginp6ejZcuWqFy5MgIDA3H9+nWsWLEifytbwNy4cQPTp09H+/bt4ebmhq+++kpuX51Oh2vXrqFu3bryg0g2Njb5XOOCiedq7svu3OV5m7uy+v7l+Zx71Gq10cOeK1euROvWrWFubg4hBMqVKyfPy+5hIcosq5vuOZ23UVFR+OSTT9CpUyecOnUKa9aswaFDh/Kn8gVAx44d0blzZ6NpvJ7IJ4LoPTV48GCxatUqIYQQixcvFh988IEoW7ascHJyEp6enqJs2bLC2tpa7Nq1SwghxOnTp0X58uXl9bdu3SoePHggv1+3bp24e/eu0T6CgoLE1q1bRcmSJcWUKVPEzp07xdmzZ4VKpRJOTk5ycXR0FEWLFjVa9+effxbffPONOHz4sChTpozYunWrPG/cuHGie/fumY7p4sWLYtOmTWL79u1i+/btwtXVVdy9e1fcuXNH2Nraivj4eHlZrVYrNBqN/D44OFj07t1bPHz4UAAQFhYWAoBwdHQUjo6OAoCwtrYWKpVKXLp06aXbm4joWZs3bxa+vr7i2rVrwsbGRkRHR4tr164JU1NTkZCQIIQQIiQkRAAQcXFxYvny5cLPz09ePywsTAAQkZGReVpPAJm+35/VvXt3sWbNGrF27VphYWEhihYtKooWLSrMzMyEq6ur6N27txBCiLS0NJGeni60Wq0QQsjfw/Xq1RP79+8XQgjh5+cnzp07l3cHREQFhlarFba2tiIxMTHTvM2bNwsHBweRnJwshBDi0qVLolatWm+6iu+UX3/9VVSoUEEIIcSFCxeEtbW1KFasmDA3NxdNmjQR9+7dy+caFkw8V/NexrnL8zb3ZPf9y/M5b6Smpsr3L/7++2/h4uIiPDw8hKWlpejQoYOIiYnJ7yoWGI0aNRI///yz0W+YnM7bn376SZQqVUoYDAYhhBBbtmwRXbp0yZe6FwR37twRQuT8G5HXE28Ge2rQe+fixYto1KgR1q5di9mzZ6NTp07o3bs3goKCcPXqVXTt2hUzZ87E1atXUatWLXk4EEB6Wjc9PR06nQ6TJ0/GrVu35Hlz5szBrVu3oNFo5OFCQkJCsG/fPsTHx+PcuXM4cuQIdDodKleujOjoaLlcu3YtU++HwYMHY8qUKahXrx4UCgVMTEyee2z+/v5o06YNWrRogRYtWsDCwgJqtRrTp0+HiYkJKlSoAG9vbygUCvj6+mLVqlUAgNTUVHh5eWHRokUoVKgQtFotkpOToVQq8ejRIwQHB8PKygqJiYnQarXw8/PLjY+CiN5zrVq1gqWlJdq3b48vv/wSTk5OOHv2LIoVKyY/veLp6YnRo0dDq9VmWn/jxo3w8PCAo6MjAGl4P39/fzg4OKBz586Ii4uTl81p3rp16+Dj4wMrKys0adIE0dHRAIBSpUpBoVAAAHx8fKBQKLB+/fpsjycuLg5jxozBxo0bERISgpCQEPj4+ODAgQNYvHgxAGDu3LkICAiAqakpYmJi5O/2xMREuLi4GG1PCMHhp4jec1euXIHBYIC/vz8sLCzQtGlT3L9/HwBw+fJlBAQEwNLSEgBQoUIFXL9+PT+r+9b69NNPjZ4Gzijz58+Xl9FoNJg9ezb69esHALh+/TpKliyJVatW4d9//4VKpULfvn3z6xAKNJ6reevpc5fnbe7J7vuX53PeWLt2LapXrw5vb2/cvHkTfn5+2LFjB06fPo27d+9i7Nix+V3FAmPx4sWZhh7P6by9fPkyGjRoIP/uqVatGs6fP/9mK12A+Pj45Dif1xNvUH5HVYjyg1arFY6OjnJPjQsXLojSpUtn6qlhZ2cndu7cKYQQ4tSpU6JixYpiyJAhomzZssLS0lIUK1ZM+Pn5CT8/P2FpaSl8fX1F6dKlxd69e432V716dXHo0CEhhBBnzpyRn+B9ulSqVEle/vHjxyIxMVEuvr6+YsuWLfL7UaNGiS5duojExEQRHx8voqOjszzOokWLis2bNwtnZ2e5V0lERIRwcnIyWu6zzz4TJUqUEBUqVBB6vV4YDAaRkJAgHBwchBBCxMbGCisrKyGEEDqdTuj1+tf8BIiIJMuWLRMKhUKEhYUJIYSYPn16tk+7LV++XCiVSmFnZycsLS2FmZmZOHz4sBBCiPv37wsrKyvx+++/i+DgYNGyZUvRqlWr585LSEgQpqamcm+7jz/+WIwZM0aeFxsbKwCIy5cvi9jYWKMebvXq1ZO/w62srET//v1FkyZNRMuWLYUQ0v81NjY2IjU11eg4UlNTBQAREhIiTE1NhbW1tVAoFMLW1lbY2dkJpVIpLCwshKmpqbwtInq3tWrVStjZ2WUqEydOFJUrVxYnT54Ut27dEs2bNxdNmjQRQggxfPhw0b9/f6PtODs782nWLERERIi7d+9mKk/3Yh4zZozw8/Mz+p5/2r1794RSqTRah14Mz9W8ldO5y/P21a1evTrL71+ez3mjatWqYseOHVnOO3LkSKZ7GPR8eKonQU7nbZs2bcSMGTPk6UlJScLW1vZNVrVAQjY9NXg98eYw8y+9lzZt2oS4uDhs2rQJKpUKZmZmqFy5MlatWoWhQ4ciICAgUyLutLQ0mJqaYu7cuQCk6PWQIUNQvXp1AEC7du0we/ZsNGzYMMt9xsTE4MiRI6hevTqCg4Mz9bzIGHPdzMwMJUuWhEajkSPlCQkJ6NKli5ysOy0tDQaDAdu3b5fXS0lJQXR0NAICAuRthoaG4sKFCyhbtiyWL1+O7777Drdu3ULp0qWN9r1x40bs3r0bU6ZMwbp169CrVy+oVCqkpqbC3t4eQggkJyfDysoK6enp+PHHH/H111+/+gdARPR/J0+ehBACp0+fRps2baDVaqFUSh1Jhw8fLufcOHbsGACgZMmS2LlzJ1JSUrBgwQL06tULly9fxurVq1GzZk306dMHAPDrr7/C09MTEREROc6zs7ODSqWCRqOBu7s7tm3bJvece3qsU1tbW9jb2xvV/fHjx/jrr79QpUoVeZrBYED58uVx5swZAFIOkKdzJj2taNGi0Gg0iIiIgK+vLx4/fgyVSgV/f38sWbLEaLtE9G777bffkJqammm6o6MjJkyYIL9fuHAhfHx8kJCQIF/DPs3c3BwpKSlwcHDI8zoXJG5ubjnOP3jwIBYsWIDTp0/D1NQ0y2VcXV1hMBgQHh4OW1vbvKjmO4vnat553rnL8/bVdenSBV26dJHfZ3z/li5dmudzLrt9+zZu376Nxo0bZznf1dUVjx8/lu+X0MvL6Xv42XkZ0+nl8XrizeLwU/Te0el0mDRpEpo1a4bixYtj4MCBCAoKws6dO1GuXDmsXr0aI0eORLly5VCuXDkMHz4cAJCcnAwnJyd5O23atMGpU6cwd+5czJ07F7Vq1cryB9O///6LyMhIDBgwAJcvX4anpyeKFi0Kb29vmJubw83NDd7e3ihatCgqVaoEQErUFB8fj7i4OERERECpVOLy5ctyYvERI0agc+fOiI+PR2JiIjQaDVQqFUxNTREdHS0Pe6LT6fD9999j5cqVWLRoEZKTk3Hy5EnUqVMny7YxMTFBly5dkJaWhqVLl6JBgwaIi4vDvXv3YGVlheTkZOh0OgY0iChXPHjwAOvWrcMPP/yAKVOmAADs7e3loaHGjRuHS5cuwWAwQK/XA5ASCnp7e6NMmTKYP38+IiMjsW/fPjx48AC+vr7ytj08PGBmZob79+/nOM/CwgLr16/H77//DldXV3zyySd48ODBC9U/q2EBlUolJk2ahLFjx2Lt2rVo167dc7ezd+9eVK1aVQ5cE9H7J+N68Nny7I/dp38IOzo6Iioqymh+YmKi0dCp9Hx3795Fp06dsGDBApQpU0aePnLkSKxdu1Z+f+rUKSiVSnh5eeVHNQs0nqt5I6tzl+dt3sn4/i1UqBDP51z2559/okWLFvJN4A4dOuD48ePy/FOnTsHNzY0BjdeQ0/fws/N4Pr8aXk+8eQxq0HtnxYoVKFmyJIoXLw4/Pz/s2rULY8aMwaNHj6BUKuHj4yPn1Jg+fbo8tnpERAQKFy6M7du3w8fHB4sWLcKxY8dw/PhxHD9+HPv378fgwYONxn2fPHkyxo8fD4PBgNWrV2Pw4ME4evQoRo8ejZCQEAQEBOCPP/5ASEgIHjx4gGvXrmWq7/bt2+Hm5oZixYo999iyy7tRpEgRtG7dGjNnzsS6dete6Cbbnj17UKtWrecuR0T0qmbMmIHWrVtj1KhRCA8Px86dO+Hn54egoCAkJibCyckJbm5uSEpKynYbQgjo9XoUKVIEd+7ckac/fPgQ6enpKFq0aI7zYmJi4ObmhuPHj+PRo0dwdnbG0KFDjfahUCgghMjxWG7fvo3g4GAAwGeffQa9Xo81a9bgiy++yHG9pKQkTJ06Fd27d5enGQyGTHmWiOj9lNMP4apVq+LUqVPyvLt37yI9PV3OM0TPl5qaihYtWqBVq1Zo3bo1kpKSkJSUBCEE/Pz88O233+LAgQPYu3cv+vXrh27dusnjkdOL47ma+7I7dytUqMDzNpdk9/1bvnx5ns+5bPfu3ahfv778vnz58hg2bBiOHz+OLVu2YOzYsfjqq6/yr4LvgJy+h5+dd/HiRXh4eORHNQssXk/kk/wc+4ooPyQkJIj//vtPDBkyRM6pIYQQQ4YMEW3bthUDBgwQ69atE0IIsX37dtG9e3chhDQu3oQJE+TlBw8eLMaOHSuEkHJgFCtWTBw7dizLfT6dU2Pp0qWicePGQghpPHZXV1dRuHBhUbhw4UzrxcfHi+LFi4t58+YZTR83bpxcr6c9nftCCCH27t0rdDqdEEKIqKgo4ezsLOrVq5dlHXft2iXPu3r1qrC0tBRBQUHy8VlYWGS5HhHRq4iIiBAWFhbi4sWLQgghpk6dKmrWrCn0er0oV66c6NSpk7h7964YM2aMACAuXrwoli9fLsqVKydiY2PF/fv3xZgxY4SpqakIDQ01yptx584d8cknn4hPP/1UCCFynHfjxg1hbm4udu3aJcLCwkS3bt1Es2bNjOpavHhxsWDBAhEaGiqOHDkiT/fz8xPHjx8X06ZNE66urnI+pVOnTglXV1dhZ2cn5/x42sGDBwUAsWrVKjFp0iRRpUoVodVqjfb39H6I6P21atUq4ePjI/bv3y/27NkjPvjgA9GjRw8hhJS3x8XFRSxbtkwIIUTv3r1FixYt8rO6Bc6WLVsEgEwlY4zsMWPGCDs7O+Ho6CgGDx4skpKS8rfCBRTP1dyX07nL8zZ3ZPf9y/M5d6WkpAi1Wi1u3LghT9NoNKJnz57CyspKFCpUSEyaNMnoWplezNP/n+V03kZFRQlzc3Oxb98+odFoRNOmTcXAgQPzq9oFxtPty+uJ/MGgBr23MoIaOp1ODB8+XPj6+oro6Gg5qLF7927RokULMWjQICGEENWqVZOThgshRHJysqhSpYr47rvvRNWqVcWCBQuy3dfTQY127dqJ2rVrC41GI+rVqyd27dqV5TpxcXGibt26okGDBpn+A39eUCMiIkL06NFD+Pj4iIcPHwohhFi0aJGwsrISJUqUkBPyPm38+PHCzc1NnDt3TjRo0EAMHTpUnhcaGiqUSmW2x0dE9LJGjhwpGjZsKL+PiYkRVlZW4sCBAyI4OFjUqlVLWFtbi7Zt2wovLy85qJFxgWhmZiYqVaoktm/fLm/j8OHDws/PT9jZ2YmOHTuK2NjYF5q3YMEC4e3tLczNzUX16tXF1atXjeq6b98+4evrK0xNTUXnzp2FEFKyb2dnZ2Fvby/atWsnQkNDRVRUlBg5cqRwcXER//zzj/jnn3+EnZ2d+PHHH+Xv8dWrVwtLS0vx8ccfCycnJ1G9enUxffp0cf78eREWFiaSkpLEnTt3RGhoqLhy5Uq2CROJ6P2R0w/hrVu3CktLS+Hk5CRcXFzEtWvX8rGmRNnjuUoFUXbfvzyfqSDAM4msczpvf/31V2FqaiocHByEj4+PiIiIyIcaE70chRDPGU+B6B01aNAg+Pv7Y/ny5YiPj8euXbvg6emJfv36oXbt2ihUqBB27tyJkSNHIjIyEvXq1cPDhw9haWkJIQQuXbqEhQsXYunSpXBzc8P48eNRo0YNeHp6wsnJSU7yDQD+/v6YO3cu7O3tUbt2bdSoUQMJCQmIjo7GhAkT0KpVK6SlpSE6OhplypTBmTNn0KdPH9ja2mL37t1GyWoBYOzYsQgLC8PKlSuNpgcGBiIgIAD29vbo378/xo4di5CQEIwdOxb//fcftmzZglWrVuH333/H4sWL0bJlSxgMBgwZMgRr165F9erVcfjwYdSvXx9dunSBv78/XF1dYWZmhkePHsHa2hqhoaHQ6/VGCcmJiN43SUlJaN26NQYOHIhWrVph2rRpmDJlClq2bIlZs2bJXbYDAwPRoUMHuLq64tixY/jpp59QunRptGjRAqmpqdiyZQt2796NCxcu4N69e0hMTDTaT9OmTbFr1678OEQiKiAiIiJw/vx5BAQEGOV/I3rb8FyldwnPZyqIcjpv7969i5s3b6JOnTqwtrbOpxoSvTgGNei91bt3bwQEBKBMmTLw8/ODlZUVAKBXr16oUaMGevfuLS+7c+dOBAYGolOnTujatSvCw8NRqlQptG/fHm3btsWlS5ewZcsWHD9+HFevXkWZMmVw/vx5eZz3pKQk7N+/H61atcLYsWMxaNAgLFmyBEuXLsWNGzfksfYqV66MwMBAbNiwASdPnsTMmTOzTND09ddf48GDB/jzzz+Npl+5cgWDBw/GvHnzULp0abRv3x579+7F0KFDMW7cOJibmwMAVq5ciQEDBqBfv36YNm0a+vfvj9GjR6NYsWKIiIjAxo0bcejQIVy+fBnh4eFITU012s/06dMxevTo3P5IiIgKrJCQECQnJ6Ns2bKZ5iUlJSE4OBh+fn7P3Y5Op4NGo4Fer4fBYIBarYaFhUVeVJmIiIiIiIioQGJQg+gl3bt3D0WKFDHqifE0nU6HxMREODg4AAAuXboEHx8f2NnZ4fTp02+0h8PZs2fh4+MDFxeXTPOCg4NhaWkJd3f3525Ho9FAq9VCr9dDCAFLS0uYmprmRZWJiIiIiIiIiIiIssWgBhERERERERERERERFQjK/K4AERERERERERERERHRi2BQg4iIiIiIiIiIiIiICgQGNYiIiIiIiIiI3mLp6ekwGAwvvHxYWBi6dOmC5OTkPKwVERFR/mBQg4iIiIiIiIjoLaHVajMFMD7//HNMnz7daJpOp4Ner89yG66urti/fz/Wr1+fZ/UkIiLKL0wUTkRERERERET0lhgxYgSOHTsGU1NTeVpgYCBKliwJGxsbeZpGo8G3336LTz75BH379sXevXuNtvPo0SOoVCo4OTkZTf/111/RrFkz9OjRA3/88QcAwMHBAR999BHmzZsHFxeX1z6GHj16wNvbGxMnTnytZXJzf0RE9O5Q5XcFiIiIiIiIiIhIMmvWLADAhg0bkPEc6oABA9CtWzd4eHgAAMzNzfHpp5/K68TFxaF379749ttvjbZ19+5duLi4wNraGgDg6ekJrVYrz+/Xrx+mTZuGkJAQ9OnTB0OGDMHatWtf+xgWLlwIpZKDgxARUd7g/zBERERERERERG+Z7t274+bNm4iOjsakSZNgZmaG6OhoBAYGYtCgQUbL1qhRA+XLl8f+/fvx8ccfIywsDADw008/oWvXrvJyAwYMgI+Pj/zezMwM9vb28Pf3xw8//IADBw7kSt0tLS1hbm6eK9siIiJ6FoMaRERERERERERvGXNzc6xduxaLFi0yKps3b4aZmZnRssOGDUOLFi1Qs2ZNlClTBuXLl8eOHTsAAN7e3vJyY8eORfny5bPcn4WFBVJSUuT3K1euRIkSJeDs7IxvvvlG7jUihMCoUaPg4uICBwcHDBw4EM+ObN6jR49MQ0ElJSWhc+fOsLW1RaNGjRAbGyvPq1+/PlasWCG/nzhxInr06AEA0Ov1GDhwIBwdHeHi4oLvvvvuhdqPiIjeXRx+ioiIiIiIiIjoLdS5c2c4OzsbTQsJCcGWLVuMpqWnpyMgIADDhw/HrFmzUKtWLXh5eSE1NRUffPDBc/eTlpaGBQsWoGbNmgCAo0ePonfv3ti0aROKFCmCjz/+GGXKlEHXrl2xZ88eLF68GIcPH4ZSqcTHH3+Mli1bokmTJjnuY/z48fjvv/8QGBiIo0ePok+fPqhYseJz6/bbb79h27ZtOH36NNLS0tCwYUO0bNkS1apVe+66RET0bmJPDSIiIiIiIiKit1B6ejrS0tKMikajybScmZkZRo8ejcGDB2PIkCFo3bo1KlSogIiICBQpUiTb7f/666+wt7eHra0tbt68iYULFwIAVq1ahdatW6Nly5bw8/PD559/jm3btgGQenQAgFarRdmyZXHnzh18+OGHzz2WzZs3Y/To0fjggw/Qu3dvVK1a9YXaoGvXrrh69SpsbW0RFBQEExMTBAUFvdC6RET0bmJPDSIiIiIiIiKit9CmTZugVquNpqWmpkKhUMjv9Xo9tFotOnTogIoVK+LixYvyvLCwMHh5ecnvtVotdDqdHJjo0qULxo8fj/bt26Nx48YoVqwYACA0NBSHDh2Cvb09AECj0aBChQoAgHr16mHUqFH44osvEB4ejnbt2mH27NmwtLTM8VjCw8ONAiwZ+8rK08Ng3b17Fz179kRERAQCAgJgZWUFvV6f476IiOjdxp4aRERERERERERvAYPBAK1WK7/fuXMnrl69alTWrFkjz9doNDh+/DgcHBzg6OiIWrVqYeDAgXB2doadnR0uX76MTz75BM7OznB2doajo6PRMFG2trbw9vbGDz/8gHnz5iEmJgYA4OnpiS+//BKXLl3CpUuXcPnyZfzxxx8AgDt37qBNmza4cuUKrl69ihMnTmDRokXPPTZXV1c8fPhQfn///n35b4VCAYPBIL8/f/68/PeQIUPQrFkzhIWFYdOmTZmG4yIiovcPgxpERERERERERG+B/fv3y8NBAUCVKlVgb29vVJo2bYro6GjY2trCzs4Ozs7OSE1NRWxsLKKjoxEdHY3IyEg0adIEgwYNwq5duxAeHo7o6GgkJibi6NGjmfbbpEkT+Pv7Y86cOQCAbt26YevWrYiIiIBKpcK4ceMwbtw4uY6tW7fGxYsXkZqaCgDQ6XTPPbZPPvkEP/74I27fvo2VK1fi5MmT8jwPDw+cOnUKALBlyxacOHFCnpeYmAitVovQ0FCMHz8e586dy5SYnIiI3i8MahARERERERERvQU++ugjJCcnIyEhAWFhYfj6668xfvx4xMXFIS4uDjNmzECPHj2wa9cuJCQkIDU1FWXLljXaxuXLl9G4cWPExcVhwoQJ6Nu3L8qWLYu//vorx31PnjxZ7q1Rp04dTJo0CZ9//jlKly4NjUYj59v44osvUK9ePTRp0gQVKlRAiRIl0L9//+ce2+TJk+Hp6YnKlStj2bJlaNq0qTxv7NixOHHiBEqXLo1169Zh4MCB8rwff/wRf/31l5wjpHHjxkZDbBER0ftHIRjeJiIiIiIiIiJ6K9y8eRMrVqzAqlWr0KhRI4wdOxalS5cGAERFRWHVqlWYNWsW3N3dMX/+fAQEBGD//v04e/YsNm/ejODgYIwYMQKjR4+GSqWCEAJr167FyJEjUaRIESxYsACVK1fO56MkIiJ6deypQURERERERET0lkhLS4ObmxsuXbqElStXygENAHBxccHw4cNx+/ZtdO/eHeXKlYNCocC9e/dw4cIFDB06FA8ePMC4ceOgUqkASPkqunTpghs3bqB69eowMTHJr0MjIiLKFeypQUREREREREREREREBQJ7ahARERERERERERERUYHAoAYRERERERERERERERUIDGoQEREREREREREREVGBwKAGEREREREREREREREVCAxqEBERERERERERERFRgcCgBhERERERERERERERFQgMahARERERERERERERUYHAoAYRERERERERERERERUIDGoQEREREREREREREVGBwKAGEREREREREREREREVCAxqEBERERERERERERFRgcCgBhERERERERERERERFQgMahARERERERERERERUYHAoAYRERERERERERERERUIDGoQEdFrO3r0KHbu3Pna20lNTc123i+//IK4uLjX3gcACCGg0Wig0Wieu2xiYiICAgKwfPnyXNk3EREREdGKFSuQnJz82tuJiYnBH3/8kWn6/Pnzcfny5RfaRmpqKmJiYuT3//77L/7888+XrsujR4/g5+eHHTt2vPS6OYmKikKpUqWwe/fuF17nzJkzWLRoUa7Wg4iI3h4MahAR0Wu7ceMGWrdujXXr1snTkpOTsWTJEjx+/DjT8hs3bsTff/+daXqjRo3w/fffZ5p++/ZtDBkyBBcuXMixHmXLloVCoXhuUSqVMDMzw8qVK597bFZWVjhz5gySkpKeuywRERER0fMkJSVh9uzZaNSoEWJjYwEAvXv3zvH6NSoqKsttXbhwAV988QX27NkjT0tOTsaYMWOwZs2aF6rPkiVLUKJECURHRwOQHljq1auXXLcXtXnzZly9ehXFixd/qfWex9raGkFBQVk+kGRhYYGFCxdmmr5//35MnDgxV+tBRERvD1V+V4CI6F11+fJl+Pv7Y/Xq1ejSpQsAYOvWrfj0009x4sQJ1KxZEzdv3kS/fv1w5swZeHl5YebMmWjVqhUAYOLEiZg0aZK8PXd3dzRt2hSzZ8+Gg4NDvhxTdr788ksolUp069YNZmZmaNOmDfR6Pfr06YMqVarAyckJo0ePxqeffooaNWrg6NGjUKlUaNOmjbyN27dv49SpU5g6dSoAIDg4GHq9Hmq1GqtWrUKJEiXg6+uLkJAQ6PV6pKamwsPDw6gt7O3t0adPH3zzzTdG9Vu6dClKly6NmjVrytN0Oh2cnJyMlktKSpJ/zD1NqVQiLS0NISEhRtPt7Ozeus+CiIiIiN5u1tbWOHHiBBo3bozPP/8c//zzDywsLDB06FBMmDDBaNn4+Hh4e3vD3Nxcnnb//n2kpaVBrVajePHi+Pjjj/HgwQP5WnXVqlWwtrZGr1695GtnExMTeHt7Z6qLVqvFrFmz0KxZMzg7OwOQAiwzZ87EyJEjsWTJkizXuXnzJiwsLKBSPbmttHDhQtSvXx9mZmaZrpsNBgPS09Ph4OCAQoUK4dGjR/jtt99gbm4OpfLJ87YeHh7o1KmT0boWFhYAYLRcBnNzc6O2yWBqagozM7NM04mI6N3AoAYRUR7x8/NDixYt8PPPP8tBjZ9//hkNGjRAzZo1kZCQgMaNG6NKlSrYtWsX/vzzT7Rr1w43btxAsWLFAEg/eA4dOgQAePDgAb7++mt88cUX2LJlyxs9lpCQEKxYsSLHp5369OmD6OhouRt9xo+LjB8hO3fuRPny5VGjRg2YmJjAxMTEaP2FCxeiZcuWqF+/PuLi4tC3b1+cPn0apqamSEhIgIWFBfz9/QFIw0dptVosX74cHTp0kLdhamoKOzu7TD/YNm7cCH9/f3Tu3DnH4/zzzz/Rq1evLOeNGjUKo0aNMpr2zTffYMqUKTluk4iIiIjoWba2tti1a5fcG1ihUMDMzAz29vbPXXfo0KHYuXMnzMzMoFAoAEi9KxITE2FjYyPf/K9evTqEENDr9ahevToOHDiQaVsLFy5ERESEUW9pc3NzzJ49G+3atUOLFi3w6aefGq3z6NEjVKhQIdv6+fj4ZDtvwoQJmDhxIjQaDW7dugW1Wi3X98SJE6hUqRJatWoFMzOzTL8XMoaQVSgUMDU1BQCYmJhkGexQq9XyMhmuXbuGJUuW4Keffsq2fkREVDAwqEFElIfGjRuHGjVq4OTJk7CxscGhQ4ewf/9+AFLvgcTERKxevRpWVlaoW7cuduzYgQULFmDOnDkApIv0KlWqAACqVKmCuLg49O7dGykpKbC0tHxjxxESEoJJkyY9twv32LFj5b/VarXRvGd/lDwtPj4eK1askAM49evXx4ABA3DgwAFERkaicOHCuHr1ao4/kDJk/LB7msFgeKFu8BlPc2m1WqOnzlQqFZYsWYIePXrI07y9vfn0FxERERG9FIPBAJ1OB7VaDUdHRzg6OgIA0tLSMG/ePPz4449Zrpeeng4bGxsAwN9//420tDSYmprK19h//vkn+vTpg6ioKKPrcL1ej/T09Cx/OwQHB+Obb77B0KFD4evrazTvs88+Q6dOndC1a1fs2rULderUked5eHggKSlJDjxoNBr4+fmhVq1aWLp0KdavXw+1Wm3UK/tZXl5eWL16tdG0xo0bo0KFCihcuDDi4+MzrfPJJ58AAH766ScMHTo0220/SwiBZcuWYejQoTA3N0fPnj1Rvnz5F16fiIjePsypQUSUhwICAtCwYUP8/PPP+OWXX1C9enU0atQIALBr1y7Url0bVlZWAKTu1BUrVsTVq1ez3Z5CoYDBYHihBNdvSpMmTVC6dGmUK1cOI0aMMJqnUCgghHjuNqZMmYImTZrAz88PJ06cwO3bt1G5cmVERUVh3bp18PPzg4ODA+Li4mAwGHLclkKhgEajQXp6ujwtNTUVrq6uz61HVk955ebyRERERPR+u3PnjtzDIuO6FQB+//13CCGyLRlDQ2WoX78+VCqVvJ0OHTogISHBaNsKhQIqlQq2traZ6pGeno7PP/8cPj4+RkPePm3p0qWoWbMmGjdujN9//12erlAoYGVlJe9/2rRpiI2NxYwZMwBIuTVeJFn41KlTERQUJL+/fPkyqlWrhsDAQERERCA2NlYuALBu3TpERERk6lkdHh6Omzdv4tq1a7h48aLR0FdHjhxB7dq1MXToUAwdOhTBwcEMaBARvQN4N4aIKI+NGzcOf//9N1avXo1x48bJ069fv57piagFCxbg119/zXI7//33H3766SfUqlVL7pau1WoxevRouLm5wdraGu3bt8ejR4+M1tu5cycqVKgAMzMz+Pn5Yffu3Ubzb968iY8++gh2dnZwdXVF//795R9XEydOhEKhQIMGDQBA/nH0dI+FoUOHYtq0aShUqBAiIiLk6RqNBkKILHtOPO3atWuYPXs2bG1tMWnSJAwcOBDffvst5s+fDx8fHwwbNgxBQUEoWrQoHBwcEBYWlu22hBBQKpVYtmwZzM3N5fqGhoZiyJAhmRIu7t2712j95wVMnvWyyxMRERHR+83R0RETJ05E3759oVAooFarodFo8PDhQ0RHRyMuLi5Tefz4MR4+fGh07blt2zZERkbKN/3d3d0xY8YMo0BAbGwsoqOjce/ePaM66PV6dO/eHVevXsX48eMRHByMmzdvZir37t3DrFmz0LRpU3z55ZdGic0z7N69Gz/88AOmTp2K1NRUhIaGyg8YhYaGGhW9Xi+vZzAYcP78eTRr1gyPHj3CxYsXER8fj2rVqqFo0aIIDQ2Fvb29XABpaF43Nze5x0qGH374AQEBAahevTqqV68u93a5c+cOWrdujfr16yMkJAQ//PBDlgEeIiIqeDj8FBFRHmvYsCFKlSqFlJQUtGjRQp4eHR2d6aLaw8PD6H18fLxRUMDb2xt79uyR3/fp0wdbt27FrFmz4ObmhjFjxqBRo0YIDAyEubk5Dh48iJYtW6JPnz746aefsHHjRrRo0QL79+9H/fr1AQAdO3aEWq3G5s2bERUVhUGDBqFIkSIYM2YM+vbtixYtWuD8+fPo168fzp07BwBGT4o1a9YMALBlyxajngsZPSWeN0RTqVKl0KNHD3h5eeHGjRtIT0/H119/DVNTUwwfPhz16tXD7du3odfr4eLiIufoyEpaWhpUKhXat2+PRo0aQaVSQaPRoFSpUli7di1q1KgBQAooNW/eHIULFzZaP6POz46/CwBffPEFvvjiiyyXJyIiIiJ6EY6OjpgwYQIOHz6MpUuXAgAuXryIunXrQq1Ww8TEBGlpadDpdLC2tpZzYqSnp+PBgwewsrJCcnKyUS6Kq1evIjw8HHXr1s20PxMTE1hYWODhw4ewt7eHpaUl/vjjD/z999/YunUrBg0ahODg4BzrfPr0adSvXx+3bt2Cg4ODPP3MmTPo2LEjDAYDevfunWm9NWvWGL0PCQlB0aJFAUg9ntetW4cWLVqgefPmaNCgAWrVqgUrKyssWrQI33//PW7fvv1CQ+4uXLjQ6KErAJg7dy4KFy6MoKAguWc8ERG9OxjUICLKY2FhYbh16xb0ej3u3LkjJwHXarXyD5ExY8bITxTVq1cPhw8fBiA9jXTs2DEYDAZcuXIF33zzDXr16oUdO3bgzp07WLlyJZYsWYKePXsCkAIEpUqVwoYNG9C9e3dMmjQJtWrVwqJFiwAAjRo1wo0bNzBp0iQ5qHH37l0MGDAADRs2BAD4+vrKN/ULFy6MwoULywkMM/J7ZOfpoMaDBw8AAHZ2djmuY2JigqVLlyIpKQn+/v5Yvny5nBy8d+/e6Nq1K+Lj4+Wnwh48eIC4uDikp6ejdOnSRvtMTEyEnZ2d0fjE165dAwDUrVtXDhrduXMHAODi4mJUl/bt26NBgwb4999/4efnB0Dqzl6zZk1s3Lgx0/E/79iIiIiIiJ6nevXqRg/LjBw5EidPnsSJEycyLbto0SJ89dVXWW4nICAgx/3s2rULTZs2Rc+ePVGnTh2UKFECV65cgVqtxnfffYfNmzfjxo0b8vLHjh1D3bp14eXlherVqxtta9++fWjbti2aNGmCAwcOYMmSJXLOi+7du8Pa2hoLFiwAAAQFBaFcuXKZHnZSq9XYuHEjatasiVmzZsk91rt3747vv/8e8+bNw+jRo3M8ppyYm5szoEFE9I7i8FNERHls5syZ8PX1ReXKlTF16lR5urW1NZKTkwEAQ4YMwcWLF/Hll18arWtiYgJ/f39UqlQJ3bt3x8KFC7Fz504cP34c58+fhxBCDkYAQPHixVG0aFG5R0VgYKDRfAD48MMP5fkA0L9/f8yYMQNNmzbFhAkTAAD+/v6vfdw3btyAk5OT0dNcOenTpw/atm0rJyGMiIjA2bNnsXTpUvj7+8tBmPr168PPzw9+fn5ISEgw2sb9+/fh5ORkNO348ePw8PAw6gXz+PFjAMi0rLW1NUJCQtC6dWusXLkS3t7eSE1NBQA0aNAA3t7eRuVFj42IiIiIKDt37tzBtWvXcPv2bdy+fRv37t2DWq2W39+6dQvXrl3D9evX0atXL6Snp0Ov10MIgX/++QcKhQLHjh3LNh+HXq9HamqqnNsPAEqUKAEAsLCwgImJCa5evZop10RYWBhUKhUKFSpkNH3btm1o2rQpGjVqhDVr1kCpVEKpVEKlUhnl+ch4n5HMXKXK/FytnZ0d+vTpAwDytb2FhQUGDx6MOXPmyNfiRERET2NPDSKiPBQVFYXFixdj7ty5sLOzQ9euXTFhwgQUKVIExYsXl7t6u7u7w93d/blPEmX8+Hjw4IH84yArGcm5s0vS/fT0adOm4bPPPsPBgwdx4MABfP/99/jll18waNCglzrWDLdv30bx4sURGBiIqlWrvtA6gwYNwqZNm9C1a1fUrl0b165dw8GDBxEREQFHR0eoVCpER0fDxcUFwcHBsLOzyzREVFhYGJKTk+Hm5mY0fdWqVWjatKnRtEePHsnJDZ/VoEEDTJo0CRMmTIC7uzuuXbuGokWLZgqAEBERERHlhuHDh+PQoUNyQCDjAZzq1atDoVDIw0+5u7sbDRV17do1dO/eHbVr14azszPu3r1rNHStEALp6emwt7fPFJh4Wnx8PA4dOiQn+s4QFhaGwoULG/WMBoDmzZtj8eLF6NatW5bX0y8jPj4e06dPR6tWrTB+/Hg0b94cZcuWRffu3XHt2jUkJSXlOPxsVp7O80dERO8m9tQgIspDc+bMgbW1Nbp164a2bdvCw8NDHmaqcePGOHToEOLi4uTlL168mOP2rl+/DkAKglSpUgUKhQKHDh2S5wcHB+PevXtyMKFq1apG8wHg4MGD8vzQ0FAMHToU5cqVw8iRI7F792507twZS5YsMVrH3NwcAKDT6XKsX3x8PBo1aoQ9e/Zg7dq1cr4NIOek2i4uLqhduzYKFSqEYcOG4dSpU6hQoQLs7e1Rr149bN68WV42OjoaH3zwQaa2CgwMBPAk8ANIXexPnDghP/2VISIiIsckgd999x369++PL7/8EkuWLEGbNm1yPG4iIiIioldx5swZzJgxA/Hx8Xj8+LHc88LZ2RmTJk1CdHQ0YmNjkZKSYhTQOHv2LBo0aABfX1/odDr4+/vD398fpUuXRrFixeDv74+KFSvC398fv/zyS451mDx5MoQQ6Nixo9H0sLAweHl5ZVrexMQEPXv2lAMaOV3nP29+v3794O3tjb///hvNmzdHjx49oNfr4e7ujlWrVmUaLjYnZ86cQf369TF48OBslwkPD3/h7RER0duLQQ0iojwSGxuLhQsXYuDAgTAzM4OJiQmGDBmCZcuWITw8HMOGDYNarUbz5s2xe/dufP/995kCEHq9HoGBgTh9+jSWLVuGYcOGwdvbGzVq1ICvry+6deuGr7/+GsuWLcM///yD1q1bo1SpUvIPkvHjx+P48ePo378/Dh48iP79++PYsWOYOHEiAMDe3h4rV67EsGHDcPToUWzbtg0nTpyAr6+vUT3KlCkDGxsb/Pjjjzh16hQWLlyIR48eGS2TmpqKTZs2oUyZMkhLS0NkZCS6dOkiz9dqtfLfGV3hM4wfPx4HDx7E1KlTUaxYMfz111+4dOkSxowZg9u3b6NChQryss7OzggICECnTp2QkpIiT9+3bx+cnJxQvHhxANJQVD179kSHDh0yjQF89+7d5/a86N69O2xsbJCcnIxt27bhr7/+em5Qh4iIiIjoRRkMBrRs2RIrV64EACQlJaFXr17o3Lkzpk+fjnHjxuHKlSuZ1vn5559Rp04d1KxZEwcPHsTJkyeRlpaG+Ph4TJgwAaVLl0ZcXBzi4uKQlpZmNATus2bMmIHZs2fjxx9/zDS0alBQUJZBjWc9nQ/k2elTp07FqFGjYGpqmikf3fjx47Fnzx6sW7cOSqUSc+fOxfXr1zMlGM9ORk+Uixcvonnz5ggICICpqSmGDRuW7TqVK1fOMehBREQFA4efIiLKI/PmzYNWq0X//v3lab169cLEiRMxa9YszJ49G4cOHcKXX36JVq1aoXz58ujbty+CgoLk5ZOSklC1alUoFAq4u7ujUaNG+P777+Uke4sXL4arqytGjx6NlJQUNG/eHPPmzZN7VjRq1Ajbt2/HmDFjsGTJEpQqVQr//POPnJ/C2toaO3bswNixY9GyZUsoFAo0aNBATuqXwdbWFmvXrsXw4cMxceJEFClSBK1bt5bn6/V6nD17Fp6enpg/fz7q1auH3r17GwUOhgwZgooVKwIANBqN/MRWSkoK1q1bhyNHjuDgwYNISEjARx99hMePH2Pp0qU4dOgQihUrhocPHwIAFAoFFixYgJIlS+Lbb7/FnDlzkJSUhDVr1qBVq1ZQKBQ4fvw4OnToADMzMzlJOgCsWbMGN27cwPbt243GFAakH4jBwcE4fPgw1q9fj8OHD+OLL77AqFGj8PPPP+Pzzz+HqakpqlSpgsqVK8PNzU0eAuuLL77IsecHEREREdHTzp07ByEEOnTogB9++AFxcXFo27YtkpOTMXPmTLi7u+PAgQOoV68efv/9d7Rt2xYKhQLjx4/HzJkzMX36dDRp0gR37tyBWq2WtxsVFQWNRoObN2/K07RaLdLT01GlShV52o0bNzBp0iRs2LABY8aMwYABAwBI18RTpkzBgwcPsGfPHsyaNeu5x/JsUMNgMEAIATMzMxw4cABBQUH48ccfjYaPHTx4MJYsWYK9e/fCx8cHAODl5YXt27fLOfYyZPRQeTbReGBgIJKSkvDLL7+gcePGOHHiBGrWrAkA+Pfff3H//n0cPXoUrq6uAIATJ04gPDw8U+4QIiIqgAQREdFr0mq14tNPPxVHjhwRN2/eFC1bthQxMTHZLt+zZ0/Rt29fIYQQaWlpwsbGRjRq1Ej8/fffIj09XQQFBQkLCwuxb98+IYQQY8aMEfXr1xdWVlZCr9cLIYSYP3++qFOnjtBqtWLcuHECgDh48KC4d++e8PHxEUWKFBG3bt0y2u+CBQsEAFG8eHFx8uTJTHUCICwtLUXHjh3FuXPnjOY/fvxYrFy5Unz55ZeiYsWKwtnZWajVatGhQ4fXbj8iIiIier+sW7dONGvWTNy5c0dMmjRJFCpUSJQpU8bo+lWr1YpBgwYJhUIhSpcuLebMmSN0Op0ICgoSQgjRpEkTYWNjI+zs7HIsVlZWws7OTt5ucnKyqFSpkvDw8BAbN27MVLe+ffuK4sWLizFjxoj09PSXPrb27duLnj17CiGE0Gg0WS5z6tQpsX///hy3o9frha+vrwAg3N3dRWRkpNF8g8EgunbtKvbu3Ztp3QcPHojSpUsLAHJRKpXi448/FmlpaS99TERE9HZRCJFNFlkiIqI3JCoqKtN4uXfv3pWf2po9ezauXr2Kzz//HA0bNgQgPQGmUCigUCiQnJyMzZs3o2vXrvL20tPT4enpabTN5ORkJCYmZpkoMTY2FoGBgahTp47c04WIiIiIKK8YDAYkJCSgdu3a6NixI0aMGJHldeiFCxcwZcoU9O/fP1Nv41f16NEjWFpawsbGJle297RPP/0U1tbWWL169Wtva9++fVAoFKhRowasrKxyoXZERPQuYFCDiIiIiIiIiIiIiIgKBCYKJyIiIiIiIiIiIiKiAoFBDSIiIiIiIiIiIiIiKhAY1CAiIiIiIiIiIiIiogKBQQ0iIiIiIiIiIiIiIioQGNQgIiIiIiIiIiIiIqICQZXfFXgTDAYDHj58CBsbGygUivyuDhERERHRaxFCIDExEYULF4ZSyeeUAF7zExEREdG7g9f7OXsvghoPHz6El5dXfleDiIiIiChXPXjwAJ6envldjbcCr/mJiIiI6F3D6/2svRdBDRsbGwDSSWBra5tn+zEYDIiKioKLiwsjaO+RNF0aPv/7c2g1WqxtvxaWasv8rhK9Yfy3/37j5/9+4+f//srvzz4hIQFeXl7ydS69/jV/fn+m7zu2f/4pVaoUwsPD4e7ujps3b+Z3dd47PPfzF9s//7Dt8xfbP/+8aNvzej9n70VQI6P7ua2tbZ4HNdLS0mBra8svhPeIWqeGqaUphImAra0tgxrvIf7bf7/x83+/8fN/f70tnz2HWXrida/535bP9H3F9s8/Ge2tVCrz9PcyZY3nfv5i++cftn3+Yvvnn5dte17vZ41nLRERERERERERERERFQgMahARERERERG9p6ytreVCREREVBC8F8NPEREREREREVFm169fR2RkJFxdXfO7KkREREQvhEENIiIiIiIiIiIiem/p9Xpotdr8rsYbYzAYoNVqkZaWxpwab1hG2+v1erb9a2BQg4iIiIiIiIiIiN47QghEREQgLi4uv6vyRgkhYDAYkJiYyETUb9jTbe/g4IBChQrxM3gFDGoQvSYzEzMsabkE0dHRMDMxy+/qEBERERERERHRC8gIaLi6usLS0vK9ubkshIBOp4NKpXpvjvltIYSAVquFRqNBVFQUAMDd3T2fa1XwMKhB9JoUCgVcrVyBZPA/AiIiIiIiKlBGjRqF8PBwuLu7Y9asWfldHSKiN0av18sBDScnp/yuzhvFoEb+EUJApVLBxsYGCoVCzmtlYmKS31UrUBjUICIiIiIiInpPrV+/HmFhYfDw8GBQg4jeKxk5NCwtLfO5JvS+yjj3tFotgxovidlIiF6TzqDDskvLsO7mOugMuvyuDhERERERERERvaCC2lMhNjYWCQkJ8vvo6GjcvHkTAJCWloYjR45ACJHjNq5evYqjR4/maT2f9rz6vGkGgwG7d+9+rW1kdUw63YvdHyyo597bgEENotekM+iw5eYW7Lq7i0ENIiIiIiIiIiLKc2PGjMHSpUvl93v27EH37t0BAMeOHUP//v2RlpaW4zYOHjyIPn36PHdf/v7+2LNnDwDg6NGjUKlUsLe3l4udnR1cXV2zXf/s2bPo3r07qlevjsTERIwaNQodO3Z8kcOUrVy5EoMHD85ynsFgwMiRI3H37l2j6S4uLggJCUFoaChUqswDFkVERODzzz/HTz/9BABYsGAB7Ozs4O3tbVTMzc2zrVf//v2xYMEC+X14eDiKFCmCe/fuvdTx0cvh8FNERERERERERERE/xcTE4OkpKQ3si9ra2s4Ojq+9HpqtRp2dnYApJ4Bbdu2xc2bN5Geno61a9dixowZsLCwkJc/deoUWrRoARsbG3maXq9HYmIivL295e0kJSVh27ZtqFu3rrycubk5zMzMAACmpqaoUqUKTp8+Lc+PiIhAtWrV5PcGgwFr1qzBiRMnsHfvXty9excffvghRo0aJW8jY3tPW7t2LQ4fPgy1Wi1PmzBhAlxcXLBhwwY0adIky7ZQKpVIS0vDt99+i9WrV0Oj0cDMzAxarRZWVlYAIO9Po9FApVJBqVSicOHC2LJlCz766CO0aNECpqam+OqrrzB9+nSj7bu5uRm912q1UCgUUKlU6NatG77//nsMGDAAAPDbb7+hYcOGKFq0KAwGA3Q6ndHxUO5gUIOIiIiIiIiIiIgIUkBjyJAJiIrSvJH9ubio8fPPk146sKFUKuWb5StWrMDMmTNhamqKX3/9FRYWFjh37hw6duyIjRs3omnTplAqlfDw8MC///4LIQQ2b96MqlWrwsvLC4CUYykgIEAOcADArVu3cPPmTcTFxeHkyZNITU2Fs7Mzzp8/D2dnZ3k5IYRRsESpVCI2NhZOTk5Yvnw5+vfvj0GDBuGTTz4BIA27lNXQS2XKlIG5ubl8XL169cKIESOQlJSE48ePY926dfKyOp0OQgiYmpoCAIYNG4Zp06YhIiIChQsXhoWFBVJTU1GqVCkAQEpKCmxsbJCWlobAwED4+fkBAGrVqoVr167B29sbhw8ffqG2nzlzJubNm2cUmFGpVHB1dZXr7u3tDb1ej5o1a2LDhg0vtF16cQxqEBEREREREREREQFISkpCVJQGFhY9YWnpnqf7SkkJR1TUMiQlJb1wUOPixYsYMWIE/v33Xxw9ehQ7duzAunXr0Lt3bwDA0KFDERAQgI4dO6Jp06ZGvQQUCgXS09OhVCoxbdo0TJ8+XQ5qzJkzB5MnT0bhwoWhVCqhUqkQEhKCffv2IT4+HufOnUNCQgJatWqFypUr59hTA4DRUFEKheKFEmH7+/vD399ffm9hYQG1Wo1JkybBxMQEFSpUAADcu3cPXl5emDhxInr27InU1FR4eXlh0aJFUCqVcuJtlUqFR48eISkpCZ6enkhMTJS3rdPpcOXKFZiamsLJyQkAYGNjg3/++Qf//POPUb08PDyM3n/zzTcYMWIETE1NoVAokJaWBnt7e1y5ckXelsFgkHuMUO5jUIOIiIiIiIiIiIjoKZaW7rC2LpLn+0lNfbnlK1asiD179sDNzQ1ff/01unbtiosXL6JLly5QKpWIiIjApk2bMHnyZISGhiI9PR2A1JvCxMQEo0ePxv79+3H37l18+eWXsLa2BgD8999/+Oqrr2BmZoaff/4ZjRs3xkcffYSPPvoI586dw5AhQ1C/fn2cPXsWERERRj06ACl/RYaYmBijYEpqaqo8tBUgDd+U8d5gMECr1crBgGedPXsWf//9N/799194enri0aNHKFu2LO7fvy8v061bN1y+fBkWFha4ePEiTExMkJSUBDs7u0y5NPR6PRQKBZKTkzFy5Ejcu3cPrVq1wqxZs9CyZUt89tlnWa6TmppqNJxXu3btcOHCBZiYmCA5ORlKpRKVK1eW5xsMBqSnp+PKlSs55huhV8OgBhEREREREREREVEBsWnTJsTFxWHTpk1QqVQwMzND5cqVsWrVKqOeGk9LS0uDqakp5s6dCyEEqlWrhiFDhiAgIACAdJN+9uzZaNiwYZb7jImJwZEjR1C9enUEBwdn6nmRcRPfzMwMJUuWhEajkYeYSkhIQJcuXeRgQVpaGgwGA7Zv3y6vl5KSgujoaLk+ABAaGooLFy6gbNmyWL58Ob777jvcunULpUuXNtr3xo0bsXv3bkyZMgXr1q1Dr169oFKpkJqaCnt7ewghkJycDCsrK6Snp+PHH3/E119/jf3792PixIlyQvWGDRsiJSUFJiYmuHnzJooUKQILCwvo9Xr4+vpi27ZtEEJAp9Nhy5Yt8vH16tVL7jnyLK1WCyFElsNt0atjUIOIiIiIiIiIiIioANDpdJg0aRKaNWuG4sWLY+DAgRgxYgR27tyJcuXKGfXUAICPPvoIc+bMQXJyslFviE8//RSnTp2Sh5GqVatWpoTYAPDvv/8iMjISAwYMwNixY9G2bVuYm5tDpVIhIiICdnZ28o1/W1tbXLt2DVFRUfL6aWlpsLa2xuXLl1GsWDEAwLfffovQ0FCsWLHCaF+mpqaIjo7OlKT9/v37qFGjBoYPH46TJ0+iTp06WbaNiYkJunTpgi5dumDDhg1YvHgx9u/fj7i4OHh6ej43+fuZM2fkvwsVKoQ///zTaDgsQAq0VK1aFWZmZlAoFEhKSkJCQgIKFy6c6XgyAiCnT5+Wh/mi3MGgBtFrMjMxw/xm8xEdHQ0zE46TR0REREREBcfHH3+M8PBwuLvn7bjxRESUO1asWIGSJUuiaNGi8PPzw65du1C1alWMGDEClSpVgo+PD77++mt07NgR//zzD/78808AkBNob9++HYMHD4YQAnZ2dnIPgrS0NNy4cQO7d++Wk29PnjwZgYGBMBgMWL16NRo1aoQPP/wQBw4cwKBBg1C/fn2MGTMGTZs2zba+27dvh5ubmxzQyEl2eTeKFCmC1q1bY+bMmdiyZQuWL1/+3G3t2bMHtWrVeu5yGVatWoVhw4bBysoKQgg8evQITZs2hbm5uTxU1sSJEzF06FBEREQAkHph1K1bFz179kSfPn1eeF/0+hjUIHpNCoUCReyKwDzdnF3JiIiIiIioQFm0aBEiIyM53jcRUQHRoUMH1K9fH/PnzwcAVK1aFQAwYsQIfPDBByhUqJDR8kqlEgBw+/ZteHp6omXLlmjRogUGDx4Ma2trTJs2DTExMahWrRomTZokBzQAqUcFAAQEBMgBh9OnT2P79u0YNGgQAKB79+7ysFJhYWFG+05ISMA333yDsWPHvtKx7tu3Dw0bNoSJiQkmTpyI0qVLo2zZsqhYsWKO6127dg0bNmzAxYsXAUhDYxkMhmyXT05ORmpqKqKjowEA33//Pf744w84Ozvj1KlTchs+TaPRoFOnTjA3N8fw4cMxceJEGAwG6PV6WFpaIi0tDdWrV8fWrVtf6dgpZwxqEBEREdGLS04G7t6VyuPHgJcXUKyY9JrNk1UF0v37wIkTwOnTQEICoFQ+KSYmUCgUsElPB+rWBbp1y+/aEhEREdF7wsbGBjY2NvJ7vV6PUaNGYfv27Th79iwmTJgAQOqp8Ntvv8HHxwcAcPDgQaOcD5MnT8aHH34IU1NT7N69G8OHD0ft2rWfu//du3cjNTUVWq0WAPDHH39k2VMjPj4en3zyCby8vNCvX7+XOsZHjx5hzJgxOHLkCE6cOAF3d3ds2rQJqampePjwIR4+fIjChQsbrXPq1CncvHkTgYGBGDVqFPr27YsPPvgAgJSoPCNh+rO0Wi0WLVqEzz77DH369MGxY8cwc+ZMBAYGYsCAARgyZAh++eUXoweZb9y4gc8//xzFixfHvn375KDOrFmzcOnSJaxevfqljpdeHoMaRK9JZ9Bh/ZX1SEhIQG/n3lAr1fldJSIietfo9UBcHBAdLQUSMl4TEgB7e8DFBXB2fvJqbQ0oFIAQgE4HpKU9Kf//8QGlUlomowgBxMdL280oMTHSa2jok0BGZGTWdTQ1BXx8pACHr6+0vafrmlFSUrJeX6EALC0BKyup/k+/OjkBnp6Ah4fxq729tP3IyCfl0SMgKko6Vp1OOt6nX01NAVfXzEWtBs6eBY4fl4IZDx7k+JEoAFgBEFotgxpERERE76CUlPC3eh96vR7p6emoV68e4uPjceTIETg5OUGn00Gn08HExAQlSpTAyJEjcfnyZQQFBaFevXoApFwPQUFB8PPzw+TJk+Hm5gaFQoFLly7B09MTTk5ORjfxMxJpX7p0CTt37kSNGjVQu3ZtREdHIzIyEvHx8UhLS0N0dDTKlCmDM2fOoE+fPrC1tcU///wj3/R/uu5Z9Zy4ffs20tLSULZsWfTv3x/Xrl1DSEgIvvrqK/z333+4ePEiVq1ahUqVKmHx4sVo2bIlDAYDhgwZgrVr16J69eqoW7cu6tevjypVquDatWtwdXWFjY0Nbt68ifDwcISGhkKv18sJyU+fPo22bdtiwYIFmDt3LsaPH4/ly5ejZMmSWL9+PT766CPUqlULI0eOxIcffoigoCDUqVMHAwcOxJQpUzIdW1afU3bDatGrY1DjTUhLk34oZ9FViQo+nUGH9dfWQ5OuQY/qPaAGgxpERPQUg0G60f7ggVQiI6Ub+6mpT0rG+6QkKVCRmGhc4uOlIMGLMjOTek2kpUn7fxO0WuDWLam8CiGkXiDJydkHTt5GvL4jem/ExMQ8N8FoQSSEQEJCAtLT0zMNp2ttbQ1HR8d8qhkRUf6wtraGi4saUVHLkJqa9/tzcVHD2tr6pddLTU2FEAIzZsyAn58frKysAEg9D9LS0vDhhx/iww8/BABcvHgRw4cPR1hYGLp27Yrw8HCULFkS7du3x8yZM3Hp0iVs2bIFS5YswdWrV1GmTBmcP38eer0eRYoUQVJSEiwsLNC0aVNMmTIFgwYNwpIlS7B06VIMHDgQPXr0gBAClStXRmBgIO7du4eGDRti5syZUKsz3yfTaDRyoORpZmZmqFOnDubNm4fSpUujffv22Lt3L4YOHYr169fD3Nwc33//PYoXL47OnTujX79+mDZtGtLT03H27FkUK1YMERER2LhxIzZt2oTx48cjPDwcqc98kNOnT5eDGmvWrIGNjQ0+++wzJCYm4siRI6hUqRIAyMNPzZ49G4MHD8auXbtQpUoVHDhwADVr1kTJkiXx8OFDOWghhIAQAvb29gCkpO4eHh4ICgp66c+XcqYQ4mV+Ib8Z0dHRqFq1Kg4dOgRvb2+jeaNHj8b169exffv2F95eQkIC7OzsEB8fD1tb21yu7RMGg0Eei1Qea00IoH176Yf+ypVAHu6f8keaLg2f/fkZNOkabOmyBZZqy/yuEr1hWf7bp/cGP//3WGoqDGFhiA0KgoNGA2V0tBS8yOgt8PChFMQIC5N6CLwLCheWemH4+EjF2Vkapik4GLh9W3rZ4jq6AADPbElEQVTNqieGSiX1tni6FwlgHKgxGKR1k5KeBDfexK/IrFhaAgEBQO3aQK1a0tBaBoNRMeh0iHn8GI7Fi0NZvPgbr+Kbur4tSF63Tfh9nr/e9vaPiYnBkCETEBWlye+q5LpTp/6GXp8OExMz1KjRxmiei4saP/88iYGNPPS2n/vvOrZ//nkb2j4tLQ13796Fj48PzM3Njea9yUD2mw4g37t3D15eXtDr9VCpVJkC2jqdDomJiXBwcAAg9c7w8fGBnZ0dTp8+LQcD3oSzZ8/Cx8cHLi4umeYFBwfD0tIS7u7uz92ORqOBVquFXq+HEAKWlpZGuUMAKUhkYWGR7TaEELmSS1cIAZ1OB5VKhfT09GzPQV7v5+yt66kRHR2NFi1aICQkJNO8f//9FwsXLsTly5fffMVe1axZwF9/SX9XqwZs3gyULp2/dSIiIqKc6XTAvXtPbtbfuweEh0uBiocPpb/j4qAE4JRXdbCyAmxspGJnJwUEnJ2fBAecnaV5cXHScEvR0cavQgDm5plLxsW7ENINeiGeBBfs7ABHR2kfGcXREShUCChaVFo/J0JIAZ27d40DGTY2TwIZL0Ovl4IbUVHSEFihoVKQKOM1Lk7avpvbk2Gk3NykYbisrKQ6qFTSMWe8pqZK23t6uKrISKlHTPnyUiDDz+9JO2XHYIAuMlLaJxG985KSkhAVpYGFRU9YWj7/5klBotVuQ2pqNCwsrODkNE6enpISjqioZUhKSmJQg4jeO46Oju/sd1/RokWR0zPuKpVKDmgAgL+/v/z3mwxoAEC1atWynVesWLEX3o5arc6yx8jTcgpoAMiVgAblnrcuqNGxY0d07twZZ86cMZpuMBjQt29fDBs2DL6+vvlUu1dQrpw03nNcHBAUJAU2/vgDaNPmeWsSERFRXhBCGuLp4UPpxvjT5c4dKYgREpI7vSucnKSn/D09pVcvL8DdXbrhbmHxpFhaSq/W1lIAwMqqYCbdViikAEihQrmzPRMTqZerra2UqyO3FKRrSSJ6q1hausPaukh+VyNXKZUm8uuzx5ZfHeaIiIiIcvLWBTUWL14MHx8fDBkyxGj6okWLcOXKFfTt2xfbtm1D06ZNnxtheys0awYEBkpBjH//lYZUaNsWGDsW+OGHgnnDgoiIcodeLyVOfjrJMfDk6faMJ+UtLbN/yt1geJLombIXGwts2QL8+aeUCPp1upJbWEjDMBUuDFGoEFJsbWFRtCiUhQoZ9xYoVEj67IiIiIiIiIgo17x1QQ0fH59M05KSkjBhwgT4+vri3r17WLVqFSZPnowjR45k2TUoPT0d6enp8vuEhAQAUm8PQx4myzQYDBBCZN6Hjw9w/DgUfftCsX69NG3aNIjAQIg1a6QbVlRgGQwGQAACIs/PMXo7Zftvn94uQkhP4B8/DsXFi4CtLYSvr/TEtq8v4OHxSoFmg0YDRUQEDPfuARERQHg4FBnDFMXEAOnpUtFonvydliYNEfT4MRQvkNpKmJtLwwLp9VKOJp1OetVqoRACQqEA1GopOXRGUauldYoVA0qUgPj/K0qUkG66JyVJuRAePADu34fiwQNpSB+1GqJoUWmooaJFAW9vqWdBQQzCx8UBW7dCsXEjsH8/FFrtC60mrK2B4sWltitWTGo7Hx/pHHF3l3oN/D+IZDAYkBAVBTMXl6wTRvN74Z2V39/9/D+HiIiIiIjeV29dUCMrf//9N5KTk3Ho0CE4OztDp9OhfPnyWLVqFfr27Ztp+WnTpmHSpEmZpkdFRSEtLS3P6mkwGBAfHw8hRNZJjubMgWXp0rD5/nso9Hoo9u2DvnJlxC1fDl3ZsnlWL8pbabo0pGvSodPqEBkZyUTh76Hn/tunN0+vhyI+Hqp792B65gzU587B9OxZmERHGy32dN8GYWoKvZcXDE5O0vo6nXRDOuNvvR6K/wcnFBqN/LfKYIBbHh+OIi1NCoRkN1+IJwGTZ128KC3z1CShVkOhyT7R6bN9PoSpKQwuLk9yMOj1UGQkTBYCBicn6D08oPfwgOH/r3oPD+gLFYJwcIDBzk4KsrwKIaCMioIiLg4GV1cIO7use6UIAZPQUKguX4bplSswvXQJ6lOnsgxk6AsVgq54cRgKFYK+UCHj1yJFYHB2zr7nS3q6lI/h//jv//2V3599YmLiG98nERERERHR26BABDVCQ0MREBAAZ2dnAFLCmgoVKuD27dtZLj927FgMHz5cfp+QkAAvLy+4uLjkabZ4g8EAhUIBFxeX7H/cfvstRO3aQMeOUERFQfXgAZzatIHYvl1KTEkFjkEY8EvzXxATEwNPd0+oTArEPyvKRS/0b59eT1QUcPmy1LshLg6Ii4MiLk4aUig2VpoWGyv1jIiNhSI+/qV3odBqobpzR8qpkIdERo8KJyepx4SLi1RcXSFcXACFAorHj+WeHIiJkf5OSJB6S5iaGheVSurBkRHUeLpXSGwsFHp95mPNIaCRFYVWC5OHD7Odr4yPl9oup+O2sZESTmeUjGPOSOrs6ioNuRUVBQQFQXHjhpSL6sYNo89TWFhIPSYyek04OUnLXbgARUxM9vv39ATatYNo1w6KatVgmkvDdfHf//srvz978+clbSciIiIiInpHFYi7r56enkh9JkPZvXv3ULNmzSyXNzMzg5mZWabpSqUyz390KhSK5++nYUPgwgUpt8bZs1AkJEDRtCnw999A06Z5Wj/KfUooUdK5JCINkVCZqHhT6z31Qv/26cXodMDVq8CpU1I5eRIIDn797drZATVrSgHkgADppv+dO9K2M4IZd+4AycnS8iYmUlGpnvxtZgaYmxsN8yTMzJBuZQUzb28oMm60/z/fApydjZdXq6HI4WZ6rmfF0GqlhNf//ScNvZXx+uCBVLciRaTi5fXkVaOR1sko9+5Jr1FR0vBKJibSa8bfQkjDbj0nR4UiMRFITJS29xrHrEhNlY4hmwcbjHh6Ap99BrRvD0X16oBSmfttDP77f5/l52fP842IiIiI6MXs3bsXDRs2hEr16rfChRBGv+d1Ot1rbY9eT4Fo+ebNm2PQoEFYtGgRWrRogb///huXL1/Gxo0b87tqr87TEzh0SAps7N4NpKYCn3wCrFkDtGv3atvUaqWndomoYIqPB44eBW7cAFJSpO+FjJKSIt2Et7GRbkZnFBcX6VWnk3MjyK/370tP+ru7SzkjfHyMi7f3iw0JlJAgDWF07Zp041qjedITIOM1I6dDRi+EjFeVSvpu0mgyv6akSAGEjNeMcvfuqyVxNjGRegA4OEjF0VF6+r96dSmQUbZs1jkPnpYxvNJL5I8QBgPiIiPh6uoKxdt2k9HU9EkejZdRqdLLLS+EdP5mnHf370vBi0ePpJ4mMTFPep3ExEjn64sqUgQoXVo61yMigLAwqfw/X5bM1RWoXNm4eHoygToREREREVEBcfv2bfTu3Rs7duyAlZVVpvnLli2Di4sLWrZsKU9r3bo1PvvsM3Tp0gXFixfHihUrUPup0XCEEJg0aRJ+//13rF+/HkFBQahUqRLc3d2Ntv3o0SMcPHgQNWrUyLTfdevWYcuWLdiwYYM8rVq1ahg/fjw+/fTTXDhyelkFIqjh5OSEnTt3YsSIERg+fDjc3d3x559/wsvLK7+r9nosLYGtW4EuXYC//pJu9HXsKD3J2rPni2/n9m1gwgRgwwbpRtT48UDz5ryR84boDDpsubEF8fHx+Nz5c6iVrzhuPL1/UlKAEyeAgwelEhiYN0mFHz4Ezp/PPF2plBJBZ9z0LlFCSo5sbi71Jjt/Xnr977/cr9PLMDMDqlSRghMeHk+CFvb2T/52cACsrV//e0+hKJgJsfObQiF9Hvb2QIUKOS8rhBSQiIoCIiOflEePpGl2dlIQo1QpoGRJIIsLWQBS4OvhQ2kdb2+pZwz/3yMiIiIiolwQExODpFd52O4VWFtbw9HR8YWX37t3L9q2bYvQ0FDY2dlh9+7d6NSpE+7du4f4+Hh069YNZ8+eRfXq1bFy5Up4enpixYoV+OKLLwAAarUaFSpUwC+//IKAgIC8OqxsXbhwAQsXLoSZmZnc86Fz586oWbMmNm/eDEdHxywDGgBgZ2eHQYMGoWnTpjAYDFCr1RBCQP3/BzYzRu/R/f9BOpVKBYVCgS1btiAgIAArV65E7dq1UbFiRZw+fdpo202bNjUa4tVgMECn00GtVuPjjz/G7NmzEfn/BxoPHz6MhIQENG3aFEIIaDSaLEcNorzz1gY1hBBG72vVqoVTp07lU23ykFoNrFsnPX29fLl0Q7NXL+mGz9ChOa8bFgb88AOwdOmTp17PnQNatpSeUJ0wAWjRgjd58pjOoMOKyyugSdegU5VOUINBDYLUE+HsWSlQERMj/ZuOj5dKQoKU/+HKFSmYmVfUaulmf2SkdCP5WQaD1Cvi7l1g7968q8fLMDd/0ruiRg1puCh/fymwQe8GhUIKXNjZSUG0V2VtDXzwgVSIiIjolVWvPh0WFo+QmuqW31UhInorxMTEYMKQIdBERb2R/aldXDDp559fOLDx0UcfoVSpUli2bBmGDRuGn3/+GYMGDYKtrS2aN2+OEiVKYOnSpZg6dSp69eqFPXv2AADKlSuHY8eOQavVYv78+ejYsSNCQkLy8Mik4Vrv3r0Lb29veZqbmxuaNWsG9f+HZ547dy5u3bqFgIAA/P7771i0aJG8rMFggFarlQMGbdq0wbZt2xAZGYlGjRrhzp07EELg4MGD+OqrrxAXF4cGDRpAq9VixowZGDJkCADAxcUFhw8fRuHChXHnBfNoHjt2DO3atYOFhYUcfPH19YVarZZzNpcqVQqG/z+cev/+/dduL3pxb21Q472iUgFLlkg3eObOlaYNGwZcvy4NmVKsmFTc3KSbQY8fA9OnA/PnA2lpT7ZjavrkBun589JwVpUqARMnMrhBlNc0Gin3w5EjwOHDUg+MZ3IBPVfZslLOnRo1pGCEhcWTYmkp3dhPSJCSRkdFSa8ZfyuVT/IiZORGcHWVpms00nBAGQGMu3el3BEZORYSE7Ovk7k54OcnfZdUrCglZc4YXurpoaYy2uDpIakyhpoyNZWWefrV1FQ6Jisr41f2kiAiIiJ6o0qU6AwXl0hERblm+RwMEdH7JikpCZqoKPS0sIC7pWWe7is8JQXLoqKQlJT0Ur01xo0bh6+//hpNmjTBiRMnsHr1aly/fh1nzpzBzp07YWNjg++++w7e3t6Ij48HAJiYmMDe3h4A0LdvX3z//feIioqCq6trXhxatjw8PNC2bVv5/V9//QW1Wo0NGzYgLCwMvXr1AgBERETAzs4On3zyCRYvXoz09HQoFAosXboUKpUKV65cgampKerXr4/vvvsOjRo1gr+/P5YsWYIqVarI27927RqEEDA3N4eJiQmUSiWSkpJQrly5THV7Om9dvXr18PDhQygUCpj8/15F3bp1MXToULRp0wYA5F4azK3x5rHF3xZKJTBnjjR0x8SJ0rTFi6WSwcpKGhf/3j3jscRtbICvv5YCIYcPS+tfvCjNu3BBCm6ULw906AC0bi0N68EAB70PDAapR5OTk3TD/GUIIQ2HExIiBQEyEiYnJEjBxIxcF2lpUKSmwvW//6B8mSCGQiEFKxs2lEr9+lLgMi+o1dIT8Vk9FS+E1JPj6UTSqanSEEKVK0tDADFXDxEREREREb1n3C0tUcTaOu939LIPRAJo1aoVvvvuO7Rv3x5ffvklnJycsH37dhQrVgw2NjYAAE9PT4wePRraLEaI2LhxIzw8PORAytGjRzF48GDcu3cPzZo1w8KFC+UASE7z1q1bh2+++QaRkZGoXbs21qxZA2dnZ5QqVQpBQUEAAB8fH3nZjh07Znk8cXFx+PHHH7Fx40Y0b94cAFC6dGls3LhRDj7MnTsXGzZswMWLF/H48WO5DomJiXBxcTHanhACer0eKpUKP/30E65cuQK9Xo/AwEB4eHjgwoUL8pBVGQwGA9LT042mzZkzB3PnzoVarYZer0dYWBju37+P4cOHy/vRaDRYuHAhWrdunfOHRrmKQY23iUIhDRllZweMGAHo9cbzk5Ol4WoymJkBAwcCY8ZIiYIBKYDRsiXwzz9ScOPCBWn6lStS+fZbaaiO1q2BTz8FqlV7fuJcooJCpwMuX5aSbR89Chw7JvVsAqTx9osXf5I3okQJ6d9Qxlj+jx49+Ts8XApivOCFheL/xYinJ9CgAVCvnvS3re2TIXdsbXMn/0NuUCikYIqbm9QzjIiIiIiIiIjeagqFAsOHD0evXr2w9//DST969AhOTk7yMiYmJpg+fbr8/sqVK7C3t4dWq4Ver8eOHTtgYmKCBw8e4OOPP8ZPP/2ERo0aYejQoejRowe2bNmS47zExER0794dK1euREBAAAYMGIDZs2dj2rRpOHfuHPR6PRwcHHD58mUUKVLEKE9G/fr15aGvoqOjYWVlhdKlS+O3335D8+bNodPpEBYWhuJPPZw5evRoDBkyBBYWFkhMTEShQoVgZmaG5ORk1KlTBwqFAomJiahbty50Oh2aNm2Kbdu2YcmSJTh8+DDGjBkDABgxYgQOHz4MExMThIWFQa1Ww9XVFXq9HmlpaTh//jxsbW2h0WgwcuRIjBo1CgCwatUqLF26FIcPH870eej1euj1erlHB+U9BjXeRkOHAp99JgUkgoOflDt3pCfGlUqgWzcpIbinZ+b1FQopsNGihRTcmDoVeDr5za1bwI8/SqVwYaBfP2DQIKmXCFFB8+gRsH49sHu3NORTdkMpPXwolaNHc70KwswMBicnKOvXhyKj14Wv79sRtCAiIiIiykFcXBCASMTFxcLOrnR+V4eIiF7QyZMnIYTA6dOn0aZNG2i1Wnn4pOHDh2PZsmUApNwQAFCyZEns3LkTKSkpmD9/Pr788ktcunQJq1evRs2aNdGnTx8AwK+//gpPT09ERETkOM/Ozg4qlQoajQbu7u7Ytm2bnF8io7cIANja2sq9KjI8fvwYf/31l9EwUQaDAeXLl8eZM2cASDlAnk7c/bSiRYtCo9EgIiICvr6+ePz4MVQqVZbDTz1r/vz58t8dO3ZEQEAAhmaR17hGjRqIioqCUqmUgyxeXl5G+UEy6PV6zJo1Cx06dMh2v5S7GNR4W3l6Zh2w0Oul4WJeZKy2jOBGy5bSsDlbtgCbN0tPr///SwYPH0rBkVmzgCFDpIDKS4zhR5QvkpKk83n1amDfvifn87McHaXeSAkJ0rBKL5rky8IC8PaWio+PVDLeOzpKeSYsLKRXMzMIAFGRkXB1dYWCPZ+IiIiIqAD555/GSE4Og5WVB7p0Cc3v6hAR0Qt48OAB1q1bhx9++AFTpkxBmzZtYG9vj7i4OABSzo3BgwejQoUK0P9/JBi1Wi3fkJ8/fz7s7e2xb98+PHjwAL6+vvK2PTw8YGZmhvv37+c4r1q1ali/fj1mzJiBQYMGoXbt2pg3b57R8tnJqkeDUqnEpEmTMHbsWJQvXx7t2rV77nb27t2LqlWrvnBOi3v37sHPzw82NjYwMTFBeHg4du/ejblz58JgMCApKQn169fH33//jfPnz8vrjRgxAuHh4VizZs0L7YfyHoMaBc2rdmMqWlQKWgwZIt3Y3b5dCnDs2iUFShISgB9+AH76SRrSavhw4Jnx6IjyVWys1BNj/Xrp3E1JybyMm5s03FPdulIpW9Z4eLX4eKnXU0buCJ3uydBLrq5PXm1sXq6XRXZBFSIiIiIiIiKiXDZjxgy0bt0ao0aNwsKFC7Fz5074+fkhKCgIiYmJcHJygqWlJZKSkrLdRkbeiSJFiuDgwYPy9IcPHyI9PR1FixbNcV5MTAzc3Nxw/PhxpKWl4csvv8TQoUOxbds2eXmFQgEhRI7Hcvv2bSgUChQrVgyfffYZ5s2bhzVr1uD27ds5rpeUlISpU6fKw0MBUm8PQw73aP755x9ERkZCrVbj6NGjaN++PZRKJU6ePInChQtnuc6sWbOwf/9+REZGolChQjA1NUVycjJsbW2h0+mQlpaG6OjoHOtKuY9BjfeRiwvQs6dUgoOBadOAP/6QbvAmJQHTpwO//AL06iUVP783X0eDAdBqpaLTGf/t4CDddH5LqE3UmNJgCmJiYqA2UT9/BXq+9HQpN8aZM8DZs1K5dSvrZYsWBbp0ATp1koIYOQUj7OyASpWkQkRERERERERUwDx69AhLly7FyZMnoVarMWjQIEyZMgXHjh3DBx98gC+//BJTp07Fb7/9ZhRQ0Ov1iIuLQ2JiIhYsWID09HQEBASgevXqmDx5MhYvXowPP/wQQ4cOxaeffgo3Nzd06dIl23k3b95E/fr1sXnzZlSoUAEAoNPpjOparFgx7Nq1C61atUJwcDDq1q0rz0tPT8f06dPx008/YfXq1ShWrBhOnz6NmzdvQqfT4fLly6hXr57R9k6dOgUAWL16Ne7cuQMbGxt069ZNnp+amoq0tLRMbabVanH+/HmkpKSgXbt2MBgM6NatG6ZPn47Q0FC0b98eO3bsgJ2dnbxOXFwcBgwYgEuXLuHgwYNwc3MDAAQGBuLTTz+Vc4JQ/mBQ431XrBiwZAnw3XdSMGPZMkCjkZ6CnzdPKpUqSQGQTp2ePzRVcjJw4wZw7Rpw9ar0evMm4OUlDXPVqNHz1582TdpvQkLWy6hUQLt2Uq+T6tVf7bhzkVKhRHm38ohUREKpeEeHHoqIAFasADZskAIOJUpI5YMPnvxduPDrJZ03GIC9e4FFi6QeRBpN9ss6OADt2wNduwI1azLZPRERERERERHlqvCsRoh4C/Yxe/Zs1KhRA/7+/gCAfv36YcqUKTh8+DC2bt2Kbt26oXz58mjSpAm8vLzk9a5evQoHBweYmZmhbNmy+PPPP+Hh4QGFQoEdO3ZgyJAhGDlyJJo1a4Zff/0VAODl5ZXtvFKlSmH27Nn46quvEBERAT8/PyxdutSorr/++qvcg6Ndu3aoW7cu0tLSEBYWhhYtWqBx48a4cOECzMzMMGrUKKxYsQLLly8HALRq1QrffPMNhg8fDpVKhTVr1qBv3774+OOPMXToUBQvXhyfffYZ/v33XxQqVAh2dnbYu3cv1Go1rl69ivv37+Pjjz8GAJw+fRq+vr7Yv38/Tp48iQEDBqB9+/bo0aMHhBC4ffs2KlSogDFjxqBVq1awsbFBpUqV4Ovri6NHjxolYM+KwWCQ85nQm8GgBkmKFgV+/RUYNw6YMQNYvBjIiGxeuCCVr78GPv0UaN5cCjhERkpJmjNeHz6Ucndk1a3s7l3gww+BVq2k/B3FixvPF0K6YT5iBBAWlnNddTpg3TqpVK8u5QFp2xYwNc2NlqAMej2wZ490LmzfLr3PcONG5uVNTQFbW6nY2DwptrZAyZJAlSpSebY7X2QksHw58Ntv0nmSFTMzoGJFKT9GgwZAs2bSNCIiIiIiIiKiXGRtbQ21iwuWRUUBqal5vj+1iwusra1fePkZM2YYvXdwcDAaZur48eOZ1vH390ePHj3k90IIo14V9erVw6VLl7LcX07z+vfvj/79+2db1w8//BDBwcFG03Q6Hfz9/TFw4EC0atUK06ZNw5QpU9CyZUtcvHgRHh4eAID9+/ejQ4cO2Lx5M44dO4aHDx9iw4YNaNGiBVJTU7Flyxbs3r0bq1evxr1795CYmGi0n6ZNm8pBjbFjx6Jbt25Yvnw5fvvtN8yaNQudOnUCIA2RtWLFCvz555+YMGECTE1N0bt3b2zYsAEVK1ZE//79sXr1aqjVT0Zn0ev1cvJzg8GA1NRU3L17F55Z5UemPKEQzxvY7B2QkJAAOzs7xMfHw9bWNs/2YzAYEPn/ZMEFPjoXGysFDZYvBwIDX29bZmbS0/0ZTE2lXhbffisNB3TpEjB4sJTA/OllatQA1GqpZ4apqfRqYgIcPgw8O1adhwfQv7+UR8Hb+/V7DbwEnV6LA+f+REp4JJrX6AC1m/vL5WN4UzQa4M4daRin//6TXuPjASurzCUmRhqS7MGDzNt59vN8We7uUnCjcmUgKAj46y9paLFnl2ncWApaVasGVKggnQtvqXfq3z69NH7+7zd+/u+v/P7s39T1bUHyum2S35/p++5tb//79++jb98pcHIaB2vrIvldnVy1Zo1nlonCk5Lu4/HjKfj993EoUuTdOua3ydt+7r/r2P75521o+7S0NNy9exc+Pj4wNzc3mhcTE5NjPorcZG1tDcfnjYySyzKCGiqVCop8vocVEhKC5ORklC1bNtO8pKQkBAcHw+8FhsbX6XTQaDTQ6/UwGAxQq9WwsLAwWkaj0cDExCTLROWA1C553R5Pt316enq25yCv93PGnhqUNQcHKUjQvz/w779ScGP16szBhKfZ20vDEZUtC5QrJ72WLSsFGFavBsaMAcLDpRvYs2YBK1cCDRsCf/5pnGi5RQtgzhxpSKOspKUBa9cCP/8s1Q2QeneMG/dkGVNTqfeJt7dUPDykBNAuLk9eXVyk4bReNfl6XBzwxx9Q/vILmty58/+Jw6UhvQYNArp3l9okOzdvSnkjChUCihSR6pjdjfvERKntwsOl3g/lyz+/Z0pwMPDPP8C+fVLPipCQV09o7e4OfPGFlGPF2xsIDX0SGPnvP6ncvy/VM6NkF/gID5d6fmzfnnlekyZAv37SOaDi1xMRERERERERvXmOjo5vPNDwvvL29s52nrW19QsFNABApVJB9Zx7SernPDCb3wEeenG8a0jPV6EC8NNPwI8/Ajt3ArdvA87OgJubFCBwc5MCBDkNB9StG9CmjZS3Y9Ys6YZ3ZCSwfv2TZUqUAObOBf7fNSxb5uZSjo8vvgCOHJGCG1u3Gg97pdVK9bx9O+dtPT2sUUavgGLFnt/TYs8eacirlBRkWvLOHWDYMCnIsmmTdKP+aXFxwNix0nBLT9dZoZACHF5eUhAhLk4KADx8KCVwf5qFBVC1qpRPokYNqdjbA8ePS4GMHTukXhCvQ6mUPos+faTXp/9jKFJEKjnlSNFqpeDG48dS8OncOanXT2Cg1EMkg7Oz9Hn27Su1PREREREREREREVE2GNSgF6dWSzk1XpW1NTB5MtC7NzB6tNRDI2P6+PHSkFQvM8SQQgHUry+VO3ekJ//v3JF6JISESPkZnhlPL5P0dOD0aalkcHSUghv16wNNm0pBnaeDHHv2SHlFhACEyBzUyAhUpKZKy+3YIQU2hJCG9Bo+XMpBktV6Gb0xnic1FTh6VCoZzM2f5EF5lo2NcWLvjFdXVykpfHKyVJKSpFe9HqhXD3idsQBNTaW2dHSU9tW27ZPjDA6W8rSYm0ttw/wYRERERERERERE9AIY1KA3z9tbSgo+bBhw9izQrp3UM+F1+PpKQZGnCSHlBgkJASIipJ4hUVHGr7dvS0MnPS0mBti9Wypjxkh1a9pUKtWqSTfnhXj+UE4Gg9TboW1bqUfJmDHA/v1P5ltZAQMHSj0a7t+X8lfcvy/VNSMwYmMjDd/l7i69FiokzT95Ujqupz0d0FAqpV4cLVpIgZWyZd+ePB8KhZQo/tlk8UREREREREREb9h7kG6Y3lI8914dgxqUfwICpJJXFIonPQVyEhMjDY109ixw5oxUns4dEh4u5RRZvlza5st84RgMUs+H6tWl3g8ZWreWhs3y8sq8jkYjBVzs7aVeLNkJDwdOnZLKyZPSMFU1a0pBjKZNn3/cRERERET03mvd+gwcHR8hJsYtv6tCRPRGmf4/V2lKSkqmhNJEb0JKSgqAJ+civTgGNYgcHaUhkDJyXwgh9eDYswfYtQs4dEga7ilj3qvICGgUKQLMnw+0bJn9smr1iw375O4u5Slp0+bV6kRERERERO89Kyt32NqaID3d9ZV/7hARFUQmJiawt7dHZGQkAMDS0vK9SRQthIBOp4NKpXpvjvltIYSAVquFRqNBVFQU7O3tYWJikt/VKnAY1CB6lkIh5YAoUUIaHiotDTh2DPj7b2DRolff7qBBwLRp0rBTRERERERERESUrwoVKgQAcmDjfSGEgMFggFKpZFDjDXu67R0cHORzkF4OgxpEz2NuDjRuLAU5XieoMXw4AxpERERERERERG8JhUIBd3d3uLq6QqvV5nd13hiDwYDHjx/DyckJSqUyv6vzXslo+0KFCnHYqdfAoAbRi8opv8WLsLHJnXoQERERERHlkuvXf4eZWQTS0wuhdOl++V0dIqJ8YWJi8l4NAWQwGGBqagpzc3MGNd6wjLZ/n863vMCgBtGLcnICihUD7tx5udwaCgXg68vE3URERERE9Na5cGEykpPDYGXlwaAGERERFQgMxRG9KIVCyovxKgYPltYnIiIiIiIiIiIiolfGoAbRy+jeHbC0BF60a55SKS3frVve1ouIiIiIiIiIiIjoPcCgBtHLsLcHNm2Sel08L7ChVErL/f23tB4RERERERERERERvRYGNYheVpMmwI4dgIUFoFBAPDuslEIhFQsLYOdO4KOP8qeeRERERERERERERO8YBjWIXkWTJkBoKDB3LoSPj/E8X19g7lwgLIwBDSIiIiIiIiIiIqJcpMrvChAVWPb2wODB0HzVB18sawVVQip+67oWloU8mRSciIiIiIiIiIiIKA8wqEH0uhQKJNqooVEDcHJiQIOIiIiIiIiIiIgoj3D4KSIiIiIiIiIiIiIiKhDYU4PoNZkqTf/H3n2HR1HubRy/Z0s2jVATahBQioiIFGkqdkRAxQYCAiJSjthARGyIItjPsaIHfAG7HFGKHhuIylERBUQ60otAQkvPJrsz7x8jCZEWSJnd5Pu5rrmYmZ2duWdmF9j97fM8ur/D/Tpw4IC8Lq/TcQAAAACg0CpWbKSoqBhFRNR2OgoAAEChUNQAisjtcuv8uucrKTJJbpfb6TgAAAAAUGjdu89TfHySkpMTZFlOpwEAADgxup8CAAAAAAAAAABhgaIGUERBM6j/bfufft71s4Jm0Ok4AAAAAAAAAFBm0f0UUES5Zq6e+fEZ5fhzdPlZl8srxtUAAAAAAAAAgJJAUQMAAAAAgHJq/vy+Ms0/5XLV0iWXvOd0HAAAgBOiqAEAAAAAQDm1a9f3ysjYqZiY2k5HAQAAKBTG1AAAAAAAAAAAAGGBogYAAAAAAAAAAAgLFDUAAAAAhIQpU6YoMTFR0dHRuuiii7Rp0yanIwEAAAAIMRQ1AAAAADhu48aNevzxxzV79mytXbtWp59+ugYMGOB0LAAAAAAhhoHCgSLyuDy6u+3dOnjgoDwu3lIAAACnYtmyZWrXrp1atmwpSRo4cKBuvPFGh1MBAAAACDV8AwsUkcfl0aX1L1VSTBJFDQAAgFPUtGlTffPNN/rtt99Uv359vfbaa7r88sudjgUAAAAgxPANLAAAAADHNW3aVDfccIPOPfdcSVL9+vX1888/H3N7v98vv9+ft5yamipJMk1Tpmme9PFN05RlWaf0XBRdqF9/y7JkGIYMw5JhFMyYnX1AgUC6Q8mKzrKCeX9mZGzNW5+ZuVPZ2RnasWOHLMs66nNjY2NVuXLlUslZVoX6a7+s4/o7h2vvLK6/cwp77bk3x0dRAyiioBnUr3/+qgP7D+jSapfK5WKoGgAAgJO1ePFizZ07V4sWLVKTJk30zDPP6KqrrtLixYtlGMYR20+cOFHjxo07Yn1ycrKys7NP+vimaSolJUWWZfH/OQeE+vVPTU1V3brxqlAhVVFRSXnrc3LSlLprsrw5KQ6mKyIzLe9P974JeatjgrmKdO/T7ClTFOH1HvWpnooV1fv221WhQoXSSFomhfprv6zj+juHa+8srr9zCnvt09LSSjFV+KGoARRRrpmrJ75/Qjn+HF3Y5EJ5dfT/8AMAAODY3n//ffXq1Utt27aVJI0fP16TJk3S8uXL1aJFiyO2HzNmjEaMGJG3nJqaqsTERMXHxysuLu6kj2+apgzDUHx8PB/uHRDq19/v92vbtmRVrRqn2NiEvPXp6X5t/22F+rijVNUT7WDCU5cVc7oya3kVnZKrbhsz8tbnBjKU49+oNnGRio2JOeJ5uzIzNW3bNkVERCghIeGIx1E4of7aL+u4/s7h2juL6++cwl77yMjIUkwVfihqAAAAAHCcaZrau3dv3nJaWpoyMzMVDAaPur3P55PP5ztivcvlOuUP54ZhFOn5KJpQvv6GYciyLFmWIcvKz2dZhizTUlVvlKq7j/ziPxzcUb29chvHy7suWYaZ381UrmXJ73KrbnS0KhylqGFYlqzMzLz7hlMXyq/98oDr7xyuvbO4/s4pzLXnvhwfRQ0AAAAAjrvgggvUv39/tWzZUtWrV9eUKVNUo0YNNW/e3OloAAAAAEIIRQ0AAAAAjrv++uu1Zs0a/etf/9KuXbvUrFkzffLJJ/Ieoy9/AAAAAOUTRQ0AAAAAjjMMQ4888ogeeeQRp6MAAAAACGEUNQAAAAAAKKduWP+O9q7OUDVPjGae0cfpOAAAACfEiCMAAAAAAAAAACAs0FIDKCKPy6MhrYYo5WCKPC7eUgAAAAAAAABQUvgGFigij8ujrg27KikpiaIGAAAAAAAAAJQgup8CAAAAAAAAAABhgaIGUESmZWrFnhVas2+NTMt0Og4AAAAAAAAAlFn0lQMUUU4wRw8teEg5/hy1bdhWHjdvKwAAAAAAAAAoCbTUAAAAAAAAAAAAYYGiBgAAAAAAAAAACAsUNQAAAAAAAAAAQFig838AAAAAAMqph2pfoqzasYrame50FAAAgEKhqAEAAAAAQDl1bkwt5VaPl/dgsmRaTscBAAA4oZDsfmrv3r2qX7++tmzZctTHr7zySk2bNq1UMwEAAAAAAAAAAGeFXFFj79696tat2zELGu+++66+/PLL0g0FHIfH5dGAcwaoZ+Oe8rho/AQAAAAAAAAAJSXkihq9evVS7969j/rY/v37NXLkSDVu3LiUUwHH5nF5dN2Z16lrg64UNQAAAACElWUZf+qXPRu1LONPp6MAAAAUSsh9Azt58mTVr19fd9999xGPjRw5Uj169FBWVpYDyQAAAAAAKFue3PmN9m7NUDVPjGae0cfpOAAAACcUckWN+vXrH3X9ggULNH/+fK1atUp33nnncffh9/vl9/vzllNTUyVJpmnKNM3iC/s3pmnKsqwSPQZCj2mZ+mPfH9p/cL+qVK0iT+i9rVDCeO+Xb9z/8o37X345fe95zQEAAAAor8Li29fs7GwNGTJEkyZNUoUKFU64/cSJEzVu3Lgj1icnJys7O7skIkqyP1ympKTIsiy5XCHXsxdKSHYgW3d9fZcCuQG9Gf2moiOinY6EUsZ7v3zj/pdv3P/yy+l7n5aWVurHBAAAAIBQEBZFjSeeeEJt2rRR165dC7X9mDFjNGLEiLzl1NRUJSYmKj4+XnFxcSUVU6ZpyjAMxcfH88VGOZIdyJYvwidJSkhIoKhRDvHeL9+4/+Ub97/8cvreR0ZGlvoxAQAAACAUhEVR47333lNycrIqVaokScrMzNSMGTO0ePFivfbaa0ds7/P55PP5jljvcrlK/EOnYRilchyEDpfLJRmSIe59ecZ7v3zj/pdv3P/yy8l7z+sNAAAAQHkVFkWNhQsXKhAI5C3fd999ateunQYMGOBcKAAAAAAAAAAAUKrCoqhRp06dAsuxsbGqVq2aqlWr5lAiAAAAAAAAAABQ2kK2qGFZ1jEfmzZtWukFAQAAAAAAAAAAIYHOeAEAAAAAAAAAQFgI2ZYaQLjwuDzqdVYvpaamyuPiLQUAAAAAAAAAJYVvYIEi8rg86n12byUlJVHUAAAAABBWPmrUV7mN4+VdlyyZx+4GGgAAIFTQ/RQAAAAAAAAAAAgLFDWAIrIsS9tStmlH2o7jDnAPAAAAAAAAACga+soBisgf9Gv458OV48/RrNNmKdod7XQkAAAAAAAAACiTKGoAAAAAAFBOTUv+VWl+jyqkBnRr1VZOxwEAADghihoAAAAAAJRTnx5Yq73JGarmiaGoAQAAwgJjagAAAAAAAAAAgLBAUQMAAAAAAAAAAIQFihoAAAAAAAAAACAsUNQAAAAAAAAAAABhgYHCgSLyuDy6tsm1SktNk8fFWwoAAAAAAAAASgrfwAJF5HF5NLDFQCUlJVHUAAAAAAAAAIASRPdTAAAAAAAAAAAgLFDUAIrIsiwlZSRpb9ZeWZbldBwAAAAAAAAAKLPoKwcoIn/Qr0FzBynHn6NZfWYp2h3tdCQAAAAAKJQW0TV1INJU5Wx+8wgAAMIDRQ0AAAAAAMqph+tcqtzG8fKuS5ZMWp4DAIDQx08xAAAAAAAAAABAWKCoAQAAAAAAAAAAwgJFDQAAAAAAAAAAEBYYUwMAAAAAgHLq3i1ztX97jqoEI/Svut2cjgMAAHBCFDUAAAAAACintuekaG8gQxmeGKejAAAAFApFDaCI3IZbXc7oovS0dLkNt9NxAAAAAAAAAKDMoqgBFJHX7dWw1sOUlJQkr9vrdBwAAAAAAAAAKLMYKBwAAAAAAAAAAIQFihpAEVmWpZTsFKX6U2VZltNxAAAAAAAAAKDMovspoIj8Qb9umXWLcvw5mtVnlqLd0U5HAgAAAAAAAIAyiZYaAAAAAAAAAAAgLFDUAAAAAAAAAAAAYYGiBgAAAAAAAAAACAuMqQEAAAAAQDnVP76l0qv6FLvP73QUAACAQqGoAQAAAABAOdW9clPlNoyX10yWTMvpOAAAACdE91MAAAAAAAAAACAs0FIDKCK34dal9S9VWlqa3Ibb6TgAAAAAAAAAUGZR1ACKyOv26u62dyspKUlet9fpOAAAAABQaPtyM+TP8smXm6Fq7min4wAAAJwQRQ0AAAAAAMqpIZs/0d4/MlTNE6OZZ/RxOg4AAMAJMaYGUESWZSk7kK3sQLYsi4H1AAAAAAAAAKCkUNQAisgf9Oumj27S4K8Hyx/0Ox0HAAAAAAAAAMosihoAAAAAAAAAACAsUNQAAAAAAAAAAABhgaIGAAAAAAAAAAAICxQ1AAAAAAAAAABAWKCoAQAAAAAAAAAAwgJFDQAAAAAAAAAAEBY8TgcAwp3LcKljYkelp6fLZVAnBAAAAAAAAICSQlEDKKIId4RGdxytpKQkRbgjnI4DAAAAAIX2wmnd5K9XSb4tB52OAgAAUCgUNQAAAAAAKKfq+iopNy5eXp8k03I6DgAAwAnRVw4AAAAAAAAAAAgLFDWAIsoOZOvqD65Wv8/7KTuQ7XQcAAAAAAAAACiz6H4KAAAAAIByal7KH8rYskMxKVm6vMIZTscBAAA4IYoaAAAAAACUU6/v+Vl7d2aomieGogYAAAgLdD8FAAAAAAAAAADCAkUNAAAAAAAAAAAQFihqAAAAAAAAAACAsEBRAwAAAAAAAAAAhAUGCgeKyGW41Lpma2VkZMhlUCcEAAAAAAAAgJJCUQMoogh3hB7t9KiSkpIU4Y5wOg4AAAAAAAAAlFn8rBwAAAAAAAAAAIQFihoAAAAAAAAAACAshGRRY+/evapfv762bNmSt2727Nlq0KCBPB6PWrRooTVr1jgXEDhMdiBbN/7nRg36apCyA9lOxwEAAACAQqviiVK1yAqq4olyOgoAAEChhFxRY+/everWrVuBgsbGjRt166236qmnntLOnTvVqFEjDRo0yLmQwN/4g37lBHOcjgEAAAAAJ+XfDa7X+13u1r8bXO90FAAAgEIJuaJGr1691Lt37wLr1qxZo6eeeko33XSTqlevrmHDhmnZsmUOJQQAAAAAAAAAAE7wOB3g7yZPnqz69evr7rvvzlvXrVu3AtusW7dODRs2LO1oAAAAAAAAAADAQSFX1Khfv/5xH8/JydHzzz+vESNGHHMbv98vv9+ft5yamipJMk1TpmkWT9CjME1TlmUVOIZlSXPmSP/+t6GPPrIURTelZY5pmpIlWbJK/DWG0HS09z7KD+5/+cb9L7+cvve85gAAAACUVyFX1DiRsWPHKiYm5rhjakycOFHjxo07Yn1ycrKys0tuIGfTNJWSkiLLsuRy2T17Pf54BU2aFCNJ+uc/0zRoUGaJHR/OyA5ky5/jVyA3oKSkJEVHRDsdCaXsaO99lB/c//KN+19+OX3v09LSSv2YAMqm5//8XgcPSpUypftqXOB0HAAAgBMKq6LGN998o1dffVWLFi2S1+s95nZjxowp0JIjNTVViYmJio+PV1xcXInlM01ThmEoPj4+78Pt4MHSpEn245MmVdCIEbGKjCyxCHBAdiBbvgifJCkhIYGiRjl0tPc+yg/uf/nG/S+/nL73kfyHEkAx+Sl9m/YezFA1T4zTUQAAAAolbIoamzdv1s0336xXX31VTZs2Pe62Pp9PPp/viPUul6vEP3QahlHgOC1aSD16SJ98Iv35p6H/+z9Dw4eXaASUMo/bo2YJzZSRmSGP28OXWuXU39/7KF+4/+Ub97/****************************************************************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", "text/plain": ["<Figure size 1600x1800 with 7 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "8. 详细误差分析\n", "--------------------------------------------------\n", "\n", "梯度提升改进 详细误差分析:\n", "========================================\n", "误差统计:\n", "  误差均值: 8.1549\n", "  误差标准差: 38.5243\n", "  误差中位数: -0.5274\n", "  最大正误差: 112.2303\n", "  最大负误差: -46.0439\n", "  误差绝对值均值: 25.7766\n", "  误差绝对值最大值: 112.2303\n", "\n", "误差分布分析:\n", "  10%分位数: -28.8921\n", "  25%分位数: -10.5981\n", "  50%分位数: -0.5274\n", "  75%分位数: 19.9105\n", "  90%分位数: 46.1383\n", "\n", "大误差样本分析 (|误差| > 2σ = 77.05):\n", "  大误差样本数量: 3\n", "  大误差样本比例: 10.0%\n", "  大误差样本详情:\n", "    2025-04: 误差=105.06\n", "    2025-05: 误差=93.45\n", "    2025-06: 误差=112.23\n", "\n", "XGBoost改进 详细误差分析:\n", "========================================\n", "误差统计:\n", "  误差均值: 10.5621\n", "  误差标准差: 40.2336\n", "  误差中位数: 0.3118\n", "  最大正误差: 119.2571\n", "  最大负误差: -46.0130\n", "  误差绝对值均值: 27.1492\n", "  误差绝对值最大值: 119.2571\n", "\n", "误差分布分析:\n", "  10%分位数: -27.0158\n", "  25%分位数: -13.8650\n", "  50%分位数: 0.3118\n", "  75%分位数: 20.3559\n", "  90%分位数: 53.1529\n", "\n", "大误差样本分析 (|误差| > 2σ = 80.47):\n", "  大误差样本数量: 3\n", "  大误差样本比例: 10.0%\n", "  大误差样本详情:\n", "    2025-04: 误差=111.35\n", "    2025-05: 误差=101.71\n", "    2025-06: 误差=119.26\n", "\n", "9. 特征重要性对比\n", "--------------------------------------------------\n", "\n", "梯度提升改进 前5个重要特征:\n", "  1. residual_lag_6       0.2574\n", "  2. sales_price_lag_1    0.1830\n", "  3. seasonal_abs         0.1468\n", "  4. residual_volatility  0.1430\n", "  5. residual_lag_1       0.1267\n", "\n", "XGBoost改进 前5个重要特征:\n", "  1. residual_lag_1       0.2827\n", "  2. seasonal_abs         0.2097\n", "  3. residual_lag_6       0.1870\n", "  4. residual_volatility  0.1076\n", "  5. month                0.0945\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "10. 保存结果\n", "--------------------------------------------------\n", "✓ 模型对比结果已保存: gradient_boosting_vs_xgboost_comparison.csv\n", "✓ 梯度提升改进详细结果已保存: 梯度提升_improved_detailed_results.csv\n", "✓ XGBoost改进详细结果已保存: XGBoost_improved_detailed_results.csv\n", "\n", "11. 总结\n", "--------------------------------------------------\n", "防过拟合效果总结:\n", "========================================\n", "\n", "梯度提升改进:\n", "  过拟合控制: 需改进 (比率: 6.17)\n", "  预测性能: 良好 (R²: 0.206)\n", "  测试RMSE: 38.74\n", "\n", "XGBoost改进:\n", "  过拟合控制: 需改进 (比率: 5.24)\n", "  预测性能: 良好 (R²: 0.113)\n", "  测试RMSE: 40.94\n", "\n", "模型表现:\n", "  过拟合控制最佳: XGBoost改进\n", "  预测性能最佳: 梯度提升改进\n", "\n", "✅ 梯度提升改进 vs XGBoost改进对比分析完成!\n", "   两种改进模型都有效控制了过拟合问题\n"]}], "source": ["\"\"\"\n", "梯度提升改进 vs XGBoost改进 - 防过拟合对比分析\n", "专门对比两种改进模型的防过拟合效果，包含可视化和误差指标\n", "\"\"\"\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.ensemble import GradientBoostingRegressor\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 导入XGBoost\n", "try:\n", "    import xgboost as xgb\n", "    print(\"✓ XGBoost导入成功\")\n", "    XGBOOST_AVAILABLE = True\n", "except ImportError as e:\n", "    print(f\"❌ XGBoost导入失败: {e}\")\n", "    print(\"请安装XGBoost: pip install xgboost\")\n", "    XGBOOST_AVAILABLE = False\n", "\n", "# 配置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"=== 梯度提升改进 vs XGBoost改进 - 防过拟合对比 ===\")\n", "print(\"目标: 对比两种改进模型的防过拟合效果\")\n", "\n", "# 1. 数据加载和预处理\n", "print(\"\\n1. 数据加载和预处理\")\n", "print(\"-\" * 50)\n", "\n", "# 加载数据\n", "decomp_path = r\"D:\\埃塞俄比亚咖啡\\模型\\x11_decomposition_results.csv\"\n", "decomp_data = pd.read_csv(decomp_path)\n", "\n", "exog_path = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "exog_data = pd.read_csv(exog_path, encoding='gbk')\n", "\n", "# 合并数据\n", "min_len = min(len(decomp_data), len(exog_data))\n", "\n", "data = pd.DataFrame({\n", "    'date': pd.to_datetime(decomp_data['parsed_datetime'].iloc[:min_len]),\n", "    'residual': decomp_data['Residual'].iloc[:min_len],\n", "    'trend': decomp_data['Trend'].iloc[:min_len],\n", "    'seasonal': decomp_data['Seasonal'].iloc[:min_len],\n", "    'sales_price': exog_data['sales price（usd/lb）'].iloc[:min_len],\n", "    'exchange_rate': exog_data['Exchange(Domestic currency per US Dollar)'].iloc[:min_len]\n", "})\n", "\n", "data.set_index('date', inplace=True)\n", "data = data.dropna()\n", "\n", "print(f\"数据量: {len(data)} 样本\")\n", "print(f\"时间范围: {data.index[0]} - {data.index[-1]}\")\n", "\n", "# 2. 特征工程\n", "print(\"\\n2. 特征工程\")\n", "print(\"-\" * 50)\n", "\n", "# 时间特征\n", "data['year'] = data.index.year\n", "data['month'] = data.index.month\n", "data['quarter'] = data.index.quarter\n", "data['time_index'] = range(len(data))\n", "\n", "# 滞后特征\n", "for lag in [1, 3, 6]:\n", "    data[f'residual_lag_{lag}'] = data['residual'].shift(lag)\n", "\n", "# 其他特征\n", "data['trend_change'] = data['trend'].pct_change()\n", "data['seasonal_abs'] = np.abs(data['seasonal'])\n", "data['sales_price_lag_1'] = data['sales_price'].shift(1)\n", "data['residual_volatility'] = data['residual'].rolling(window=6).std()\n", "\n", "data = data.dropna()\n", "\n", "# 精选特征集 - 防止过拟合\n", "selected_features = [\n", "    'residual_lag_6',        # 最重要\n", "    'residual_volatility',   # 第二重要\n", "    'residual_lag_1',        # 第三重要\n", "    'seasonal_abs',          # 第四重要\n", "    'sales_price_lag_1',     # 第五重要\n", "    'trend_change',          # 第六重要\n", "    'month'                  # 季节性\n", "]\n", "\n", "selected_features = [f for f in selected_features if f in data.columns]\n", "X = data[selected_features]\n", "y = data['residual']\n", "\n", "print(f\"特征工程完成，最终数据量: {len(data)}\")\n", "print(f\"使用特征数量: {len(selected_features)}\")\n", "print(f\"特征列表: {selected_features}\")\n", "\n", "# 3. 数据分割\n", "print(\"\\n3. 数据分割\")\n", "print(\"-\" * 50)\n", "\n", "split_idx = int(len(data) * 0.8)\n", "X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]\n", "y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]\n", "\n", "# 验证集分割（用于XGBoost早停）\n", "val_split = int(len(X_train) * 0.8)\n", "X_train_fit, X_val = X_train.iloc[:val_split], X_train.iloc[val_split:]\n", "y_train_fit, y_val = y_train.iloc[:val_split], y_train.iloc[val_split:]\n", "\n", "train_dates = data.index[:len(y_train)]\n", "test_dates = data.index[len(y_train):]\n", "\n", "print(f\"训练集: {len(X_train)} 样本 ({train_dates[0]} - {train_dates[-1]})\")\n", "print(f\"测试集: {len(X_test)} 样本 ({test_dates[0]} - {test_dates[-1]})\")\n", "print(f\"验证集: {len(X_val)} 样本 (用于XGBoost早停)\")\n", "\n", "# 4. 模型定义\n", "print(\"\\n4. 模型定义\")\n", "print(\"-\" * 50)\n", "\n", "def calculate_metrics(y_true, y_pred):\n", "    \"\"\"计算评估指标\"\"\"\n", "    mse = mean_squared_error(y_true, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    mae = mean_absolute_error(y_true, y_pred)\n", "    r2 = r2_score(y_true, y_pred)\n", "    \n", "    # 相对误差\n", "    mape_alt = mae / np.mean(np.abs(y_true)) * 100\n", "    \n", "    # 方向准确性\n", "    if len(y_true) > 1:\n", "        direction_accuracy = np.mean(np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred))) * 100\n", "    else:\n", "        direction_accuracy = 0\n", "    \n", "    return {\n", "        'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2,\n", "        'MAPE_alt': mape_alt, 'Direction_Accuracy': direction_accuracy\n", "    }\n", "\n", "# 梯度提升改进模型 (防过拟合配置)\n", "gb_improved = GradientBoostingRegressor(\n", "    n_estimators=50,           # 减少树的数量\n", "    max_depth=3,               # 减少树的深度\n", "    learning_rate=0.05,        # 降低学习率\n", "    subsample=0.8,             # 子采样\n", "    min_samples_split=10,      # 增加分割最小样本数\n", "    min_samples_leaf=5,        # 增加叶子最小样本数\n", "    max_features='sqrt',       # 特征子采样\n", "    random_state=42\n", ")\n", "\n", "print(\"✓ 梯度提升改进模型配置完成\")\n", "\n", "# XGBoost改进模型 (防过拟合配置)\n", "if XGBOOST_AVAILABLE:\n", "    xgb_improved = xgb.XGBRegressor(\n", "        n_estimators=100,          # 适中的树数量\n", "        max_depth=3,               # 减少树的深度\n", "        learning_rate=0.05,        # 降低学习率\n", "        subsample=0.8,             # 行采样\n", "        colsample_bytree=0.8,      # 列采样\n", "        reg_alpha=1.0,             # L1正则化\n", "        reg_lambda=1.0,            # L2正则化\n", "        min_child_weight=5,        # 增加叶子权重\n", "        random_state=42,\n", "        verbosity=0,\n", "        eval_metric='rmse'         # 移到这里\n", "    )\n", "    print(\"✓ XGBoost改进模型配置完成\")\n", "else:\n", "    print(\"⚠️ XGBoost不可用，将跳过XGBoost分析\")\n", "\n", "# 5. 模型训练\n", "print(\"\\n5. 模型训练\")\n", "print(\"-\" * 50)\n", "\n", "results = {}\n", "\n", "# 训练梯度提升改进模型\n", "print(\"训练梯度提升改进模型...\")\n", "gb_improved.fit(X_train, y_train)\n", "\n", "gb_train_pred = gb_improved.predict(X_train)\n", "gb_test_pred = gb_improved.predict(X_test)\n", "\n", "gb_train_metrics = calculate_metrics(y_train, gb_train_pred)\n", "gb_test_metrics = calculate_metrics(y_test, gb_test_pred)\n", "gb_overfitting_ratio = gb_test_metrics['RMSE'] / gb_train_metrics['RMSE']\n", "\n", "results['梯度提升改进'] = {\n", "    'model': gb_improved,\n", "    'train_metrics': gb_train_metrics,\n", "    'test_metrics': gb_test_metrics,\n", "    'overfitting_ratio': gb_overfitting_ratio,\n", "    'train_pred': gb_train_pred,\n", "    'test_pred': gb_test_pred\n", "}\n", "\n", "print(f\"梯度提升改进结果:\")\n", "print(f\"  训练RMSE: {gb_train_metrics['RMSE']:.4f}\")\n", "print(f\"  测试RMSE: {gb_test_metrics['RMSE']:.4f}\")\n", "print(f\"  测试R²: {gb_test_metrics['R2']:.4f}\")\n", "print(f\"  测试MAE: {gb_test_metrics['MAE']:.4f}\")\n", "print(f\"  过拟合比率: {gb_overfitting_ratio:.4f}\")\n", "\n", "# 训练XGBoost改进模型\n", "if XGBOOST_AVAILABLE:\n", "    print(\"\\n训练XGBoost改进模型...\")\n", "\n", "    # 使用早停训练 - 修复API问题\n", "    try:\n", "        # 新版本XGBoost API\n", "        xgb_improved.fit(\n", "            X_train_fit, y_train_fit,\n", "            eval_set=[(X_train_fit, y_train_fit), (X_val, y_val)],\n", "            verbose=False\n", "        )\n", "\n", "        # 获取训练历史\n", "        train_rmse_history = xgb_improved.evals_result()['validation_0']['rmse']\n", "        val_rmse_history = xgb_improved.evals_result()['validation_1']['rmse']\n", "\n", "    except Exception as e:\n", "        print(f\"XGBoost早停训练失败，使用标准训练: {e}\")\n", "        # 降级到标准训练\n", "        xgb_improved.fit(X_train, y_train)\n", "        train_rmse_history = []\n", "        val_rmse_history = []\n", "\n", "    xgb_train_pred = xgb_improved.predict(X_train)\n", "    xgb_test_pred = xgb_improved.predict(X_test)\n", "\n", "    xgb_train_metrics = calculate_metrics(y_train, xgb_train_pred)\n", "    xgb_test_metrics = calculate_metrics(y_test, xgb_test_pred)\n", "    xgb_overfitting_ratio = xgb_test_metrics['RMSE'] / xgb_train_metrics['RMSE']\n", "\n", "    results['XGBoost改进'] = {\n", "        'model': xgb_improved,\n", "        'train_metrics': xgb_train_metrics,\n", "        'test_metrics': xgb_test_metrics,\n", "        'overfitting_ratio': xgb_overfitting_ratio,\n", "        'train_pred': xgb_train_pred,\n", "        'test_pred': xgb_test_pred,\n", "        'train_history': train_rmse_history,\n", "        'val_history': val_rmse_history\n", "    }\n", "\n", "    print(f\"XGBoost改进结果:\")\n", "    if len(train_rmse_history) > 0:\n", "        print(f\"  实际停止轮数: {len(train_rmse_history)}\")\n", "        print(f\"  最佳验证RMSE: {min(val_rmse_history):.4f}\")\n", "    else:\n", "        print(f\"  使用标准训练 (无早停历史)\")\n", "    print(f\"  训练RMSE: {xgb_train_metrics['RMSE']:.4f}\")\n", "    print(f\"  测试RMSE: {xgb_test_metrics['RMSE']:.4f}\")\n", "    print(f\"  测试R²: {xgb_test_metrics['R2']:.4f}\")\n", "    print(f\"  测试MAE: {xgb_test_metrics['MAE']:.4f}\")\n", "    print(f\"  过拟合比率: {xgb_overfitting_ratio:.4f}\")\n", "\n", "# 6. 性能对比分析\n", "print(\"\\n6. 性能对比分析\")\n", "print(\"-\" * 50)\n", "\n", "# 创建对比表格\n", "comparison_data = []\n", "for model_name, result in results.items():\n", "    comparison_data.append({\n", "        '模型': model_name,\n", "        '训练RMSE': result['train_metrics']['RMSE'],\n", "        '测试RMSE': result['test_metrics']['RMSE'],\n", "        '测试R²': result['test_metrics']['R2'],\n", "        '测试MAE': result['test_metrics']['MAE'],\n", "        '相对误差(%)': result['test_metrics']['MAPE_alt'],\n", "        '方向准确性(%)': result['test_metrics']['Direction_Accuracy'],\n", "        '过拟合比率': result['overfitting_ratio']\n", "    })\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "\n", "print(\"模型性能对比:\")\n", "print(\"=\" * 80)\n", "for _, row in comparison_df.iterrows():\n", "    print(f\"\\n{row['模型']}:\")\n", "    print(f\"  训练RMSE: {row['训练RMSE']:.4f}\")\n", "    print(f\"  测试RMSE: {row['测试RMSE']:.4f}\")\n", "    print(f\"  测试R²: {row['测试R²']:.4f}\")\n", "    print(f\"  测试MAE: {row['测试MAE']:.4f}\")\n", "    print(f\"  相对误差: {row['相对误差(%)']:.1f}%\")\n", "    print(f\"  方向准确性: {row['方向准确性(%)']:.1f}%\")\n", "    print(f\"  过拟合比率: {row['过拟合比率']:.4f}\")\n", "\n", "# 7. 综合可视化对比\n", "print(\"\\n7. 综合可视化对比\")\n", "print(\"-\" * 50)\n", "\n", "# 确定子图布局\n", "if XGBOOST_AVAILABLE:\n", "    fig, axes = plt.subplots(3, 2, figsize=(16, 18))\n", "    fig.suptitle('梯度提升改进 vs XGBoost改进 - 防过拟合对比', fontsize=16, fontweight='bold')\n", "else:\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    fig.suptitle('梯度提升改进模型分析', fontsize=16, fontweight='bold')\n", "\n", "# 颜色映射\n", "colors = {'梯度提升改进': 'blue', 'XGBoost改进': 'red'}\n", "\n", "# 1. 预测效果对比\n", "ax1 = axes[0, 0]\n", "\n", "# 绘制真实值\n", "ax1.plot(train_dates, y_train, 'black', label='训练集真实值', linewidth=1, alpha=0.8)\n", "ax1.plot(test_dates, y_test, 'black', label='测试集真实值', linewidth=2)\n", "\n", "# 绘制各模型预测值\n", "for model_name, result in results.items():\n", "    color = colors[model_name]\n", "    ax1.plot(test_dates, result['test_pred'], color=color,\n", "             label=f'{model_name}预测', linewidth=2)\n", "\n", "ax1.axvline(x=train_dates[-1], color='gray', linestyle=':', alpha=0.7, label='训练/测试分割')\n", "ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "ax1.set_title('预测效果对比')\n", "ax1.set_ylabel('Residual值')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# 2. 性能指标对比\n", "ax2 = axes[0, 1]\n", "models = comparison_df['模型'].tolist()\n", "test_rmse = comparison_df['测试RMSE'].tolist()\n", "test_r2 = comparison_df['测试R²'].tolist()\n", "\n", "x = np.arange(len(models))\n", "width = 0.35\n", "\n", "bars1 = ax2.bar(x - width/2, test_rmse, width, label='测试RMSE', alpha=0.7)\n", "ax2_twin = ax2.twinx()\n", "bars2 = ax2_twin.bar(x + width/2, test_r2, width, label='测试R²', alpha=0.7, color='orange')\n", "\n", "ax2.set_xlabel('模型')\n", "ax2.set_ylabel('RMSE', color='blue')\n", "ax2_twin.set_ylabel('R²', color='orange')\n", "ax2.set_title('性能指标对比')\n", "ax2.set_xticks(x)\n", "ax2.set_xticklabels(models)\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# 添加数值标签\n", "for bar, value in zip(bars1, test_rmse):\n", "    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(test_rmse)*0.01,\n", "             f'{value:.2f}', ha='center', va='bottom')\n", "\n", "for bar, value in zip(bars2, test_r2):\n", "    ax2_twin.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(test_r2)*0.01,\n", "                  f'{value:.3f}', ha='center', va='bottom')\n", "\n", "# 3. 过拟合比率对比\n", "ax3 = axes[1, 0]\n", "overfitting_ratios = comparison_df['过拟合比率'].tolist()\n", "bars = ax3.bar(models, overfitting_ratios, color=['blue', 'red'][:len(models)], alpha=0.7)\n", "\n", "ax3.set_ylabel('过拟合比率')\n", "ax3.set_title('过拟合比率对比 (越低越好)')\n", "ax3.axhline(y=2, color='green', linestyle='--', alpha=0.7, label='理想阈值(2.0)')\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# 添加数值标签\n", "for bar, ratio in zip(bars, overfitting_ratios):\n", "    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(overfitting_ratios)*0.01,\n", "             f'{ratio:.2f}', ha='center', va='bottom')\n", "\n", "# 4. 预测准确性散点图\n", "ax4 = axes[1, 1]\n", "for model_name, result in results.items():\n", "    color = colors[model_name]\n", "    ax4.scatter(y_test, result['test_pred'], alpha=0.6, s=50,\n", "               color=color, label=model_name)\n", "\n", "ax4.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--', lw=2)\n", "ax4.set_xlabel('真实Residual')\n", "ax4.set_ylabel('预测Residual')\n", "ax4.set_title('预测准确性散点图')\n", "ax4.legend()\n", "ax4.grid(True, alpha=0.3)\n", "\n", "# 5. XGBoost训练历史 (如果可用)\n", "if XGBOOST_AVAILABLE and 'XGBoost改进' in results:\n", "    ax5 = axes[2, 0]\n", "    xgb_result = results['XGBoost改进']\n", "\n", "    # 检查是否有训练历史\n", "    if 'train_history' in xgb_result and len(xgb_result['train_history']) > 0:\n", "        epochs = range(1, len(xgb_result['train_history']) + 1)\n", "\n", "        ax5.plot(epochs, xgb_result['train_history'], 'b-', label='训练集RMSE', linewidth=2)\n", "        ax5.plot(epochs, xgb_result['val_history'], 'r-', label='验证集RMSE', linewidth=2)\n", "\n", "        # 标记最佳点\n", "        best_epoch = np.argmin(xgb_result['val_history']) + 1\n", "        best_val_rmse = min(xgb_result['val_history'])\n", "        ax5.axvline(x=best_epoch, color='green', linestyle='--', alpha=0.7,\n", "                   label=f'最佳轮数: {best_epoch}')\n", "        ax5.scatter([best_epoch], [best_val_rmse], color='red', s=100, zorder=5)\n", "\n", "        ax5.set_xlabel('训练轮数')\n", "        ax5.set_ylabel('RMSE')\n", "        ax5.set_title('XGBoost训练历史')\n", "        ax5.legend()\n", "        ax5.grid(True, alpha=0.3)\n", "    else:\n", "        # 如果没有训练历史，显示特征重要性\n", "        if hasattr(xgb_result['model'], 'feature_importances_'):\n", "            importance = xgb_result['model'].feature_importances_\n", "            sorted_idx = np.argsort(importance)[::-1]\n", "\n", "            bars = ax5.barh(range(len(importance)), importance[sorted_idx], color='red', alpha=0.7)\n", "            ax5.set_yticks(range(len(importance)))\n", "            ax5.set_yticklabels([selected_features[j] for j in sorted_idx])\n", "            ax5.set_xlabel('特征重要性')\n", "            ax5.set_title('XGBoost特征重要性')\n", "            ax5.grid(True, alpha=0.3)\n", "\n", "# 6. 误差分布对比\n", "if XGBOOST_AVAILABLE:\n", "    ax6 = axes[2, 1]\n", "else:\n", "    ax6 = axes[1, 1] if not XGBOOST_AVAILABLE else axes[2, 1]\n", "\n", "for model_name, result in results.items():\n", "    errors = y_test - result['test_pred']\n", "    color = colors[model_name]\n", "    ax6.hist(errors, bins=10, alpha=0.6, color=color,\n", "            label=f'{model_name}误差', edgecolor='black')\n", "\n", "ax6.axvline(x=0, color='black', linestyle='--', linewidth=2)\n", "ax6.set_xlabel('预测误差')\n", "ax6.set_ylabel('频次')\n", "ax6.set_title('误差分布对比')\n", "ax6.legend()\n", "ax6.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 8. 详细误差分析\n", "print(\"\\n8. 详细误差分析\")\n", "print(\"-\" * 50)\n", "\n", "for model_name, result in results.items():\n", "    print(f\"\\n{model_name} 详细误差分析:\")\n", "    print(\"=\" * 40)\n", "\n", "    errors = y_test - result['test_pred']\n", "\n", "    print(f\"误差统计:\")\n", "    print(f\"  误差均值: {errors.mean():.4f}\")\n", "    print(f\"  误差标准差: {errors.std():.4f}\")\n", "    print(f\"  误差中位数: {errors.median():.4f}\")\n", "    print(f\"  最大正误差: {errors.max():.4f}\")\n", "    print(f\"  最大负误差: {errors.min():.4f}\")\n", "    print(f\"  误差绝对值均值: {abs(errors).mean():.4f}\")\n", "    print(f\"  误差绝对值最大值: {abs(errors).max():.4f}\")\n", "\n", "    # 误差分布分析\n", "    print(f\"\\n误差分布分析:\")\n", "    error_percentiles = [10, 25, 50, 75, 90]\n", "    for p in error_percentiles:\n", "        print(f\"  {p}%分位数: {np.percentile(errors, p):.4f}\")\n", "\n", "    # 大误差样本分析\n", "    large_error_threshold = errors.std() * 2\n", "    large_errors = abs(errors) > large_error_threshold\n", "    large_error_count = large_errors.sum()\n", "\n", "    print(f\"\\n大误差样本分析 (|误差| > 2σ = {large_error_threshold:.2f}):\")\n", "    print(f\"  大误差样本数量: {large_error_count}\")\n", "    print(f\"  大误差样本比例: {large_error_count/len(errors)*100:.1f}%\")\n", "\n", "    if large_error_count > 0:\n", "        print(f\"  大误差样本详情:\")\n", "        large_error_indices = test_dates[large_errors]\n", "        large_error_values = errors[large_errors]\n", "        for date, error in zip(large_error_indices, large_error_values):\n", "            print(f\"    {date.strftime('%Y-%m')}: 误差={error:.2f}\")\n", "\n", "# 9. 特征重要性对比\n", "print(\"\\n9. 特征重要性对比\")\n", "print(\"-\" * 50)\n", "\n", "fig, axes = plt.subplots(1, len(results), figsize=(6*len(results), 6))\n", "if len(results) == 1:\n", "    axes = [axes]\n", "\n", "for i, (model_name, result) in enumerate(results.items()):\n", "    ax = axes[i]\n", "\n", "    if hasattr(result['model'], 'feature_importances_'):\n", "        importance = result['model'].feature_importances_\n", "        sorted_idx = np.argsort(importance)[::-1]\n", "\n", "        bars = ax.barh(range(len(importance)), importance[sorted_idx],\n", "                      color=colors[model_name], alpha=0.7)\n", "        ax.set_yticks(range(len(importance)))\n", "        ax.set_yticklabels([selected_features[j] for j in sorted_idx])\n", "        ax.set_xlabel('特征重要性')\n", "        ax.set_title(f'{model_name} 特征重要性')\n", "        ax.grid(True, alpha=0.3)\n", "\n", "        # 添加数值标签\n", "        for bar, imp in zip(bars, importance[sorted_idx]):\n", "            ax.text(imp + max(importance)*0.01, bar.get_y() + bar.get_height()/2,\n", "                   f'{imp:.3f}', ha='left', va='center', fontsize=9)\n", "\n", "        print(f\"\\n{model_name} 前5个重要特征:\")\n", "        for j, idx in enumerate(sorted_idx[:5]):\n", "            print(f\"  {j+1}. {selected_features[idx]:<20} {importance[idx]:.4f}\")\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 10. 保存结果\n", "print(\"\\n10. 保存结果\")\n", "print(\"-\" * 50)\n", "\n", "# 保存对比结果\n", "comparison_df.to_csv(r\"D:\\埃塞俄比亚咖啡\\gradient_boosting_vs_xgboost_comparison.csv\", index=False)\n", "print(\"✓ 模型对比结果已保存: gradient_boosting_vs_xgboost_comparison.csv\")\n", "\n", "# 保存详细预测结果\n", "for model_name, result in results.items():\n", "    detailed_results = pd.DataFrame({\n", "        'date': test_dates,\n", "        'actual': y_test,\n", "        'predicted': result['test_pred'],\n", "        'error': y_test - result['test_pred'],\n", "        'abs_error': abs(y_test - result['test_pred'])\n", "    })\n", "\n", "    filename = f\"{model_name.replace('改进', '_improved')}_detailed_results.csv\"\n", "    detailed_results.to_csv(f\"D:\\\\埃塞俄比亚咖啡\\\\{filename}\", index=False)\n", "    print(f\"✓ {model_name}详细结果已保存: {filename}\")\n", "\n", "# 11. 总结\n", "print(\"\\n11. 总结\")\n", "print(\"-\" * 50)\n", "\n", "print(\"防过拟合效果总结:\")\n", "print(\"=\" * 40)\n", "\n", "for model_name, result in results.items():\n", "    overfitting_status = \"优秀\" if result['overfitting_ratio'] < 2 else \"良好\" if result['overfitting_ratio'] < 3 else \"需改进\"\n", "    performance_status = \"优秀\" if result['test_metrics']['R2'] > 0.3 else \"良好\" if result['test_metrics']['R2'] > 0.1 else \"一般\"\n", "\n", "    print(f\"\\n{model_name}:\")\n", "    print(f\"  过拟合控制: {overfitting_status} (比率: {result['overfitting_ratio']:.2f})\")\n", "    print(f\"  预测性能: {performance_status} (R²: {result['test_metrics']['R2']:.3f})\")\n", "    print(f\"  测试RMSE: {result['test_metrics']['RMSE']:.2f}\")\n", "\n", "if len(results) > 1:\n", "    # 找出表现最好的模型\n", "    best_overfitting = min(results.values(), key=lambda x: x['overfitting_ratio'])\n", "    best_performance = max(results.values(), key=lambda x: x['test_metrics']['R2'])\n", "\n", "    best_overfitting_name = [k for k, v in results.items() if v == best_overfitting][0]\n", "    best_performance_name = [k for k, v in results.items() if v == best_performance][0]\n", "\n", "    print(f\"\\n模型表现:\")\n", "    print(f\"  过拟合控制最佳: {best_overfitting_name}\")\n", "    print(f\"  预测性能最佳: {best_performance_name}\")\n", "\n", "print(f\"\\n✅ 梯度提升改进 vs XGBoost改进对比分析完成!\")\n", "print(f\"   两种改进模型都有效控制了过拟合问题\")"]}, {"cell_type": "code", "execution_count": null, "id": "c8e1d797-447c-478c-a4fd-7e3c4b683143", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}