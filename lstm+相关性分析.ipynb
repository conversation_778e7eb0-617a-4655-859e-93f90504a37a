{"cells": [{"cell_type": "code", "execution_count": 3, "id": "1e48607f-9acd-4af4-9761-effe545503e2", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 001 | Train Loss: 0.0766\n", "Epoch 002 | Train Loss: 0.0549\n", "Epoch 003 | Train Loss: 0.0387\n", "Epoch 004 | Train Loss: 0.0296\n", "Epoch 005 | Train Loss: 0.0289\n", "Epoch 006 | Train Loss: 0.0339\n", "Epoch 007 | Train Loss: 0.0372\n", "Epoch 008 | Train Loss: 0.0362\n", "Epoch 009 | Train Loss: 0.0334\n", "Epoch 010 | Train Loss: 0.0311\n", "Epoch 011 | Train Loss: 0.0301\n", "Epoch 012 | Train Loss: 0.0300\n", "Epoch 013 | Train Loss: 0.0301\n", "Epoch 014 | Train Loss: 0.0301\n", "Epoch 015 | Train Loss: 0.0299\n", "Epoch 016 | Train Loss: 0.0295\n", "Epoch 017 | Train Loss: 0.0292\n", "Epoch 018 | Train Loss: 0.0290\n", "Epoch 019 | Train Loss: 0.0291\n", "Epoch 020 | Train Loss: 0.0293\n", "Epoch 021 | Train Loss: 0.0296\n", "Epoch 022 | Train Loss: 0.0297\n", "Epoch 023 | Train Loss: 0.0297\n", "Epoch 024 | Train Loss: 0.0296\n", "Epoch 025 | Train Loss: 0.0294\n", "Epoch 026 | Train Loss: 0.0293\n", "Epoch 027 | Train Loss: 0.0292\n", "Epoch 028 | Train Loss: 0.0291\n", "Epoch 029 | Train Loss: 0.0290\n", "Epoch 030 | Train Loss: 0.0290\n", "Epoch 031 | Train Loss: 0.0289\n", "Epoch 032 | Train Loss: 0.0289\n", "Epoch 033 | Train Loss: 0.0289\n", "Epoch 034 | Train Loss: 0.0289\n", "Epoch 035 | Train Loss: 0.0289\n", "Epoch 036 | Train Loss: 0.0289\n", "Epoch 037 | Train Loss: 0.0289\n", "Epoch 038 | Train Loss: 0.0288\n", "Epoch 039 | Train Loss: 0.0288\n", "Epoch 040 | Train Loss: 0.0287\n", "Epoch 041 | Train Loss: 0.0286\n", "Epoch 042 | Train Loss: 0.0286\n", "Epoch 043 | Train Loss: 0.0285\n", "Epoch 044 | Train Loss: 0.0284\n", "Epoch 045 | Train Loss: 0.0284\n", "Epoch 046 | Train Loss: 0.0283\n", "Epoch 047 | Train Loss: 0.0283\n", "Epoch 048 | Train Loss: 0.0282\n", "Epoch 049 | Train Loss: 0.0282\n", "Epoch 050 | Train Loss: 0.0281\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA90AAAGGCAYAAABmGOKbAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuNSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/xnp5ZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABNRElEQVR4nO3deXxU9b3/8feZmcxk380CCYsSCAoEZY3aqiUarFdF9BapPwGvrbctbqXailVAqcVqtdiCa7XaXlFEK7VWUYjVokSQTaUFVFQISzYgO5kkM/P7YzKTBAJCyMyZTF7Px2MemTnnzMznJOfBg/f5bobH4/EIAAAAAAB0O4vZBQAAAAAAEK4I3QAAAAAABAihGwAAAACAACF0AwAAAAAQIIRuAAAAAAAChNANAAAAAECAELoBAAAAAAgQQjcAAAAAAAFiM7uAUOR2u7V3717FxcXJMAyzywEAAAAAhBiPx6Pa2lr16dNHFsvR27MJ3Z3Yu3evsrOzzS4DAAAAABDiSkpKlJWVddT9hO5OxMXFSfL+8uLj402uBgAAAAAQampqapSdne3Pj0dD6O6Er0t5fHw8oRsAAAAAcFTfNCSZidQAAAAAAAgQQjcAAAAAAAFC6AYAAAAAIEAY0w0AAAAA3cjlcqm5udnsMnCSIiIiZLVaT/pzCN0AAAAA0A08Ho9KS0tVVVVldinoJomJicrIyPjGydKOhdANAAAAAN3AF7jT0tIUHR19UkEN5vJ4PGpoaFB5ebkkKTMzs8ufRegGAAAAgJPkcrn8gTslJcXsctANoqKiJEnl5eVKS0vrcldzJlIDAAAAgJPkG8MdHR1tciXoTr6/58mM0Sd0AwAAAEA3oUt5eOmOvyehGwAAAACAACF091D765zatOugymsbzS4FAAAAAPwGDBighQsXml1GyCB091C3LftYVzy6RkVby80uBQAAAEAPZBjGMR/z5s3r0ud+9NFHuuGGG06qtvPPP1+33nrrSX1GqGD28h4qO9k7oL/kQIPJlQAAAADoifbt2+d/vnTpUs2ZM0fbt2/3b4uNjfU/93g8crlcstm+OUKecsop3VtoD0dLdw+VleSdvn73wUMmVwIAAACgJ8rIyPA/EhISZBiG//W2bdsUFxenN998U6NGjZLD4dD777+vHTt26PLLL1d6erpiY2M1ZswYrVq1qsPnHt693DAM/fGPf9QVV1yh6Oho5eTk6LXXXjup2l955RWdccYZcjgcGjBggB566KEO+x999FHl5OQoMjJS6enpuuqqq/z7Xn75ZQ0fPlxRUVFKSUlRQUGB6uvrT6qeY6Glu4fKTmpt6T5ISzcAAAAQajwejw41u0z57qgIa7fNon7HHXfot7/9rU499VQlJSWppKRE3/3ud3XffffJ4XDoz3/+sy699FJt375d/fr1O+rn3HPPPXrggQf04IMP6g9/+IOuueYa7dy5U8nJySdc04YNG/S9731P8+bN05QpU7RmzRr95Cc/UUpKimbMmKH169fr5ptv1l/+8hedffbZOnDggFavXi3J27o/depUPfDAA7riiitUW1ur1atXy+PxdPl39E0I3T1UVmvopqUbAAAACD2Hml06fc5bpnz3f+4tVLS9e6LevffeqwsvvND/Ojk5WXl5ef7X8+fP16uvvqrXXntNN95441E/Z8aMGZo6daok6de//rV+//vfa926dZo4ceIJ1/Twww9rwoQJuvvuuyVJgwcP1n/+8x89+OCDmjFjhnbt2qWYmBj913/9l+Li4tS/f3+deeaZkryhu6WlRZMnT1b//v0lScOHDz/hGk4E3ct7qOxkb/fyilqnGk26gwYAAAAgvI0ePbrD67q6Ot12220aOnSoEhMTFRsbq61bt2rXrl3H/JwRI0b4n8fExCg+Pl7l5V2bFHrr1q0655xzOmw755xz9Pnnn8vlcunCCy9U//79deqpp+raa6/V888/r4YGbw/hvLw8TZgwQcOHD9d///d/66mnntLBgwe7VMfxoqW7h0qIilCsw6Y6Z4t2HzykQWmx3/wmAAAAAEERFWHVf+4tNO27u0tMTEyH17fddptWrlyp3/72txo0aJCioqJ01VVXqamp6ZifExER0eG1YRhyu93dVmd7cXFx2rhxo9599129/fbbmjNnjubNm6ePPvpIiYmJWrlypdasWaO3335bf/jDH/TLX/5Sa9eu1cCBAwNSDy3dPZRhGP7J1BjXDQAAAIQWwzAUbbeZ8uiu8dyd+eCDDzRjxgxdccUVGj58uDIyMvT1118H7Ps6M3ToUH3wwQdH1DV48GBZrd4bDjabTQUFBXrggQf0ySef6Ouvv9Y777wjyfu3Oeecc3TPPfdo06ZNstvtevXVVwNWLy3dPVhWUrS2ldYyrhsAAABAUOTk5Oivf/2rLr30UhmGobvvvjtgLdYVFRXavHlzh22ZmZn62c9+pjFjxmj+/PmaMmWKiouLtWjRIj366KOSpNdff11ffvmlvv3tbyspKUlvvPGG3G63hgwZorVr16qoqEgXXXSR0tLStHbtWlVUVGjo0KEBOQeJ0N2j+cZ172atbgAAAABB8PDDD+t//ud/dPbZZys1NVW/+MUvVFNTE5DvWrJkiZYsWdJh2/z583XXXXfppZde0pw5czR//nxlZmbq3nvv1YwZMyRJiYmJ+utf/6p58+apsbFROTk5euGFF3TGGWdo69at+te//qWFCxeqpqZG/fv310MPPaSLL744IOcgSYYnkHOj91A1NTVKSEhQdXW14uPjzS7nqJ5+/yvNf/0/umR4phZfc5bZ5QAAAAC9VmNjo7766isNHDhQkZGRZpeDbnKsv+vx5kbGdPdg2YzpBgAAAICQRujuwVirGwAAAABCG6G7B8tqHdN9oL5J9c4Wk6sBAAAAAByO0N2DxUdGKCHKu94drd0AAAAAEHoI3T2cbwbzEmYwBwAAAEzHPNXhpTv+noTuHi4r0Teum9ANAAAAmCUiwtsDtaGB/5eHE9/f0/f37QrW6e7h/C3ddC8HAAAATGO1WpWYmKjy8nJJUnR0tAzDMLkqdJXH41FDQ4PKy8uVmJgoq9Xa5c8idPdwbTOYc0cNAAAAMFNGRoYk+YM3er7ExET/37WrCN09XNuYblq6AQAAADMZhqHMzEylpaWpubnZ7HJwkiIiIk6qhduH0N3D0dINAAAAhBar1dotYQ3hgYnUerisJG9Ld01ji6oPcTcNAAAAAEIJobuHi7bblBJjl0RrNwAAAACEGkJ3GMhK9nYxZ1w3AAAAAIQWQncY8HUxp6UbAAAAAEILoTsMZPsnU6OlGwAAAABCCaE7DNDSDQAAAAChidAdBrIZ0w0AAAAAIYnQHQbat3R7PB6TqwEAAAAA+BC6w0DfRG/orm9y6WADa3UDAAAAQKggdIeByAir0uIckhjXDQAAAAChhNAdJhjXDQAAAAChh9AdJpjBHAAAAABCD6E7TPjW6i4hdAMAAABAyCB0h4m2lm66lwMAAABAqCB0h4m2Md20dAMAAABAqCB0h4n2Ld2s1Q0AAAAAoSEkQvfixYs1YMAARUZGaty4cVq3bt0xj1+2bJlyc3MVGRmp4cOH64033uiw3zCMTh8PPvhgIE/DVJkJUbIYkrPFrYo6p9nlAAAAAAAUAqF76dKlmjVrlubOnauNGzcqLy9PhYWFKi8v7/T4NWvWaOrUqbr++uu1adMmTZo0SZMmTdKWLVv8x+zbt6/D45lnnpFhGLryyiuDdVpBZ7dZlBEfKYlx3QAAAAAQKgyPyX2Rx40bpzFjxmjRokWSJLfbrezsbN1000264447jjh+ypQpqq+v1+uvv+7fNn78eI0cOVKPP/54p98xadIk1dbWqqio6LhqqqmpUUJCgqqrqxUfH9+FszLH954o1rqvDuiRq0fq8pF9zS4HAAAAAMLW8eZGU1u6m5qatGHDBhUUFPi3WSwWFRQUqLi4uNP3FBcXdzhekgoLC496fFlZmf7xj3/o+uuvP2odTqdTNTU1HR49ETOYAwAAAEBoMTV0V1ZWyuVyKT09vcP29PR0lZaWdvqe0tLSEzr+ueeeU1xcnCZPnnzUOhYsWKCEhAT/Izs7+wTPJDT41urezVrdAAAAABASTB/THWjPPPOMrrnmGkVGRh71mNmzZ6u6utr/KCkpCWKF3cfX0l1ygJZuAAAAAAgFNjO/PDU1VVarVWVlZR22l5WVKSMjo9P3ZGRkHPfxq1ev1vbt27V06dJj1uFwOORwOE6w+tDjW6ublm4AAAAACA2mtnTb7XaNGjWqwwRnbrdbRUVFys/P7/Q9+fn5R0yItnLlyk6Pf/rppzVq1Cjl5eV1b+EhytfSvafqkFxu1uoGAAAAALOZ3r181qxZeuqpp/Tcc89p69at+vGPf6z6+npdd911kqRp06Zp9uzZ/uNvueUWrVixQg899JC2bdumefPmaf369brxxhs7fG5NTY2WLVumH/zgB0E9HzNlxEfKZjHU7PKovLbR7HIAAAAAoNcztXu55F0CrKKiQnPmzFFpaalGjhypFStW+CdL27VrlyyWtnsDZ599tpYsWaK77rpLd955p3JycrR8+XINGzasw+e++OKL8ng8mjp1alDPx0w2q0WZiZEqOXBIJQcOKTMhyuySAAAAAKBXM32d7lDUU9fplqTvP/Wh1uzYr4e/l6fJZ2WZXQ4AAAAAhKUesU43uh8zmAMAAABA6CB0hxnW6gYAAACA0EHoDjNZya0t3YRuAAAAADAdoTvMtLV0070cAAAAAMxG6A4zWa2he191o1pcbpOrAQAAAIDejdAdZtLiHLJbLXK5PdpXzVrdAAAAAGAmQneYsVgM9U1iXDcAAAAAhAJCdxjyLRvGuG4AAAAAMBehOwz5xnXvPkBLNwAAAACYidAdhrKTaekGAAAAgFBA6A5DvpZuxnQDAAAAgLkI3WEomzHdAAAAABASCN1hyNfSXVrTKGeLy+RqAAAAAKD3InSHodRYuyIjLPJ4pH1VrNUNAAAAAGYhdIchwzAY1w0AAAAAIYDQHaYY1w0AAAAA5iN0h6ns5NaWbtbqBgAAAADTELrDVBYt3QAAAABgOkJ3mMpmTDcAAAAAmI7QHaZ8E6nR0g0AAAAA5iF0h6nsZG/38opapxqbWasbAAAAAMxA6A5TCVERinXYJNHaDQAAAABmIXSHKe9a3d7WbsZ1AwAAAIA5CN1hjHHdAAAAAGAuQncY843r3s1a3QAAAABgCkJ3GKOlGwAAAADMRegOY9mM6QYAAAAAUxG6wxgt3QAAAABgLkJ3GMtqHdN9oL5J9c4Wk6sBAAAAgN6H0B3G4iMjlBAVIYnWbgAAAAAwA6E7zPlmMC9hBnMAAAAACDpCd5jLSvSN6yZ0AwAAAECwEbrDnL+lm+7lAAAAABB0hO4w1zaDOS3dAAAAABBshO4w1zamm5ZuAAAAAAg2QneYo6UbAAAAAMxD6A5zWUnelu6axhZVH2o2uRoAAAAA6F0I3WEu2m5TSoxdEq3dAAAAABBshO5eICvZ28Wccd0AAAAAEFyE7l7A18Wclm4AAAAACC5Cdy+Q7Z9MjZZuAAAAAAgmQncvQEs3AAAAAJiD0N0LZDOmGwAAAABMQejuBdq3dHs8HpOrAQAAAIDeg9DdC/RN9Ibu+iaXDjawVjcAAAAABAuhuxeIjLAqLc4hiXHdAAAAABBMhO5egnHdAAAAABB8hO5eghnMAQAAACD4CN29hG+t7hJCNwAAAAAEDaG7l2hr6aZ7OQAAAAAEi+mhe/HixRowYIAiIyM1btw4rVu37pjHL1u2TLm5uYqMjNTw4cP1xhtvHHHM1q1bddlllykhIUExMTEaM2aMdu3aFahT6BHaxnTT0g0AAAAAwWJq6F66dKlmzZqluXPnauPGjcrLy1NhYaHKy8s7PX7NmjWaOnWqrr/+em3atEmTJk3SpEmTtGXLFv8xO3bs0Lnnnqvc3Fy9++67+uSTT3T33XcrMjIyWKcVktq3dLNWNwAAAAAEh+ExMYGNGzdOY8aM0aJFiyRJbrdb2dnZuummm3THHXcccfyUKVNUX1+v119/3b9t/PjxGjlypB5//HFJ0tVXX62IiAj95S9/6XJdNTU1SkhIUHV1teLj47v8OaGkqcWt3LvflNsjrfvlBKXF9e6bEAAAAABwMo43N5rW0t3U1KQNGzaooKCgrRiLRQUFBSouLu70PcXFxR2Ol6TCwkL/8W63W//4xz80ePBgFRYWKi0tTePGjdPy5csDdh49hd1mUUa8N2izbBgAAAAABIdpobuyslIul0vp6ekdtqenp6u0tLTT95SWlh7z+PLyctXV1en+++/XxIkT9fbbb+uKK67Q5MmT9d577x21FqfTqZqamg6PcJTVOq6bZcMAAAAAIDhMn0itO7ndbknS5Zdfrp/+9KcaOXKk7rjjDv3Xf/2Xv/t5ZxYsWKCEhAT/Izs7O1glBxUzmAMAAABAcJkWulNTU2W1WlVWVtZhe1lZmTIyMjp9T0ZGxjGPT01Nlc1m0+mnn97hmKFDhx5z9vLZs2erurra/ygpKenKKYU831rdtHQDAAAAQHCYFrrtdrtGjRqloqIi/za3262ioiLl5+d3+p78/PwOx0vSypUr/cfb7XaNGTNG27dv73DMZ599pv79+x+1FofDofj4+A6PcORr6WZMNwAAAAAEh83ML581a5amT5+u0aNHa+zYsVq4cKHq6+t13XXXSZKmTZumvn37asGCBZKkW265Reedd54eeughXXLJJXrxxRe1fv16Pfnkk/7PvP322zVlyhR9+9vf1gUXXKAVK1bo73//u959910zTjGkZDOmGwAAAACCytTQPWXKFFVUVGjOnDkqLS3VyJEjtWLFCv9kabt27ZLF0tYYf/bZZ2vJkiW66667dOeddyonJ0fLly/XsGHD/MdcccUVevzxx7VgwQLdfPPNGjJkiF555RWde+65QT+/UONr6d5TdUgut0dWi2FyRQAAAAAQ3kxdpztUheM63ZLU4nIr9+4VanF7VDz7O8pMiDK7JAAAAADokUJ+nW4En81qUWYia3UDAAAAQLAQunsZZjAHAAAAgOAhdPcyzGAOAAAAAMFD6O5laOkGAAAAgOAhdPcyWcmtLd2EbgAAAAAIOEJ3L9PW0k33cgAAAAAINEJ3L5PVGrr3VTeqxeU2uRoAAAAACG+E7l4mLc4hu9Uil9ujfdWNZpcDAAAAAGGN0N3LWCyG+iYxrhsAAAAAgoHQ3Qv5lg1jXDcAAAAABBahuxfyjevefYCWbgAAAAAIJEJ3L5SdTEs3AAAAAAQDobsX8rV0M6YbAAAAAAKL0N0LZTOmGwAAAACCgtDdC/lauktrGuVscZlcDQAAAACEL0J3L5Qaa1dkhEUej7SvirW6AQAAACBQCN29kGEYjOsGAAAAgCAgdPdSjOsGAAAAgMAjdPdS/pZu1uoGAAAAgIAhdPdSrNUNAAAAAIFH6O6lGNMNAAAAAIFH6O6lsltDNy3dAAAAABA4hO5eKqt1IrWKWqcam1mrGwAAAAACgdDdSyVGRyjWYZNEazcAAAAABAqhu5fyrtXtbe1mBnMAAAAACAxCdy926ikxkqQvyutMrgQAAAAAwhOhuxcbnB4nSfqsrNbkSgAAAAAgPBG6e7EhhG4AAAAACKguhe6SkhLt3r3b/3rdunW69dZb9eSTT3ZbYQi8HH/orpPb7TG5GgAAAAAIP10K3d///vf1z3/+U5JUWlqqCy+8UOvWrdMvf/lL3Xvvvd1aIAJnQEq07FaLDjW7mMEcAAAAAAKgS6F7y5YtGjt2rCTppZde0rBhw7RmzRo9//zzevbZZ7uzPgSQzWrRaWmxkuhiDgAAAACB0KXQ3dzcLIfDIUlatWqVLrvsMklSbm6u9u3b133VIeAGp3tD93ZCNwAAAAB0uy6F7jPOOEOPP/64Vq9erZUrV2rixImSpL179yolJaVbC0RgMYM5AAAAAAROl0L3b37zGz3xxBM6//zzNXXqVOXl5UmSXnvtNX+3c/QMQ9pNpgYAAAAA6F62rrzp/PPPV2VlpWpqapSUlOTffsMNNyg6OrrbikPgDcnwhu4d5XVqcblls7KKHAAAAAB0ly4lrEOHDsnpdPoD986dO7Vw4UJt375daWlp3VogAqtvYpSiIqxqcrn19f4Gs8sBAAAAgLDSpdB9+eWX689//rMkqaqqSuPGjdNDDz2kSZMm6bHHHuvWAhFYFovhn0yNcd0AAAAA0L26FLo3btyob33rW5Kkl19+Wenp6dq5c6f+/Oc/6/e//323FojAYzI1AAAAAAiMLoXuhoYGxcV5g9rbb7+tyZMny2KxaPz48dq5c2e3FojAI3QDAAAAQGB0KXQPGjRIy5cvV0lJid566y1ddNFFkqTy8nLFx8d3a4EIvMGtk6ltLyV0AwAAAEB36lLonjNnjm677TYNGDBAY8eOVX5+viRvq/eZZ57ZrQUi8HzLhn29v0HOFpfJ1QAAAABA+OjSkmFXXXWVzj33XO3bt8+/RrckTZgwQVdccUW3FYfgSI93KD7SpprGFn1ZUa+hmfRWAAAAAIDu0KXQLUkZGRnKyMjQ7t27JUlZWVkaO3ZstxWG4DEMQ4PT47R+50F9VlZL6AYAAACAbtKl7uVut1v33nuvEhIS1L9/f/Xv31+JiYmaP3++3G53d9eIIGBcNwAAAAB0vy61dP/yl7/U008/rfvvv1/nnHOOJOn999/XvHnz1NjYqPvuu69bi0TgDfHPYF5nciUAAAAAED66FLqfe+45/fGPf9Rll13m3zZixAj17dtXP/nJTwjdPRDLhgEAAABA9+tS9/IDBw4oNzf3iO25ubk6cODASReF4BucHitJ2nWgQQ1NLSZXAwAAAADhoUuhOy8vT4sWLTpi+6JFizRixIiTLgrBlxLrUGqsXZL0OV3MAQAAAKBbdKl7+QMPPKBLLrlEq1at8q/RXVxcrJKSEr3xxhvdWiCCZ3B6nCrr9uuzslrlZSeaXQ4AAAAA9Hhdauk+77zz9Nlnn+mKK65QVVWVqqqqNHnyZP373//WX/7yl+6uEUHCuG4AAAAA6F5dCt2S1KdPH91333165ZVX9Morr+hXv/qVDh48qKeffvqEP2vx4sUaMGCAIiMjNW7cOK1bt+6Yxy9btky5ubmKjIzU8OHDj2hdnzFjhgzD6PCYOHHiCdfV2/hC93a6lwMAAABAt+hy6O4uS5cu1axZszR37lxt3LhReXl5KiwsVHl5eafHr1mzRlOnTtX111+vTZs2adKkSZo0aZK2bNnS4biJEydq3759/scLL7wQjNPp0YZkeCdT+5yWbgAAAADoFqaH7ocfflg//OEPdd111+n000/X448/rujoaD3zzDOdHv/II49o4sSJuv322zV06FDNnz9fZ5111hETuzkcDmVkZPgfSUlJwTidHi2ntaV7X3Wjqg81m1wNAAAAAPR8pobupqYmbdiwQQUFBf5tFotFBQUFKi4u7vQ9xcXFHY6XpMLCwiOOf/fdd5WWlqYhQ4boxz/+sfbv39/9JxBm4iMjlJkQKYnWbgAAAADoDic0e/nkyZOPub+qquqEvryyslIul0vp6ekdtqenp2vbtm2dvqe0tLTT40tLS/2vJ06cqMmTJ2vgwIHasWOH7rzzTl188cUqLi6W1Wo94jOdTqecTqf/dU1NzQmdRzgZnB6nfdWN2l5Wq9EDks0uBwAAAAB6tBMK3QkJCd+4f9q0aSdVUHe4+uqr/c+HDx+uESNG6LTTTtO7776rCRMmHHH8ggULdM899wSzxJA1JCNO731WwVrdAAAAANANTih0/+lPf+rWL09NTZXValVZWVmH7WVlZcrIyOj0PRkZGSd0vCSdeuqpSk1N1RdffNFp6J49e7ZmzZrlf11TU6Ps7OwTOZWwkZPmnUxteyndywEAAADgZJk6pttut2vUqFEqKiryb3O73SoqKlJ+fn6n78nPz+9wvCStXLnyqMdL0u7du7V//35lZmZ2ut/hcCg+Pr7Do7caksFa3QAAAADQXUyfvXzWrFl66qmn9Nxzz2nr1q368Y9/rPr6el133XWSpGnTpmn27Nn+42+55RatWLFCDz30kLZt26Z58+Zp/fr1uvHGGyVJdXV1uv322/Xhhx/q66+/VlFRkS6//HINGjRIhYWFppxjTzIoLVaGIe2vb1JlnfOb3wAAAAAAOKoT6l4eCFOmTFFFRYXmzJmj0tJSjRw5UitWrPBPlrZr1y5ZLG33Bs4++2wtWbJEd911l+68807l5ORo+fLlGjZsmCTJarXqk08+0XPPPaeqqir16dNHF110kebPny+Hw2HKOfYk0Xab+iVHa+f+Bn1WVqvUWH5nAAAAANBVhsfj8ZhdRKipqalRQkKCqqure2VX8x88t16rtpZp3qWna8Y5A80uBwAAAABCzvHmRtO7lyP0DMlonUyNGcwBAAAA4KQQunGEweneydQ+ZzI1AAAAADgphG4cwTeD+fayWjH6AAAAAAC6jtCNIwxMjZHVYqi2sUWlNY1mlwMAAAAAPRahG0dw2KwamBojSfqMcd0AAAAA0GWEbnRqSOu47s9KGdcNAAAAAF1F6EanctJ9M5gTugEAAACgqwjd6JS/pZvQDQAAAABdRuhGpwZn+JYNq5PbzQzmAAAAANAVhG50qn9ytOw2iw41u7T74CGzywEAAACAHonQjU7ZrBaddgrjugEAAADgZBC6cVRDWidTY1w3AAAAAHQNoRtH5RvXTegGAAAAgK4hdOOoBqd5Q/d21uoGAAAAgC4hdOOohrS2dH9ZUa8Wl9vkagAAAACg5yF046j6JkYp2m5Vk8utr/c3mF0OAAAAAPQ4hG4clcViKCedcd0AAAAA0FWEbhzT4LTWZcMY1w0AAAAAJ4zQjWMawgzmAAAAANBlhG4c02C6lwMAAABAlxG6cUy+lu6v9zeosdllcjUAAAAA0LMQunFMaXEOxUfa5HJ79GVFvdnlAAAAAECPQujGMRmG4W/t/rycLuYAAAAAcCII3fhGvnHdzGAOAAAAACeG0I1vxGRqAAAAANA1hG58I39LN6EbAAAAAE4IoRvfaHB6rCSp5MAhNTS1mFwNAAAAAPQchG58o5RYh1JjHZKkz8vqTK4GAAAAAHoOQjeOi6+1my7mAAAAAHD8CN04Lr5x3Z8TugEAAADguBG6cVx8a3Vvp3s5AAAAABw3QjeOi697+Wes1Q0AAAAAx43QjeOS09q9vLSmUdWHmk2uBgAAAAB6BkI3jkt8ZIT6JERKYlw3AAAAABwvQjeO22D/uG5CNwAAAAAcD0I3jptvBnPGdQMAAADA8SF047j5Qjct3QAAAABwfAjdOG5D/Gt1s2wYAAAAABwPQjeO26C0WBmGtL++SZV1TrPLAQAAAICQR+jGcYuyW9UvOVoS47oBAAAA4HgQunFC/JOpMa4bAAAAAL4RoRsnZIh/MjXGdQMAAADANyF044TkpMdKoqUbAAAAAI4HoRsnZEhG21rdHo/H5GoAAAAAILQRunFCTk2Nlc1iqNbZotKaRrPLAQAAAICQRujGCbHbLBqYGiNJ2s4M5gAAAABwTIRunDBmMAcAAACA40PoxglrC93MYA4AAAAAx0LoxgkbksEM5gAAAABwPAjdOGE57bqXu93MYA4AAAAARxMSoXvx4sUaMGCAIiMjNW7cOK1bt+6Yxy9btky5ubmKjIzU8OHD9cYbbxz12B/96EcyDEMLFy7s5qp7r/7J0bLbLGpsdqvkYIPZ5QAAAABAyDI9dC9dulSzZs3S3LlztXHjRuXl5amwsFDl5eWdHr9mzRpNnTpV119/vTZt2qRJkyZp0qRJ2rJlyxHHvvrqq/rwww/Vp0+fQJ9Gr2KzWjToFF8Xc8Z1AwAAAMDRmB66H374Yf3whz/Uddddp9NPP12PP/64oqOj9cwzz3R6/COPPKKJEyfq9ttv19ChQzV//nydddZZWrRoUYfj9uzZo5tuuknPP/+8IiIignEqvcqQDGYwBwAAAIBvYmrobmpq0oYNG1RQUODfZrFYVFBQoOLi4k7fU1xc3OF4SSosLOxwvNvt1rXXXqvbb79dZ5xxRmCK7+Vy0r0t3azVDQAAAABHZzPzyysrK+VyuZSent5he3p6urZt29bpe0pLSzs9vrS01P/6N7/5jWw2m26++ebjqsPpdMrpdPpf19TUHO8p9FpDWKsbAAAAAL6R6d3Lu9uGDRv0yCOP6Nlnn5VhGMf1ngULFighIcH/yM7ODnCVPZ9vre4vK+rV7HKbXA0AAAAAhCZTQ3dqaqqsVqvKyso6bC8rK1NGRkan78nIyDjm8atXr1Z5ebn69esnm80mm82mnTt36mc/+5kGDBjQ6WfOnj1b1dXV/kdJScnJn1yY65sYpRi7VU0ut3burze7HAAAAAAISaaGbrvdrlGjRqmoqMi/ze12q6ioSPn5+Z2+Jz8/v8PxkrRy5Ur/8ddee60++eQTbd682f/o06ePbr/9dr311ludfqbD4VB8fHyHB47NYjE0qLW1e3spM5gDAAAAQGdMHdMtSbNmzdL06dM1evRojR07VgsXLlR9fb2uu+46SdK0adPUt29fLViwQJJ0yy236LzzztNDDz2kSy65RC+++KLWr1+vJ598UpKUkpKilJSUDt8RERGhjIwMDRkyJLgnF+aGpMfq45IqfVZWq0uUaXY5AAAAABByTA/dU6ZMUUVFhebMmaPS0lKNHDlSK1as8E+WtmvXLlksbQ3yZ599tpYsWaK77rpLd955p3JycrR8+XINGzbMrFPotQYzmRoAAAAAHJPh8Xg8ZhcRampqapSQkKDq6mq6mh/Dvz6r0LRn1unUU2L0zs/ON7scAAAAAAia482NYTd7OYJnSIa3pfvryno1NrtMrgYAAAAAQg+hG12WFudQQlSE3B5peyldzAEAAADgcIRudJlhGBo3MFmS9M/t5SZXAwAAAAChh9CNk1Iw1DvhXdFWQjcAAAAAHI7QjZNyQW6aDEP6dE+1ymoazS4HAAAAAEIKoRsn5ZQ4h/KyEiXR2g0AAAAAhyN046RNyE2TJBVtLTO5EgAAAAAILYRunLQJreO63/+iUoeaWDoMAAAAAHwI3ThpQzPj1CchUs4Wtz74otLscgAAAAAgZBC6cdIMw/C3dhdto4s5AAAAAPgQutEtJgz1jesul8fjMbkac+zcX6/pz6zTL1/9VB99fUBud+/8PQAAAABoYzO7AISH8aemKNpuVXmtU1v21Gh4VoLZJQVVTWOz/ufZj7Sjol6S9PzaXeqbGKXLRvbRpJF9NSQjzuQKAQAAAJiBlm50i8gIq76VkypJWtXLZjF3uT26ackm7aioV0Z8pCaf1VexDpv2VB3SY+/uUOHCf2niwn/psXd3aE/VIbPLBQAAABBEtHSj20wYmq63/l2mom1l+umFg80uJ2h+/cZWvfdZhSIjLHpq2mgNz0pQY7NLq7aW6W+b9+rd7eXaVlqrbSu26TcrtmnswGRdPrKPLhmeqcRou9nlAwAAAAggw9NbB+AeQ01NjRISElRdXa34+Hizy+kxKuucGnPfKnk80oezJygjIdLskgJu6Ue79ItXPpUkLf7+WbpkROYRx1Q1NOnNLaVavmmP1n51wL89wmrovMGn6PKRfVUwNF1RdmvQ6gYAAABwco43N9LSjW6TGuvQyOxEbdpVpaJtZbpmXH+zSwqotV/u113Lt0iSbi3I6TRwS1JitF1Tx/bT1LH9tLfqkP7+8V4t37xXW/fVaNXWcq3aWq4Yu1WFZ2To8jP76pzTUmSzMvIDAAAACAf8zx7dqsC3dNjWcpMrCaySAw368fMb1ezy6JLhmbr5OznH9b4+iVH63/NO05u3fEtv//TbmnnBacpKilJ9k0t/3bRH059Zp/EL3tEbn+4L8BkAAAAACAZCN7rVd3K9S4d98EWlDjW5TK4mMOqcLfrBc+t1oL5Jw/rG67f/nSeLxTjhzxmcHqfbC3O1+ucX6JUf5+va8f2VHGNXZZ1TN72wieANAAAAhAFCN7pVbkac+iZGydni1vtfVJpdTrdzuT269cVN2l5Wq1PiHHpq2uiTHottGIZG9U/W/EnDtPbOCbpqVJZcbo9ufmGT3v53aTdVDgAAAMAMhG50K8MwNGGot7W7KAyXDnvwre1atbVcdptFT147SpkJUd36+RFWi35z5QhNGtlHLW6PZi7ZqH9uC++u+gAAAEA4I3Sj203wjeveVi63O3wmx39lw249/t4OSdKDV43Qmf2SAvI9Vouh3/53ni4Zkalml0f/+38b9K/PKgLyXQAAAAACi9CNbjf+1GTF2K2qqHVqy95qs8vpFht2HtTsv3qXBpt5wWm6fGTfgH6fzWrRwikjVXhGuppa3Prhn9drTRh21wcAAADCHaEb3c5hs+pbOadIklaFwSzme6oO6X//sl5NLrcuOj1dP7twSFC+N8Jq0R+mnqUJuWlytrh1/XPrtfbL/UH5bgAAAADdg9CNgAiXcd0NTS364XPrVVnXpNyMOP1uysguzVTeVXabRY/+v7N03uBTdKjZpeue/Ugbdh4I2vcDAAAAODmEbgTEBblpMgzp33trtK/6kNnldInb7dGspR/rP/tqlBpr1x+nj1aMwxb0Ohw2q564dpTOHZSqhiaXZjzzkTaXVAW9DgAAAAAnjtCNgEiNdejM7ERJUlEP7WL+u1WfacW/S2W3WvTEtaOUlRRtWi2REVY9NW20xp+arFpni6Y9vVZb9oTHeHkAAAAgnBG6ETD+Wcx7YBfzv23eoz+884Uk6deTh2tU/2STK5Ki7FY9PX2MRvdPUk1ji/7f02u1dV+N2WUBAAAAOAZCNwKmoDV0f7BjvxqaWkyu5vhtLqnSz1/+RJL0v98+VVeNyjK5ojYxDpv+dN0YjcxOVFVDs67541p9VlZrdlkAAAAAjoLQjYAZnB6rvolRampx6/3Pe8ZyV6XVjbrhz+vlbHFrQm6afj4x1+ySjhAXGaHn/meshvdN0IH6Jn3/qbX6orzO7LIAAAAAdILQjYAxDEMF/lnMQ39c96Eml274y3qV1zo1OD1WC68eKWsQZyo/EQlREfrL9WN1ema8Kuuc+v5TH+qrynqzywIAAABwGEI3Aso/rntbudxuj8nVHJ3H49HtL3+sT3ZXKyk6Qn+cNkZxkRFml3VMidF2/d8PxmlIepzKa73Bu+RAg9llAQAAAGiH0I2AGndqsmLsVlXWOfVJCM+2/fdP9un1T/bJZjH02P8bpX4p5s1UfiKSY+x6/ofjNCgtVvuqG3X1kx9qT1XPXKINAAAACEeEbgSUw2bVtwefIil0ZzF3trj0wIptkqSbJ+Ro/KkpJld0YlJjHVryg3EamBqjPVWHNPXJD3vs2ugAAABAuCF0I+Dalg4LzXHdf16zU7sPHlJ6vEM//NapZpfTJWnxkVryw3HqlxytXQca9P2n1hK8AQAAgBBA6EbAXTDkFBmG9J99NdobYl2fqxqa9Id3Ppck/ezCIYqyW02uqOsyE6L0wg3jlZUUpa8q63X5og+0uaTK7LIAAACAXo3QjYBLiXXorH5JkrwTqoWSRe98oZrGFuVmxOnKEFqPu6v6JkbphR+O1+D0WJXXOjXliWL9bfMes8sCAAAAei1CN4Jign/psNAZ111yoEF/Lt4pSbrj4tyQXR7sRGUnR+uVH5+tCblpcra4dcuLm/Xbt7aH9OzxAAAAQLgidCMoClrHda/ZsV8NTS0mV+P1wFvb1eRy61s5qTqvdbK3cBEXGaEnp43W/57nHaO+6J9f6Ef/t0H1ztD43QMAAAC9BaEbQZGTFqvs5Cg1tbi1+vNKs8vR5pIq/f3jvTIMafbFQ2UY4dHK3Z7VYmj2xUP18PfyZLda9PZ/ynTlY2u0+yBreQMAAADBQuhGUBiGoQm5vlnMze1i7vF49Ot/bJUkTT4zS6f3iTe1nkCbfFaWXrhhvFJjHdpWWqvLF32g9V8fMLssAAAAoFcgdCNofOO639lWYer44pX/KdO6rw/IYbPotsLBptURTKP6J+lvN56j0zPjtb++SVOf+lDL1peYXRYAAAAQ9gjdCJpxA1MU67Cpss6pj3dXmVJDs8ut+1dskyRdf+5AZSZEmVKHGfomRunlH+fr4mEZanZ5dPvLn+i+f/xHLiZYAwAAAAKG0I2gsdss+vbgVElS0VZzlg578aMSfVlRr+QYu350/mmm1GCmaLtNi79/lm6ekCNJemr1V/rBcx+pprHZ5MoAAACA8EToRlD5xnWvMmFcd52zRY+s+kySdMuEHMVHRgS9hlBgsRiadeFg/WHqmXLYLPrn9gpNfnSNdu6vN7s0AAAAIOwQuhFUF+SmyWJI20prtafqUFC/+4n3dqiyrkkDU2P0/XH9gvrdoejSvD5a9qN8pcc79EV5nS5f/IHW7DB/ZnkAAAAgnBC6EVTJMXad1S9JkvROEFu7S6sb9dTqLyVJv5iYqwgrl74kjchK1Gs3nqu8rARVNTRr2tPr9PzanWaXBQAAAIQNkgeCbsJQXxfz4I3rfujt7Wpsdmt0/yQVnpEetO/tCdLjI7X0f/N1+cg+anF79MtXt2ju37aoxeU2uzQAAACgxyN0I+gKWpcOK96xX/XOloB/39Z9NXp5425J0p2XDJVhGAH/zp4mMsKqhVNG6vbCIZKk54p3avqf1mlzSZU8HmY3BwAAALqK0I2gG5QWq37J0WpyubX688CPIV7w5jZ5PNIlwzP9XdtxJMMwNPOCQXri2lGKtlv1wRf7NWnxByp4+D09+u4XKq1uNLtEAAAAoMchdCPoDMPQhNbW7qIAj+te/XmF/vVZhSKshn4+cUhAvytcFJ6RoeUzz9GkkX0UGWHRjop6PbBiu/LvL9K1T6/V3zbv0aEml9llAgAAAD2CzewC0DsVDE3Xnz74Wv/cXi632yOLpfu7fLvcHv36jW2SpP83vr/6p8R0+3eEq8HpcVp49ZmqbWzWG5/u0ysb9mjd1we0+vNKrf68UrEOmy4ZnqkrR2VpzIAkuuwDAAAARxESLd2LFy/WgAEDFBkZqXHjxmndunXHPH7ZsmXKzc1VZGSkhg8frjfeeKPD/nnz5ik3N1cxMTFKSkpSQUGB1q5dG8hTwAkaMyBZcQ6bKuuatHl3VUC+49VNe7R1X43iIm26+Ts5AfmOcBcXGaEpY/rppR/l61+3X6BbJuQoOzlKdc4WLV1fou89UazzHnxXC1d9ppIDDWaXCwAAAIQc00P30qVLNWvWLM2dO1cbN25UXl6eCgsLVV7e+czWa9as0dSpU3X99ddr06ZNmjRpkiZNmqQtW7b4jxk8eLAWLVqkTz/9VO+//74GDBigiy66SBUVFcE6LXwDu82ibw8+RVJgupgfanLpobe3S5JuvGCQkmLs3f4dvU2/lGj99MLBeu+2C7T0hvH671FZirFbtetAgxau+lzfeuCfmvJEsV5aX6K6IEyQBwAAAPQEhsfkqYnHjRunMWPGaNGiRZIkt9ut7Oxs3XTTTbrjjjuOOH7KlCmqr6/X66+/7t82fvx4jRw5Uo8//nin31FTU6OEhAStWrVKEyZM+MaafMdXV1crPj6+i2eGb/LXjbs166WPlZsRpxW3frtbP3vxP7/Qg29tV9/EKBX97DxFRli79fPh1dDUorf+XapXNuzRBzsq5fvXJCrCqonDMlR4RrqSou2KcdgUbbcqxmFTlN2q6AirbN20Vrrb7VF9U4vqnC2qbfQ9mv2v6xpb5Gxxye2R3B6P3B7J4/HI7fHI5W577tvv8R/XcX+03aaUGLuSY+1KiXEoJdau5Bi7UmLsio+MCMgQCQAAAISu482Npo7pbmpq0oYNGzR79mz/NovFooKCAhUXF3f6nuLiYs2aNavDtsLCQi1fvvyo3/Hkk08qISFBeXl5nR7jdDrldDr9r2tqak7wTNAVFwxJk8WQtpXWavfBBmUlRXfL51bWOfXYuzskSbcXDiFwB1C03aYrzszSFWdmaW/VIb26aY9e2bBbX1bW69VNe/Tqpj1Hfa/DZmkL43ZvGI9xWBVttynGblW0w6boCKsMQ6pztqimNUD7AnVda8Cua2qR2auaWS2GP4Antz5SYx3+5ykxdqXEOtQnMVKZCVGyEtABAAB6DVNDd2VlpVwul9LT0ztsT09P17Zt2zp9T2lpaafHl5aWdtj2+uuv6+qrr1ZDQ4MyMzO1cuVKpaamdvqZCxYs0D333HMSZ4KuSIqxa1T/JH309UG9s61c0/IHdMvn/r7oc9U5WzSsb7wuy+vTLZ+Jb9YnMUozLxikn5x/mjaVVOmVDbv1ye5q1Te1qMHp8v5scsnl9iZkZ4tbzpYmHajvnu+3WQzFRdoUFxmhWIet9bn3tcNmkcViyGJIFsOQxTBk+J+r9XX7/Wo93vvcMAzVOVt0oK5J++ud2l/fpAP1TTpQ16RaZ4tcbo8qap2qqHV+Y512q0VZyVHqnxyt/ikx6p8SrQEpMeqXEq2spCg5bNwkCmUej0eHml3+Gz/1Tpea3W5JkiHvteL9KXmfeZ+r3bbOXjtsFu+NJ7tNURFWek4AABBGwnb28gsuuECbN29WZWWlnnrqKX3ve9/T2rVrlZaWdsSxs2fP7tB6XlNTo+zs7GCW22tNGJquj74+qJX/KeuW0P1lRZ2WrN0lSbrzu0P5j6sJDMPQWf2SOl0T3ePxqMnl7hDC650tOtTkUn2TSw1N3hDT4NvX2ood57Aptl2gjo/s+Dou0iaHzWLKLOrOFpcO1Ddpf11rEK9vag3lTu2va/IH9P11Tu2pOqQml1tfVtTry4p6SR3nmTAMqU9ClPqntA/k0eqX7H0e4wjbf7KDpsXl1v76Ju9NkjqnqhqaVOd0tQZo7zAFf6Bu8vamaL+93tkidxB6VkRGWPw9QKLt3h4g0Yc9j2rfS8RuVWxkhP9mU3xkhOLbve6u4RwAAODEmfo/uNTUVFmtVpWVdZxIq6ysTBkZGZ2+JyMj47iOj4mJ0aBBgzRo0CCNHz9eOTk5evrppzt0ZfdxOBxyOBwneTboioKhabr/zW1a/XmlLvjtu8o/LUVnn5ai/FNTlBJ74n+T36zYpha3R9/JTdPZp3XeswHmMQxDDptVDps1bCa3c9isykyIUmZC1Dce63J7tK/6kHbub/A+DtRrZ2WDdh5o0M799WpocmlP1SHtqTqkNTv2H/H+1FiHspOjlJUUrewk78+spChlJ0erT2Jkr20ld7s9qj7UrIo6p7/HgS9UV9Q6Vdlu+4GGpm4ZjmAYUqzDpliHTRFWizzyzgfg+2zfdCkeebf598u337vXt83Z7FJDs8v//sZmtxqbm6Ru6gkSFWH1hvGoCH8PEG84t7UL596fMQ6b/0ZXrO+GlyNCkRHm3NgCAKCnMzV02+12jRo1SkVFRZo0aZIk70RqRUVFuvHGGzt9T35+voqKinTrrbf6t61cuVL5+fnH/C63291h3DZCw2mnxOrykX3094/36qvKen1VWe9vqc7NiGsN4akaOzBZCVERx/ysj74+oLf+XSaLIc2+ODcY5QMnxGoxWoNytM4Z1HGfx+NRZV2Tdu6vbw3l9dp5oEFf72/Qrv31OtjQrMo6b4DctKuq089Pj3couzWIZyVF+wN6VpL3poDd1nNaO1tcbh1o8PYgqKxz+n9W+l97n/tCdcsJND9bDO8NjFPiHEqKtivW0Ro0I22KcVgV64hQrMOq2EibYuztwqejLYRGRVi7PYB6PB41Nrv9PT0amlxHf+5sUUOzy9tLxOndXuv0znlQc6jZP6ngoWaXJOlQs0uHml0qP44hEEdjtRj+30Fcu0Due+37XUXbrYqKsCrK3v659bDn3t8hQR4A0BuYPnv50qVLNX36dD3xxBMaO3asFi5cqJdeeknbtm1Tenq6pk2bpr59+2rBggWSvEuGnXfeebr//vt1ySWX6MUXX9Svf/1rbdy4UcOGDVN9fb3uu+8+XXbZZcrMzFRlZaUWL16sJUuWaMOGDTrjjDO+sSZmLw++msZmrfvygNbs2K81Oyq1rbS2w36LIQ3vm6D801J19mkpGj0gSdH2tntGHo9Hkx9bo027qjR1bLYWTB4R7FMAAqr6ULN27W/Q7oMN2n3wkEpaf+4+2KCSA4f84epoLIaUER+pPolRSoyOOGzse4RiW1s9vdva9sVHRijGcfyzzXs8HjW7PHK2uNTU4pazxX3YT+/2+iZXh/DcPljvr2/SwS60SCdFR+iUOIc/UJ/i+9n6aB+0e8tkds0ut+oaW1TT6A3i/p/tgnltu33tu9f7fwZwskLD8LbCR9utioxoC+aOCKscNou3Z0yERQ6bRZHtt9ksrdu9wd2/zWbxv9dus8huPexn++1WC0OQAAAnpUfMXi55lwCrqKjQnDlzVFpaqpEjR2rFihX+ydJ27doli6XtP3tnn322lixZorvuukt33nmncnJytHz5cg0bNkySZLVatW3bNj333HOqrKxUSkqKxowZo9WrVx9X4IY54iMjVHB6ugpO9/7d99c59eGXB7RmR6WKd+zXl5X1+nh3tT7eXa3H39uhCKuhM7OT/N3R91U3atOuKkXbrfppwWCTzwbofglRERqelaDhWQlH7PN4PDpQ39Qawn2BvMH/evfBBjU2u7W3ulF7qxu79P3Rdqs/iEfbbWp2tYVpZ7sw7Wxxn+yp+hmGvDO/xziUGte2VFtqrEOprT99gTolxtGjWvKDJcJqUVKM/aSGc7jdbZPH1bYP487m1vHwrUv0tW4/1ORtVW9ocrV77t3ua533XScej/yt92aIsBqK6CyYW70BPsLa+rBZZG891vewt9/WemyHY2wWRVgM2awWRVgN2SwW2azGYc8tsllaf1oN2SxH7rNZLLJYvD0NLIYhq8WQ1TC4YQAAPYjpLd2hiJbu0LOv+pCKd+z3toR/UXnU4HBrQY5uJXQDHfi6ru8+2KC9VY3eFs3WFs6aRt8a582HrXXu3XayIdoXZBztWhkdNouiIqxKaQ3P3p9tQdoXrHtTi3Rv42oN8odag3lDs7eLfGNrAG9yudXY7A3nTt/P1ps7jc3en87mtm3OlvbHt94Ear0x5Hs0u7wTOYaTtgAufxBvH8qtrSHdMNrNot86Y3772fZbdxyxrf0M+/75CtrNReDxePzzFOiwbUcc326+A3e7eQ7c7eY3cLd7r8fT8Xt87/F/Xbv/vXqOeHL0Yw8/L99KFv5zb/09WCzebb7VLiTfahZqu/nR7nfc/mExvDdQfH8Dm/XIbVarcZSbMq03W/w/DUW07rNZO97IabsJdORzu63ttb39jaDWz+OmDdA9jjc3Ero7QegObR6PR7sONLR2Rd+v4h2VqqxrUt/EKL39028zwzPQjZpa3P5Q3jZOuEV2q7VDmG772W473XcRYnwrKPjDuMsXyNuGQHTc7lGzy+3v2dHscqvJt+3w1662z2l2edTU4lKLy6Nmt0cut3dbi8utFrfnsOdutbg8amk9xuXb5vb4l1gEupsv0NvbBXS7rfOeHP79/ucde4gcHvIdnXxex21GJ9taP8vWduOB+R7QExC6TwKhu2fxeDz6srJeydEn14USAIBQ4nZ75PJ4w7fb99OtI7Z12O/xyOXWEdsktWuBPnI2/c5aqNtar+VvAZbU2irescVcUrvX3oPaWtfbWtDbWo7bnndsbfa+w9uqfOR72uew9qGsfet827Z2+41259ru/N2tNzbc7VrW/c9bfydtr9uee/8Gbv/v2uX/Wx19m9vt8d5M8XjkOuwGTLP76DdiOuxvvZHje97U7oaP92ZP22vf/iaXO2DzMgSKYagtiLdryfcP+7C1bfPdDLBZjNahIB3f4993WOj3vdfW4Vhvr4P2vQZs7XoL+J4f3tuAXlm9V48Z0w2cLMMwdNopsWaXAQBAt7JYDFlkKKJ3rgaIbuTrQdHk76XRGtDdh/XkaPG0O6bzXh/OdsG+/dCNts9o6wXS1OLqcDPgyOM8/t4l7Xk88vc86Qkshvzd/yNsltbg3jY8wD9vg+8Y65HzOkRYjz3coONwhKMfG9HuBsGx55DoeGOBYQeBRegGAAAAwph3vLl3lYBQ5PF4/K38zS0dW/B9oby53dCOw28G+G8gtLsJ4Gvt999saP3sDq/bv7+1p0FTS8ceB00ut7e3QbsbD4eP/HD7bhJIkkkTQ3aHo9086NjK3xrsLd4eBx2PaZs3wNcbwT+nQLueBkcbytBxm/czEqIilJkQZfav5qQRugEAAACYxjAMf3BTDxgp2H7uhWZ/jwHv87ZhAR7/jYDDhw00udoPF2i7OdDV4QYdvrfd57W4PR2eH/5ZhwvFmwcXnp6up6aNNruMk0boBgAAAIDj5Os5IElymFtLV/l6F7QP/u0Dva9l/4jW/sNuJLQd13Yzofnw5y1HTjrZ1NKxl8ERE1O2vicxKsLsX1W3IHQDAAAAQC/S1rtAilJoDjsIJxazCwAAAAAAIFwRugEAAAAACBBCNwAAAAAAAULoBgAAAAAgQAjdAAAAAAAECKEbAAAAAIAAIXQDAAAAABAghG4AAAAAAAKE0A0AAAAAQIAQugEAAAAACBBCNwAAAAAAAULoBgAAAAAgQGxmFxCKPB6PJKmmpsbkSgAAAAAAociXF3358WgI3Z2ora2VJGVnZ5tcCQAAAAAglNXW1iohIeGo+w3PN8XyXsjtdmvv3r2Ki4uTYRhml9OpmpoaZWdnq6SkRPHx8WaXAxyBaxShjmsUoY5rFKGOaxShLtDXqMfjUW1trfr06SOL5egjt2np7oTFYlFWVpbZZRyX+Ph4/pFDSOMaRajjGkWo4xpFqOMaRagL5DV6rBZuHyZSAwAAAAAgQAjdAAAAAAAECKG7h3I4HJo7d64cDofZpQCd4hpFqOMaRajjGkWo4xpFqAuVa5SJ1AAAAAAACBBaugEAAAAACBBCNwAAAAAAAULoBgAAAAAgQAjdPdTixYs1YMAARUZGaty4cVq3bp3ZJaGX+te//qVLL71Uffr0kWEYWr58eYf9Ho9Hc+bMUWZmpqKiolRQUKDPP//cnGLR6yxYsEBjxoxRXFyc0tLSNGnSJG3fvr3DMY2NjZo5c6ZSUlIUGxurK6+8UmVlZSZVjN7mscce04gRI/xryObn5+vNN9/07+f6RKi5//77ZRiGbr31Vv82rlOYad68eTIMo8MjNzfXvz8Urk9Cdw+0dOlSzZo1S3PnztXGjRuVl5enwsJClZeXm10aeqH6+nrl5eVp8eLFne5/4IEH9Pvf/16PP/641q5dq5iYGBUWFqqxsTHIlaI3eu+99zRz5kx9+OGHWrlypZqbm3XRRRepvr7ef8xPf/pT/f3vf9eyZcv03nvvae/evZo8ebKJVaM3ycrK0v33368NGzZo/fr1+s53vqPLL79c//73vyVxfSK0fPTRR3riiSc0YsSIDtu5TmG2M844Q/v27fM/3n//ff++kLg+Pehxxo4d65k5c6b/tcvl8vTp08ezYMECE6sCPB5JnldffdX/2u12ezIyMjwPPvigf1tVVZXH4XB4XnjhBRMqRG9XXl7ukeR57733PB6P93qMiIjwLFu2zH/M1q1bPZI8xcXFZpWJXi4pKcnzxz/+kesTIaW2ttaTk5PjWblypee8887z3HLLLR6Ph39HYb65c+d68vLyOt0XKtcnLd09TFNTkzZs2KCCggL/NovFooKCAhUXF5tYGXCkr776SqWlpR2u14SEBI0bN47rFaaorq6WJCUnJ0uSNmzYoObm5g7XaG5urvr168c1iqBzuVx68cUXVV9fr/z8fK5PhJSZM2fqkksu6XA9Svw7itDw+eefq0+fPjr11FN1zTXXaNeuXZJC5/q0Be2b0C0qKyvlcrmUnp7eYXt6erq2bdtmUlVA50pLSyWp0+vVtw8IFrfbrVtvvVXnnHOOhg0bJsl7jdrtdiUmJnY4lmsUwfTpp58qPz9fjY2Nio2N1auvvqrTTz9dmzdv5vpESHjxxRe1ceNGffTRR0fs499RmG3cuHF69tlnNWTIEO3bt0/33HOPvvWtb2nLli0hc30SugEAvcLMmTO1ZcuWDuO8gFAwZMgQbd68WdXV1Xr55Zc1ffp0vffee2aXBUiSSkpKdMstt2jlypWKjIw0uxzgCBdffLH/+YgRIzRu3Dj1799fL730kqKiokysrA3dy3uY1NRUWa3WI2bcKysrU0ZGhklVAZ3zXZNcrzDbjTfeqNdff13//Oc/lZWV5d+ekZGhpqYmVVVVdTieaxTBZLfbNWjQII0aNUoLFixQXl6eHnnkEa5PhIQNGzaovLxcZ511lmw2m2w2m9577z39/ve/l81mU3p6OtcpQkpiYqIGDx6sL774ImT+HSV09zB2u12jRo1SUVGRf5vb7VZRUZHy8/NNrAw40sCBA5WRkdHheq2pqdHatWu5XhEUHo9HN954o1599VW98847GjhwYIf9o0aNUkRERIdrdPv27dq1axfXKEzjdrvldDq5PhESJkyYoE8//VSbN2/2P0aPHq1rrrnG/5zrFKGkrq5OO3bsUGZmZsj8O0r38h5o1qxZmj59ukaPHq2xY8dq4cKFqq+v13XXXWd2aeiF6urq9MUXX/hff/XVV9q8ebOSk5PVr18/3XrrrfrVr36lnJwcDRw4UHfffbf69OmjSZMmmVc0eo2ZM2dqyZIl+tvf/qa4uDj/+K2EhARFRUUpISFB119/vWbNmqXk5GTFx8frpptuUn5+vsaPH29y9egNZs+erYsvvlj9+vVTbW2tlixZonfffVdvvfUW1ydCQlxcnH8eDJ+YmBilpKT4t3Odwky33XabLr30UvXv31979+7V3LlzZbVaNXXq1JD5d5TQ3QNNmTJFFRUVmjNnjkpLSzVy5EitWLHiiMmqgGBYv369LrjgAv/rWbNmSZKmT5+uZ599Vj//+c9VX1+vG264QVVVVTr33HO1YsUKxoUhKB577DFJ0vnnn99h+5/+9CfNmDFDkvS73/1OFotFV155pZxOpwoLC/Xoo48GuVL0VuXl5Zo2bZr27dunhIQEjRgxQm+99ZYuvPBCSVyf6Bm4TmGm3bt3a+rUqdq/f79OOeUUnXvuufrwww91yimnSAqN69PweDyeoH4jAAAAAAC9BGO6AQAAAAAIEEI3AAAAAAABQugGAAAAACBACN0AAAAAAAQIoRsAAAAAgAAhdAMAAAAAECCEbgAAAAAAAoTQDQAAAABAgBC6AQBAwBiGoeXLl5tdBgAApiF0AwAQpmbMmCHDMI54TJw40ezSAADoNWxmFwAAAAJn4sSJ+tOf/tRhm8PhMKkaAAB6H1q6AQAIYw6HQxkZGR0eSUlJkrxdvx977DFdfPHFioqK0qmnnqqXX365w/s//fRTfec731FUVJRSUlJ0ww03qK6ursMxzzzzjM444ww5HA5lZmbqxhtv7LC/srJSV1xxhaKjo5WTk6PXXnstsCcNAEAIIXQDANCL3X333bryyiv18ccf65prrtHVV1+trVu3SpLq6+tVWFiopKQkffTRR1q2bJlWrVrVIVQ/9thjmjlzpm644QZ9+umneu211zRo0KAO33HPPffoe9/7nj755BN997vf1TXXXKMDBw4E9TwBADCL4fF4PGYXAQAAut+MGTP0f//3f4qMjOyw/c4779Sdd94pwzD0ox/9SI899ph/3/jx43XWWWfp0Ucf1VNPPaVf/OIXKikpUUxMjCTpjTfe0KWXXqq9e/cqPT1dffv21XXXXadf/epXndZgGIbuuusuzZ8/X5I3yMfGxurNN99kbDkAoFdgTDcAAGHsggsu6BCqJSk5Odn/PD8/v8O+/Px8bd68WZK0detW5eXl+QO3JJ1zzjlyu93avn27DMPQ3r17NWHChGPWMGLECP/zmJgYxcfHq7y8vKunBABAj0LoBgAgjMXExBzR3bu7REVFHddxERERHV4bhiG32x2IkgAACDmM6QYAoBf78MMPj3g9dOhQSdLQoUP18ccfq76+3r//gw8+kMVi0ZAhQxQXF6cBAwaoqKgoqDUDANCT0NINAEAYczqdKi0t7bDNZrMpNTVVkrRs2TKNHj1a5557rp5//nmtW7dOTz/9tCTpmmuu0dy5czV9+nTNmzdPFRUVuummm3TttdcqPT1dkjRv3jz96Ec/Ulpami6++GLV1tbqgw8+0E033RTcEwUAIEQRugEACGMrVqxQZmZmh21DhgzRtm3bJHlnFn/xxRf1k5/8RJmZmXrhhRd0+umnS5Kio6P11ltv6ZZbbtGYMWMUHR2tK6+8Ug8//LD/s6ZPn67Gxkb97ne/02233abU1FRdddVVwTtBAABCHLOXAwDQSxmGoVdffVWTJk0yuxQAAMIWY7oBAAAAAAgQQjcAAAAAAAHCmG4AAHopRpgBABB4tHQDAAAAABAghG4AAAAAAAKE0A0AAAAAQIAQugEAAAAACBBCNwAAAAAAAULoBgAAAAAgQAjdAAAAAAAECKEbAAAAAIAAIXQDAAAAABAg/x+1Q78oUBmRDAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["MSE: 19654.4434, RMSE: 140.1943, MAE: 104.5653, R2: -0.8791\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader, TensorDataset\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import matplotlib.pyplot as plt\n", "\n", "# 设备与随机种子\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "torch.manual_seed(42)\n", "np.random.seed(42)\n", "\n", "# 数据加载与预处理\n", "# 读取 Excel，并将 datetime 列解析为日期，设置为索引\n", "df = pd.read_excel(\"D:\\\\埃塞俄比亚咖啡\\\\2005(2013)-2017(2025) monthly data(1).xlsx\", parse_dates=['datetime'])\n", "df.set_index('datetime', inplace=True)\n", "\n", "# 仅使用 'IN MILLION USD' 列进行单变量预测\n", "rec_df = df[['IN MILLION USD']].copy()\n", "rec_df.fillna(method='ffill', inplace=True)\n", "assert not rec_df.isnull().any().any(), \"数据中仍存在缺失值\"\n", "\n", "# 转为 1D numpy 浮点数组 (单变量)\n", "values = rec_df.values.flatten().astype(np.float32)\n", "\n", "# 定义 Dataset 类\n", "class TimeSeriesDataset(Dataset):\n", "    def __init__(self, series, n_in, n_out=1, step=1):\n", "        self.X, self.y = [], []\n", "        for i in range(0, len(series) - n_in - n_out + 1, step):\n", "            self.X.append(series[i:i + n_in])\n", "            self.y.append(series[i + n_in:i + n_in + n_out])\n", "        # X shape: (samples, n_in), y shape: (samples, n_out)\n", "        self.X = torch.FloatTensor(np.array(self.X)).unsqueeze(-1)  # (samples, n_in, 1)\n", "        self.y = torch.FloatTensor(np.array(self.y))  # (samples, n_out)\n", "    def __len__(self):\n", "        return len(self.X)\n", "    def __getitem__(self, idx):\n", "        return self.X[idx], self.y[idx]\n", "\n", "# 参数设置\n", "n_in = 12         # 输入时间步长\n", "n_out = 1        # 输出步长\n", "step = 1\n", "batch_size = 64\n", "train_ratio = 0.8\n", "\n", "total_len = len(values)\n", "split = int(total_len * train_ratio)\n", "train_series = values[:split]\n", "test_series  = values[split:]\n", "\n", "# 创建 Dataset\n", "train_ds = TimeSeriesDataset(train_series, n_in, n_out, step)\n", "test_ds  = TimeSeriesDataset(test_series,  n_in, n_out, step)\n", "\n", "# 归一化，仅使用训练集拟合\n", "scaler = MinMaxScaler()\n", "scaler.fit(train_series.reshape(-1, 1))  # 训练集一维数据需 reshape\n", "\n", "def scale_dataset(ds):\n", "    X = ds.<PERSON><PERSON>nump<PERSON>().reshape(-1, 1)\n", "    y = ds.y.numpy().reshape(-1, 1)\n", "    X_scaled = scaler.transform(X).reshape(ds.X.shape)\n", "    y_scaled = scaler.transform(y).reshape(ds.y.shape)\n", "    return TensorDataset(torch.FloatTensor(X_scaled), torch.FloatTensor(y_scaled))\n", "\n", "train_scaled = scale_dataset(train_ds)\n", "test_scaled  = scale_dataset(test_ds)\n", "\n", "# DataLoader\n", "data_train = DataLoader(train_scaled, batch_size=batch_size, shuffle=False)\n", "data_test  = DataLoader(test_scaled,  batch_size=batch_size, shuffle=False)\n", "\n", "# 定义 LSTM 模型\n", "class LSTMUnivariate(nn.Module):\n", "    def __init__(self, n_hidden=32, n_layers=2):\n", "        super().__init__()\n", "        self.lstm = nn.LSTM(input_size=1, hidden_size=n_hidden, num_layers=n_layers, batch_first=True)\n", "        self.fc = nn.Linear(n_hidden * n_in, n_out)\n", "    def forward(self, x):\n", "        out, _ = self.lstm(x)\n", "        out = out.reshape(out.size(0), -1)\n", "        return self.fc(out)\n", "\n", "model = LSTMUnivariate().to(device)\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "\n", "# 训练\n", "train_losses = []\n", "for epoch in range(50):\n", "    model.train()\n", "    total_loss = 0\n", "    for X_batch, y_batch in data_train:\n", "        X_batch, y_batch = X_batch.to(device), y_batch.to(device)\n", "        optimizer.zero_grad()\n", "        preds = model(X_batch)\n", "        loss = criterion(preds, y_batch)\n", "        loss.backward()\n", "        optimizer.step()\n", "        total_loss += loss.item() * X_batch.size(0)\n", "    epoch_loss = total_loss / len(data_train.dataset)\n", "    train_losses.append(epoch_loss)\n", "    print(f\"Epoch {epoch+1:03d} | Train Loss: {epoch_loss:.4f}\")\n", "\n", "# 绘制训练损失曲线\n", "plt.figure(figsize=(10, 4))\n", "plt.plot(train_losses, label='Train Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 测试与评估\n", "model.eval()\n", "y_true, y_pred = [], []\n", "with torch.no_grad():\n", "    for X_batch, y_batch in data_test:\n", "        X_batch = X_batch.to(device)\n", "        preds = model(X_batch)\n", "        y_true.extend(y_batch.cpu().numpy().flatten())\n", "        y_pred.extend(preds.cpu().numpy().flatten())\n", "\n", "# 反归一化\n", "y_true = scaler.inverse_transform(np.array(y_true).reshape(-1,1)).flatten()\n", "y_pred = scaler.inverse_transform(np.array(y_pred).reshape(-1,1)).flatten()\n", "\n", "mse  = mean_squared_error(y_true, y_pred)\n", "rmse = np.sqrt(mse)\n", "mae  = mean_absolute_error(y_true, y_pred)\n", "r2   = r2_score(y_true, y_pred)\n", "print(f\"MSE: {mse:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}, R2: {r2:.4f}\")\n", "\n", "# 可视化真实值 vs 预测值 (最后 100 点)\n", "plt.figure(figsize=(12,4))\n", "plt.plot(y_true[-100:], label='True')\n", "plt.plot(y_pred[-100:], label='Pred')\n", "plt.xlabel('Samples')\n", "plt.ylabel('IN MILLION USD')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 1, "id": "8a50a5d7-b40a-4202-a9c9-1b1593b0fd60", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据基本信息:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 156 entries, 0 to 155\n", "Data columns (total 13 columns):\n", " #   Column                                        Non-Null Count  Dtype  \n", "---  ------                                        --------------  -----  \n", " 0   Index                                         156 non-null    int64  \n", " 1   Ethiopia Calendar                             156 non-null    int64  \n", " 2   Unnamed: 2                                    156 non-null    object \n", " 3   datetime                                      156 non-null    object \n", " 4   QTY (Ton)                                     156 non-null    float64\n", " 5   IN MILLION USD                                156 non-null    float64\n", " 6   sales price（usd/lb）                           156 non-null    float64\n", " 7   ICO Composite Indicator Price(I-CIP)(usd/lb)  78 non-null     float64\n", " 8   Exchange(Domestic currency per US Dollar)     156 non-null    float64\n", " 9   Production Cost                               0 non-null      float64\n", " 10  Production                                    0 non-null      float64\n", " 11  Total/year QYT                                14 non-null     object \n", " 12  Total/year MILLION USD                        13 non-null     object \n", "dtypes: float64(7), int64(2), object(4)\n", "memory usage: 16.0+ KB\n", "None\n", "\n", "数据预览:\n", "   Index  Ethiopia Calendar        Unnamed: 2 datetime  QTY (Ton)  \\\n", "0      1               2005        A ???/July   Jul-12  12402.280   \n", "1      2               2005      B ???/August   Aug-12  21794.548   \n", "2      3               2005  C?????/September   Sep-12  13451.116   \n", "3      4               2005    D ????/October   Oct-12  15905.918   \n", "4      5               2005    E ???/November   Nov-12  17367.370   \n", "\n", "   IN MILLION USD  sales price（usd/lb）  \\\n", "0           54.78             2.003427   \n", "1           89.24             1.857312   \n", "2           54.97             1.853827   \n", "3           58.95             1.681170   \n", "4           63.99             1.671289   \n", "\n", "   ICO Composite Indicator Price(I-CIP)(usd/lb)  \\\n", "0                                      3.511479   \n", "1                                      3.278146   \n", "2                                      3.339514   \n", "3                                      3.247682   \n", "4                                      3.009934   \n", "\n", "   Exchange(Domestic currency per US Dollar)  Production Cost  Production  \\\n", "0                                  17.800005              NaN         NaN   \n", "1                                  17.878167              NaN         NaN   \n", "2                                  17.935412              NaN         NaN   \n", "3                                  18.003773              NaN         NaN   \n", "4                                  18.075355              NaN         NaN   \n", "\n", "  Total/year QYT Total/year MILLION USD  \n", "0            NaN                    NaN  \n", "1            NaN                    NaN  \n", "2            NaN                    NaN  \n", "3            NaN                    NaN  \n", "4            NaN                    NaN  \n", "\n", "各变量缺失值情况:\n", "sales price（usd/lb）                          0\n", "IN MILLION USD                               0\n", "Exchange(Domestic currency per US Dollar)    0\n", "QTY (Ton)                                    0\n", "dtype: int64\n", "\n", "相关性矩阵:\n", "                                           sales price（usd/lb）  \\\n", "sales price（usd/lb）                                   1.000000   \n", "IN MILLION USD                                        0.736443   \n", "Exchange(Domestic currency per US Dollar)             0.653640   \n", "QTY (Ton)                                             0.490698   \n", "\n", "                                           IN MILLION USD  \\\n", "sales price（usd/lb）                              0.736443   \n", "IN MILLION USD                                   1.000000   \n", "Exchange(Domestic currency per US Dollar)        0.730992   \n", "QTY (Ton)                                        0.926211   \n", "\n", "                                           Exchange(Domestic currency per US Dollar)  \\\n", "sales price（usd/lb）                                                         0.653640   \n", "IN MILLION USD                                                              0.730992   \n", "Exchange(Domestic currency per US Dollar)                                   1.000000   \n", "QTY (Ton)                                                                   0.612143   \n", "\n", "                                           QTY (Ton)  \n", "sales price（usd/lb）                         0.490698  \n", "IN MILLION USD                              0.926211  \n", "Exchange(Domestic currency per US Dollar)   0.612143  \n", "QTY (Ton)                                   1.000000  \n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_13808\\3693058070.py:34: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  analysis_df['QTY (Ton)'] = pd.to_numeric(analysis_df['QTY (Ton)'], errors='coerce')\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "IN MILLION USD 与其他变量的相关性分析:\n", "\n", "变量: sales price（usd/lb）\n", "皮尔逊相关系数: 0.7364, p 值: 6.2989e-28\n", "斯皮尔曼相关系数: 0.6390, p 值: 2.8003e-19\n", "\n", "变量: Exchange(Domestic currency per US Dollar)\n", "皮尔逊相关系数: 0.7310, p 值: 2.4087e-27\n", "斯皮尔曼相关系数: 0.5603, p 值: 2.8236e-14\n", "\n", "变量: QTY (Ton)\n", "皮尔逊相关系数: 0.9262, p 值: 3.9627e-67\n", "斯皮尔曼相关系数: 0.9270, p 值: 1.7032e-67\n"]}, {"ename": "KeyError", "evalue": "'temp'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\pandas\\core\\indexes\\base.py:3802\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key, method, tolerance)\u001b[0m\n\u001b[0;32m   3801\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3802\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3803\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON>rro<PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\pandas\\_libs\\index.pyx:138\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\pandas\\_libs\\index.pyx:165\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:5745\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:5753\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'temp'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 84\u001b[0m\n\u001b[0;32m     82\u001b[0m \u001b[38;5;66;03m# 散点图 1：ghradiation vs temp\u001b[39;00m\n\u001b[0;32m     83\u001b[0m plt\u001b[38;5;241m.\u001b[39msubplot(\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m2\u001b[39m, \u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m---> 84\u001b[0m \u001b[43msns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mregplot\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mtemp\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mghiradiation\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43manalysis_df\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mscatter_kws\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43malpha\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m:\u001b[49m\u001b[38;5;241;43m0.5\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     85\u001b[0m plt\u001b[38;5;241m.\u001b[39mtitle(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mGHI 辐射量与温度的关系\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     86\u001b[0m plt\u001b[38;5;241m.\u001b[39mxlabel(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m温度 (temp)\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\seaborn\\regression.py:763\u001b[0m, in \u001b[0;36mregplot\u001b[1;34m(data, x, y, x_estimator, x_bins, x_ci, scatter, fit_reg, ci, n_boot, units, seed, order, logistic, lowess, robust, logx, x_partial, y_partial, truncate, dropna, x_jitter, y_jitter, label, color, marker, scatter_kws, line_kws, ax)\u001b[0m\n\u001b[0;32m    752\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mregplot\u001b[39m(\n\u001b[0;32m    753\u001b[0m     data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m, x\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, y\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01m<PERSON>one\u001b[39;00m,\n\u001b[0;32m    754\u001b[0m     x_estimator\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, x_bins\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, x_ci\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mci\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    760\u001b[0m     scatter_kws\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, line_kws\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, ax\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    761\u001b[0m ):\n\u001b[1;32m--> 763\u001b[0m     plotter \u001b[38;5;241m=\u001b[39m \u001b[43m_RegressionPlotter\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx_estimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx_bins\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx_ci\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    764\u001b[0m \u001b[43m                                 \u001b[49m\u001b[43mscatter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfit_reg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mci\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mn_boot\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43munits\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    765\u001b[0m \u001b[43m                                 \u001b[49m\u001b[43morder\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogistic\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlowess\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrobust\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogx\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    766\u001b[0m \u001b[43m                                 \u001b[49m\u001b[43mx_partial\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_partial\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtruncate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdropna\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    767\u001b[0m \u001b[43m                                 \u001b[49m\u001b[43mx_jitter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_jitter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcolor\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlabel\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    769\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ax \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    770\u001b[0m         ax \u001b[38;5;241m=\u001b[39m plt\u001b[38;5;241m.\u001b[39mgca()\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\seaborn\\regression.py:107\u001b[0m, in \u001b[0;36m_RegressionPlotter.__init__\u001b[1;34m(self, x, y, data, x_estimator, x_bins, x_ci, scatter, fit_reg, ci, n_boot, units, seed, order, logistic, lowess, robust, logx, x_partial, y_partial, truncate, dropna, x_jitter, y_jitter, color, label)\u001b[0m\n\u001b[0;32m    104\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMutually exclusive regression options.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    106\u001b[0m \u001b[38;5;66;03m# Extract the data vals from the arguments or passed dataframe\u001b[39;00m\n\u001b[1;32m--> 107\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mestablish_variables\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43munits\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43munits\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    108\u001b[0m \u001b[43m                         \u001b[49m\u001b[43mx_partial\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mx_partial\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_partial\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43my_partial\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    110\u001b[0m \u001b[38;5;66;03m# Drop null observations\u001b[39;00m\n\u001b[0;32m    111\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m dropna:\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\seaborn\\regression.py:44\u001b[0m, in \u001b[0;36m_LinearPlotter.establish_variables\u001b[1;34m(self, data, **kws)\u001b[0m\n\u001b[0;32m     42\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m var, val \u001b[38;5;129;01min\u001b[39;00m kws\u001b[38;5;241m.\u001b[39mitems():\n\u001b[0;32m     43\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(val, \u001b[38;5;28mstr\u001b[39m):\n\u001b[1;32m---> 44\u001b[0m         vector \u001b[38;5;241m=\u001b[39m \u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[43mval\u001b[49m\u001b[43m]\u001b[49m\n\u001b[0;32m     45\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(val, \u001b[38;5;28mlist\u001b[39m):\n\u001b[0;32m     46\u001b[0m         vector \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39masarray(val)\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\pandas\\core\\frame.py:3807\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3805\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   3806\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 3807\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3808\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   3809\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32m~\\anaconda3\\envs\\py38env\\lib\\site-packages\\pandas\\core\\indexes\\base.py:3804\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key, method, tolerance)\u001b[0m\n\u001b[0;32m   3802\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine\u001b[38;5;241m.\u001b[39mget_loc(casted_key)\n\u001b[0;32m   3803\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m-> 3804\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m   3805\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mType<PERSON>rror\u001b[39;00m:\n\u001b[0;32m   3806\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3808\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3809\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'temp'"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 相关性分析代码\n", "import pandas as pd\n", "import numpy as np\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import pearsonr, spearmanr\n", "import matplotlib.pyplot as plt\n", "\n", "# 设置中文字体（适配常用系统）\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 黑体（Windows 推荐）\n", "plt.rcParams['axes.unicode_minus'] = False    # 负号正常显示\n", "\n", "\n", "# 设置文件路径\n", "file_path = r\"D:\\\\埃塞俄比亚咖啡\\\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "\n", "# 读取 CSV 文件\n", "df = pd.read_csv(file_path,encoding='gbk')\n", "\n", "# 查看数据基本信息\n", "print(\"数据基本信息:\")\n", "print(df.info())\n", "print(\"\\n数据预览:\")\n", "print(df.head())\n", "\n", "# 选择需要分析的数值型变量（排除非数值型变量）\n", "selected_columns = ['sales price（usd/lb）','IN MILLION USD','Exchange(Domestic currency per US Dollar)','QTY (Ton)'\n", "    \n", "]\n", "\n", "# 筛选数据\n", "analysis_df = df[selected_columns]\n", "# 假设你的DataFrame名为 df\n", "analysis_df['QTY (Ton)'] = pd.to_numeric(analysis_df['QTY (Ton)'], errors='coerce')\n", "\n", "# 检查缺失值\n", "print(\"\\n各变量缺失值情况:\")\n", "print(analysis_df.isnull().sum())\n", "\n", "# 处理缺失值（示例：简单删除含有缺失值的行，实际应用中可选择其他方法）\n", "analysis_df = analysis_df.dropna()\n", "\n", "# 计算相关性矩阵（皮尔逊相关系数）\n", "correlation_matrix = analysis_df.corr()\n", "\n", "# 打印相关性矩阵\n", "print(\"\\n相关性矩阵:\")\n", "print(correlation_matrix)\n", "\n", "# 保存相关性矩阵到 CSV 文件\n", "#correlation_matrix.to_csv(r\"D:\\\\GHI预测\\\\f广西\\\\correlation_matrix.csv\")\n", "\n", "# 可视化相关性矩阵 - 热图\n", "plt.figure(figsize=(16, 12))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, square=True, \n", "            cbar_kws={\"shrink\": .8}, fmt=\".2f\")\n", "plt.title('变量相关性热图')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.yticks(rotation=0)\n", "plt.tight_layout()\n", "plt.savefig(r\"D:\\GHI预测\\f广西\\correlation_heatmap.png\", dpi=300)\n", "plt.show()\n", "\n", "# 计算并打印皮尔逊和斯皮尔曼相关系数及 p 值（以 ghradiation 与其他变量为例）\n", "target_variable = 'IN MILLION USD'\n", "print(f\"\\n{target_variable} 与其他变量的相关性分析:\")\n", "\n", "for column in analysis_df.columns:\n", "    if column != target_variable:\n", "        # 皮尔逊相关\n", "        pearson_corr, pearson_p = pearsonr(analysis_df[target_variable], analysis_df[column])\n", "        # 斯皮尔曼相关\n", "        spearman_corr, spearman_p = spearmanr(analysis_df[target_variable], analysis_df[column])\n", "        \n", "        print(f\"\\n变量: {column}\")\n", "        print(f\"皮尔逊相关系数: {pearson_corr:.4f}, p 值: {pearson_p:.4e}\")\n", "        print(f\"斯皮尔曼相关系数: {spearman_corr:.4f}, p 值: {spearman_p:.4e}\")\n", "\n", "# 可视化部分变量的相关性散点图（示例：ghradiation 与 temp 和 solarradiation）\n", "plt.figure(figsize=(14, 6))\n", "\n", "# 散点图 1：ghradiation vs temp\n", "plt.subplot(1, 2, 1)\n", "sns.regplot(x='temp', y='ghiradiation', data=analysis_df, scatter_kws={'alpha':0.5})\n", "plt.title('GHI 辐射量与温度的关系')\n", "plt.xlabel('温度 (temp)')\n", "plt.ylabel('GHI 辐射量 (ghiradiation)')\n", "\n", "# 散点图 2：ghradiation vs solarradiation\n", "plt.subplot(1, 2, 2)\n", "sns.regplot(x='solarradiation', y='ghiradiation', data=analysis_df, scatter_kws={'alpha':0.5})\n", "plt.title('GHI 辐射量与太阳辐射量的关系')\n", "plt.xlabel('太阳辐射量 (solarradiation)')\n", "plt.ylabel('GHI 辐射量 (ghiradiation)')\n", "\n", "plt.tight_layout()\n", "plt.savefig(r\"D:\\GHI预测\\f广西\\scatter_plots.png\", dpi=300)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "id": "c87a84ed-7cf0-4bba-aa23-4ca192db33fc", "metadata": {}, "outputs": [], "source": ["\n", "if df[cols].iloc[-1].isnull().all():\n", "    df = df.iloc[:-1]"]}, {"cell_type": "code", "execution_count": 5, "id": "bb3e56c3-151d-43ac-b338-f3c5c3188b35", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 156 entries, 0 to 155\n", "Data columns (total 13 columns):\n", " #   Column                                        Non-Null Count  Dtype  \n", "---  ------                                        --------------  -----  \n", " 0   Index                                         156 non-null    int64  \n", " 1   Ethiopia Calendar                             156 non-null    int64  \n", " 2   Unnamed: 2                                    156 non-null    object \n", " 3   datetime                                      156 non-null    object \n", " 4   QTY (Ton)                                     156 non-null    float64\n", " 5   IN MILLION USD                                156 non-null    float64\n", " 6   sales price（usd/lb）                           156 non-null    float64\n", " 7   ICO Composite Indicator Price(I-CIP)(usd/lb)  78 non-null     float64\n", " 8   Exchange(Domestic currency per US Dollar)     156 non-null    float64\n", " 9   Production Cost                               0 non-null      float64\n", " 10  Production                                    0 non-null      float64\n", " 11  Total/year QYT                                14 non-null     object \n", " 12  Total/year MILLION USD                        13 non-null     object \n", "dtypes: float64(7), int64(2), object(4)\n", "memory usage: 16.0+ KB\n", "None\n", "Empty DataFrame\n", "Columns: [Index, Ethiopia Calendar, Unnamed: 2, datetime, QTY (Ton), IN MILLION USD, sales price（usd/lb）, ICO Composite Indicator Price(I-CIP)(usd/lb), Exchange(Domestic currency per US Dollar), Production Cost, Production, Total/year QYT, Total/year MILLION USD]\n", "Index: []\n"]}], "source": ["# 1. 先看 df.info(), 确认哪一行有缺失\n", "print(df.info())\n", "\n", "# 2. 把含缺失值的整行打印出来\n", "cols = ['sales price（usd/lb）','IN MILLION USD','Exchange(Domestic currency per US Dollar)','QTY (Ton)']\n", "missing_rows = df[df[cols].isnull().any(axis=1)]\n", "print(missing_rows)"]}, {"cell_type": "code", "execution_count": 10, "id": "9a67b8b6-7eec-4f31-bafc-7eaacbc2048f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 700x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# 如果需要显示中文标签，取消下面两行注释：\n", "# plt.rcParams['font.sans-serif'] = ['SimHei']\n", "# plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 1. 读取数据\n", "file_path = r\"D:\\\\埃塞俄比亚咖啡\\\\2005(2013)-2017(2025) monthly data(1).csv\"   # ← 修改为你的文件路径\n", "df = pd.read_csv(\n", "    file_path,\n", "    parse_dates=['datetime'], \n", "    index_col='datetime',\n", "    encoding='gbk'  # 如有编码问题可改为 'gbk' 或 'gb18030'\n", ")\n", "\n", "# 2. 只取前 78 条\n", "df2 = df.iloc[:78].copy()\n", "\n", "# 3. 选取需要做相关分析的列\n", "cols = [\n", "    'sales price（usd/lb）',\n", "    'Exchange(Domestic currency per US Dollar)',\n", "    'IN MILLION USD',\n", "    'QTY (Ton)',\n", "    'ICO Composite Indicator Price(I-CIP)(usd/lb)'\n", "]\n", "data = df2[cols]\n", "\n", "# 4. 计算相关系数矩阵\n", "corr = data.corr()\n", "\n", "# 5. 绘制热力图\n", "plt.figure(figsize=(7, 5))\n", "sns.heatmap(\n", "    corr,\n", "    annot=True,\n", "    fmt=\".2f\",\n", "    cmap='coolwarm',\n", "    center=0,\n", "    square=True,\n", "    cbar_kws={'shrink': .8}\n", ")\n", "plt.title('五变量相关性热图 (前78条)')\n", "plt.xticks(rotation=45, ha='right', fontsize=8)\n", "plt.yticks(rotation=0, fontsize=8)\n", "plt.tight_layout()\n", "\n", "# 6. 保存并展示\n", "#plt.savefig(r\"D:\\path\\to\\output\\corr_heatmap_78.png\", dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 11, "id": "4b9f6aec-1f4b-4a80-95a3-5cf1171911cb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 700x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# 如果需要显示中文标签，取消下面两行注释：\n", "# plt.rcParams['font.sans-serif'] = ['SimHei']\n", "# plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 1. 读取数据\n", "file_path = r\"D:\\\\埃塞俄比亚咖啡\\\\2005(2013)-2017(2025) monthly data(1).csv\"   # ← 修改为你的文件路径\n", "df = pd.read_csv(\n", "    file_path,\n", "    parse_dates=['datetime'], \n", "    index_col='datetime',\n", "    encoding='gbk'  # 如有编码问题可改为 'gbk' 或 'gb18030'\n", ")\n", "\n", "# 2. 只取前 78 条\n", "df2 = df.iloc[:78].copy()\n", "\n", "# 3. 选取需要做相关分析的列\n", "cols = [\n", "    'sales price（usd/lb）',\n", "    'Exchange(Domestic currency per US Dollar)',\n", "    'IN MILLION USD',\n", "    'QTY (Ton)'\n", "    \n", "]\n", "data = df2[cols]\n", "\n", "# 4. 计算相关系数矩阵\n", "corr = data.corr()\n", "\n", "# 5. 绘制热力图\n", "plt.figure(figsize=(7, 5))\n", "sns.heatmap(\n", "    corr,\n", "    annot=True,\n", "    fmt=\".2f\",\n", "    cmap='coolwarm',\n", "    center=0,\n", "    square=True,\n", "    cbar_kws={'shrink': .8}\n", ")\n", "plt.title('五变量相关性热图 (前78条)')\n", "plt.xticks(rotation=45, ha='right', fontsize=8)\n", "plt.yticks(rotation=0, fontsize=8)\n", "plt.tight_layout()\n", "\n", "# 6. 保存并展示\n", "#plt.savefig(r\"D:\\path\\to\\output\\corr_heatmap_78.png\", dpi=300)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "2eed2bfa-4b0e-4ea6-95e9-5f805a3fc378", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}