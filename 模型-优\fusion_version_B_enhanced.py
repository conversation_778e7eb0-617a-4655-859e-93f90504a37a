"""
版本B增强版：X11分解融合预测模型 - Trend和Residual都使用productivity_lag1
Trend: 线性回归8特征模型 (添加productivity_lag1)
Seasonal: 完全周期性模式
Residual: 梯度提升14特征模型 (添加productivity_lag1)
最终预测 = Trend预测 + Seasonal预测 + Residual预测

实验目的: 测试产能(productivity_lag1)在Trend和Residual中的全面影响
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("=== 版本B增强版：X11分解融合预测模型 - Trend和Residual都使用productivity_lag1 ===")
print("Trend: 线性回归8特征模型 (添加productivity_lag1)")
print("Seasonal: 完全周期性模式")
print("Residual: 梯度提升14特征模型 (添加productivity_lag1)")
print("实验目的: 测试产能(productivity_lag1)在Trend和Residual中的全面影响")

# 1. 数据加载
print("\n1. 数据加载")
print("-" * 50)

# 加载X11分解结果
decomp_path = r"D:\埃塞俄比亚咖啡\模型\x11_decomposition_results.csv"
decomp_data = pd.read_csv(decomp_path)

# 加载外生变量（包含prod_lag1和productivity_lag1）
exog_path = r"D:\埃塞俄比亚咖啡\2005(2013)-2017(2025) monthly data(1).csv"
exog_data = pd.read_csv(exog_path, encoding='gbk')

print(f"X11分解数据: {decomp_data.shape}")
print(f"外生变量数据: {exog_data.shape}")

# 检查数据列
if 'prod_lag1' in exog_data.columns:
    print("Found prod_lag1 column")
if 'productivity_lag1' in exog_data.columns:
    print("Found productivity_lag1 column")
    print(f"productivity_lag1样本值: {exog_data['productivity_lag1'].dropna().head().tolist()}")

# 2. 数据合并和预处理
print("\n2. 数据合并和预处理")
print("-" * 50)

# 合并数据
min_len = min(len(decomp_data), len(exog_data))

# 处理productivity_lag1变量
productivity_lag1_col = exog_data['productivity_lag1'].iloc[:min_len]
if productivity_lag1_col.dtype == 'object':
    productivity_lag1_col = productivity_lag1_col.astype(str).str.replace(',', '').str.replace('"', '')
    productivity_lag1_col = pd.to_numeric(productivity_lag1_col, errors='coerce')
    print("productivity_lag1已转换为数值格式")

data = pd.DataFrame({
    'date': pd.to_datetime(decomp_data['parsed_datetime'].iloc[:min_len]),
    'original': decomp_data['Original'].iloc[:min_len],
    'trend': decomp_data['Trend'].iloc[:min_len],
    'seasonal': decomp_data['Seasonal'].iloc[:min_len],
    'residual': decomp_data['Residual'].iloc[:min_len],
    'sales_price': exog_data['sales price（usd/lb）'].iloc[:min_len],
    'exchange_rate': exog_data['Exchange(Domestic currency per US Dollar)'].iloc[:min_len],
    'productivity_lag1': productivity_lag1_col  # 仅使用产能
})

data.set_index('date', inplace=True)
data = data.dropna()

print(f"合并后数据量: {len(data)} 样本")
print(f"时间范围: {data.index[0]} - {data.index[-1]}")
print(f"productivity_lag1统计: 最小值={data['productivity_lag1'].min():.2f}, 最大值={data['productivity_lag1'].max():.2f}")

# 3. 数据分割
print("\n3. 数据分割")
print("-" * 50)

split_date = '2022-12-01'
train_data = data[data.index < split_date]
test_data = data[data.index >= split_date]

print(f"训练集: {len(train_data)} 样本 ({train_data.index[0]} - {train_data.index[-1]})")
print(f"测试集: {len(test_data)} 样本 ({test_data.index[0]} - {test_data.index[-1]})")

# 4. Trend预测 - 添加productivity_lag1
print("\n4. Trend预测 - 线性回归8特征模型（添加productivity_lag1）")
print("-" * 50)

def create_trend_features(data):
    """创建Trend预测的8个特征（添加productivity_lag1）"""
    features_data = data.copy()
    
    # 时间特征
    features_data['year'] = features_data.index.year
    features_data['month'] = features_data.index.month
    features_data['time_index'] = range(len(features_data))
    
    # Trend历史特征
    features_data['trend_lag_1'] = features_data['trend'].shift(1)
    features_data['trend_lag_12'] = features_data['trend'].shift(12)
    
    # 外生变量
    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)
    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)
    
    # 版本B增强版：仅使用产能
    features_data['productivity_lag1'] = features_data['productivity_lag1']
    
    return features_data.dropna()

# Trend特征列表（8个特征）
trend_features_list = [
    'time_index', 'trend_lag_1', 'trend_lag_12', 'sales_price_lag_1', 
    'exchange_rate_lag_1', 'year', 'month', 'productivity_lag1'
]

trend_train_data = create_trend_features(train_data)
trend_test_data = create_trend_features(test_data)

if len(trend_test_data) == 0:
    full_trend_data = create_trend_features(data)
    trend_split_idx = int(len(full_trend_data) * 0.8)
    trend_train_data = full_trend_data.iloc[:trend_split_idx]
    trend_test_data = full_trend_data.iloc[trend_split_idx:]

X_trend_train = trend_train_data[trend_features_list]
y_trend_train = trend_train_data['trend']
X_trend_test = trend_test_data[trend_features_list]
y_trend_test = trend_test_data['trend']

# 标准化和训练
trend_scaler = StandardScaler()
X_trend_train_scaled = trend_scaler.fit_transform(X_trend_train)
X_trend_test_scaled = trend_scaler.transform(X_trend_test)

trend_model = LinearRegression()
trend_model.fit(X_trend_train_scaled, y_trend_train)

trend_train_pred = trend_model.predict(X_trend_train_scaled)
trend_test_pred = trend_model.predict(X_trend_test_scaled)

print(f"Trend模型训练完成（版本B增强版：含productivity_lag1）")
print(f"  训练集RMSE: {np.sqrt(mean_squared_error(y_trend_train, trend_train_pred)):.2f}")
print(f"  测试集RMSE: {np.sqrt(mean_squared_error(y_trend_test, trend_test_pred)):.2f}")
print(f"  测试集R²: {r2_score(y_trend_test, trend_test_pred):.4f}")

# 特征重要性分析
trend_feature_importance = pd.DataFrame({
    'feature': trend_features_list,
    'coefficient': trend_model.coef_,
    'abs_coefficient': np.abs(trend_model.coef_)
}).sort_values('abs_coefficient', ascending=False)

productivity_importance_rank = trend_feature_importance[trend_feature_importance['feature'] == 'productivity_lag1'].index[0] + 1
productivity_coefficient = trend_feature_importance[trend_feature_importance['feature'] == 'productivity_lag1']['coefficient'].iloc[0]
print(f"  productivity_lag1重要性排名: {productivity_importance_rank}/8")
print(f"  productivity_lag1系数: {productivity_coefficient:.6f}")

# 5. Seasonal预测 - 不变
print("\n5. Seasonal预测 - 完全周期性模式")
print("-" * 50)

seasonal_pattern = train_data.groupby(train_data.index.month)['seasonal'].mean()
print(f"季节性模式计算完成")

def predict_seasonal(dates, pattern):
    months = [date.month for date in dates]
    return np.array([pattern[month] for month in months])

seasonal_train_pred = predict_seasonal(trend_train_data.index, seasonal_pattern)
seasonal_test_pred = predict_seasonal(trend_test_data.index, seasonal_pattern)

seasonal_train_actual = trend_train_data['seasonal'].values
seasonal_test_actual = trend_test_data['seasonal'].values

print(f"  测试集RMSE: {np.sqrt(mean_squared_error(seasonal_test_actual, seasonal_test_pred)):.4f}")
print(f"  测试集R²: {r2_score(seasonal_test_actual, seasonal_test_pred):.4f}")

# 6. Residual预测 - 添加productivity_lag1（增强版特色）
print("\n6. Residual预测 - 梯度提升14特征模型（添加productivity_lag1）")
print("-" * 50)

def create_residual_features(data):
    """创建Residual预测的14个特征（添加productivity_lag1）"""
    features_data = data.copy()

    features_data['month'] = features_data.index.month
    
    for lag in [1, 3, 6]:
        features_data[f'residual_lag_{lag}'] = features_data['residual'].shift(lag)

    features_data['residual_volatility'] = features_data['residual'].rolling(window=6).std()
    features_data['trend_change'] = features_data['trend'].pct_change()
    features_data['seasonal_abs'] = np.abs(features_data['seasonal'])
    
    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)
    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)
    features_data['sales_price_change'] = features_data['sales_price'].pct_change()
    features_data['exchange_rate_change'] = features_data['exchange_rate'].pct_change()
    features_data['exchange_sales_interaction'] = features_data['exchange_rate'] * features_data['sales_price']
    
    features_data['exchange_rate'] = features_data['exchange_rate']
    features_data['sales_price'] = features_data['sales_price']
    
    # 增强版特色：添加产能变量到Residual模型
    features_data['productivity_lag1'] = features_data['productivity_lag1']

    return features_data.dropna()

# Residual特征列表（14个特征，包含productivity_lag1）
residual_features_list = [
    'residual_lag_1', 'residual_lag_6', 'residual_volatility',
    'exchange_rate', 'sales_price',
    'exchange_rate_lag_1', 'sales_price_lag_1',
    'exchange_rate_change', 'sales_price_change',
    'exchange_sales_interaction',
    'seasonal_abs', 'trend_change',
    'month',
    'productivity_lag1'  # 新增：产能变量
]

residual_train_data = create_residual_features(train_data)
residual_test_data = create_residual_features(test_data)

if len(residual_test_data) == 0:
    full_residual_data = create_residual_features(data)
    residual_split_idx = int(len(full_residual_data) * 0.8)
    residual_train_data = full_residual_data.iloc[:residual_split_idx]
    residual_test_data = full_residual_data.iloc[residual_split_idx:]

available_features = [f for f in residual_features_list if f in residual_train_data.columns]
print(f"可用Residual特征: {len(available_features)}/{len(residual_features_list)}")
print(f"版本B增强版：Residual模型也包含productivity_lag1")

X_residual_train = residual_train_data[available_features]
y_residual_train = residual_train_data['residual']
X_residual_test = residual_test_data[available_features]
y_residual_test = residual_test_data['residual']

residual_model = GradientBoostingRegressor(
    n_estimators=50, max_depth=3, learning_rate=0.05, subsample=0.8,
    min_samples_split=10, min_samples_leaf=5, max_features='sqrt', random_state=42
)

residual_model.fit(X_residual_train, y_residual_train)

residual_train_pred = residual_model.predict(X_residual_train)
residual_test_pred = residual_model.predict(X_residual_test)

print(f"Residual模型训练完成（版本B增强版：含productivity_lag1）")
print(f"  训练集RMSE: {np.sqrt(mean_squared_error(y_residual_train, residual_train_pred)):.2f}")
print(f"  测试集RMSE: {np.sqrt(mean_squared_error(y_residual_test, residual_test_pred)):.2f}")
print(f"  测试集R²: {r2_score(y_residual_test, residual_test_pred):.4f}")

# 分析productivity_lag1在Residual模型中的重要性
residual_feature_importance = pd.DataFrame({
    'feature': available_features,
    'importance': residual_model.feature_importances_
}).sort_values('importance', ascending=False)

if 'productivity_lag1' in residual_feature_importance['feature'].values:
    productivity_res_importance_rank = residual_feature_importance[residual_feature_importance['feature'] == 'productivity_lag1'].index[0] + 1
    productivity_res_importance = residual_feature_importance[residual_feature_importance['feature'] == 'productivity_lag1']['importance'].iloc[0]
    print(f"  productivity_lag1在Residual模型中的重要性排名: {productivity_res_importance_rank}/{len(available_features)}")
    print(f"  productivity_lag1在Residual模型中的重要性: {productivity_res_importance:.6f}")

# 7. 最终融合预测
print("\n7. 最终融合预测")
print("-" * 50)

split_date = pd.to_datetime('2022-12-01')
original_train_data = data[data.index < split_date]
original_test_data = data[data.index >= split_date]

def predict_step_by_step(test_data, train_data, models, scalers, seasonal_pattern):
    trend_model, residual_model = models
    trend_scaler = scalers

    trend_predictions = []
    seasonal_predictions = []
    residual_predictions = []
    final_predictions = []

    full_data = pd.concat([train_data, test_data])

    for i, test_date in enumerate(test_data.index):
        print(f"  预测 {test_date.strftime('%Y-%m')}...")

        current_data = full_data[full_data.index <= test_date]

        # Trend预测
        trend_features_data = create_trend_features(current_data)
        if len(trend_features_data) > 0 and test_date in trend_features_data.index:
            trend_X = trend_features_data.loc[[test_date], trend_features_list]
            trend_X_scaled = trend_scaler.transform(trend_X)
            trend_pred = trend_model.predict(trend_X_scaled)[0]
        else:
            trend_pred = current_data['trend'].iloc[-1]

        # Seasonal预测
        seasonal_pred = seasonal_pattern[test_date.month]

        # Residual预测
        residual_features_data = create_residual_features(current_data)
        if len(residual_features_data) > 0 and test_date in residual_features_data.index:
            residual_X = residual_features_data.loc[[test_date], available_features]
            residual_pred = residual_model.predict(residual_X)[0]
        else:
            residual_pred = 0.0

        final_pred = trend_pred + seasonal_pred + residual_pred

        trend_predictions.append(trend_pred)
        seasonal_predictions.append(seasonal_pred)
        residual_predictions.append(residual_pred)
        final_predictions.append(final_pred)

    return (np.array(trend_predictions), np.array(seasonal_predictions),
            np.array(residual_predictions), np.array(final_predictions))

print("开始逐步预测测试集...")

models = (trend_model, residual_model)
scalers = trend_scaler

(trend_test_pred_full, seasonal_test_pred_full,
 residual_test_pred_full, final_test_pred_full) = predict_step_by_step(
    original_test_data, original_train_data, models, scalers, seasonal_pattern)

# 训练集预测对齐
train_available_mask = original_train_data.index.isin(trend_train_data.index)
final_train_dates = original_train_data.index[train_available_mask]
final_train_actual = original_train_data['original'][train_available_mask].values

train_trend_aligned = []
train_seasonal_aligned = []
train_residual_aligned = []

for date in final_train_dates:
    if date in trend_train_data.index:
        idx = trend_train_data.index.get_loc(date)
        train_trend_aligned.append(trend_train_pred[idx])
        train_seasonal_aligned.append(predict_seasonal([date], seasonal_pattern)[0])

        if date in residual_train_data.index:
            residual_idx = residual_train_data.index.get_loc(date)
            train_residual_aligned.append(residual_train_pred[residual_idx])
        else:
            train_residual_aligned.append(0.0)

final_train_pred = np.array(train_trend_aligned) + np.array(train_seasonal_aligned) + np.array(train_residual_aligned)

final_test_dates = original_test_data.index
final_test_actual = original_test_data['original'].values
final_test_pred = final_test_pred_full

print(f"版本B增强版预测完成!")

# 8. 性能评估
def calculate_metrics(y_true, y_pred):
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100

    if len(y_true) > 1:
        direction_accuracy = np.mean(np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred))) * 100
    else:
        direction_accuracy = 0

    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2, 'MAPE': mape, 'Direction_Accuracy': direction_accuracy}

final_train_metrics = calculate_metrics(final_train_actual, final_train_pred)
final_test_metrics = calculate_metrics(final_test_actual, final_test_pred)
final_overfitting_ratio = final_test_metrics['RMSE'] / final_train_metrics['RMSE']

print(f"\n🎉 版本B增强版：Trend和Residual都使用productivity_lag1的融合预测完成!")
print(f"\n训练集性能:")
print(f"  RMSE: {final_train_metrics['RMSE']:.2f} 百万美元")
print(f"  R²: {final_train_metrics['R2']:.4f}")
print(f"  MAPE: {final_train_metrics['MAPE']:.2f}%")

print(f"\n测试集性能:")
print(f"  RMSE: {final_test_metrics['RMSE']:.2f} 百万美元")
print(f"  R²: {final_test_metrics['R2']:.4f}")
print(f"  MAPE: {final_test_metrics['MAPE']:.2f}%")
print(f"  方向准确性: {final_test_metrics['Direction_Accuracy']:.1f}%")
print(f"  过拟合比率: {final_overfitting_ratio:.3f}")

# 9. productivity_lag1全面影响分析
print("\n9. productivity_lag1全面影响分析")
print("-" * 50)

print(f"📊 productivity_lag1在两个模型中的贡献:")

print(f"\n1. 在Trend预测模型中:")
print(f"   重要性排名: {productivity_importance_rank}/8")
print(f"   回归系数: {productivity_coefficient:.6f}")
if abs(productivity_coefficient) > 1:
    print(f"   影响程度: 显著")
elif abs(productivity_coefficient) > 0.1:
    print(f"   影响程度: 中等")
else:
    print(f"   影响程度: 较小")

if 'productivity_lag1' in residual_feature_importance['feature'].values:
    print(f"\n2. 在Residual预测模型中:")
    print(f"   重要性排名: {productivity_res_importance_rank}/{len(available_features)}")
    print(f"   特征重要性: {productivity_res_importance:.6f}")
    if productivity_res_importance > 0.05:
        print(f"   影响程度: 显著")
    elif productivity_res_importance > 0.02:
        print(f"   影响程度: 中等")
    else:
        print(f"   影响程度: 较小")

print(f"\n3. 总体评估:")
total_contribution = abs(productivity_coefficient) + (productivity_res_importance if 'productivity_lag1' in residual_feature_importance['feature'].values else 0)
print(f"   productivity_lag1总贡献度: {total_contribution:.6f}")
print(f"   主要贡献来源: {'Trend模型' if abs(productivity_coefficient) > productivity_res_importance else 'Residual模型'}")

# 10. 结果保存
print("\n10. 结果保存")
print("-" * 50)

# 保存详细结果
results_df = pd.DataFrame({
    'date': np.concatenate([final_train_dates, final_test_dates]),
    'actual': np.concatenate([final_train_actual, final_test_actual]),
    'predicted': np.concatenate([final_train_pred, final_test_pred]),
    'trend_pred': np.concatenate([train_trend_aligned, trend_test_pred_full]),
    'seasonal_pred': np.concatenate([train_seasonal_aligned, seasonal_test_pred_full]),
    'residual_pred': np.concatenate([train_residual_aligned, residual_test_pred_full]),
    'dataset': ['train'] * len(final_train_dates) + ['test'] * len(final_test_dates)
})

results_path = r"D:\埃塞俄比亚咖啡\模型\version_B_enhanced_results.csv"
results_df.to_csv(results_path, index=False)
print(f"版本B增强版结果已保存: {results_path}")

# 保存性能指标
performance_df = pd.DataFrame({
    '版本': ['B_enhanced'],
    '产量变量': ['productivity_lag1_full'],
    'Trend特征数': [8],
    'Residual特征数': [len(available_features)],
    '测试集RMSE': [final_test_metrics['RMSE']],
    '测试集R²': [final_test_metrics['R2']],
    '测试集MAPE': [final_test_metrics['MAPE']],
    '方向准确性': [final_test_metrics['Direction_Accuracy']],
    '过拟合比率': [final_overfitting_ratio],
    'productivity_trend_rank': [productivity_importance_rank],
    'productivity_trend_coef': [productivity_coefficient],
    'productivity_residual_rank': [productivity_res_importance_rank if 'productivity_lag1' in residual_feature_importance['feature'].values else None],
    'productivity_residual_importance': [productivity_res_importance if 'productivity_lag1' in residual_feature_importance['feature'].values else None]
})

performance_path = r"D:\埃塞俄比亚咖啡\模型\version_B_enhanced_performance.csv"
performance_df.to_csv(performance_path, index=False)
print(f"版本B增强版性能指标已保存: {performance_path}")

# 11. 可视化对比分析
print("\n11. 可视化对比分析")
print("-" * 50)

fig, axes = plt.subplots(3, 2, figsize=(16, 18))
fig.suptitle('版本B增强版：Trend和Residual都使用productivity_lag1的融合预测分析', fontsize=16, fontweight='bold')

# 1. 最终预测效果对比
ax1 = axes[0, 0]
ax1.plot(final_train_dates, final_train_actual, 'black', label='训练集真实值', linewidth=1, alpha=0.8)
ax1.plot(final_test_dates, final_test_actual, 'black', label='测试集真实值', linewidth=2)
ax1.plot(final_train_dates, final_train_pred, 'blue', label='训练集预测', linewidth=1, alpha=0.7)
ax1.plot(final_test_dates, final_test_pred, 'red', label='测试集预测', linewidth=2)

ax1.axvline(x=final_train_dates[-1], color='gray', linestyle=':', alpha=0.7, label='训练/测试分割')
ax1.set_title('最终预测效果 (含productivity_lag1全面应用)')
ax1.set_ylabel('出口额 (百万美元)')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 添加性能指标
textstr = f'测试集RMSE: {final_test_metrics["RMSE"]:.1f}\n测试集R²: {final_test_metrics["R2"]:.3f}\n测试集MAPE: {final_test_metrics["MAPE"]:.1f}%'
props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
ax1.text(0.02, 0.98, textstr, transform=ax1.transAxes, fontsize=10,
         verticalalignment='top', bbox=props)

# 2. 预测准确性散点图
ax2 = axes[0, 1]
ax2.scatter(final_test_actual, final_test_pred, alpha=0.7, s=60, color='red')
ax2.plot([final_test_actual.min(), final_test_actual.max()],
         [final_test_actual.min(), final_test_actual.max()], 'k--', lw=2)

ax2.set_xlabel('真实出口额 (百万美元)')
ax2.set_ylabel('预测出口额 (百万美元)')
ax2.set_title(f'预测准确性 (全面使用productivity_lag1)\nR²={final_test_metrics["R2"]:.3f}')
ax2.grid(True, alpha=0.3)

# 添加拟合线
z = np.polyfit(final_test_actual, final_test_pred, 1)
p = np.poly1d(z)
ax2.plot(final_test_actual, p(final_test_actual), "b--", alpha=0.8, linewidth=1)

# 3. 各组件预测对比
ax3 = axes[1, 0]
ax3.plot(final_test_dates, trend_test_pred_full, 'blue', label='Trend预测(含productivity_lag1)', linewidth=2)
ax3.plot(final_test_dates, seasonal_test_pred_full, 'green', label='Seasonal预测', linewidth=2)
ax3.plot(final_test_dates, residual_test_pred_full, 'orange', label='Residual预测(含productivity_lag1)', linewidth=2)
ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)

ax3.set_title('各组件预测对比 (Trend+Residual都含productivity_lag1)')
ax3.set_ylabel('组件值')
ax3.legend()
ax3.grid(True, alpha=0.3)

# 4. 预测误差分析
ax4 = axes[1, 1]
test_errors = final_test_actual - final_test_pred
ax4.plot(final_test_dates, test_errors, 'ro-', markersize=4, linewidth=1)
ax4.axhline(y=0, color='black', linestyle='--', alpha=0.7)
ax4.axhline(y=test_errors.std(), color='red', linestyle=':', alpha=0.7, label='+1σ')
ax4.axhline(y=-test_errors.std(), color='red', linestyle=':', alpha=0.7, label='-1σ')

ax4.set_ylabel('预测误差 (百万美元)')
ax4.set_title('测试集预测误差 (全面使用productivity_lag1)')
ax4.legend()
ax4.grid(True, alpha=0.3)

# 5. Trend特征重要性（高亮productivity_lag1）
ax5 = axes[2, 0]
trend_importance_plot = trend_feature_importance.head(8)
bars = ax5.barh(range(len(trend_importance_plot)), trend_importance_plot['abs_coefficient'], alpha=0.7)
ax5.set_yticks(range(len(trend_importance_plot)))
ax5.set_yticklabels(trend_importance_plot['feature'])
ax5.set_xlabel('回归系数绝对值')
ax5.set_title('Trend模型特征重要性 (含productivity_lag1)')
ax5.grid(True, alpha=0.3)

# 高亮productivity_lag1
for i, (_, row) in enumerate(trend_importance_plot.iterrows()):
    if row['feature'] == 'productivity_lag1':
        bars[i].set_color('orange')
        bars[i].set_alpha(0.9)

# 6. Residual特征重要性（高亮productivity_lag1）
ax6 = axes[2, 1]
residual_importance_plot = residual_feature_importance.head(10)
bars = ax6.barh(range(len(residual_importance_plot)), residual_importance_plot['importance'], alpha=0.7)
ax6.set_yticks(range(len(residual_importance_plot)))
ax6.set_yticklabels(residual_importance_plot['feature'])
ax6.set_xlabel('特征重要性')
ax6.set_title('Residual模型特征重要性 (含productivity_lag1)')
ax6.grid(True, alpha=0.3)

# 高亮productivity_lag1
for i, (_, row) in enumerate(residual_importance_plot.iterrows()):
    if row['feature'] == 'productivity_lag1':
        bars[i].set_color('orange')
        bars[i].set_alpha(0.9)

plt.tight_layout()
plt.show()

# 12. 与标准版本B的对比分析
print("\n12. 与标准版本B的对比分析")
print("-" * 50)

print("📈 版本B增强版 vs 标准版本B对比:")
print("=" * 50)

print("标准版本B:")
print("  Trend模型: 8特征 (含productivity_lag1)")
print("  Residual模型: 13特征 (不含生产量变量)")
print("  总特征数: 21")

print(f"\n版本B增强版:")
print(f"  Trend模型: 8特征 (含productivity_lag1)")
print(f"  Residual模型: {len(available_features)}特征 (含productivity_lag1)")
print(f"  总特征数: {8 + len(available_features)}")
print(f"  测试集R²: {final_test_metrics['R2']:.4f}")
print(f"  测试集RMSE: {final_test_metrics['RMSE']:.2f}")
print(f"  测试集MAPE: {final_test_metrics['MAPE']:.2f}%")

print(f"\n💡 增强版特色:")
print(f"  • 在Trend和Residual模型中全面应用productivity_lag1")
print(f"  • 能够捕获产能对长期趋势和短期异常的双重影响")
print(f"  • 提供更全面的生产能力信息利用")

print(f"\n🎯 建议:")
print(f"  将此版本与标准版本B进行对比，评估:")
print(f"  1. Residual中加入productivity_lag1是否提升预测精度")
print(f"  2. 是否存在过拟合风险")
print(f"  3. 模型复杂度的增加是否值得")

print(f"\n版本B增强版完成！全面使用productivity_lag1的融合预测模型已准备就绪!")