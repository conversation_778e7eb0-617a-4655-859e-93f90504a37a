{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4392981c-8088-40b4-8fda-50ca5f4d1503", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MSE: 7554.1959, RMSE: 86.9149, MAE: 56.5710, R2: 0.0964\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import xgboost as xgb\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import matplotlib.pyplot as plt\n", "\n", "# 1. 读取数据\n", "file_path = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "df = pd.read_csv(file_path, parse_dates=['datetime'], index_col='datetime', encoding='gb18030')\n", "df.sort_index(inplace=True)\n", "\n", "# 2. 选择变量并删除末尾含空值行\n", "cols = [\n", "    'sales price（usd/lb）',\n", "    'Exchange(Domestic currency per US Dollar)',\n", "    'IN MILLION USD'\n", "]\n", "df = df[cols].copy()\n", "df.dropna(subset=cols, how='any', inplace=True)\n", "\n", "# 3. 特征工程：添加滞后特征和周期哑变量\n", "# 3A. 目标变量滞后\n", "for lag in [1, 2, 3, 6, 12]:\n", "    df[f'y_lag{lag}'] = df['IN MILLION USD'].shift(lag)\n", "\n", "# 3B. 外生变量滞后\n", "exogs = ['sales price（usd/lb）', 'Exchange(Domestic currency per US Dollar)']\n", "for var in exogs:\n", "    for lag in [1, 2, 3]:\n", "        df[f'{var}_lag{lag}'] = df[var].shift(lag)\n", "\n", "# 3C. 月份哑变量\n", "df['month'] = df.index.month\n", "df = pd.get_dummies(df, columns=['month'], prefix='m', drop_first=True)\n", "\n", "# 4. 删除滞后产生的空值行\n", "df.dropna(inplace=True)\n", "\n", "# 5. 构建特征和标签\n", "feature_cols = [c for c in df.columns if c != 'IN MILLION USD']\n", "X = df[feature_cols].values\n", "y = df['IN MILLION USD'].values\n", "\n", "# 6. 划分训练集/测试集 (8:2)\n", "split = int(len(df) * 0.8)\n", "X_train, X_test = X[:split], X[split:]\n", "y_train, y_test = y[:split], y[split:]\n", "\n", "# 7. 归一化 (仅用训练集拟合)\n", "scaler_x = MinMaxScaler().fit(X_train)\n", "scaler_y = MinMaxScaler().fit(y_train.reshape(-1, 1))\n", "X_train_scaled = scaler_x.transform(X_train)\n", "X_test_scaled = scaler_x.transform(X_test)\n", "\n", "# 8. XGBoost训练 (无验证集)\n", "dtrain = xgb.DMatrix(X_train_scaled, label=y_train)\n", "dtest  = xgb.DMatrix(X_test_scaled)\n", "params = {\n", "    'objective': 'reg:squarederror',\n", "    'learning_rate': 0.01,\n", "    'max_depth': 6,\n", "    'subsample': 1,\n", "    'colsample_bytree': 1,\n", "    'seed': 42\n", "}\n", "model = xgb.train(params, dtrain, num_boost_round=500)\n", "\n", "# 9. 预测与评估\n", "y_pred = model.predict(dtest)\n", "mse  = mean_squared_error(y_test, y_pred)\n", "rmse = np.sqrt(mse)\n", "mae  = mean_absolute_error(y_test, y_pred)\n", "r2   = r2_score(y_test, y_pred)\n", "print(f\"MSE: {mse:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}, R2: {r2:.4f}\")\n", "\n", "# 10. 可视化预测 vs 实际\n", "plt.figure(figsize=(10, 4))\n", "plt.plot(df.index[split:], y_test, label='True')\n", "plt.plot(df.index[split:], y_pred, label='Pred')\n", "plt.title('IN MILLION USD: True vs Predicted')\n", "plt.xlabel('Date')\n", "plt.ylabel('IN MILLION USD')\n", "plt.xticks(rotation=45)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "a836430a-624d-4959-a5d4-d3b431af77e2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}