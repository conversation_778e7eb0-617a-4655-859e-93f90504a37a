{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2dc994a4-93d6-489d-aa04-134fd58a0156", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 001 | Train Loss: 0.0764\n", "Epoch 002 | Train Loss: 0.0638\n", "Epoch 003 | Train Loss: 0.0528\n", "Epoch 004 | Train Loss: 0.0434\n", "Epoch 005 | Train Loss: 0.0358\n", "Epoch 006 | Train Loss: 0.0305\n", "Epoch 007 | Train Loss: 0.0279\n", "Epoch 008 | Train Loss: 0.0279\n", "Epoch 009 | Train Loss: 0.0296\n", "Epoch 010 | Train Loss: 0.0315\n", "Epoch 011 | Train Loss: 0.0322\n", "Epoch 012 | Train Loss: 0.0317\n", "Epoch 013 | Train Loss: 0.0306\n", "Epoch 014 | Train Loss: 0.0296\n", "Epoch 015 | Train Loss: 0.0289\n", "Epoch 016 | Train Loss: 0.0286\n", "Epoch 017 | Train Loss: 0.0285\n", "Epoch 018 | Train Loss: 0.0285\n", "Epoch 019 | Train Loss: 0.0285\n", "Epoch 020 | Train Loss: 0.0285\n", "Epoch 021 | Train Loss: 0.0284\n", "Epoch 022 | Train Loss: 0.0283\n", "Epoch 023 | Train Loss: 0.0281\n", "Epoch 024 | Train Loss: 0.0281\n", "Epoch 025 | Train Loss: 0.0280\n", "Epoch 026 | Train Loss: 0.0280\n", "Epoch 027 | Train Loss: 0.0281\n", "Epoch 028 | Train Loss: 0.0282\n", "Epoch 029 | Train Loss: 0.0282\n", "Epoch 030 | Train Loss: 0.0282\n", "Epoch 031 | Train Loss: 0.0282\n", "Epoch 032 | Train Loss: 0.0282\n", "Epoch 033 | Train Loss: 0.0281\n", "Epoch 034 | Train Loss: 0.0281\n", "Epoch 035 | Train Loss: 0.0280\n", "Epoch 036 | Train Loss: 0.0280\n", "Epoch 037 | Train Loss: 0.0279\n", "Epoch 038 | Train Loss: 0.0279\n", "Epoch 039 | Train Loss: 0.0278\n", "Epoch 040 | Train Loss: 0.0278\n", "Epoch 041 | Train Loss: 0.0278\n", "Epoch 042 | Train Loss: 0.0278\n", "Epoch 043 | Train Loss: 0.0277\n", "Epoch 044 | Train Loss: 0.0277\n", "Epoch 045 | Train Loss: 0.0277\n", "Epoch 046 | Train Loss: 0.0277\n", "Epoch 047 | Train Loss: 0.0276\n", "Epoch 048 | Train Loss: 0.0276\n", "Epoch 049 | Train Loss: 0.0276\n", "Epoch 050 | Train Loss: 0.0275\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["MSE: 16606.3223, RMSE: 128.8655, MAE: 94.2195, R2: -0.8952\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKUAAAGFCAYAAAAsKUDaAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuNSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/xnp5ZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACKrUlEQVR4nOzdd3hUZfrG8e9Meg9JSEIgQKgh9E4QVIoUWVcEOwqIZUWsWBDXii4ormv57QpWsCE2UEEFESkCoUiH0AmEkgIJKaTPzPn9EYgEAiSQ5KTcn+uaKzPnvDPzDJrJzH3e9zkWwzAMREREREREREREKpHV7AJERERERERERKT2USglIiIiIiIiIiKVTqGUiIiIiIiIiIhUOoVSIiIiIiIiIiJS6RRKiYiIiIiIiIhIpVMoJSIiIiIiIiIilU6hlIiIiIiIiIiIVDpnswuoChwOB0ePHsXHxweLxWJ2OSIiIiIiIiIi1ZZhGGRmZhIWFobVev75UAqlgKNHjxIeHm52GSIiIiIiIiIiNcahQ4do0KDBefcrlAJ8fHyAwn8sX19fk6sREREREREREam+MjIyCA8PL8pbzkehFBQt2fP19VUoJSIiIiIiIiJSDi7WIkmNzkVEREREREREpNIplBIRERERERERkUqnUEpERERERERERCqdekqVksPhID8/3+wyqh0XFxecnJzMLkNEREREREREqhiFUqWQn59PXFwcDofD7FKqJX9/f0JDQy/a4ExEREREREREag+FUhdhGAYJCQk4OTkRHh6O1aoVj6VlGAbZ2dkkJycDUK9ePZMrEhEREREREZGqQqHURdhsNrKzswkLC8PT09PscqodDw8PAJKTkwkODtZSPhEREREREREB1Oj8oux2OwCurq4mV1J9nQ7zCgoKTK5ERERERERERKoKhVKlpH5Il07/diIiIiIiIiJyNoVSIiIiIiIiIiJS6RRKiYiIiIiIiIiYLN/mYM6GwxiGYXYplUaNzkVERERERERETLQ7KZNHZ28iNiEDhwE3dm5gdkmVQjOlaiCLxXLBy4svvmh2iSIiIiIiIiK1nsNh8NGKOP72fyuITcggwMsVPw8Xs8uqNJopVQMlJCQUXf/qq694/vnn2bVrV9E2b2/vouuGYWC323F21v8KIiIiIiIiIpXlaFoOT3yzmVX7UgDo07Iur93YjmAfd5MrqzyaKVVGhmGQnW8z5VLadaWhoaFFFz8/PywWS9HtnTt34uPjwy+//ELnzp1xc3NjxYoVjB49mqFDhxZ7nEcffZSrr7666LbD4WDKlClERETg4eFB+/bt+fbbb8vxX1dERERERESk5vth0xEGvbWcVftS8HBx4l83tOHj0V1rVSAFmilVZjkFdqKeX2jKc8dOGoina/n8J3v66af597//TZMmTahTp06p7jNlyhQ+//xzpk+fTvPmzVm+fDl33HEHdevW5aqrriqXukRERERERERqqvTsAp79YRvzNh8FoH24P2/e3J4mdb0vcs+aqcrMlHr11VexWCw8+uijRdtyc3MZN24cgYGBeHt7M3z4cJKSkordLz4+niFDhuDp6UlwcDBPPvkkNputkquvfiZNmsQ111xD06ZNCQgIuOj4vLw8Jk+ezMcff8zAgQNp0qQJo0eP5o477uC9996rhIpFREREREREqq8Ve44z8K3lzNt8FCerhcf6t+C7+6NrbSAFVWSm1Lp163jvvfdo165dse2PPfYYP/30E9988w1+fn48+OCDDBs2jJUrVwJgt9sZMmQIoaGhrFq1ioSEBEaOHImLiwuTJ0+ukFo9XJyInTSwQh67NM9dXrp06VKm8Xv37iU7O5trrrmm2Pb8/Hw6duxYbnWJiIiIiIiI1CS5BXZeW7CTGSsPABAR5MWbt3SgQ7i/qXVVBaaHUidPnmTEiBF88MEHvPLKK0Xb09PT+eijj5g1axZ9+/YFYMaMGbRq1YrVq1fTo0cPfv31V2JjY/ntt98ICQmhQ4cOvPzyy0yYMIEXX3wRV1fXcq/XYrGU2xI6M3l5eRW7bbVaz+lZVVBQUHT95MmTAPz000/Ur1+/2Dg3N7cKqlJERERERESk+tp2JJ3HvtrEnuTC79R39mjExGsja0SuUB5MX743btw4hgwZQv/+/YttX79+PQUFBcW2R0ZG0rBhQ2JiYgCIiYmhbdu2hISEFI0ZOHAgGRkZbN++/bzPmZeXR0ZGRrFLbVe3bt1iZ+0D2LRpU9H1qKgo3NzciI+Pp1mzZsUu4eHhlVytiIiIiIiISNVldxj8b8lebnh3JXuST1LXx40Zd3Xl5aFtFEidwdR/idmzZ7NhwwbWrVt3zr7ExERcXV3x9/cvtj0kJITExMSiMWcGUqf3n953PlOmTOGll166zOprlr59+/L666/z6aefEh0dzeeff862bduKlub5+PjwxBNP8Nhjj+FwOOjVqxfp6emsXLkSX19fRo0aZfIrEBERERERETFffEo247/exJ8HTwAwqHUok4e1JcCr/FdzVXemhVKHDh3ikUceYdGiRbi7V+4pDydOnMj48eOLbmdkZNT62T4DBw7kueee46mnniI3N5cxY8YwcuRItm7dWjTm5Zdfpm7dukyZMoX9+/fj7+9Pp06deOaZZ0ysXERERERERMR8hmHwzZ+HeWnedrLy7Xi7OfPi31szvFN9LBaL2eVVSRbj7EZCleT777/nhhtuwMnpr+bddrsdi8WC1Wpl4cKF9O/fnxMnThSbLdWoUSMeffRRHnvsMZ5//nl+/PHHYsvM4uLiaNKkCRs2bCh1A+6MjAz8/PxIT0/H19e32L7c3Fzi4uKIiIio9PCsptC/oYiIiIiIiNRkx0/mMXHOVhbFJgHQrXEAb9zcnvAAT5MrM8eFcpYzmdZTql+/fmzdupVNmzYVXbp06cKIESOKrru4uLB48eKi++zatYv4+Hiio6MBiI6OZuvWrSQnJxeNWbRoEb6+vkRFRVX6axIRERERERGR2mXxjiQGvbWcRbFJuDhZmDg4ki/v61FrA6myMG35no+PD23atCm2zcvLi8DAwKLtd999N+PHjycgIABfX18eeughoqOj6dGjBwADBgwgKiqKO++8k6lTp5KYmMizzz7LuHHjdEY4EREREREREakwWXk2XvlpB1+ujQegZYgPb97Sgaiw888MkuKqdMv3N998E6vVyvDhw8nLy2PgwIG8++67RfudnJyYP38+Y8eOJTo6Gi8vL0aNGsWkSZNMrFpEREREREREarIN8ScY/9UmDqRkY7HAPb0ieHxAS9xdnC5+ZyliWk+pqkQ9pSqW/g1FRERERESkJiiwO/i/xXv475K9OAwI83Pn3ze3p2fTILNLq1JK21OqSs+UEhERERERERGpCvYdO8ljX21iy+F0AG7oWJ8X/94aPw8XkyurvhRKiYiIiIiIiIich2EYfLb6IJN/3kFugQM/Dxf+dUMb/tYuzOzSqj2FUiIiIiIiIiIiJUjKyOXJb7ewfPcxAHo3D+L1G9sT6qfWNOVBoZSIiIiIiIiIyFl+3prAM3O3kpZdgJuzlYmDIxkZ3Rir1WJ2aTWGQim5LKNHjyYtLY3vv//e7FJERERERERELltGbgEv/rCdORuPANCmvi9v3dKBZsE+JldW81jNLkAqxujRo7FYLFgsFlxdXWnWrBmTJk3CZrOZXZqIiIiIiIhIlbR6fwqD3/qDORuPYLXAg32aMWfsFQqkKohmStVggwYNYsaMGeTl5fHzzz8zbtw4XFxcmDhxYrFx+fn5uLq6mlSliIiIiIiIiLnybHbe+HU3H/yxH8OAhgGevHlLezo3CjC7tBpNM6VqMDc3N0JDQ2nUqBFjx46lf//+/Pjjj4wePZqhQ4fyr3/9i7CwMFq2bAnAoUOHuPnmm/H39ycgIIDrr7+eAwcOFD2e3W5n/Pjx+Pv7ExgYyFNPPYVhGCa9OhEREREREZHLtyMhg+v/u5L3lxcGUrd2DeeXR3orkKoEmilVVoYBBdnmPLeLJ1guvaGah4cHKSkpACxevBhfX18WLVoEQEFBAQMHDiQ6Opo//vgDZ2dnXnnlFQYNGsSWLVtwdXXljTfeYObMmXz88ce0atWKN954g7lz59K3b99yeXkiIiIiIiIilcXuMPhoxX7+vXA3+XYHgV6uvDq8HddEhZhdWq2hUKqsCrJhcpg5z/3MUXD1KvPdDMNg8eLFLFy4kIceeohjx47h5eXFhx9+WLRs7/PPP8fhcPDhhx9iORV8zZgxA39/f5YuXcqAAQN46623mDhxIsOGDQNg+vTpLFy4sPxen4iIiIiIiEglOHwim8e/3syauFQA+rcK5tXh7QjydjO5stpFoVQNNn/+fLy9vSkoKMDhcHD77bfz4osvMm7cONq2bVusj9TmzZvZu3cvPj7Fm7fl5uayb98+0tPTSUhIoHv37kX7nJ2d6dKli5bwiYiIiIiISLVgGAZzNx7hhR+2k5lnw9PVief/FsUtXcOLJmhI5VEoVVYunoUzlsx67jLo06cP06ZNw9XVlbCwMJyd//rP7eVVfMbVyZMn6dy5M1988cU5j1O3bt1Lq1dERERERESkijiRlc8/v9/Kz1sTAejU0J83b+lAo8Cyr0iS8qFQqqwslktaQmcGLy8vmjVrVqqxnTp14quvviI4OBhfX98Sx9SrV481a9Zw5ZVXAmCz2Vi/fj2dOnUqt5pFREREREREyktugZ2YfSn8GpvIgm2JnMguwNlq4dH+zbn/qqY4O+n8b2ZSKCUAjBgxgtdff53rr7+eSZMm0aBBAw4ePMicOXN46qmnaNCgAY888givvvoqzZs3JzIykv/85z+kpaWZXbqIiIiIiIhIkfScApbuSubX7Uks3ZVMVr69aF/Tul68dUtH2jbwM7FCOU2hlADg6enJ8uXLmTBhAsOGDSMzM5P69evTr1+/oplTjz/+OAkJCYwaNQqr1cqYMWO44YYbSE9PN7l6ERERERERqc0S03NZtCOJX7cnErMvBZvjr97HIb5uDIgKZUDrEHo0CcRFs6OqDIuhLtVkZGTg5+dHenr6OUvXcnNziYuLIyIiAnd3d5MqrN70bygiIiIiIiLlbW9yJgu3J/FrbBKbD6UV29c82JsBrUMYEBVK2/p+WK1qYl6ZLpSznEkzpURERERERESkynM4DDYdTuPX7Un8GpvI/mNZxfZ3aujPgNahDIgKoUldb5OqlLJQKCUiIiIiIiIiVVK+zUHM/hQWbk9kUWwSxzLziva5OFno2TSIga1D6d8qmGBfrcypbhRKiYiIiIiIiEiVkZlbwNJdx/g1NomlO5PJzLMV7fN2c6ZPZDADokK4umVdfNxdTKxULpdCKRERERERERExVXJmLr/FJrNweyKr9h2nwP5X++u6Pm5cExXCwNah9GgSgJuzk4mVSnlSKFVK6gd/6fRvJyIiIiIiImfbf+wkv8YWnjFv46E0zvzq2CTIq7A/VOsQOjTwV6PyGkqh1EU4ORUmsPn5+Xh4eJhcTfWUnZ0NgIuLplWKiIiIiIjUVg6HwdYj6fwam8iv25PYk3yy2P724f4MiAphYOsQmgX7mFSlVCaFUhfh7OyMp6cnx44dw8XFBavVanZJ1YZhGGRnZ5OcnIy/v39RwCciIiIiIiK1Q4Hdwer9Kfy6PYlFsUkkZuQW7XO2WohuGsiA1qFc0yqEUD81Kq9tFEpdhMVioV69esTFxXHw4EGzy6mW/P39CQ0NNbsMERERERERqQRZeTaW7T7Gr9sTWbwzmczcvxqVe7k6cXXLYAa0DuHqlsH4eWhFTW2mUKoUXF1dad68Ofn5+WaXUu24uLhohpSIiIiIiEgNl1tg54dNR1i4PYkVe4+Tb3MU7QvydqV/q8JG5dFNA3F30XdEKaRQqpSsVivu7ppKKCIiIiIiInImwzC4+5N1rNybUrStUaAnA1uHMiAqhI4N6+CkRuVSAoVSIiIiIiIiInLJvt90hJV7U3B3sTLu6mYMaB1KixBvLBYFUXJhCqVERERERERE5JKk5xTwr592AvBQ3+aM69PM5IqkOtGp5ERERERERETkkry5aDfHT+bRJMiLe3pHmF2OVDMKpURERERERESkzLYfTefTmAMATLq+DW7OamAuZaNQSkRERERERETKxOEweO77bTgMGNKuHr2aB5ldklRDpoZS06ZNo127dvj6+uLr60t0dDS//PJL0f6rr74ai8VS7HL//fcXe4z4+HiGDBmCp6cnwcHBPPnkk9hstsp+KSIiIiIiIiK1xrfrD7MhPg1PVyeeHdLK7HKkmjK10XmDBg149dVXad68OYZh8Mknn3D99dezceNGWrduDcC9997LpEmTiu7j6elZdN1utzNkyBBCQ0NZtWoVCQkJjBw5EhcXFyZPnlzpr0dERERERESkpkvLzufVBYXNzR/t35x6fh4mVyTVlamh1HXXXVfs9r/+9S+mTZvG6tWri0IpT09PQkNDS7z/r7/+SmxsLL/99hshISF06NCBl19+mQkTJvDiiy/i6upa4a9BREREREREpDZ5feEuUrPyaRHizV1XqLm5XLoq01PKbrcze/ZssrKyiI6OLtr+xRdfEBQURJs2bZg4cSLZ2dlF+2JiYmjbti0hISFF2wYOHEhGRgbbt28/73Pl5eWRkZFR7CIiIiIiIiIiF7b5UBqz1sYDhc3NXZyqTKwg1ZCpM6UAtm7dSnR0NLm5uXh7ezN37lyioqIAuP3222nUqBFhYWFs2bKFCRMmsGvXLubMmQNAYmJisUAKKLqdmJh43uecMmUKL730UgW9IhEREREREZGax+4weO6HbRgGDO0QRo8mgWaXJNWc6aFUy5Yt2bRpE+np6Xz77beMGjWKZcuWERUVxX333Vc0rm3bttSrV49+/fqxb98+mjZtesnPOXHiRMaPH190OyMjg/Dw8Mt6HSIiIiIiIiI12ex18Ww5nI6PmzPPqLm5lAPT59m5urrSrFkzOnfuzJQpU2jfvj1vv/12iWO7d+8OwN69ewEIDQ0lKSmp2JjTt8/XhwrAzc2t6Ix/py8iIiIiIiIiUrKUk3lMXbALgPEDWhDs425yRVITmB5Knc3hcJCXl1fivk2bNgFQr149AKKjo9m6dSvJyclFYxYtWoSvr2/REkARERERERERuTyvLdhJek4Brer5cmePRmaXIzWEqcv3Jk6cyODBg2nYsCGZmZnMmjWLpUuXsnDhQvbt28esWbO49tprCQwMZMuWLTz22GNceeWVtGvXDoABAwYQFRXFnXfeydSpU0lMTOTZZ59l3LhxuLm5mfnSRERERERERGqE9QdP8PWfhwF4ZWhrnNXcXMqJqaFUcnIyI0eOJCEhAT8/P9q1a8fChQu55pprOHToEL/99htvvfUWWVlZhIeHM3z4cJ599tmi+zs5OTF//nzGjh1LdHQ0Xl5ejBo1ikmTJpn4qkRERERERERqBpvdwXPfbwPgps4N6NwowOSKpCaxGIZhmF2E2TIyMvDz8yM9PV39pUREREREREROmbkyjhfnxeLr7sySJ64m0FurkuTiSpuzaM6diIiIiIiIiJwjOTOXN37dDcCTgyIVSEm5UyglIiIiIiIiIud49eedZObZaNfAj9u7NTS7HKmBFEqJiIiIiIiISDFr9qcwZ+MRLBZ4+fo2OFktZpckNZBCKREREREREREpUmB38NwPhc3Nb+vWkPbh/uYWJDWWQikRERERERERKTJz5QF2J52kjqcLTw5oaXY5UoMplBIRERERERERABLTc3nrt8Lm5k8PjqSOl6vJFUlNplBKRERERERERAB45adYsvLtdGroz02dw80uR2o4hVIiIiIiIiIiwsq9x5m/JQGrBSZd3warmptLBVMoJSIiIiIiIlLL5dv+am5+Z49GtKnvZ3JFUhsolBIRERERERGp5T5csZ/9x7II8nZjvJqbSyVRKCUiIiIiIiJSix1Jy+H/Fu8F4JlrI/HzcDG5IqktFEqJiIiIiIiI1GKT5m0np8BOt8YB3NCxvtnlSC2iUEpERERERESkllqyK5mF25NwslqYNLQ1Fouam0vlUSglIiIiIiIiUgvlFth58cftANzVszGRob4mVyS1jUIpERERERERkVrovWX7OZiSTYivG49e08LscqQWUiglIiIiIiIiUsvEp2Tz7tLC5ub/HBKFt5uzyRVJbaRQSkRERERERKSWeWnedvJsDno2DeS6dvXMLkdqKYVSIiIiIiIiIrXIotgkFu9MxsXJwqTr26i5uZhGoZSIiIiIiIhILZGT/1dz83t6N6FZsLfJFUltplBKREREREREpJb435K9HEnLIczPnYf6NjO7HKnlFEqJiIiIiIiI1AL7j53k/eX7AXj+uig8XdXcXMylUEpERERERESkhjMMgxd+3E6+3cFVLeoysHWo2SWJKJQSERERERERqel+2ZbIH3uO4+pk5aW/t1Zzc6kSFEqJiIiIiIiI1GBZeTZenh8LwP1XNaFxkJfJFYkUUiglIiIiIiIiUoO98/seEtJzCQ/w4IE+am4uVYdCKREREREREZEaak9SJh/9EQfAi9e1xt3FyeSKRP6iUEpERERERESkBjIMg+d+2IbNYdC/VTD9WoWYXZJIMQqlRERERERERGqgHzcfZfX+VNycrbxwXWuzyxE5h0IpERERERERkRomM7eAf/20A4AH+zQjPMDT5IpEzqVQSkRERERERKSGeXPRHpIz82gc6Ml9VzUxuxyREimUEhEREREREalBdiRk8EnMAQBeur4Nbs5qbi5Vk6mh1LRp02jXrh2+vr74+voSHR3NL7/8UrQ/NzeXcePGERgYiLe3N8OHDycpKanYY8THxzNkyBA8PT0JDg7mySefxGazVfZLERERERERETGdYRg89/027A6DwW1CuapFXbNLEjkvU0OpBg0a8Oqrr7J+/Xr+/PNP+vbty/XXX8/27dsBeOyxx5g3bx7ffPMNy5Yt4+jRowwbNqzo/na7nSFDhpCfn8+qVav45JNPmDlzJs8//7xZL0lERERERETENN9tOMKfB0/g6erEc3+LMrsckQuyGIZhmF3EmQICAnj99de58cYbqVu3LrNmzeLGG28EYOfOnbRq1YqYmBh69OjBL7/8wt/+9jeOHj1KSEjhqS2nT5/OhAkTOHbsGK6urqV6zoyMDPz8/EhPT8fX17fCXpuIiIiIiIhIRUnPLqDvG0tJycrn6cGR3H9VU7NLklqqtDlLlekpZbfbmT17NllZWURHR7N+/XoKCgro379/0ZjIyEgaNmxITEwMADExMbRt27YokAIYOHAgGRkZRbOtSpKXl0dGRkaxi4iIiIiIiEh19saiXaRk5dMs2JsxV0SYXY7IRZkeSm3duhVvb2/c3Ny4//77mTt3LlFRUSQmJuLq6oq/v3+x8SEhISQmJgKQmJhYLJA6vf/0vvOZMmUKfn5+RZfw8PDyfVEiIiIiIiIilWjbkXQ+X30QgEnXt8bV2fSv+yIXZfr/pS1btmTTpk2sWbOGsWPHMmrUKGJjYyv0OSdOnEh6enrR5dChQxX6fCIiIiIiIiIVxeEwePb7bTgM+Hv7MHo2DTK7JJFScTa7AFdXV5o1awZA586dWbduHW+//Ta33HIL+fn5pKWlFZstlZSURGhoKAChoaGsXbu22OOdPjvf6TElcXNzw83NrZxfiYiIiIiIiEjl++rPQ2w6lIa3mzP/HNLK7HJESs30mVJnczgc5OXl0blzZ1xcXFi8eHHRvl27dhEfH090dDQA0dHRbN26leTk5KIxixYtwtfXl6gonWVAREREREREarYTWfm8tmAnAI/2b06Ir7vJFYmUXplnSu3Zs4cffviBAwcOYLFYiIiIYOjQoTRp0qTMTz5x4kQGDx5Mw4YNyczMZNasWSxdupSFCxfi5+fH3Xffzfjx4wkICMDX15eHHnqI6OhoevToAcCAAQOIiorizjvvZOrUqSQmJvLss88ybtw4zYQSERERERGRGm/qwp2kZRcQGerD6J6NzS5HpEzKFEpNmTKF559/HofDQXBwMIZhcOzYMZ5++mkmT57ME088UaYnT05OZuTIkSQkJODn50e7du1YuHAh11xzDQBvvvkmVquV4cOHk5eXx8CBA3n33XeL7u/k5MT8+fMZO3Ys0dHReHl5MWrUKCZNmlSmOkRERERERESqm43xJ5i9rrBH8qTr2+DsVOUWQ4lckMUwDKM0A5csWUL//v157rnneOSRR6hTpw4AqampvPXWW0yePJnff/+dK6+8skILrggZGRn4+fmRnp6Or6+v2eWIiIiIiIiIXJDdYXD9/1aw7UgGwzrV5z83dzC7JJEipc1ZSh1K3XLLLfj7+/Pee++VuP++++4jMzOTL7/88tIqNpFCKREREREREalOPos5wHM/bMfH3ZnfH7+auj5qYSNVR2lzllLP7Vu7di133nnnefffeeedrF69umxVioiIiIiIiEiZHD+Zx+sLdwHw5MCWCqSk2ip1KJWUlETjxo3Puz8iIoLExMTyqElEREREREREzmPKzzvJyLXROsyXEd0bmV2OyCUrdSiVm5uLq6vrefe7uLiQn59fLkWJiIiIiIiIyLnWHUjluw2HAXh5aBucrBaTKxK5dGU6+96HH36It7d3ifsyMzPLpSAREREREREROZfN7uC577cBcGvXcDo1rGNyRSKXp9ShVMOGDfnggw8uOkZEREREREREyt8nMQfZmZiJv6cLTw2KNLsckctW6lDqwIEDFViGiIiIiIiIiJxPUkYuby7aDcCEQZEEeJ2/vY5IdVHqnlIiIiIiIiIiYo7JP+/gZJ6N9uH+3NIl3OxyRMpFqUOpmJgY5s+fX2zbp59+SkREBMHBwdx3333k5eWVe4EiIiIiIiIitdnvO5P4YdNRLBZ45fo2WNXcXGqIUodSkyZNYvv27UW3t27dyt13303//v15+umnmTdvHlOmTKmQIkVERERERERqo4T0HB7/ejMAd/WMoG0DP5MrEik/pQ6lNm3aRL9+/Ypuz549m+7du/PBBx8wfvx43nnnHb7++usKKVJERERERESktrHZHTzy5SZOZBfQpr4vEwa3NLskkXJV6lDqxIkThISEFN1etmwZgwcPLrrdtWtXDh06VL7ViYiIiIiIiNRSby/ew9oDqXi7OfPf2zrh5uxkdkki5arUoVRISAhxcXEA5Ofns2HDBnr06FG0PzMzExcXl/KvUERERERERKSWWbn3OP9dsheAycPa0jjIy+SKRMpfqUOpa6+9lqeffpo//viDiRMn4unpSe/evYv2b9myhaZNm1ZIkSIiIiIiIiK1xbHMPB6ZvQnDgFu7hvP39mFmlyRSIZxLO/Dll19m2LBhXHXVVXh7e/PJJ5/g6upatP/jjz9mwIABFVKkiIiIiIiISG3gcBiM/3oTx0/m0SLEmxeua212SSIVptShVFBQEMuXLyc9PR1vb2+cnIqvZf3mm2/w9vYu9wJFREREREREaotpy/bxx57juLtY+d/tnfBwVR8pqblKHUqd5udX8uknAwICLrsYERERERERkdpq3YFU/rNoNwCTrm9D8xAfkysSqVilDqU6duyIxWI5Z7ufnx8tWrTg0UcfpVWrVuVanIiIiIiIiEhtcCIrn4e/3IjdYTC0Qxg3dW5gdkkiFa7UodTQoUNL3J6WlsaGDRvo0KEDv//+O1dccUV51SYiIiIiIiJS4xmGwZPfbiYhPZeIIC9euaFtiZNCRGoai2EYRnk80D//+U9Wr17N4sWLy+PhKlVGRgZ+fn6kp6fj6+trdjkiIiIiIiJSi3y0Io6X58fi6mxl7gM9aR1WctsckeqitDmLtbye8Pbbb2fr1q3l9XAiIiIiIiIiNd6Ww2m8+ssOAJ4d0kqBlNQq5RZKOTk54XA4yuvhRERERERERGq0jNwCHpy1kQK7waDWodzZo5HZJYlUqnILpebMmUNUVFR5PZyIiIiIiIhIjWUYBhPnbCU+NZsGdTx47cZ26iMltU6pG52/8847JW5PT09n/fr1/PTTT/zyyy/lVpiIiIiIiIhITTVrbTw/bUnA2Wrh/27riJ+Hi9kliVS6UodSb775ZonbfX19admyJcuXLyc6OrrcChMRERERERGpiXYkZDBpXiwATw1qSceGdUyuSMQcpQ6l4uLiKrIOERERERERkRovK8/Gg7M2kGdz0KdlXe7p1cTskkRMU249pURERERERETkwp7/YTv7jmUR4uvGGzd3wGpVHympvRRKiYiIiIiIiFSC79Yf5rsNh7Fa4J1bOxLg5Wp2SSKmUiglIiIiIiIiUsH2Jp/kuR+2AfBo/xZ0bxJockUi5lMoJSIiIiIiIlKBcgvsPDhrA9n5dno2DWRcn2ZmlyRSJSiUEhEREREREalAL8+PZWdiJoFerrx1Swec1EdKBChDKBUfH1+qS1lMmTKFrl274uPjQ3BwMEOHDmXXrl3Fxlx99dVYLJZil/vvv/+c2oYMGYKnpyfBwcE8+eST2Gy2MtUiIiIiIiIiUt5+2pLAF2sKvyu/eUsHgn3dTa5IpOpwLu3Axo0bY7Gcm+YahlG03WKxlCkMWrZsGePGjaNr167YbDaeeeYZBgwYQGxsLF5eXkXj7r33XiZNmlR029PTs+i63W5nyJAhhIaGsmrVKhISEhg5ciQuLi5Mnjy51LWIiIiIiIiIlKf4lGye/m4LAA9c3ZQrW9Q1uSKRqqXUodTGjRtL3G4YBrNnz+add97B29u7TE++YMGCYrdnzpxJcHAw69ev58orryza7unpSWhoaImP8euvvxIbG8tvv/1GSEgIHTp04OWXX2bChAm8+OKLuLrqbAYiIiIiIiJSufJtDh78cgOZeTa6NKrD+GtamF2SSJVT6uV77du3P+dy7Ngx7rnnHt59912eeuop9u3bd1nFpKenAxAQEFBs+xdffEFQUBBt2rRh4sSJZGdnF+2LiYmhbdu2hISEFG0bOHAgGRkZbN++vcTnycvLIyMjo9hFREREREREpLxMXbCTLYfT8fNw4e3bOuLspJbOImcr9UypM23YsIEJEybwxx9/cM899/Dzzz8THBx8WYU4HA4effRRrrjiCtq0aVO0/fbbb6dRo0aEhYWxZcsWJkyYwK5du5gzZw4AiYmJxQIpoOh2YmJiic81ZcoUXnrppcuqV0RERERERKQkv8Um8eGKOAD+fVN76vt7mFyRSNVUplBq3759PPPMM3z33XfcfPPNxMbG0qRJk3IpZNy4cWzbto0VK1YU237fffcVXW/bti316tWjX79+7Nu3j6ZNm17Sc02cOJHx48cX3c7IyCA8PPzSChcRERERERE55WhaDk98uxmAMVdEcE1UyEXuIVJ7lXr+4AMPPEBUVBTp6en8+eefzJo1q9wCqQcffJD58+ezZMkSGjRocMGx3bt3B2Dv3r0AhIaGkpSUVGzM6dvn60Pl5uaGr69vsYuIiIiIiIjI5bDZHTwyeyNp2QW0re/HhMEtzS5JpEor9Uyp6dOn4+7uTnJyMmPGjDnvuA0bNpT6yQ3D4KGHHmLu3LksXbqUiIiIi95n06ZNANSrVw+A6Oho/vWvf5GcnFy0hHDRokX4+voSFRVV6lpERERERERELsdbv+1h3YETeLs589/bO+Lm7GR2SSJVWqlDqRdeeKHcn3zcuHHMmjWLH374AR8fn6IeUH5+fnh4eLBv3z5mzZrFtddeS2BgIFu2bOGxxx7jyiuvpF27dgAMGDCAqKgo7rzzTqZOnUpiYiLPPvss48aNw83NrdxrFhERERERETnbij3H+d/SwhU9rw5vS6NAL5MrEqn6LIZhGKY9ucVS4vYZM2YwevRoDh06xB133MG2bdvIysoiPDycG264gWeffbbYkruDBw8yduxYli5dipeXF6NGjeLVV1/F2bl0mVtGRgZ+fn6kp6drKZ+IiIiIiIiUSXJmLte+vYLjJ/O4rVtDpgxra3ZJIqYqbc5ySaHUli1b2L17NwAtWrQomrVUXSmUEhERERERkUthdxiM/HgNK/em0DLEhx8evAJ3Fy3bk9qttDlLmc6+t3btWu6++25iY2M5nWVZLBZat27NRx99RNeuXS+vahEREREREZFqZNrSvazcm4KHixP/G9FRgZRIGZT67HuxsbH069cPDw8PPv/8czZs2MCGDRv47LPPcHNzo1+/fsTGxlZkrSIiIiIiIiJVxtq4VP6zqHAV0ctD29As2MfkikSql1Iv37v55pux2Wx899135/SCMgyDYcOG4eLiwtdff10hhVYkLd8TERERERGRskjNyufat/8gMSOXYR3r859bOphdkkiVUe7L95YsWcIvv/xSYnNyi8XCM888w7XXXntp1YqIiIiIiIhUE4Zh8MQ3m0nMyKVJkBcvD21jdkki1VKpl+9lZmYSEhJy3v2hoaFkZmaWS1EiIiIiIiIiVdVHK+L4fWcyrs5W/nt7J7zcytSuWUROKXUo1ahRI9auXXve/WvWrKFRo0blUpSIiIiIiIhIVbTpUBqvLdgJwHN/iyIqTC1gRC5VqUOpW2+9lfHjx7Nt27Zz9m3dupUnnniCW265pVyLExEREREREakq0nMKeOjLDRTYDa5tG8od3RuaXZJItVbqRue5ubn069ePNWvWcM0119CqVSsMw2DHjh389ttvdOvWjd9//x13d/eKrrncqdG5iIiIiIiIXIhhGIybtYGftyYSHuDB/Id64+fhYnZZIlVSuTc6d3d3Z8mSJbz55pt8+eWXLFu2DIAWLVrwyiuv8Nhjj+Hm5nb5lYuIiIiIiIhUMV+siefnrYk4Wy38322dFEiJlINSz5SqyTRTSkRERERERM4n9mgGQ99dSb7NwbNDWnFP7yZmlyRSpZU2Zyl1TykRERERERGR2iYrz8aDszaQb3PQLzKYu3tFmF2SSI1R6uV7derUwWKxXHRcamrqZRUkIiIiIiIiUlU898M29h/Pop6fO/++qX2pvheLSOmUOpR66623KrAMERERERERkarl2/WHmbPhCFYLvH1rR+p4uZpdkkiNUupQatSoURfcb7PZSE5OvuyCRERERERERMy2NzmT577fBsD4a1rQLSLA5IpEap5y6ym1fft2wsPDy+vhREREREREREyRW2DnwVkbySmw06tZEGOvbmZ2SSI1khqdi4iIiIiIiJxh0vxYdiZmEuTtyn9uaY+TVX2kRCqCQikRERERERGRU+ZvOcqsNfFYLPDWLR0J9nE3uySRGkuhlIiIiIiIiAhwMCWLid9tBWDc1c3o1TzI5IpEarZSNzrfsmXLBffv2rXrsosRERERkYplGAZH03PZlZjBzsRM6nq7MaxTAy1NEZFaL89W2EcqM89G18Z1eLR/c7NLEqnxSh1KdejQAYvFgmEY5+w7vd1i0YcZERERkaoiM7eAXYmZ7EzMZGdiRtH1zFxbsXE/bj7K27d2JECnOheRWuy1X3ax9Ug6/p4uvH1rR5ydtLBIpKKVOpSKi4uryDpERERE5BLZ7A7ijmexIzGzcAZUQmH4dCQtp8TxzlYLTet60zTYiyU7j/HHnuNc938rmHZHJ9o18K/c4kVEqoDfYpP4eGXhd95/39ieMH8PkysSqR1KHUo1atTogvvT0tL4+eefLzpORERERC6NYRgkZ+axI6Fw1tOuxEx2JGayL/kk+XZHifep5+dOy1AfWob60CrUl5ahPjSt642rc+EMgJ2JGdz/2XoOpGRz47QYJl3fmlu7NazMlyUiYiq7w+Cl+dsBuKdXBP2jQkyuSKT2sBglrce7BJs3b6ZTp07Y7fbyeLhKlZGRgZ+fH+np6fj6+ppdjoiIiAhZeTZ2JxXOeNp1avndzsRM0rILShzv5ep0KnzyJTLUh8hTQZS/58WX5GXkFvD415tZFJsEwM1dGjDp+ja4uziV62sSEamKfotN4p5P/8TPw4XVE/vh4ar3PpHLVdqcpdQzpURERESk/NkdBgdSsgqDp4SMU/2fMolPzS5xvNUCEUFeRNbzJTLk1Ayoer7U9/fAeonNyn3dXXjvjs5MW7aPN37dxdd/HiY2IYNpIzoTHuB5OS9PRKTKm7nqAAC3dg1XICVSyRRKiYiIiFSSY5l5xWY97UrMZHdSJnm2kpfe1fVxO2PWU+EMqGbB3hUyg8lqtTCuTzPaN/Dn4dkb2XYkg+v+u4K3bunA1S2Dy/35RESqgr3JmazYexyrBe7ooVY0IpVNoZSIiIhIBci3Ofh5awJbDqezK6mwB9Txk/kljvVwcaJFiDeRp3o+nV56F+jtVslVQ6/mQcx7qBcPfL6ezYfTuWvmOh7t14KH+ja75JlYIiJV1SerDgLQv1WIZoaKmKDUodQ777xzwf1Hjhy57GJEREREaoLsfBv3fbqeFXuPF9tusUDjQC9ahvgQWe+vGVANAzxxqkKBT31/D76+P5pJ82L5Yk08b/62m02HTvDWLR3x83QxuzwRkXKRkVvAdxsOAzC6Z2NzixGppUodSr355psXHdOwoc7UIiIiIrVbek4BY2auY/3BE3i6OnFzl3Ci6hXOgGoe4o2na/WYqO7m7MS/bmhLh3B/nv1+G0t2HeNv//2DaSM606a+n9nliYhctm/+PEx2vp0WId5ENw00uxyRWqnUn4ri4uIqsg4RERGRau/4yTxGfrSW2IQM/DxcmHlXVzo2rGN2WZflpi7hRIX5cv/n6zmUmsPwaav41w1tubFzA7NLExG5ZA6HwWcxBwAY1bMxFkvVma0qUptYzS5AREREpCZISM/h5vdiiE3IIMjbjdn39aj2gdRprcP8mP9gb/q0rEuezcET32zmn3O3kmezm12aiMglWbb7GAdSsvF1d+aGjvXNLkek1iq3nlKnPfzww5dcjIiIiEh1dDAli9s/WMORtBzC/Nz5/J7uNKnrbXZZ5crP04WPRnXl/37fy1uLd/PFmni2Hc1g2ohOhPl7mF2eiEiZzFh1AICbu4RXm2XVIjWRxTAMozQDIyIiLv5gFgv79+8v9ZNPmTKFOXPmsHPnTjw8POjZsyevvfYaLVu2LBqTm5vL448/zuzZs8nLy2PgwIG8++67hISEFI2Jj49n7NixLFmyBG9vb0aNGsWUKVNwdi7dm0tGRgZ+fn6kp6fj6+tb6vpFREREdiVmcudHa0jOzCMiyIvP7+lO/Roe0izZlcyjszeRnlNAgJcr79zakV7Ng8wuS0SkVPYdO0m/N5ZhscCyJ/rQMFBn3RMpb6XNWUztKbVs2TLGjRtH165dsdlsPPPMMwwYMIDY2Fi8vLwAeOyxx/jpp5/45ptv8PPz48EHH2TYsGGsXLkSALvdzpAhQwgNDWXVqlUkJCQwcuRIXFxcmDx5crnXLCIiInLa5kNpjJqxlrTsAiJDffjs7u7U9XEzu6wK16dlMPMf6sX9n69n+9EMRn68hscHtGTsVU2xVqGzCIqIlOSzmIMA9IsMViAlYrJSz5SqDMeOHSM4OJhly5Zx5ZVXkp6eTt26dZk1axY33ngjADt37qRVq1bExMTQo0cPfvnlF/72t79x9OjRotlT06dPZ8KECRw7dgxXV9eLPq9mSomIiEhZrdmfwt2f/MnJPBsdwv2ZeVdX/D0v/rmjJsktsPP8D9v4+s/CU6pfExXCGze3x9fdxeTKRERKlplbQI/Ji8nKt/PZ3d3o3byu2SWJ1EjlPlPq008/LdW4kSNHlvYhz5Geng5AQEAAAOvXr6egoID+/fsXjYmMjKRhw4ZFoVRMTAxt27Yttpxv4MCBjB07lu3bt9OxY8dznicvL4+8vLyi2xkZGZdcs4iIiNQ+S3Ylc/9n68mzOYhuEsgHo7rg7Vb7epK4uzgx9cb2dGpYh+d/2M6i2CT+/n8rmH5nZyJDdaBPRKqe79YfJivfTtO6XvRqpmXHImYr9aen0aNH4+3tjbOzM+ebXGWxWC45lHI4HDz66KNcccUVtGnTBoDExERcXV3x9/cvNjYkJITExMSiMWcGUqf3n95XkilTpvDSSy9dUp0iIiJSu/20JYFHv9pIgd2gX2Qw/xvRCXcXJ7PLMtWt3RrSqp4vD3yxgQMp2dzwv1W8Orwt13fQGa1EpOpwOAw+PbV0b3TPxlgsWm4sYjZraQe2atUKV1dXRo4cybJlyzhx4sQ5l9TU1EsuZNy4cWzbto3Zs2df8mOU1sSJE0lPTy+6HDp0qMKfU0RERKq/r/88xENfbqDAbnBd+zCm39m51gdSp7UP92feQ73o3TyInAI7j8zexIs/biff5jC7tAplGAYOR5XphiEiF/DH3uPsP56Fj5szwzo1MLscEaEModT27dv56aefyMnJ4corr6RLly5MmzatXJa+Pfjgg8yfP58lS5bQoMFfbw6hoaHk5+eTlpZWbHxSUhKhoaFFY5KSks7Zf3pfSdzc3PD19S12EREREbmQGSvjeOrbLTgMuLVrOG/d0gEXp1J/lKoVArxcmXlXNx7s0wyAmasOcNsHq0nKyDW5svJlsztYufc4z36/lW6TF9N9ymJ2J2WaXZaIXMTMlYUn77qxSwO8auGSa5GqqEyfpLp37857771HQkICDz/8MF9//TX16tVjxIgRxXo0lZZhGDz44IPMnTuX33//nYiIiGL7O3fujIuLC4sXLy7atmvXLuLj44mOjgYgOjqarVu3kpycXDRm0aJF+Pr6EhUVVeaaRERERM5kGAb/t3gPL82LBeCeXhFMGdYWJ51lrkROVgtPDGzJhyO74OPuzPqDJxjyzgpW708xu7TLkmezs2RnMk99u5mu//qNER+u4fPV8RzLzONYZh6jPl7L0bQcs8sUkfM4cDyLpbuPYbHAqOjGZpcjIqdc1tn3li9fzgsvvMDy5cs5fvw4derUKdP9H3jgAWbNmsUPP/xAy5Yti7b7+fnh4eEBwNixY/n555+ZOXMmvr6+PPTQQwCsWrUKALvdTocOHQgLC2Pq1KkkJiZy5513cs899zB58uRS1aGz74mIiEhJDMNgyi87eX/5fgAe69+Ch/s1Ux+SUjpwPIv7P1/PzsRMnKwWnh4UyT29I6rNv19Ovp1lu5P5ZVsiv+9IJjPPVrQvwMuVAVEh9I0MZurCXexNPknzYG++uT+61p2FUaQ6mDQvlo9XxtGnZV1m3NXN7HJEarzS5ixlDqWOHDnCJ598wowZM8jKyuKOO+5gzJgxREZGlrnI830gmTFjBqNHjwYgNzeXxx9/nC+//JK8vDwGDhzIu+++W2xp3sGDBxk7dixLly7Fy8uLUaNG8eqrr+LsXLopmQqlRERE5Gx2h8FzP2xj1pp4AJ4d0op7ejcxuarqJyffzjNztzJ34xEArm0bytQb21fZsxVm5hbw+85kFmxLZOmuY+QU2Iv2Bfu4MahNKIPahNKtcQDOp5ZvHknLYfi7q0jMyKVLozp8fk939RoTqUKy8mz0mLyYzDwbM+/qytUtg80uSaTGK/dQ6uuvv2bGjBksW7aMgQMHctdddzFkyBCcnKr/H1yFUiIiInKmAruDJ77ZzA+bjmKxwJQb2nJrt4Zml1VtGYbB56sPMml+LAV2g6Z1vXjvzs40C/YxuzQATmTls2hHEgu2JbJiz3Hy7X81Z29Qx4PBp4KojuF1sJ5n2eauxExunL6KzFwb/VuFMP2OTkWhlYiY67OYAzz3w3YigrxYPP6q8/4ei0j5KfdQymq10rBhQ0aMGEFISMh5xz388MNlr9ZkCqVERETktNwCOw/O2shvO5Jwtlp485YOXNc+zOyyaoQN8Sd44PMNJGbk4uXqxNQb2zOkXT1TaknOzOXX7YVBVMz+FOxnnEGvSV0vBrcJZXCberQO8y31csM1+1O48+O15Nsc3NYtnMk3tK02SxVFairDMLjmzeXsTT7Ji9dFMfqKiIvfSUQuW7mHUo0bN77oH1WLxcL+/fvLVmkVoFBKREREoHCJx32f/cnKvSm4OVuZdkcn+kae/2CclN3xk3k8NGsjMacan9/bO4IJgyIrZVbRkbQcFmxLZMG2BP48eIIzPwW3qudbNCOqebD3JYdJC7Yl8sAX63EY8HC/5oy/pkU5VS8il2LFnuPc8dEavFydWP1MP3zcXcwuSaRWKG3OUurF/AcOHCiPukRERESqpPScAu6asZYN8Wl4uTrx4aiuRDcNNLusGifI243P7u7G67/u4r1l+/ngjzi2HE7n/27vSLCPe7k/X9zxrKIgavPh9GL72of7FwZRrUNpHORVLs83qE0ok65vw7Pfb+OdxXsI9nHjjh6NyuWxRaTsZq6KA+DGzg0USIlUQVWzw6SIiIhIJTp+Mo87P1rLjoQM/Dxc+GRMNzqE+5tdVo3l7GRl4uBWdGjgz5PfbmFNXCp/e2cF747oRJfGAZf12IZhsDvpJL9sS2DBtkR2JmYW7bNYoGvjAAa1LpwRFebvcbkvpUR39GhEcmYe7yzew/M/bCPIu7BBuohUrviUbBbvTAZgZM/G5hYjIiVSKCUiIiK12tG0HO74aA37j2UR5O3G5/d0IzJUy/krw+C29WgR6sP9n61nT/JJbn1/Nf8c0orRPS/eNuJMhmGw7UhGURC1/3hW0T4nq4WeTQMZ1CaUAVGh1PVxq4iXco7H+jfnWGYeX66N5+HZG/lsTDe6N9HMO5HK9NnqAxgGXNmiLk3reptdjoiUoNQ9pWoy9ZQSERGpnQ4cz2LEh2s4kpZDfX8PPr+nOxHltIxLSi8rz8aE77Ywf0sCAH9vH8arw9vi6Xr+46cOh8GG+BP8si2RBdsSOZKWU7TP1clK7+ZBDGoTyjVRIfh7ulb4ayiJze5g7BcbWBSbhI+7M9/cH63AU6SSZOfb6DF5MRm5Nj4e3UX9AUUqWbk3Oq/JFEpVHpvdgZPVojPRiIiI6XYlZnLHR2s4lplHRJAXn9/TnfoVtJxLLs4wDGasPMDkn3dgcxi0DPFh2h2daHLG7Aab3cGauFQWbEtk4fZEkjPzivZ5uDjRJ7Iug9rUo0/LulWmd0xugZ07PlzDnwdPEOrrzncP9NT/ZyKV4Is1B/nn3G00CvRkyeNXY7Xq+4dIZVIoVQYKpSqeze5g5qoDvPXbHto18OPj0V1xd3EyuywREamlNh9KY9SMtaRlFxAZ6sNnd3evtGVdcmFr41IZN2sDxzLz8HFz5rUb2+Hh4sQv2xJYFJvEieyCorE+bs70axXMoDb1uKpFXTxcq+Zni7TsfG6aHsOe5JM0revFt/f3pI6XObO3RGoDwzAY+NZydied5Lm/RXF3rwizSxKpdRRKlYFCqYq17Ug6T8/ZwrYjGUXbrm0byn9v66QjFiIiUulW70/h7pnryMq30yHcn0/u6oafZ9WYVSOFkjNyGTdrA+sOnDhnXx1PFwZEhTKobSg9mwbi5lw1g6izHU3LYfi0VSSk59KxoT+z7ulRZUM0kepu1b7j3P7BGjxdnVj9TD98q8jMSZHapLQ5i7W0D2i1WnFycrrgxdlZfdPlL1l5Nl6ZH8vf/7uCbUcy8HV35oGrm+LiZOHnrYm8tmCn2SWKiEgts2RnMqM+XktWvp2eTQP5/J7uCqSqoGBfd2bd24MxVxTObgj2cePOHo2YdU931v2zP6/d2I4+LYOrTSAFEObvwadjuuHn4cLG+DQenLUBm91hdlkiNdLMlQcAGNapvgIpkSqu1DOlfvjhh/Pui4mJ4Z133sHhcJCbm1tuxVUWzZQqf7/vTOK577cXNR29rn0Yz/2tFcE+7ny/8QiPfrUJgJeHtuHOHo1MrFRERGqLn7Yk8MjsjdgcBv1bBfPf2ztpKXk1kJqVj7+HS42ZXf3ngVRGfLiGPJuDm7s04LXh7dRrU6QcHT6RzZVTl+AwYNFjV9I8xMfskkRqpdLmLKWe2nT99defs23Xrl08/fTTzJs3jxEjRjBp0qRLq1ZqjOSMXF6aF8tPWwvPnlPf34NXbmhDn5bBRWOGdqzPodRs3li0mxd+2EYDfw/6RAaf7yFFREQu29frDvH0nC04jMIzu71xc3tcnEo9YVxMFFDDei91aRzAf2/vxD8++5Ov/zxMsI87TwxsaXZZIjXGZ6sP4jCgV7MgBVIi1cAlfRo7evQo9957L23btsVms7Fp0yY++eQTGjXSjJfayuEw+GLNQfr9Zxk/bU3AyWrhviubsGj8lcUCqdMe7NuMmzo3wGHAuFkb2HYk3YSqRUSkNvhoRRxPfVcYSN3WLZw3b+mgQEpMdU1UCP+6oS0A/12yl09jDphbkEgNkZNvZ/baQwCM6tnY3GJEpFTK9IksPT2dCRMm0KxZM7Zv387ixYuZN28ebdq0qaj6pBrYnZTJze/F8M+528jMtdGugR8/jLuCZ65thadryZPxLBYLk4e1pVezILLz7YyZuY6jp5b6iYiIlAfDMHhn8R5enh8LwL29I5h8Q1ucasgyMKnebuvWkMf6twDghR+38/OpWeYicul+2HSE9JwCGtTxoK9WYohUC6UOpaZOnUqTJk2YP38+X375JatWraJ3794VWZtUcbkFdt74dRdD3vmDPw+ewNPVief/FsXcB66gTX2/i97fxcnKu3d0okWIN8mZedw1Yx0ZuQUXvZ+IiMjFGIbBlF928p9FuwEYf00Lnrm2lXr3SJXycL9mjOjeEMOAR2dvImZfitkliVRbhmEwc9UBAEZFN9YBCJFqotSNzq1WKx4eHvTv3x8np/M3BZ0zZ065FVdZ1Oi87FbtO84/524j7ngWAP1bBfPS9W2o7+9R5sc6kpbD0P+t5FhmHr2bB/Hx6K5aViEiIpfM7jB49vttfLk2HoDn/hbF3b0iTK5KpGR2h8G4LzawYHsiPm7OfH1/NK3q6fOoSFmt2Z/CLe+vxsPFidUT++nMqiImK/dG5yNHjtTRReFEVj7/+nkH364/DBSeovmlv7dmUJvQS/7/o76/BzNGd+Xm92L4Y89xnp27jVeHt9X/byIiUmYFdgePf72ZHzcfxWKBV4e15ZauDc0uS+S8nKwW3rq1AyM/WsvaA6mM+ngt343tSXiAp9mliVQrp2dJDe1YX4GUSDVS6plSNZlmSl2cYRjM3XiEV37aQWpWPhYL3NG9EU8Oaomve/m86f++M4l7PvkThwFPDmzJuD7NyuVxRUSkdsgtsPPgrA38tiMZZ6uFN2/pwHXtw8wuS6RU0nMKuHl6DLuSMmkS5MW3Y3vWuDMPilSUo2k59J66BLvDYMGjvYkM1Xc6EbOVNmfRGim5qIMpWdz50VrGf72Z1Kx8Wob48O39PXl5aJtyC6QA+kaG8NLfWwPw+sJd/LDpSLk9toiI1GxZeTbGzFzHbzuScXO28v7IzgqkpFrx83DhkzHdqO/vwf7jWYyZuY7sfJvZZYlUC5+vPojdYRDdJFCBlEg1U+rle8OGDSvVuOrYU0pKVmB38P7y/byzeA95NgduzlYe7tec+65sUmE9n+6MbsyhEzm8v3w/T36zhVBfd7o3CayQ5xIRkZohPbuA0TPXsjE+DS9XJz4a3ZUe+tsh1VConzufjOnKjdNj2HQojXFfbOD9kV3Ua1PkAnIL7EU9BEf1bGxuMSJSZqX+C+fn51eqi9QM6w+e4G/vrOD1hbvIszm4olkgCx+9knF9mlX4B6OnB0UyuE0o+XYH9322nn3HTlbo84mISPV1LDOPW96PYWN8Gn4eLnxxbw8FUlKtNQv24aNRXXF3sbJk1zGe/m4r6rYhcn4/bj7KiewC6vt70L9VsNnliEgZqacU6il1pozcAl5fsIvP1xzEMCDAy5Vnh7Tiho71K7XxeG6Bnds+WM3G+DQaBngy54GeBHm7Vdrzi4hI1XckLYc7P1zD/uNZBHm78fk93bRsQ2qMxTuSuO+z9dgdBmOvbsqEQZFmlyRS5RiGwd/+bwXbj2bw9OBI7r+qqdklicgp6iklZWIYBr9sTeCa/yzjs9WFgdSNnRvw2/irGNapQaWfCc/dxYkPR3ahYYAn8anZ3PPJn+QW2Cu1BhERqbrijmdx8/QY9h/Por6/B9/cH61ASmqUfq1CmHJDWwCmLd3HjJVxJlckUvWsP3iC7UczcHO2ckuXcLPLEZFLoFBKOJqWw72frmfsFxtIysijcaAns+7pzr9vam/qWV8Cvd2YeVdX/D1d2HQojUdnb8LhqPUT+0REar3YoxncNH0VR9JyaBLkxTf3RxMR5GV2WSLl7uau4TwxoAUAk+bHMn/LUZMrEqlaZqw6AMDQDvWpo7NVilRLCqVqMbvD4OMVcVzzn2X8tiMJFycLD/VtxoJHr6RnsyCzywOgSV1v3r+zC65OVhZsT2TKLzvMLklEREz054FUbnk/huMn84mq58vX90cT5u9hdlkiFWZcn2aMjG6EYcD4rzazau9xs0sSqRIS03NZsC0RUINzkepMoVQttf1oOsPeXcmk+bFk5dvp3KgOPz3cm8cHtMTdxcns8orpFhHA6ze1A+CDP+L4NOaAuQWJiIgplu0+xh0frSEz10bXxnX48r4e6jcoNZ7FYuGF61pzbdu/TgKz/Wi62WWJmO6LNQexOwy6RQQQFabl2yLVlUKpWiY738bkn3fw9/+uZPPhdHzcnfnXDW345h/RtAjxMbu887q+Q32eHNgSgBd/3M7iHUkmVyQiIpXp560J3PPJOnILHFzVoi6fjumOn4eL2WWJVAonq4X/3NyB7hEBnMyzMXrGOg6lZptdlohpcgvszFoTD8BozZISqdYUStUiS3YlM+DN5by/fD92h8GQtvVYPP4qRnRvhNVauY3ML8UDVzfl1q7hOAx4cNZGth7WUUIRkdrgq3XxPDhrAwV2gyHt6vHByC54uFatWb0iFc3dxYkPRnUhMtSHY5l5jPx4LSkn88wuS8QUP21JICUrn3p+7gyICjG7HBG5DAqlaoFjmXk89OVG7pqxjsMncqjv78HHo7vwvxGdCPZ1N7u8UrNYLLw8tA29mweRU2BnzCfrOHxCRwlFRGqyD5bvZ8J3W3EYcFu3cN65tSOuzvr4IrWTr7sLn4zpRn1/D+KOZzFm5jqy8mxmlyVSqQzDYOapBud39GiEs5P+JohUZ/oNrsEcDoPZa+Pp98ZS5m0+itUCd/eK4NfHrqRvZPU8ouDiZOXdEZ2KjhKOmbmO9JwCs8sSEZFyZhgG/164i3/9XHiCi39c2YTJN7TFqRrM7BWpSCG+7nx6dzfqeLqw+XA6Y7/YQIHdYXZZZZaalc+8zUeZ8O0Wrnj1d3q99jvzNuvsgnJxG+LT2HokHVdnK7d2DTe7HBG5TBbDMAyzizBbRkYGfn5+pKen4+tbM5rk7U3O5Jk521h7IBWANvV9mXJDO9o28DO5svJxNC2HG95dSVJGHj2bBjLzrm46ci4iUkM4HAYvztvOpzEHAXhyYEseuLopFosCKZHTNsaf4PYP1pBTYGdYx/r8+6b2VbodQ26BnfUHT/DHnuOs2HuM7UczKOlbyJB29Xj5+jYEeLlWfpFSLTz85UZ+3HyUGzs34N83tTe7HBE5j9LmLKZ+i1++fDnXXXcdYWFhWCwWvv/++2L7R48ejcViKXYZNGhQsTGpqamMGDECX19f/P39ufvuuzl58mQlvoqqJbfAzn8W7Wbw23+w9kAqHi5OPDukFd8/cEWNCaQAwvw9+Hh0V7xcnVi1L4Vn5m5F+aqISPVXYHfw+Deb+TTmIBYLvDy0DeP6NFMgJXKWjg3r8O4dnXCyWpiz8QivLdxpdknFGIZB7NEM3l++jzs/WkOHSb8y4sM1TF+2j21HCgOpyFAf7ukVwcy7uvJIv+Y4WS38tCWBAW8u49ftiWa/BKmCkjNy+XlrAqAG5yI1hbOZT56VlUX79u0ZM2YMw4YNK3HMoEGDmDFjRtFtN7fip34eMWIECQkJLFq0iIKCAu666y7uu+8+Zs2aVaG1V0Wr9xeGM/uPZQHQNzKYSde3pkEdT5Mrqxitw/z474hO3PPJn3y7/jANAzx5uF9zs8sSEZFLlFtg58FZG/htR/Kps4215/oO9c0uS6TK6tMymNeGt+OJbzbz3rL9BPu4c3evCNPqSUzPZcXe46zYc4wVe49z/GR+sf3BPm70ahZE7xZBXNEsiGCfv3qbXt0ymP6tQnj8m03sTjrJfZ+tZ1jH+rxwXWv8PHWmTSn0xZp4bA6DLo3q0KZ+zTngLlKbVZnlexaLhblz5zJ06NCibaNHjyYtLe2cGVSn7dixg6ioKNatW0eXLl0AWLBgAddeey2HDx8mLCysVM9dE5bv5dsc9Pn3Uo6k5VDXx40Xr2vNtW1Da8WR5S/WHOSfc7cB8OYt7bmhYwOTKxIRkbI6mWfjnk/WsXp/Km7Ohf0D+7Wqnv0PRSrbu0v3MnXBLgDevrVDpYW5WXk21sSlFC7J23OcPcnFVyt4uDjRvUlAYRDVvC4tQrwv+tk0t8DOm7/t5oPl+3EYEOLrxmvD23F1y+CKfClSDeTbHPR89XeOn8zj/27ryHXtS/ddT0TMUdqcxdSZUqWxdOlSgoODqVOnDn379uWVV14hMDAQgJiYGPz9/YsCKYD+/ftjtVpZs2YNN9xwQ4mPmZeXR17eX6fQzcjIqNgXUQlcna28MrQNi3YkMWFQJH4eteeI0ojujYhPzea9Zft56tsthPp6EN000OyyRESklFKz8hk9Yy1bDqfj7ebMh6O60KOJ3sdFSmvsVU1Jzshj5qoDPPHNZgK8XOndvG65P4/dYbDlcBor9hznj73H2Rh/ggL7X8e3LRZoV9+PXs2D6NWsLp0a+ePm7FSm53B3cWLi4FYMiArliW82E3c8i9Ez1nFbt3D+OSQKb7cq//VFKsjPWxM4fjKPEF83BrUJNbscESknVfpdfdCgQQwbNoyIiAj27dvHM888w+DBg4mJicHJyYnExESCg4sfNXF2diYgIIDExPOvQ58yZQovvfRSRZdf6fpEBtMnsnYeRZowMJLDqTn8tDWBf3z2J3Me6EmzYB+zyxIRkYtITM/lzo/WsCf5JHU8C093366Bv9lliVQrFouF5/8WxbGTefy0JYH7P1vPV/+ILpflTQdTsopmQq3ad5yMXFux/eEBHvRqVpfezYPo2TQQf8/yaVDeuVEdfn64N1MX7mTGygN8ufYQf+w5ztQb29GzaVC5PIdULzNXHQAKD0i7OOkERyI1RZUOpW699dai623btqVdu3Y0bdqUpUuX0q9fv0t+3IkTJzJ+/Pii2xkZGYSH63Si1ZnVauGNm9uTmJHL+oMnGD1jHXMfuIK6Pm4Xv7OIiJjiYEoWIz5cw+ETOYT6uvPZ3d1oHqIDCiKXwnqqD9uJrHxW7Uth9Iy1fDe2J40Cvcr0OGnZhfc/fZa8Q6k5xfb7ujvTs2kQvZoH0bt5UJkfvyw8XJ144brWDIgK5clvN3P4RA63f7CG0T0b89Sglni6VumvMlKONh1KY9OhNFydrNzWraHZ5YhIOapW7+RNmjQhKCiIvXv30q9fP0JDQ0lOTi42xmazkZqaSmjo+ad0urm5ndMwXao/dxcnPhjZhWHvruRASjb3fLKO2fdF4+FatmnjIiJS8XYlZnLHR2s4lplHo0BPPr+7O+EBNfPEHCKVxc3Ziffu7Mwt760mNiGDkR+v5dv7e17wIF2+zcGG+BNFS/K2Hk7DcUbHWWerhU6N6tC7WWEQ1ba+H86VPEslumkgCx69ksk/72DWmnhmrjrA0l3JvHFzezo3CqjUWsQcn5yaJfW3dvV00FmkhqlWodThw4dJSUmhXr16AERHR5OWlsb69evp3LkzAL///jsOh4Pu3bubWaqYJMDLlRl3dWPYuyvZfDidR2ZvZNodnXGy1vyG7yIi1cXG+MIZrek5BUSG+vDp3d2KnYVLRC6dj7sLM8d0Zfi0VRxMyWbMzHV8eV+Pol5MhmGwJ/nkqSV5x1gTl0p2vr3YYzQP9i6aCdU9IhCvKtDHydvNmck3tGVg61AmfLuFAynZ3Dg9hvt6N+Gxa1rg7qKDkDXVscw85m85CsCono3NLUZEyp2pZ987efIke/fuBaBjx4785z//oU+fPgQEBBAQEMBLL73E8OHDCQ0NZd++fTz11FNkZmaydevWoplOgwcPJikpienTp1NQUMBdd91Fly5dmDVrVqnrqAln35Pi/jyQyu0friHf5mDMFRE8f12U2SWJiAiwcu9x7v30T7Lz7XRs6M/M0d10uneRChB3PIvh01aRmpVP7+ZBDO/UgOV7jrFy73GSMvKKjQ3yduWKU2fI69UsiFC/qh0Sp+cUMGleLN9tOAwUhmhv3Nxe/ehqqHcW7+E/i3bTsaE/cx+4wuxyRKSUSpuzmBpKLV26lD59+pyzfdSoUUybNo2hQ4eyceNG0tLSCAsLY8CAAbz88suEhPx1iujU1FQefPBB5s2bh9VqZfjw4bzzzjt4e3uXug6FUjXTvM1HeejLjQC8cF0Ud10RYXJFIiK124JtiTz85Uby7Q56NQvivTs7V4kZGCI11eZDadz2wepzZkK5OVvpFhFA71NnyYsM9cFaDWeVL4pNYuKcrRw/mYeT1cK4q5vyYN/muDqrCXZNkW9z0Ou130nOzOPtWztwfYf6ZpckIqVULUKpqkKhVM01bek+XluwE4sF3rujMwNaV+/Tx9odBjsSMgj1cyfIW+vpRaT6+Hb9YZ76djMOAwa1DuXt2zqU+VTxIlJ2y3cf47GvNlHP373oLHmdG9WpMcvdTmTl89wP25i/JQGAqHq+vHFze1rV02f6muDHzUd5+MuN1PVxY+WEvgocRaoRhVJloFCq5jIMg2fmbuPLtfG4u1j56r5o2of7m11WmaTnFPDHnmP8viOZpbuPkZqVT5C3G9/eH03joIo7442ISHmZsTKOl+bFAnBj5wa8OqxtpTdKFpGabf6Wozz3/TZOZBfg4mThkX7Nuf+qpnqvqeaGT1vF+oMneKRfcx67poXZ5YhIGSiUKgOFUjWbze7g7k/+ZNnuYwR5uzH3gZ5V+gxPhmGw79hJft+ZzOIdyfx58AR2x7m/puEBHnx3f0+Cfat23wcRqb0Mw+DtxXt467c9AIy5IoJnh7SqlsuERKTqO5aZxzNzt7IoNgmA9g38eOPm9jQL9jG5MrkUWw+nc91/V+DiZGHlhL76zCtSzSiUKgOFUjXfyTwbN02PYUdCBs2CvflubE/8PKpOY93cAjtr4lL5fUcSv+9K5lBqTrH9zYK96RcZTJ/IYBoFenLr+6s5mJJNZKgPX/0jukq9FhERAIfD4OWfYpmx8gAAj/VvwcP9mmGxKJASkYpjGAZzNx7hhR+3k5lrw9XZypMDWjKmV4TOxlzNPP71Zr7bcJjrO4Tx9q0dzS5HRMpIoVQZKJSqHRLSc7jhf6tIzMglukkgn4zpZuq69MT0XJbsKpwNtXLvcXIK/mpC6upsJbpJIH0jg+kbGXzOzK74lGyGT1/Fscw8ujUO4NO7u9WY3hAiUv3Z7A6enrOVb9cXnhlLJ5sQkcqWkJ7DhO+2snz3MQC6Nq7Dv29qT6NAtT6oDlJO5hH96u/k2xzMeaAnnRrWMbskESkjhVJloFCq9tiRkMFN02M4mWdjWKf6vHFT+0o7am93GGw+nMaSU8vyYhMyiu0P8XWjb2QIfSODuaJZIJ6uFz4jVezRDG55P4bMXBv9WwUz/Y7O6psgIqbLs9l5+MuNLNyehJPVwtTh7RjeuYHZZYlILWQYBrPXHeKV+bFk5dvxcHFi4rWR3NG9kZYRV3H/W7KX1xfuol0DP34Yd4Vm2YpUQwqlykChVO2ybPcxxsxch91h8Gj/5jzav+KaJpbUpPw0iwU6hPsXLcuLqudb5j+4a+NSufOjNeTZHAzv1IB/39ROf7RFxDRZeTb+8dl6Vuw9jquTlf+7vSMDq/lZT0Wk+juUms1T324hZn8KAD2bBjL1xnY0qFN1e4zWZgV2B71fW0JiRi7/ubk9wzrpwIZIdaRQqgwUStU+X66NZ+KcrQD8+6b23FhOR/Ev1qTcx92ZK1vUpV9kMFe1qEugt9tlP+dvsUn84/P12B0G913ZhGeubXXZjykiUlZp2fncNXMdG+PT8HR14oORXbiiWZDZZYmIAIV97j6NOcCrC3aSW+DA282Z5/7Wipu7hOuAXhXz05YExs3aQJC3Kyuf7oubs1pUiFRHCqXKQKFU7TR1wU7eXboPZ6uFT8d0o+clfnk63aR8yc5kFu9MKrFJ+eneUJ0b1cGlApbYffPnIZ78dgsAEwdH8o+rmpb7c4iInE9yRi53frSWXUmZ+Hm4MPOurnRU/w8RqYLijmfx+Neb2BCfBkCflnV5dXg7QnRmtyrj5ukxrD2QykN9m/H4gJZmlyMil0ihVBkolKqdHA6DR77axLzNR/Fxd2bO2J40DyndKYMv2KTcyUqPpoH0bVmXvpEhNAysnKnh7y/fx+SfdwIw9cZ23NwlvFKeV0Rqt0Op2dzx0RoOpmRT18eNz+7uRmSo/paKSNVldxh8+Md+3li0m3ybAz8PF176e2uu7xCmWVMm2340nSHvrMDZamHFhL6E+iksFKmuSpuzXLiTskgNZrVaeP3GdiSm57DuwAlGz1jH3HE9CfY5949f6ZqUB9OnZTBXNAvCy63yf7Xuu7IpKSfzeW/5fp7+bgt1PF25Jiqk0usQkdpjT1Imd3y0hqSMPMIDPPj87u46s5WIVHlOVgv/uKopfSODefybzWw5nM6jX23il20J/OuGtgSVQ3sFuTSfrDoAwKA2oQqkRGoJzZRCM6VquxNZ+Qybtoq441m0a+DH7Pt64Onq/FeT8p3JLN1VcpPyvi2D6dvq0pqUVwTDMHjq2y18s/4wrs5WPhvTje5NAs0uS0RqoC2H0xj18VpOZBfQPNibz+7uri8QIlLtFNgdTF+6j7cX78HmMAjwcuWVoW24tm09s0urdU5k5dNjymLybA6+vT+aLo0DzC5JRC6Dlu+VgUIpOXA8i2HTVpGalU/XxnVwslpYd6DkJuV9WwZzdcvyaVJeEWx2B/d/voHfdiTh4+bM7H/0oHWYn9lliUgNErMvhXs//ZOTeTbaN/Bj5l3dqOPlanZZIiKXbPvRdB7/ejM7EzMB+Hv7MCZd3xp/T723VZZpS/fx2oKdtA7zZf5DvarEAV8RuXQKpcpAoZQArD94gts+WE2+zVG07XST8j4tg+nSuGKalFeE3AI7Iz9ey9q4VIK83fhubLSW1IhIufgtNokHZm0g3+YgukkgH4zqgrcJS5ZFRMpbns3OO4v3MG3pPhwG1PVx49VhbenXSu0QKprN7uDKqUs4mp7L6ze24yb1RhWp9hRKlYFCKTlt6a5kvll/mK6N6lRqk/KKkJFbwC3vrWZHQgYNAzz59v5ognVmGRG5DN9vPMLj32zG7jDo3yqE/97eEXcXnapbRGqWTYfSePzrTew7lgXATZ0b8Nx1Ufi6u5hcWc21YFsC93++gQAvV1Y93Vd/W0RqAIVSZaBQSmqq5MxcbpwWQ3xqNq3q+TL7vh74eegDlYiU3WcxB3j+x+0YBtzQsT5Tb2xXbWaPioiUVW6BnTd+3cWHK+IwDAjzc+feK5vQPSKQyFAfrFYtLStPt74fw+r9qTxwdVOeGhRpdjkiUg4USpWBQimpyQ6mZDF8WgzHT+bRrXEAn97dTUefRKTUDMPg3aX7eH3hLgBGRjfixeta6wuZiNQK6w6k8sQ3mzmYkl20zc/Dha6NA+geEUD3JgFE1fPFWSH9JduRkMHgt//AyWrhj6f6EObvYXZJIlIOFEqVgUIpqem2H03n1vdWk5lno3+rEKbf0UkfnkTkogzDYMovO3l/+X4AHurbjPHXtFDzWRGpVbLzbXwWc5AVe4+z/uAJsvPtxfZ7uznTuVEdujcJoHtEIG3r++HqrM9ZpTVxzha+XHuIa9uG8u6IzmaXIyLlRKFUGSiUktpgzf4URn68ljybg5s6N2Dqje30xVJEzsvuMPjn3K3MXncIgH9e24p7r2xiclUiIuYqsDvYfjSDNftTWBuXytoDqWTm2oqN8XBxolMjf7pHBNItIoAO4f6apX4eadn59JiymNwCB1/d14PuTQLNLklEyolCqTJQKCW1xaLYJO7/fD12h8E/rmzCxGtbmV2SiFRBsUczeGbuVjYdSsNqgSnD2nJL14ZmlyUiUuXYHQY7EjJYG5fKmrjCoOpEdkGxMa7OVjqE+xcu94sIpFMjfzxdddZSgPeX72PyzzuJDPXhl0d664CpSA2iUKoMFEpJbfL1n4d46tstAEwcHMk/rmpqckUiUlVk59t4+7c9fLgiDrvDwNvNmak3tuPatvXMLk1EpFpwOAz2HjvJmv0prI5LZc3+VI6fzCs2xtlqoW0DP7pHBNI9IoAujevgUwvP7Gd3GFz1+hIOn8jhteE6+CFS0yiUKgOFUlLbvLdsH1N+2QnA6ze246Yu4SZXJCJmW7Irmee+38bhEzkADG4TygvXtSbUz93kykREqi/DMIg7nsWauNTC2VT7UzianltsjNUCrcP86BZR2Dy9W0QA/p6uJlVceX7dnsh9n63H39OF1RP7aYmjSA1T2pxF80ZFaqF/XNWUlKx83l++n6fnbMXf05VrokLMLktETJCckcuk+bHM35IAFJ72fNL1beiv9wQRkctmsVhoUtebJnW9ua1bQwzD4PCJHNacCqjWxKUSn5rN1iPpbD2Szkcr4gCIDPU5FVAV9qWq6+Nm8ispf5/EHADglq7hCqREajHNlEIzpaR2MgyDJ7/dwrfrD+PmbOXTMd3UXFKkFnE4DGatjee1BTvJzLVhtcCYKyJ47JoWeLnpmJWISGVJSM9hbVwqq/ensjYuhX3Hss4Z07SuF90iAulx6gx/1X0W6+6kTAa8uRyrBZY/1YcGdTzNLklEypmW75WBQimprWx2B/d/vp7fdiTj4+bMV/+IJipMvwMiNd2uxEwmztnChvg0ANo18GPyDW1pU9/P3MJERIRjmXmFZ/aLK5xJtTMx85wxDQM8CxunNynsS9Wgjke1ahL+z7lb+WJNPANbh/DenV3MLkdEKoBCqTJQKCW1WW6BnZEfrWXtgVSCvN2YM7YnDQN1tEqkJsotsPPO4j28v3w/NoeBl6sTTwxsycjoxjhZq8+XGRGR2iQtO//U2f0K+1JtP5qO46xvcGF+7nRpHEBkPR8iQ31oEeJDff+qGVSl5xTQY/JicgrszLq3Oz2bBpldkohUAIVSZaBQSmq79JwCbnkvhp2JmTQM8OTbsdEE+1TvaeEiUtzy3cd49vttxKdmAzAgKoQX/96aMH8PkysTEZGyyMgtYP2BE4V9qeJS2Ho4HdvZKRXg7eZMixBvWob60vL0z1AfArzMbaL+4R/7eeWnHbQM8WHBo72rZHAmIpdPoVQZKJQSKWx2fOP0GOJTs2lVz5ev/tED31p4emKRmuZYZh6v/BTLD5uOAhDq685L17dmYOtQkysTEZHykJ1vY8PBNDYfTmNXYia7EjPZd+xkiUEVQF0fN1qG+NAy1KfoZ/MQbzxdK76foN1h0OffS4lPzWbyDW25vXvDCn9OETGHQqkyUCglUuhgShbDp8Vw/GQe3SIC+HRMN50NRaSacjgMvv7zEFN+2Ul6TgFWC4zq2ZjHB7TEW43MRURqtHybg7jjWexMzGB3UmFQtSspk0OpOSWOt1gK+1S1CClc/nc6sGoc5IWLk7Xc6lq8I4m7P/kTX3dnVj/Tr1KCMBExh0KpMlAoJfKX7UfTufW91WTm2bgmKoRpIzrhXI4fRkSk4u1JyuSZuVtZd+AEAK3DfJkyrC3tGvibW5iIiJjqZJ6NPWeEVLsSM9mdlMnxk/kljnd1stKkrldhn6rQy+9XdedHa/hjz3Hu7R3BP4dEXe7LEZEqTKFUGSiUEilu9f4URn68lnybg5u7NOC14e203l+kGsgtsPO/JXuZvmwfBXYDT1cnxl/TgtE9GytcFhGR8zp+Mo/diZnsPBVSnf6ZnW8vcXxJ/aoiQ32oc4F+VXuTT9L/P8uwWGDZE310Yh2RGq60OYvmS4rIOXo0CeS/t3Xk/s/X8/Wfh6nj5crEwa3MLktELmDl3uP8c+5WDqQUNjLvFxnMS9e3pkEdfegXEZELC/J2I6iZGz2b/XUmPIfD4EhaTvGg6lS/qpN5NjbEp7EhPq3Y49T1cSuaTXV6CeDpflWfxhwAoF9kiAIpESli6kyp5cuX8/rrr7N+/XoSEhKYO3cuQ4cOLdpvGAYvvPACH3zwAWlpaVxxxRVMmzaN5s2bF41JTU3loYceYt68eVitVoYPH87bb7+Nt7d3qevQTCmRkn297hBPfbcFgGeujeS+K5uaXJGInC3lZB7/+nkHczYcASDYx42X/t6aQW1CNcNRRETK3aX2q0pMzyXP5uDzu7vTq3lQiWNFpOaoFjOlsrKyaN++PWPGjGHYsGHn7J86dSrvvPMOn3zyCRERETz33HMMHDiQ2NhY3N0LT1c/YsQIEhISWLRoEQUFBdx1113cd999zJo1q7JfjkiNc3PXcFKz83n1l51M/nknAV5u3Ni5gdlliQiFB26+WX+YyT/vIC27AIsFRvZoxOMDW+rMmSIiUmFcna2Fs6BCfYptL6lf1a7ETFKy8jl4ahZvixBvrmgWaEbZIlJFVZmeUhaLpdhMKcMwCAsL4/HHH+eJJ54AID09nZCQEGbOnMmtt97Kjh07iIqKYt26dXTp0gWABQsWcO2113L48GHCwsJK9dyaKSVyfoZhMPnnHXzwRxxOVgvv3dGZ/lEhZpclUqvtO3aSZ+ZsZU1cKgCRoT5MGdaWjg3rmFyZiIhIcaf7VcWlZNGrWRCNAr3MLklEKkFpc5Yq2/U0Li6OxMRE+vfvX7TNz8+P7t27ExMTA0BMTAz+/v5FgRRA//79sVqtrFmz5ryPnZeXR0ZGRrGLiJTMYrEwcXArhndqgN1hMG7WBtae+iIsIpUrz2bnzUW7GfzWH6yJS8XdxcrEwZHMe6iXAikREamSgrwLe1WN6N5IgZSInKPKhlKJiYkAhIQUn5EREhJStC8xMZHg4OBi+52dnQkICCgaU5IpU6bg5+dXdAkPDy/n6kVqFqvVwqvD29IvMpg8m4O7P1nHjgSFuSKVKWZfCoPf/oO3F+8h3+7g6pZ1WfTYVfzjqqa46Mx6IiIiIlIN1cpPsRMnTiQ9Pb3ocujQIbNLEqnyXJys/G9EJ7o2rkNmro2RH68l/lR/ABGpOCey8nnym83c9sFq9h/Loq6PG/+7vRMzRnclPEBnLxIRERGR6qvKhlKhoaEAJCUlFduelJRUtC80NJTk5ORi+202G6mpqUVjSuLm5oavr2+xi4hcnLuLEx+O6kpkqA/HMvO48+M1JGfmml2WSI1kGAbfrT9Mv/8s45v1h7FY4I4eDflt/FUMaVdPZ9YTERERkWqvyoZSERERhIaGsnjx4qJtGRkZrFmzhujoaACio6NJS0tj/fr1RWN+//13HA4H3bt3r/SaRWoDPw8XPh3TjfAADw6mZDP643Vk5BaYXZZIjRJ3PIsRH67h8W82k5qVT8sQH769vyevDG2Ln4fOrCciIiIiNYOzmU9+8uRJ9u7dW3Q7Li6OTZs2ERAQQMOGDXn00Ud55ZVXaN68ORERETz33HOEhYUVnaGvVatWDBo0iHvvvZfp06dTUFDAgw8+yK233lrqM++JSNkF+7rz2Zju3Dh9FbEJGdz7yZ98MqYb7i5OZpdWJM9mJyEtl6NpORxOy+FoWg5HTuRwND2HhLRcmtT14pF+LWjbwM/sUkWK5NscvLdsH/+3ZC/5NgduzlYe6d+ce3s3Ud8oEREREalxLIZhGGY9+dKlS+nTp88520eNGsXMmTMxDIMXXniB999/n7S0NHr16sW7775LixYtisampqby4IMPMm/ePKxWK8OHD+edd97B29u71HWU9lSFIlLctiPp3Pb+ajLzbAyICuHdEZ1wroQvzoZhkJFj40haDkdOB06nLycKfx7LzCvVYw1uE8rjA1rSLLj07xkiFWFtXCrPzN3K3uSTAPRuHsS/hralYaD6RomIiIhI9VLanMXUUKqqUCglculi9qUwasZa8m0ObukSzqvD2152rxu7wyA5M7coYDpyxkynwuu5nMyzXfRx3F2shPl7UP+MS5i/B3V93Ji78QjfbzqCYYDVAsM7NeCR/s1pUEcBgFSutOx8Xv1lJ7PXFZ50I8jbleeva8116hslIiIiItWUQqkyUCglcnkWbk9k7OfrcRgw9uqmTBgUecHxOfn2YjOczgycjqTlkJiei81x8bemAC/XU0GTO/X9PQnzd6dBHY+iICrAy/WCX+p3Jmbwxq+7WRRbeEIFVycrt3dvyIN9mxHk7Va2fwSRMjIMgx83H+Xl+bEcP5kPwG3dwnl6UCv8PNU3SkRERESqL4VSZaBQSuTyfbUungnfbQVgwqBIejUL4khaNkfSCmc8nbnELjUr/6KP52y1EOrnTpi/Bw1OzXCqf0bgVN/fAw/X8ulhtSH+BK8v2EXM/hQAPF2duLtXBPde2QRfd4UDUv6OpOUwcc5Wlu8+BkDzYG8mD2tL18YBJlcmIiIiInL5FEqVgUIpkfIxbek+Xluws1RjvVydqF/nryV1p6/XP3U92McdJ2vlLV0yDIOVe1OYunAnWw6nA4VnGhx7dVNGRTcutwBMajfDMPhq3SFe+WkHJ/NsuDpbebhvM+67simuzmpkLiIiIlIj2fIh5wTkpEJ26lnXT93OPuNn/xeg5WCzq74spc1ZTD37nojULPdf1YTM3AKmL9tHoLdbsZCpKHw6dfH1cK5S/XIsFgu9mgdxRbMrWLg9kX//upu9ySd59ZedfLwijof7NeeWruE6A5pcsoT0HCZ899fsqE4N/fn3Te1pUldN9kVERESqBYcD8tKLB0jnDZhSIfvUvvyTZXuejCMVU38VpJlSaKaUSHkzDKNKBU6Xwu4wmLvxCG8u2s2RtBwAGgZ4Mv6aFvy9fRjWSpzFJdWbYRh8s/4wL8+LJTPPhpuzlScGtGRMr4hKnQ0oIiIiIqcYBhTkFA+TigVMJ0oImFIhNw0MxyU+qQU8/MEjADzqgGdA4fXTPz38/7oe3Ap8Qsvv9ZpAy/fKQKGUiJxPns3Ol2vi+e+SvUXNqCNDfXh8QEv6twqu9uGbVKzE9FwmztnCkl2Fs6M6npod1VSzo0REREQujy0PctMhN+PUzzTIO339zO3pxbefDprseZf+3C5epwIk/+LBkuepwKmkbe5+YK09LUEUSpWBQikRuZjsfBszVh5g+rJ9ZObagMLlV08OjCS6aaDJ1UlVYxgG3204wkvztpOZW9g76vFrWnBP7yaaHSVSFRlG4Zcbe15h3w973qnb+WDLPWPb2ftOXXcUgMMGDnvhxbCfcdtWeFS92G37X2MdtpK3lXjbVrh0pNjtUo4BsFgAS/Hr5/tZ4hhKMeZSH8cKVudTlzOuW5xOXXc6dTm9/czxZ4yxOJ21/extpXlsp+KPWeynCzi5FF53cjnP7TPHuRQ+p4gUZxiQn3VuYJSbURgulbj9rIDJlnv5dVidS5i5VOcCs5lObXPWmbovRqFUGSiUEpHSSsvO573l+5mxMo7cgsKpu72bB/HkwJa0a+BvbnFSJSRl5DJxzlZ+35kMQPtwf964qR3Ngn1MrkykijIMsBdAQXbhUoqC7L+u52cV/rTl/hUCFf08KyQ6Z9sZP225F97nKDD7X0FqMou1dOGV0/lCr9KOcznPddcz7lPCdSfXi9z/1PNodnjtdfp92pYDBbmFP215f70/n/5py4X87NKHTIa9fOpz8y2chXT6p7sfuJ9x/Zzt/n+FTG4++n+7giiUKgOFUlIlnH6zdxSc+mk7/22Ho/Co69kXjLO2nX377Etp95fxcTAoPAJqPXU5dTTUYj1ru/Wv7UX7LOduP+d+Z4y56ONZz/OYZx/5PP2h7syjqWceaXUpdpQ1OSOX//t9L1+ujcfmKHwbHdwmlMcHtFD4UEsZRmEfshd/3E5Grg1XJyuPXtOc+3o3wVkN8qU6Ozswyj87PDodIGWXHCwV3ef07RwoyPrren5W+X0xKS9OruDkBs5n/HR2L9zu7HbuPifXEmbqOJ01o+f07dOzcM687XyebdaSx5z3Oc7zvJbTy0VOfew3jMLr5/t5zpgS7nNJj2Oc2lzCGKOkWWZnziRz/HXdccYYw37W9rNnjZ31OKV+rDPGGHawn95e0ue0M7ZXtf+Xy1OZwq7TgVlJ151KDtWszucGbiWFc0Xbzt5XUljnTIlBXnUOIU7P7CwKiHKLB0MFOWftLylAKun+uee/jy33MvooXYTVuYTw6PRP/4sHTW4+1KYlcdWJQqkyUChVAxnGuUdUzznKeuZ0/LOOsBZ94Djrg8b5PoCUZZzDXvs+xNQ4lqIPSg6LEzkOKzk2sOGEHSfcXV3x9fLAxeX0h6yzgy6nsz4slbTf5dwPccWOXrpeYN9ZHxZLPAJ61j6rU/X+gGay5Ixcnpm7jd92JAHQroEf/76pPS1CFFBKBXE4Tn15yCn+RaMg568vFQXZZ31Ryf7rC8g5Y88Iic4OnipzFpHFCVy9wMXj1MULXNyLB0LnhEJuJew7Y1uxIKmU+/R+KJeqKMwqxUHGUn+WvND9zjPOnn+R6/lnjD91/czHqemfSy1OJYddVmeKB6pQLJw9+3rRuLLe5+z7l+F57PmX+qrLiaXw/dn51Huzizs4e5zx0+MCs5RK2ObioffcGqq0OYtzJdYkNY1hnApY8s/6A5l//j+KjoKLTLU/c1sJPRsu1OOhWOh0GU3rqpKS/mCePsJ03llEJcwYKvFyof3nma1U2seAM46InmfW1YVmdZ3+Q3yhmVhF28+3z3HumNP7HPYzjpLa//rwWOz2qUuJjFPjC7ACXoDXmX9LC4C0Cvk/omKdHV4VBV9lDMHOPEpa2mUEpQnOLrTEwKQPM4Zh8MOmo7zw43bScwpwcbLwaP8W/ONKzY6qFU4fZCj6QnfGQQ17/l/bzxcanTdUKilIOh0enbq/GV9KLFZw8Tx18Sj86Xrm7dMh0qnrReHSGeNPXy+274z9Jv4+i5QLqxWsroCr2ZVcHoej+PvZ+YKs84VaFwzC7CWHcQ5b6Q7mFn2vODuos1Fi2FcSww42O1AOPYnMZLGeFQi5nxEWeZwRGp21raQwydntjPucb5y7gnspdwqlapLYHyH90Kk3fFvJfxSKtp3xBl5iqGQrYWwJ968urC7nHl09Zxr+6f0uxb8En7eh5XmmHl9OH4Czb+sN31yng9czQ6pitwvO2b/r6Almxexnx5FUnCwOvJwNBkfVZVBUEF7OnPVYJTzm+T6sFfuye4lHPYuu55c8Bfv0l+hq9KtdxHq+8KqEGWrnvZSw3+n8Y7IKYOHOFHYkZTMcJ4IDPBnauRGhvsdgy5q/xp+5BOHs57CcDpg5/zLVkpahnrOthHElLYe96P0u8p5zoaW8p8Peiy73Lc3FOOPxLrJkuNj/7yWFQjaKBURnHjw58/fk9BjHWeMvtL+iljKUlZPrX0enzzxSfeaXi7O/aLh4Fv+i4eJ1KmA6Myg6HSCduq4vIiK1h9UKVrea0cy5WLB19uesMwKu05/riprxQ9Hf0tPX4a+/w2W6XsJjnfl+WpbncHL96/3cyeWS/1lEqgot36MGLd+b+Tc48Ie5NVisJQQ6rsWDmGJT5t3PM+3+9JR894v0czh72r77uY/v5IrOeiKVbcWe47y+cCebD6cD4OfhwtirmzIqujEerlVg3XvREdCLhFdnh9Vnh2MXHVfWQO3Mceep6bwz2KRcnB1UFZuJKBdm+evvz5kzCy8aCHn+dTS66HpJY88TNKmXhoiIiFQx6ilVBjUmlFr+OhzbVXzJzTnLXM5a/nJOA0KXEu5/dqh09swe17+2KfwRKWIYBgu3J/HGr7vYk3wSgGAfNx7q15xbuoTj6qzfl0tiGKWbDWa/0Ky0As6d+XbGbXvJ+7Pz8ojZk8ThlExcsBPo6UT3xn74u1lK/RjFa7Dz13LVkpa2nr2N8yxjPWtbVXDBpb8Xu5wKxc5eqlzismGnwiCo2PLSM5aPFlvy6Vp8WWix8Ois+xS774X2nbFN4ZCIiIgIoFCqTGpMKCUiVZLdUXhWtjcX7eZIWg4ADQM8eeya5vy9fX2crFoOUx3M33KU53/YTmpWPs5WCw/1bc4DfZriUhV7R50OuS4YcJ0ZZnH+gOt08FOafnRFIZL+nxYRERGpzRRKlYFCKRGpDHk2O7PXHuL/ft/L8ZOFzfhbhvjwxMCW9G8VjEVf5KuklJN5PP/Ddn7amgBAZKgPb9zcntZhfiZXJiIiIiJSNSmUKgOFUiJSmbLzbcxYeYDpy/aRmVvYH6ljQ3+eHNiSnk2DTK5OzvTL1gSe/X4bKadmRz3QpxkP9mmmpZciIiIiIhegUKoMFEqJiBnSswuYvnwfM1bGkVtQuISqd/MgnhzYknYN/M0trpZLzcrn+R+2MX/LX7Oj/n1Te9rU1+woEREREZGLUShVBgqlRMRMyRm5/HfJXr5cG0+BvfAteWDrEP7evj49mgQQ6F0DTsdcjSzYlsiz32/l+Ml8nKwWHri6KQ/1ba7ZUSIiIiIipaRQqgwUSolIVRCfks1bv+1m7qYjnPnOHBnqQ3TTQHo2DaJbRAB+Hi7mFVmDncjK54Uft/Pj5qMAtAjx5t83tdesNRERERGRMlIoVQYKpUSkKtmVmMnsdfHE7EthZ2JmsX1WC7Sp70d000CimwTStXEAXm7OJlVac/y6PZFn5m7j+Mk8rBa4/6qmPNK/OW7OTmaXJiIiIiJS7SiUKgOFUiJSVaWczGP1/lRW7TtOzL4U9h/PKrbf2WqhQ7h/YUjVNJBODevg7qIgpbTSsvN58cftfL+pcHZU8+DC2VHtw/3NLUxEREREpBpTKFUGCqVEpLpITM8lZv9xVu1NYdW+FI6k5RTb7+pspUujOkQ3CaRns0DaNfDHxUm9kEryW2wSE+du5Vhm4eyo+65syqP9myvUExERERG5TAqlykChlIhUV4dSs4tmUa3al0JyZl6x/Z6uTnRtHEDPUz2posJ8cbJaTKq2akjPLuCl+duZs+EIAE3revHvm9rTsWEdkysTEREREakZFEqVgUIpEakJDMNg37EsYvanEHMqqDqRXVBsjK+7M92bBBaFVC1CvLFYak9I9fvOJCbO2UpSRuHsqHt7N+Gxa1podpSIiIiISDlSKFUGCqVEpCZyOAx2JmYWhVRr9qeSmWcrNibQy5UeTQtDqugmgUQEedXIkCo9p4CX58fy7frDADQJ8uL1m9rTuZFmR4mIiIiIlDeFUmWgUEpEagOb3cH2oxms2pfCqn3H+fPACXIK7MXGhPq6FwZUpy4N6niaVG35Wbormae/20piRi4WC9zTK4LHB7TU7CgRERERkQqiUKoMFEqJSG2Ub3Ow+XAaq/amELP/OBsOppFvdxQb0zDA86+Qqkkgwb7uJlVbdhm5BbwyP5av/yycHdU40JN/39SeLo0DTK5MRERERKRmUyhVBgqlREQgt8DO+oMnihqnbz6cjt1R/E9Es2DvwjP7NQ2kUaAXDsPA7jCwn/5Z0qWkfecbX9qxhoHdXvjT4TCwnbXN7jDYfjSdpIw8LBa4q2cETw5siYerZkeJiIiIiFQ0hVJloFBKRORcJ/NsrItLJWZ/4XK/7UczqG5/MRoFevL6je3pFqHZUSIiIiIilaW0OYtzJdYkIiLViLebM30ig+kTGQxAWnY+a+JSidmXQsy+FFKy8nGygrPVivX0T8vp2xacrOBkteJkOWuM1YKT5dS+M7Y5Wy1YLWfcr+gxLTg7Fe5ztlpO3f/cbcX2WS14uTnTJ7Iunq76UyciIiIiUhVV6U/qL774Ii+99FKxbS1btmTnzp0A5Obm8vjjjzN79mzy8vIYOHAg7777LiEhIWaUKyJSo/l7ujKwdSgDW4eaXYqIiIiIiNQAVrMLuJjWrVuTkJBQdFmxYkXRvscee4x58+bxzTffsGzZMo4ePcqwYcNMrFZEREREREREREqjSs+UAnB2diY09Nyj8unp6Xz00UfMmjWLvn37AjBjxgxatWrF6tWr6dGjR2WXKiIiIiIiIiIipVTlZ0rt2bOHsLAwmjRpwogRI4iPjwdg/fr1FBQU0L9//6KxkZGRNGzYkJiYmAs+Zl5eHhkZGcUuIiIiIiIiIiJSeap0KNW9e3dmzpzJggULmDZtGnFxcfTu3ZvMzEwSExNxdXXF39+/2H1CQkJITEy84ONOmTIFPz+/okt4eHgFvgoRERERERERETlblV6+N3jw4KLr7dq1o3v37jRq1Iivv/4aDw+PS37ciRMnMn78+KLbGRkZCqZERERERERERCpRlZ4pdTZ/f39atGjB3r17CQ0NJT8/n7S0tGJjkpKSSuxBdSY3Nzd8fX2LXUREREREREREpPJUq1Dq5MmT7Nu3j3r16tG5c2dcXFxYvHhx0f5du3YRHx9PdHS0iVWKiIiIiIiIiMjFVOnle0888QTXXXcdjRo14ujRo7zwwgs4OTlx22234efnx91338348eMJCAjA19eXhx56iOjoaJ15T0RERERERESkiqvSodThw4e57bbbSElJoW7duvTq1YvVq1dTt25dAN58802sVivDhw8nLy+PgQMH8u6775pctYiIiIiIiIiIXIzFMAzD7CLMlpGRgZ+fH+np6eovJSIiIiIiIiJyGUqbs1SrnlIiIiIiIiIiIlIzKJQSEREREREREZFKV6V7SlWW0ysYMzIyTK5ERERERERERKR6O52vXKxjlEIpIDMzE4Dw8HCTKxERERERERERqRkyMzPx8/M77341OgccDgdHjx7Fx8cHi8VidjmXLCMjg/DwcA4dOqSG7SIXoN8VkdLR74rIxen3RKR09LsicnE16ffEMAwyMzMJCwvDaj1/5yjNlAKsVisNGjQwu4xy4+vrW+3/BxapDPpdESkd/a6IXJx+T0RKR78rIhdXU35PLjRD6jQ1OhcRERERERERkUqnUEpERERERERERCqdQqkaxM3NjRdeeAE3NzezSxGp0vS7IlI6+l0RuTj9noiUjn5XRC6uNv6eqNG5iIiIiIiIiIhUOs2UEhERERERERGRSqdQSkREREREREREKp1CKRERERERERERqXQKpUREREREREREpNIplKpB/ve//9G4cWPc3d3p3r07a9euNbskkSrlxRdfxGKxFLtERkaaXZaIqZYvX851111HWFgYFouF77//vth+wzB4/vnnqVevHh4eHvTv3589e/aYU6yIiS72uzJ69Ohz/sYMGjTInGJFTDJlyhS6du2Kj48PwcHBDB06lF27dhUbk5uby7hx4wgMDMTb25vhw4eTlJRkUsUi5ijN78rVV199zt+V+++/36SKK45CqRriq6++Yvz48bzwwgts2LCB9u3bM3DgQJKTk80uTaRKad26NQkJCUWXFStWmF2SiKmysrJo3749//vf/0rcP3XqVN555x2mT5/OmjVr8PLyYuDAgeTm5lZypSLmutjvCsCgQYOK/Y358ssvK7FCEfMtW7aMcePGsXr1ahYtWkRBQQEDBgwgKyuraMxjjz3GvHnz+Oabb1i2bBlHjx5l2LBhJlYtUvlK87sCcO+99xb7uzJ16lSTKq44FsMwDLOLkMvXvXt3unbtyn//+18AHA4H4eHhPPTQQzz99NMmVydSNbz44ot8//33bNq0yexSRKoki+X/27v7mCrr/4/jLzQtue+IgJDcaRANyNSBpMEMC2wZKK6hVuKcpmFK4jRcjjFtTV1klHbzjzCMbtSw5qhV2EmG2BbTGE0UTng3hPRsYIB6SPj9wc+zUMp9Fc+F8HxsZzt8zudc12tn++y9vflc1+WkkpISpaSkSOrZJeXn56esrCytXbtWktTa2iofHx8VFBQoLS3NwLSAcW5cK1LPTqmWlpabdlABQ9mFCxfk7e2tn3/+WXFxcWptbdWYMWNUXFysefPmSZJqa2sVHh6uyspKTZ061eDEgDFuXCtSz06piRMnavv27caGu8vYKTUI2Gw2VVVVaebMmfaxYcOGaebMmaqsrDQwGTDw1NXVyc/PTyEhIVq4cKHOnDljdCRgwGpoaFBTU1Ov+uLh4aGYmBjqC9AHs9ksb29vhYWFacWKFbJarUZHAgzV2toqSTKZTJKkqqoqdXZ29qorjzzyiAICAqgrGNJuXCvXffrpp/Ly8lJERISys7PV0dFhRLy76j6jA+DOXbx4UdeuXZOPj0+vcR8fH9XW1hqUChh4YmJiVFBQoLCwMJ0/f165ubl68sknVVNTIzc3N6PjAQNOU1OTJPVZX65/BqBHUlKS5s6dq+DgYFksFm3YsEGzZs1SZWWlhg8fbnQ8wOG6urqUmZmpadOmKSIiQlJPXRk5cqQ8PT17zaWuYCjra61I0oIFCxQYGCg/Pz9VV1dr/fr1OnHihL766isD0/Y/mlIAhoxZs2bZ30dFRSkmJkaBgYH68ssvtWTJEgOTAQDudf+8nDUyMlJRUVEaP368zGazEhISDEwGGCMjI0M1NTXcvxO4hX9bK8uWLbO/j4yM1NixY5WQkCCLxaLx48c7OuZdw+V7g4CXl5eGDx9+01Mrmpub5evra1AqYODz9PRUaGio6uvrjY4CDEjXawj1BfjfhYSEyMvLixqDIWnlypU6cOCAfvrpJz300EP2cV9fX9lsNrW0tPSaT13BUPVva6UvMTExkjTo6gpNqUFg5MiRmjx5ssrKyuxjXV1dKisrU2xsrIHJgIGtra1NFotFY8eONToKMCAFBwfL19e3V325dOmSfvnlF+oLcAvnzp2T1WqlxmBI6e7u1sqVK1VSUqKDBw8qODi41+eTJ0/WiBEjetWVEydO6MyZM9QVDCm3Wit9uf6wpsFWV7h8b5BYs2aNFi1apClTpig6Olrbt29Xe3u7Fi9ebHQ0YMBYu3atZs+ercDAQDU2NionJ0fDhw/X/PnzjY4GGKatra3Xf9waGhp07NgxmUwmBQQEKDMzU5s3b9bDDz+s4OBgbdy4UX5+fr2eOgYMBf+1Vkwmk3Jzc5WamipfX19ZLBatW7dOEyZMUGJiooGpAcfKyMhQcXGxvv76a7m5udnvE+Xh4aFRo0bJw8NDS5Ys0Zo1a2QymeTu7q7XXntNsbGxPHkPQ8qt1orFYlFxcbGeffZZjR49WtXV1Xr99dcVFxenqKgog9P3L6fu7u5uo0Ogf3zwwQfatm2bmpqaNHHiROXn59u3+AHoud/HoUOHZLVaNWbMGE2fPl1vvfXWoLomG/hfmc1mzZgx46bxRYsWqaCgQN3d3crJydEnn3yilpYWTZ8+XTt37lRoaKgBaQHj/Nda+fDDD5WSkqKjR4+qpaVFfn5+euaZZ7Rp06abHhQADGZOTk59ju/atUvp6emSpCtXrigrK0ufffaZrl69qsTERO3cuZPL9zCk3GqtnD17Vi+++KJqamrU3t6ucePGac6cOXrzzTfl7u7u4LR3F00pAAAAAAAAOBz3lAIAAAAAAIDD0ZQCAAAAAACAw9GUAgAAAAAAgMPRlAIAAAAAAIDD0ZQCAAAAAACAw9GUAgAAAAAAgMPRlAIAAAAAAIDD0ZQCAAAAAACAw9GUAgAAGKScnJy0f/9+o2MAAAD0iaYUAADAHbhw4YJWrFihgIAA3X///fL19VViYqIqKiqMjgYAADCg3Wd0AAAAgHtZamqqbDabCgsLFRISoubmZpWVlclqtRodDQAAYEBjpxQAAMBtamlpUXl5ubZs2aIZM2YoMDBQ0dHRys7O1vPPPy9JysvLU2RkpFxcXDRu3Di9+uqramtrsx+joKBAnp6eOnDggMLCwuTs7Kx58+apo6NDhYWFCgoK0oMPPqhVq1bp2rVr9u8FBQVp06ZNmj9/vlxcXOTv768dO3b8Z96zZ8/qhRdekKenp0wmk5KTk3Xq1Cn752azWdHR0XJxcZGnp6emTZum06dP9++PBgAA8P9oSgEAANwmV1dXubq6av/+/bp69Wqfc4YNG6b8/Hz9/vvvKiws1MGDB7Vu3bpeczo6OpSfn6/PP/9c3333ncxms+bMmaPS0lKVlpaqqKhIH3/8sfbu3dvre9u2bdNjjz2mo0eP6o033tDq1av1ww8/9Jmjs7NTiYmJcnNzU3l5uSoqKuTq6qqkpCTZbDb9/fffSklJUXx8vKqrq1VZWally5bJycmpf34sAACAGzh1d3d3Gx0CAADgXrVv3z4tXbpUly9f1qRJkxQfH6+0tDRFRUX1OX/v3r1avny5Ll68KKlnp9TixYtVX1+v8ePHS5KWL1+uoqIiNTc3y9XVVZKUlJSkoKAgffTRR5J6dkqFh4fr22+/tR87LS1Nly5dUmlpqaSeG52XlJQoJSVFu3fv1ubNm3X8+HF7o8lms8nT01P79+/XlClTNHr0aJnNZsXHx9+dHwsAAOAf2CkFAABwB1JTU9XY2KhvvvlGSUlJMpvNmjRpkgoKCiRJP/74oxISEuTv7y83Nze99NJLslqt6ujosB/D2dnZ3pCSJB8fHwUFBdkbUtfH/vzzz17njo2Nvenv48eP95nzt99+U319vdzc3Ow7vEwmk65cuSKLxSKTyaT09HQlJiZq9uzZeu+993T+/Pk7/XkAAAD+FU0pAACAO/TAAw/o6aef1saNG3X48GGlp6crJydHp06d0nPPPaeoqCjt27dPVVVV9vs+2Ww2+/dHjBjR63hOTk59jnV1dd12xra2Nk2ePFnHjh3r9Tp58qQWLFggSdq1a5cqKyv1xBNP6IsvvlBoaKiOHDly2+cEAAD4LzSlAAAA+tmjjz6q9vZ2VVVVqaurS++8846mTp2q0NBQNTY29tt5bmwYHTlyROHh4X3OnTRpkurq6uTt7a0JEyb0enl4eNjnPf7448rOztbhw4cVERGh4uLifssLAADwTzSlAAAAbpPVatVTTz2l3bt3q7q6Wg0NDdqzZ4+2bt2q5ORkTZgwQZ2dnXr//ff1xx9/qKioyH5PqP5QUVGhrVu36uTJk9qxY4f27Nmj1atX9zl34cKF8vLyUnJyssrLy9XQ0CCz2axVq1bp3LlzamhoUHZ2tiorK3X69Gl9//33qqur+9cmFwAAwJ26z+gAAAAA9ypXV1fFxMTo3XfflcViUWdnp8aNG6elS5dqw4YNGjVqlPLy8rRlyxZlZ2crLi5Ob7/9tl5++eV+OX9WVpZ+/fVX5ebmyt3dXXl5eUpMTOxzrrOzsw4dOqT169dr7ty5+uuvv+Tv76+EhAS5u7vr8uXLqq2tVWFhoaxWq8aOHauMjAy98sor/ZIVAADgRjx9DwAA4B4UFBSkzMxMZWZmGh0FAADgtnD5HgAAAAAAAByOphQAAAAAAAAcjsv3AAAAAAAA4HDslAIAAAAAAIDD0ZQCAAAAAACAw9GUAgAAAAAAgMPRlAIAAAAAAIDD0ZQCAAAAAACAw9GUAgAAAAAAgMPRlAIAAAAAAIDD0ZQCAAAAAACAw/0flAHe6rwLriIAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1200x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader, TensorDataset\n", "import os\n", "# 禁用 CUDA，避免 GPU 相关库加载错误\n", "os.environ['CUDA_VISIBLE_DEVICES'] = ''\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "import matplotlib.pyplot as plt\n", "\n", "# 设备与随机种子\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "torch.manual_seed(42)\n", "np.random.seed(42)\n", "\n", "# 数据加载与预处理\n", "# 读取 Excel，并将 datetime 列解析为日期，设置为索引\n", "df = pd.read_excel(\"D:\\\\埃塞俄比亚咖啡\\\\2005(2013)-2017(2025) monthly data(1).xlsx\", parse_dates=['datetime'])\n", "df.set_index('datetime', inplace=True)\n", "\n", "# 仅使用 'IN MILLION USD' 列进行单变量预测\n", "rec_df = df[['IN MILLION USD']].copy()\n", "rec_df.fillna(method='ffill', inplace=True)\n", "assert not rec_df.isnull().any().any(), \"数据中仍存在缺失值\"\n", "\n", "# 转为 1D numpy 浮点数组 (单变量)\n", "values = rec_df.values.flatten().astype(np.float32)\n", "\n", "# 定义 Dataset 类\n", "class TimeSeriesDataset(Dataset):\n", "    def __init__(self, series, n_in, n_out=1, step=1):\n", "        self.X, self.y = [], []\n", "        for i in range(0, len(series) - n_in - n_out + 1, step):\n", "            self.X.append(series[i:i + n_in])\n", "            self.y.append(series[i + n_in:i + n_in + n_out])\n", "        # X shape: (samples, n_in), y shape: (samples, n_out)\n", "        self.X = torch.FloatTensor(np.array(self.X)).unsqueeze(-1)  # (samples, n_in, 1)\n", "        self.y = torch.FloatTensor(np.array(self.y))  # (samples, n_out)\n", "    def __len__(self):\n", "        return len(self.X)\n", "    def __getitem__(self, idx):\n", "        return self.X[idx], self.y[idx]\n", "\n", "# 参数设置\n", "n_in = 6         # 输入时间步长\n", "n_out = 1        # 输出步长\n", "step = 1\n", "batch_size = 64\n", "train_ratio = 0.8\n", "\n", "total_len = len(values)\n", "split = int(total_len * train_ratio)\n", "train_series = values[:split]\n", "test_series  = values[split:]\n", "\n", "# 创建 Dataset\n", "train_ds = TimeSeriesDataset(train_series, n_in, n_out, step)\n", "test_ds  = TimeSeriesDataset(test_series,  n_in, n_out, step)\n", "\n", "# 归一化，仅使用训练集拟合\n", "scaler = MinMaxScaler()\n", "scaler.fit(train_series.reshape(-1, 1))  # 训练集一维数据需 reshape\n", "\n", "def scale_dataset(ds):\n", "    X = ds.<PERSON><PERSON>nump<PERSON>().reshape(-1, 1)\n", "    y = ds.y.numpy().reshape(-1, 1)\n", "    X_scaled = scaler.transform(X).reshape(ds.X.shape)\n", "    y_scaled = scaler.transform(y).reshape(ds.y.shape)\n", "    return TensorDataset(torch.FloatTensor(X_scaled), torch.FloatTensor(y_scaled))\n", "\n", "train_scaled = scale_dataset(train_ds)\n", "test_scaled  = scale_dataset(test_ds)\n", "\n", "# DataLoader\n", "data_train = DataLoader(train_scaled, batch_size=batch_size, shuffle=False)\n", "data_test  = DataLoader(test_scaled,  batch_size=batch_size, shuffle=False)\n", "\n", "# 定义 LSTM 模型\n", "class LSTMUnivariate(nn.Module):\n", "    def __init__(self, n_hidden=32, n_layers=2):\n", "        super().__init__()\n", "        self.lstm = nn.LSTM(input_size=1, hidden_size=n_hidden, num_layers=n_layers, batch_first=True)\n", "        self.fc = nn.Linear(n_hidden * n_in, n_out)\n", "    def forward(self, x):\n", "        out, _ = self.lstm(x)\n", "        out = out.reshape(out.size(0), -1)\n", "        return self.fc(out)\n", "\n", "model = LSTMUnivariate().to(device)\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "\n", "# 训练\n", "train_losses = []\n", "for epoch in range(50):\n", "    model.train()\n", "    total_loss = 0\n", "    for X_batch, y_batch in data_train:\n", "        X_batch, y_batch = X_batch.to(device), y_batch.to(device)\n", "        optimizer.zero_grad()\n", "        preds = model(X_batch)\n", "        loss = criterion(preds, y_batch)\n", "        loss.backward()\n", "        optimizer.step()\n", "        total_loss += loss.item() * X_batch.size(0)\n", "    epoch_loss = total_loss / len(data_train.dataset)\n", "    train_losses.append(epoch_loss)\n", "    print(f\"Epoch {epoch+1:03d} | Train Loss: {epoch_loss:.4f}\")\n", "\n", "# 绘制训练损失曲线\n", "plt.figure(figsize=(10, 4))\n", "plt.plot(train_losses, label='Train Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 测试与评估\n", "model.eval()\n", "y_true, y_pred = [], []\n", "with torch.no_grad():\n", "    for X_batch, y_batch in data_test:\n", "        X_batch = X_batch.to(device)\n", "        preds = model(X_batch)\n", "        y_true.extend(y_batch.cpu().numpy().flatten())\n", "        y_pred.extend(preds.cpu().numpy().flatten())\n", "\n", "# 反归一化\n", "y_true = scaler.inverse_transform(np.array(y_true).reshape(-1,1)).flatten()\n", "y_pred = scaler.inverse_transform(np.array(y_pred).reshape(-1,1)).flatten()\n", "\n", "mse  = mean_squared_error(y_true, y_pred)\n", "rmse = np.sqrt(mse)\n", "mae  = mean_absolute_error(y_true, y_pred)\n", "r2   = r2_score(y_true, y_pred)\n", "print(f\"MSE: {mse:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}, R2: {r2:.4f}\")\n", "\n", "# 可视化真实值 vs 预测值 (最后 100 点)\n", "plt.figure(figsize=(12,4))\n", "plt.plot(y_true[-100:], label='True')\n", "plt.plot(y_pred[-100:], label='Pred')\n", "plt.xlabel('Samples')\n", "plt.ylabel('IN MILLION USD')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "8ba32303-583f-4c54-9b7a-58ae0a513ad6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}