"""
X11分解最终融合预测模型 - 仅Trend添加生产量变量版本
Trend: 线性回归8特征模型 (添加prod_lag1)
Seasonal: 完全周期性模式
Residual: 梯度提升13特征模型 (不添加prod_lag1，保持原版本)
最终预测 = Trend预测 + Seasonal预测 + Residual预测

变更：仅在Trend模型中添加prod_lag1，Residual模型保持原版本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import warnings
warnings.filterwarnings('ignore')

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("=== X11分解最终融合预测模型 - 仅Trend添加生产量变量版本 ===")
print("Trend: 线性回归8特征模型 (添加prod_lag1)")
print("Seasonal: 完全周期性模式")
print("Residual: 梯度提升13特征模型 (保持原版本，不添加prod_lag1)")
print("变更: 仅在Trend模型中添加prod_lag1")

# 1. 数据加载
print("\n1. 数据加载")
print("-" * 50)

# 加载X11分解结果
decomp_path = r"D:\埃塞俄比亚咖啡\模型\x11_decomposition_results.csv"
decomp_data = pd.read_csv(decomp_path)

# 加载外生变量（包含prod_lag1）
exog_path = r"D:\埃塞俄比亚咖啡\2005(2013)-2017(2025) monthly data(1).csv"
exog_data = pd.read_csv(exog_path, encoding='gbk')

print(f"X11分解数据: {decomp_data.shape}")
print(f"外生变量数据: {exog_data.shape}")

# 检查prod_lag1列是否存在
if 'prod_lag1' in exog_data.columns:
    print("✓ 找到prod_lag1列")
    print(f"prod_lag1样本值: {exog_data['prod_lag1'].dropna().head().tolist()}")
else:
    print("❌ 未找到prod_lag1列，将使用production_annual作为替代")

# 2. 数据合并和预处理
print("\n2. 数据合并和预处理")
print("-" * 50)

# 合并数据
min_len = min(len(decomp_data), len(exog_data))

# 处理prod_lag1变量
if 'prod_lag1' in exog_data.columns:
    prod_lag1_col = exog_data['prod_lag1'].iloc[:min_len]
    # 转换字符串格式的数值（如 '376,823' -> 376823）
    if prod_lag1_col.dtype == 'object':
        prod_lag1_col = prod_lag1_col.astype(str).str.replace(',', '').str.replace('"', '')
        # 过滤掉非数字字符串，转换为数值
        prod_lag1_col = pd.to_numeric(prod_lag1_col, errors='coerce')
        print("✓ prod_lag1已转换为数值格式")
else:
    # 如果没有prod_lag1，使用production_annual作为替代并进行滞后处理
    if 'production_annual' in exog_data.columns:
        prod_lag1_col = exog_data['production_annual'].iloc[:min_len]
        print("⚠️ 使用production_annual替代prod_lag1")
    else:
        # 如果都没有，创建一个默认值
        prod_lag1_col = pd.Series([500000] * min_len)  # 使用平均产量作为默认值
        print("⚠️ 创建默认prod_lag1值")

data = pd.DataFrame({
    'date': pd.to_datetime(decomp_data['parsed_datetime'].iloc[:min_len]),
    'original': decomp_data['Original'].iloc[:min_len],
    'trend': decomp_data['Trend'].iloc[:min_len],
    'seasonal': decomp_data['Seasonal'].iloc[:min_len],
    'residual': decomp_data['Residual'].iloc[:min_len],
    'sales_price': exog_data['sales price（usd/lb）'].iloc[:min_len],
    'exchange_rate': exog_data['Exchange(Domestic currency per US Dollar)'].iloc[:min_len],
    'prod_lag1': prod_lag1_col  # 生产量变量
})

data.set_index('date', inplace=True)
data = data.dropna()

print(f"合并后数据量: {len(data)} 样本")
print(f"时间范围: {data.index[0]} - {data.index[-1]}")
print(f"prod_lag1统计: 最小值={data['prod_lag1'].min():.0f}, 最大值={data['prod_lag1'].max():.0f}")

# 3. 数据分割 - 使用与原版本相同的分割点
print("\n3. 数据分割")
print("-" * 50)

# 使用固定的分割点：2022-12-01，与原版本保持一致
split_date = '2022-12-01'
train_data = data[data.index < split_date]
test_data = data[data.index >= split_date]

train_dates = train_data.index
test_dates = test_data.index

print(f"训练集: {len(train_data)} 样本 ({train_dates[0]} - {train_dates[-1]})")
print(f"测试集: {len(test_data)} 样本 ({test_dates[0]} - {test_dates[-1]})")

# 4. Trend预测 - 线性回归8特征模型（添加prod_lag1）
print("\n4. Trend预测 - 线性回归8特征模型（添加prod_lag1）")
print("-" * 50)

def create_trend_features(data):
    """创建Trend预测的8个特征（添加prod_lag1）"""
    features_data = data.copy()
    
    # 时间特征
    features_data['year'] = features_data.index.year
    features_data['month'] = features_data.index.month
    features_data['time_index'] = range(len(features_data))
    
    # Trend历史特征
    features_data['trend_lag_1'] = features_data['trend'].shift(1)
    features_data['trend_lag_12'] = features_data['trend'].shift(12)
    
    # 外生变量
    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)
    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)
    
    # 新增：生产量变量（已经是滞后的，直接使用）
    features_data['prod_lag1'] = features_data['prod_lag1']
    
    return features_data.dropna()

# 创建Trend特征（8个特征）
trend_features_list = [
    'time_index', 'trend_lag_1', 'trend_lag_12', 'sales_price_lag_1', 
    'exchange_rate_lag_1', 'year', 'month', 'prod_lag1'  # 新增prod_lag1
]

trend_train_data = create_trend_features(train_data)
trend_test_data = create_trend_features(test_data)

# 确保测试集有足够的历史数据
if len(trend_test_data) == 0:
    full_trend_data = create_trend_features(data)
    trend_split_idx = int(len(full_trend_data) * 0.8)
    trend_train_data = full_trend_data.iloc[:trend_split_idx]
    trend_test_data = full_trend_data.iloc[trend_split_idx:]

X_trend_train = trend_train_data[trend_features_list]
y_trend_train = trend_train_data['trend']
X_trend_test = trend_test_data[trend_features_list]
y_trend_test = trend_test_data['trend']

# 标准化
trend_scaler = StandardScaler()
X_trend_train_scaled = trend_scaler.fit_transform(X_trend_train)
X_trend_test_scaled = trend_scaler.transform(X_trend_test)

# 训练Trend模型
trend_model = LinearRegression()
trend_model.fit(X_trend_train_scaled, y_trend_train)

# Trend预测
trend_train_pred = trend_model.predict(X_trend_train_scaled)
trend_test_pred = trend_model.predict(X_trend_test_scaled)

print(f"✓ Trend模型训练完成（8特征版本，包含prod_lag1）")
print(f"  训练集RMSE: {np.sqrt(mean_squared_error(y_trend_train, trend_train_pred)):.2f}")
print(f"  测试集RMSE: {np.sqrt(mean_squared_error(y_trend_test, trend_test_pred)):.2f}")
print(f"  测试集R²: {r2_score(y_trend_test, trend_test_pred):.4f}")

# 分析prod_lag1的重要性
trend_feature_importance = pd.DataFrame({
    'feature': trend_features_list,
    'coefficient': trend_model.coef_,
    'abs_coefficient': np.abs(trend_model.coef_)
}).sort_values('abs_coefficient', ascending=False)

prod_importance_rank = trend_feature_importance[trend_feature_importance['feature'] == 'prod_lag1'].index[0] + 1
prod_coefficient = trend_feature_importance[trend_feature_importance['feature'] == 'prod_lag1']['coefficient'].iloc[0]
print(f"  prod_lag1在Trend模型中的重要性排名: {prod_importance_rank}/8")
print(f"  prod_lag1系数: {prod_coefficient:.6f}")

# 5. Seasonal预测 - 完全周期性模式（保持不变）
print("\n5. Seasonal预测 - 完全周期性模式（保持不变）")
print("-" * 50)

# 计算每个月的平均季节性成分
seasonal_pattern = train_data.groupby(train_data.index.month)['seasonal'].mean()

print(f"✓ 季节性模式计算完成（与原版本相同）")
print(f"  12个月的季节性模式:")
for month, value in seasonal_pattern.items():
    print(f"    {month:2d}月: {value:>8.4f}")

# 生成Seasonal预测
def predict_seasonal(dates, pattern):
    """根据日期生成季节性预测"""
    months = [date.month for date in dates]
    return np.array([pattern[month] for month in months])

seasonal_train_pred = predict_seasonal(trend_train_data.index, seasonal_pattern)
seasonal_test_pred = predict_seasonal(trend_test_data.index, seasonal_pattern)

# 对应的真实值
seasonal_train_actual = trend_train_data['seasonal'].values
seasonal_test_actual = trend_test_data['seasonal'].values

print(f"  训练集RMSE: {np.sqrt(mean_squared_error(seasonal_train_actual, seasonal_train_pred)):.4f}")
print(f"  测试集RMSE: {np.sqrt(mean_squared_error(seasonal_test_actual, seasonal_test_pred)):.4f}")
print(f"  测试集R²: {r2_score(seasonal_test_actual, seasonal_test_pred):.4f}")

# 6. Residual预测 - 梯度提升13特征模型（保持原版本，不添加prod_lag1）
print("\n6. Residual预测 - 梯度提升13特征模型（保持原版本，不添加prod_lag1）")
print("-" * 50)

def create_residual_features(data):
    """创建Residual预测的13个特征（不添加prod_lag1）"""
    features_data = data.copy()

    # 时间特征
    features_data['month'] = features_data.index.month

    # 残差历史特征 - 包含更多滞后项
    for lag in [1, 3, 6]:
        features_data[f'residual_lag_{lag}'] = features_data['residual'].shift(lag)

    # 残差波动性
    features_data['residual_volatility'] = features_data['residual'].rolling(window=6).std()

    # X11分解相关特征
    features_data['trend_change'] = features_data['trend'].pct_change()
    features_data['seasonal_abs'] = np.abs(features_data['seasonal'])

    # 外生变量特征 - 完整配置
    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)
    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)
    features_data['sales_price_change'] = features_data['sales_price'].pct_change()
    features_data['exchange_rate_change'] = features_data['exchange_rate'].pct_change()
    features_data['exchange_sales_interaction'] = features_data['exchange_rate'] * features_data['sales_price']

    # 当前外生变量 - 重要！
    features_data['exchange_rate'] = features_data['exchange_rate']
    features_data['sales_price'] = features_data['sales_price']
    
    # 注意：这里不添加prod_lag1，保持原版本的13个特征

    return features_data.dropna()

# Residual特征列表 - 13个特征（不添加prod_lag1）
residual_features_list = [
    # 残差历史信息
    'residual_lag_1', 'residual_lag_6', 'residual_volatility',

    # 当前外生变量 (重要)
    'exchange_rate', 'sales_price',

    # 外生变量历史信息
    'exchange_rate_lag_1', 'sales_price_lag_1',

    # 外生变量变化
    'exchange_rate_change', 'sales_price_change',

    # 交互特征
    'exchange_sales_interaction',

    # X11分解相关
    'seasonal_abs', 'trend_change',

    # 时间特征
    'month'
    
    # 注意：不添加prod_lag1
]

# 创建Residual特征
residual_train_data = create_residual_features(train_data)
residual_test_data = create_residual_features(test_data)

# 确保测试集有足够的历史数据
if len(residual_test_data) == 0:
    full_residual_data = create_residual_features(data)
    residual_split_idx = int(len(full_residual_data) * 0.8)
    residual_train_data = full_residual_data.iloc[:residual_split_idx]
    residual_test_data = full_residual_data.iloc[residual_split_idx:]

# 确保特征存在
available_features = [f for f in residual_features_list if f in residual_train_data.columns]
print(f"可用Residual特征: {len(available_features)}/{len(residual_features_list)}")
print(f"保持原版本13特征，不添加prod_lag1")

X_residual_train = residual_train_data[available_features]
y_residual_train = residual_train_data['residual']
X_residual_test = residual_test_data[available_features]
y_residual_test = residual_test_data['residual']

# 训练Residual模型 - 使用与原版本相同的梯度提升配置
residual_model = GradientBoostingRegressor(
    n_estimators=50,
    max_depth=3,
    learning_rate=0.05,
    subsample=0.8,
    min_samples_split=10,
    min_samples_leaf=5,
    max_features='sqrt',
    random_state=42
)

residual_model.fit(X_residual_train, y_residual_train)

# Residual预测
residual_train_pred = residual_model.predict(X_residual_train)
residual_test_pred = residual_model.predict(X_residual_test)

print(f"✓ Residual模型训练完成（13特征版本，不含prod_lag1）")
print(f"  训练集RMSE: {np.sqrt(mean_squared_error(y_residual_train, residual_train_pred)):.2f}")
print(f"  测试集RMSE: {np.sqrt(mean_squared_error(y_residual_test, residual_test_pred)):.2f}")
print(f"  测试集R²: {r2_score(y_residual_test, residual_test_pred):.4f}")

# 显示Residual模型特征重要性（不含prod_lag1）
residual_feature_importance = pd.DataFrame({
    'feature': available_features,
    'importance': residual_model.feature_importances_
}).sort_values('importance', ascending=False)

print(f"  Residual模型前5个重要特征:")
for i, (_, row) in enumerate(residual_feature_importance.head(5).iterrows()):
    print(f"    {i+1}. {row['feature']:<25} {row['importance']:.6f}")

# 7. 最终融合预测 - 修正版：确保测试集完整覆盖
print("\n7. 最终融合预测")
print("-" * 50)

# 重新处理，确保测试集从原始分割点开始，没有空缺
split_date = pd.to_datetime('2022-12-01')

# 使用原始的训练和测试分割
original_train_data = data[data.index < split_date]
original_test_data = data[data.index >= split_date]

print(f"原始训练集: {len(original_train_data)} 样本 ({original_train_data.index[0]} - {original_train_data.index[-1]})")
print(f"原始测试集: {len(original_test_data)} 样本 ({original_test_data.index[0]} - {original_test_data.index[-1]})")

# 对于测试集的每个时间点，逐步预测
def predict_step_by_step(test_data, train_data, models, scalers, seasonal_pattern):
    """逐步预测测试集，使用滚动窗口方式"""

    trend_model, residual_model = models
    trend_scaler = scalers

    # 初始化预测结果
    trend_predictions = []
    seasonal_predictions = []
    residual_predictions = []
    final_predictions = []

    # 合并训练和测试数据用于特征构建
    full_data = pd.concat([train_data, test_data])

    for i, test_date in enumerate(test_data.index):
        print(f"  预测 {test_date.strftime('%Y-%m')}...")

        # 使用到当前时间点为止的所有数据构建特征
        current_data = full_data[full_data.index <= test_date]

        # Trend预测
        trend_features_data = create_trend_features(current_data)
        if len(trend_features_data) > 0 and test_date in trend_features_data.index:
            trend_X = trend_features_data.loc[[test_date], trend_features_list]
            trend_X_scaled = trend_scaler.transform(trend_X)
            trend_pred = trend_model.predict(trend_X_scaled)[0]
        else:
            # 如果没有足够的历史数据，使用最近的趋势值
            trend_pred = current_data['trend'].iloc[-1]

        # Seasonal预测（完全确定性）
        seasonal_pred = seasonal_pattern[test_date.month]

        # Residual预测
        residual_features_data = create_residual_features(current_data)
        if len(residual_features_data) > 0 and test_date in residual_features_data.index:
            residual_X = residual_features_data.loc[[test_date], available_features]
            residual_pred = residual_model.predict(residual_X)[0]
        else:
            # 如果没有足够的历史数据，预测为0
            residual_pred = 0.0

        # 最终预测
        final_pred = trend_pred + seasonal_pred + residual_pred

        # 保存预测结果
        trend_predictions.append(trend_pred)
        seasonal_predictions.append(seasonal_pred)
        residual_predictions.append(residual_pred)
        final_predictions.append(final_pred)

    return (np.array(trend_predictions), np.array(seasonal_predictions),
            np.array(residual_predictions), np.array(final_predictions))

print("开始逐步预测测试集...")

# 执行逐步预测
models = (trend_model, residual_model)
scalers = trend_scaler

(trend_test_pred_full, seasonal_test_pred_full,
 residual_test_pred_full, final_test_pred_full) = predict_step_by_step(
    original_test_data, original_train_data, models, scalers, seasonal_pattern)

# 训练集预测（使用已有的结果，但需要对齐到原始训练集）
# 找到训练集中有预测结果的部分
train_available_mask = original_train_data.index.isin(trend_train_data.index)
final_train_dates = original_train_data.index[train_available_mask]
final_train_actual = original_train_data['original'][train_available_mask].values

# 对应的训练集预测
train_trend_aligned = []
train_seasonal_aligned = []
train_residual_aligned = []

for date in final_train_dates:
    if date in trend_train_data.index:
        idx = trend_train_data.index.get_loc(date)
        train_trend_aligned.append(trend_train_pred[idx])
        train_seasonal_aligned.append(predict_seasonal([date], seasonal_pattern)[0])

        if date in residual_train_data.index:
            residual_idx = residual_train_data.index.get_loc(date)
            train_residual_aligned.append(residual_train_pred[residual_idx])
        else:
            train_residual_aligned.append(0.0)

final_train_pred = np.array(train_trend_aligned) + np.array(train_seasonal_aligned) + np.array(train_residual_aligned)

# 测试集结果
final_test_dates = original_test_data.index
final_test_actual = original_test_data['original'].values
final_test_pred = final_test_pred_full

print(f"✓ 逐步预测完成!")
print(f"最终训练数据: {len(final_train_pred)} 样本")
print(f"最终测试数据: {len(final_test_pred)} 样本 (完整覆盖，无空缺)")

# 计算最终指标
def calculate_metrics(y_true, y_pred):
    """计算评估指标"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100

    # 方向准确性
    if len(y_true) > 1:
        direction_accuracy = np.mean(np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred))) * 100
    else:
        direction_accuracy = 0

    return {
        'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2,
        'MAPE': mape, 'Direction_Accuracy': direction_accuracy
    }

final_train_metrics = calculate_metrics(final_train_actual, final_train_pred)
final_test_metrics = calculate_metrics(final_test_actual, final_test_pred)
final_overfitting_ratio = final_test_metrics['RMSE'] / final_train_metrics['RMSE']

print(f"🎉 X11融合预测完成（仅Trend添加prod_lag1版本）!")
print(f"\n训练集性能:")
print(f"  RMSE: {final_train_metrics['RMSE']:.2f} 百万美元")
print(f"  MAE: {final_train_metrics['MAE']:.2f} 百万美元")
print(f"  R²: {final_train_metrics['R2']:.4f}")
print(f"  MAPE: {final_train_metrics['MAPE']:.2f}%")

print(f"\n测试集性能:")
print(f"  RMSE: {final_test_metrics['RMSE']:.2f} 百万美元")
print(f"  MAE: {final_test_metrics['MAE']:.2f} 百万美元")
print(f"  R²: {final_test_metrics['R2']:.4f}")
print(f"  MAPE: {final_test_metrics['MAPE']:.2f}%")
print(f"  方向准确性: {final_test_metrics['Direction_Accuracy']:.1f}%")

print(f"\n过拟合分析:")
print(f"  过拟合比率: {final_overfitting_ratio:.4f}")
if final_overfitting_ratio < 1.5:
    print("  ✅ 过拟合控制优秀")
elif final_overfitting_ratio < 2.0:
    print("  ✅ 过拟合控制良好")
else:
    print("  ⚠️ 存在过拟合")

# 8. prod_lag1在Trend模型中的影响分析
print("\n8. prod_lag1在Trend模型中的影响分析")
print("-" * 50)

print(f"📊 prod_lag1变量在Trend模型中的影响评估:")

print(f"\n1. 重要性分析:")
print(f"   重要性排名: {prod_importance_rank}/8")
print(f"   回归系数: {prod_coefficient:.6f}")
if abs(prod_coefficient) > 0.01:
    print(f"   影响程度: 显著")
elif abs(prod_coefficient) > 0.001:
    print(f"   影响程度: 中等")
else:
    print(f"   影响程度: 较小")

print(f"\n2. prod_lag1数据统计:")
print(f"   最小值: {data['prod_lag1'].min():.0f}")
print(f"   最大值: {data['prod_lag1'].max():.0f}")
print(f"   平均值: {data['prod_lag1'].mean():.0f}")
print(f"   标准差: {data['prod_lag1'].std():.0f}")
print(f"   变异系数: {data['prod_lag1'].std() / data['prod_lag1'].mean():.3f}")

print(f"\n3. Trend模型所有特征重要性:")
for i, (_, row) in enumerate(trend_feature_importance.iterrows()):
    marker = "🔥" if row['feature'] == 'prod_lag1' else "  "
    print(f"   {marker} {i+1}. {row['feature']:<20} 系数: {row['coefficient']:>10.6f}")

# 9. 模型版本对比分析
print("\n9. 模型版本对比分析")
print("-" * 50)

print("📈 模型版本对比:")
print("=" * 40)

print("原版本 (不含prod_lag1):")
print("  Trend特征数: 7")
print("  Residual特征数: 13") 
print("  总特征数: 20")

print(f"\n当前版本 (仅Trend含prod_lag1):")
print(f"  Trend特征数: 8 (+1)")
print(f"  Residual特征数: 13 (不变)")
print(f"  总特征数: 21 (+1)")
print(f"  测试集R²: {final_test_metrics['R2']:.4f}")
print(f"  测试集RMSE: {final_test_metrics['RMSE']:.2f}")
print(f"  测试集MAPE: {final_test_metrics['MAPE']:.2f}%")

print(f"\n💡 此版本特点:")
print("  • 仅在Trend预测中引入生产量信息")
print("  • Residual模型保持原始配置，专注于异常波动")
print("  • 可以清楚看出prod_lag1对趋势预测的独立贡献")
print("  • 模型复杂度增加最小（仅+1特征）")

# 10. 可视化对比分析
print("\n10. 可视化对比分析")
print("-" * 50)

fig, axes = plt.subplots(3, 2, figsize=(18, 16))
fig.suptitle('X11分解融合预测分析 - 仅Trend添加prod_lag1版本', fontsize=16, fontweight='bold')

# 1. 最终预测效果
ax1 = axes[0, 0]
ax1.plot(final_train_dates, final_train_actual, 'black', label='训练集真实值', linewidth=1, alpha=0.8)
ax1.plot(final_test_dates, final_test_actual, 'black', label='测试集真实值', linewidth=2)
ax1.plot(final_train_dates, final_train_pred, 'blue', label='训练集预测', linewidth=1, alpha=0.7)
ax1.plot(final_test_dates, final_test_pred, 'red', label='测试集预测', linewidth=2)

ax1.axvline(x=final_train_dates[-1], color='gray', linestyle=':', alpha=0.7, label='训练/测试分割')
ax1.set_title('最终预测效果 (仅Trend含prod_lag1)')
ax1.set_ylabel('出口额 (百万美元)')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 添加性能指标
textstr = f'测试集RMSE: {final_test_metrics["RMSE"]:.1f}\n测试集R²: {final_test_metrics["R2"]:.3f}\n测试集MAPE: {final_test_metrics["MAPE"]:.1f}%'
props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
ax1.text(0.02, 0.98, textstr, transform=ax1.transAxes, fontsize=10,
         verticalalignment='top', bbox=props)

# 2. Trend特征重要性（高亮prod_lag1）
ax2 = axes[0, 1]
trend_importance_plot = trend_feature_importance.head(8)
bars = ax2.barh(range(len(trend_importance_plot)), trend_importance_plot['abs_coefficient'], alpha=0.7)
ax2.set_yticks(range(len(trend_importance_plot)))
ax2.set_yticklabels(trend_importance_plot['feature'])
ax2.set_xlabel('回归系数绝对值')
ax2.set_title('Trend模型特征重要性 (含prod_lag1)')
ax2.grid(True, alpha=0.3)

# 高亮prod_lag1
for i, (_, row) in enumerate(trend_importance_plot.iterrows()):
    if row['feature'] == 'prod_lag1':
        bars[i].set_color('orange')
        bars[i].set_alpha(0.9)

# 3. Residual特征重要性（不含prod_lag1）
ax3 = axes[1, 0]
residual_importance_plot = residual_feature_importance.head(10)
bars = ax3.barh(range(len(residual_importance_plot)), residual_importance_plot['importance'], alpha=0.7)
ax3.set_yticks(range(len(residual_importance_plot)))
ax3.set_yticklabels(residual_importance_plot['feature'])
ax3.set_xlabel('特征重要性')
ax3.set_title('Residual模型特征重要性 (不含prod_lag1)')
ax3.grid(True, alpha=0.3)

# 4. prod_lag1与出口额和趋势的关系
ax4 = axes[1, 1]
ax4_twin = ax4.twinx()

# 绘制prod_lag1、出口额和趋势的关系
line1 = ax4.plot(data.index, data['original'], 'red', label='出口额', linewidth=1, alpha=0.7)
line2 = ax4.plot(data.index, data['trend'], 'blue', label='趋势成分', linewidth=1, alpha=0.7)
line3 = ax4_twin.plot(data.index, data['prod_lag1']/1000, 'green', label='生产量(千吨)', linewidth=1, alpha=0.7)

ax4.axvline(x=final_train_dates[-1], color='gray', linestyle=':', alpha=0.7)
ax4.set_ylabel('出口额/趋势 (百万美元)', color='black')
ax4_twin.set_ylabel('生产量 (千吨)', color='green')
ax4.set_title('prod_lag1与出口趋势关系')

# 合并图例
lines = line1 + line2 + line3
labels = [l.get_label() for l in lines]
ax4.legend(lines, labels, loc='upper left')
ax4.grid(True, alpha=0.3)

# 5. 预测准确性散点图
ax5 = axes[2, 0]
ax5.scatter(final_test_actual, final_test_pred, alpha=0.7, s=60, color='red')
ax5.plot([final_test_actual.min(), final_test_actual.max()],
         [final_test_actual.min(), final_test_actual.max()], 'k--', lw=2)

ax5.set_xlabel('真实出口额 (百万美元)')
ax5.set_ylabel('预测出口额 (百万美元)')
ax5.set_title(f'预测准确性 (仅Trend含prod_lag1)\nR²={final_test_metrics["R2"]:.3f}')
ax5.grid(True, alpha=0.3)

# 6. 各组件预测对比
ax6 = axes[2, 1]
ax6.plot(final_test_dates, trend_test_pred_full, 'blue', label='Trend预测(含prod_lag1)', linewidth=2)
ax6.plot(final_test_dates, seasonal_test_pred_full, 'green', label='Seasonal预测', linewidth=2)
ax6.plot(final_test_dates, residual_test_pred_full, 'orange', label='Residual预测(不含prod_lag1)', linewidth=2)
ax6.axhline(y=0, color='black', linestyle='-', alpha=0.3)

ax6.set_title('各组件预测对比')
ax6.set_ylabel('组件值')
ax6.legend()
ax6.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 11. 结果保存
print("\n11. 结果保存")
print("-" * 50)

# 保存融合预测结果
fusion_results = pd.DataFrame({
    'date': np.concatenate([final_train_dates, final_test_dates]),
    'actual': np.concatenate([final_train_actual, final_test_actual]),
    'predicted': np.concatenate([final_train_pred, final_test_pred]),
    'trend_pred': np.concatenate([train_trend_aligned, trend_test_pred_full]),
    'seasonal_pred': np.concatenate([train_seasonal_aligned, seasonal_test_pred_full]),
    'residual_pred': np.concatenate([train_residual_aligned, residual_test_pred_full]),
    'dataset': ['train'] * len(final_train_dates) + ['test'] * len(final_test_dates)
})

fusion_results_path = r"D:\埃塞俄比亚咖啡\模型\fusion_trend_only_production_results.csv"
fusion_results.to_csv(fusion_results_path, index=False)
print(f"✓ 融合预测结果已保存: {fusion_results_path}")

# 保存性能对比
performance_comparison = pd.DataFrame({
    '版本': ['仅Trend含prod_lag1'],
    'Trend特征数': [8],
    'Residual特征数': [len(available_features)],
    '测试集RMSE': [final_test_metrics['RMSE']],
    '测试集MAE': [final_test_metrics['MAE']],
    '测试集R²': [final_test_metrics['R2']],
    '测试集MAPE(%)': [final_test_metrics['MAPE']],
    '方向准确性(%)': [final_test_metrics['Direction_Accuracy']],
    '过拟合比率': [final_overfitting_ratio],
    'prod_lag1_trend_rank': [prod_importance_rank],
    'prod_lag1_trend_coef': [prod_coefficient]
})

performance_path = r"D:\埃塞俄比亚咖啡\模型\performance_trend_only_production.csv"
performance_comparison.to_csv(performance_path, index=False)
print(f"✓ 性能对比已保存: {performance_path}")

# 保存特征重要性（仅Trend包含prod_lag1）
feature_importance_combined = pd.DataFrame({
    '模型': ['Trend'] * len(trend_feature_importance) + ['Residual'] * len(residual_feature_importance),
    '特征': list(trend_feature_importance['feature']) + list(residual_feature_importance['feature']),
    '重要性/系数': list(trend_feature_importance['abs_coefficient']) + list(residual_feature_importance['importance']),
    '排名': list(range(1, len(trend_feature_importance) + 1)) + list(range(1, len(residual_feature_importance) + 1))
})

importance_path = r"D:\埃塞俄比亚咖啡\模型\feature_importance_trend_only_production.csv"
feature_importance_combined.to_csv(importance_path, index=False)
print(f"✓ 特征重要性已保存: {importance_path}")

# 12. 总结报告
print("\n12. 总结报告")
print("-" * 50)

print("🎉 X11融合预测模型 - 仅Trend添加prod_lag1版本完成!")
print("=" * 50)

print(f"\n✅ 主要特点:")
print(f"  • 仅在Trend模型中添加prod_lag1特征 (7→8特征)")
print(f"  • Residual模型保持原版本配置 (13特征，不变)")
print(f"  • Seasonal模型保持不变（完全周期性）")
print(f"  • 模型复杂度增加最小，便于分析prod_lag1的独立贡献")

print(f"\n📊 模型性能:")
print(f"  • 测试集R²: {final_test_metrics['R2']:.4f}")
print(f"  • 测试集RMSE: {final_test_metrics['RMSE']:.2f} 百万美元")
print(f"  • 测试集MAPE: {final_test_metrics['MAPE']:.2f}%")
print(f"  • 方向准确性: {final_test_metrics['Direction_Accuracy']:.1f}%")
print(f"  • 过拟合比率: {final_overfitting_ratio:.3f}")

print(f"\n🔍 prod_lag1在Trend模型中的贡献:")
print(f"  • 重要性排名: {prod_importance_rank}/8")
print(f"  • 回归系数: {prod_coefficient:.6f}")
print(f"  • 影响评估: {'显著' if abs(prod_coefficient) > 0.01 else '中等' if abs(prod_coefficient) > 0.001 else '较小'}")

print(f"\n📁 输出文件:")
print(f"  • fusion_trend_only_production_results.csv - 详细预测结果")
print(f"  • performance_trend_only_production.csv - 性能对比")
print(f"  • feature_importance_trend_only_production.csv - 特征重要性")

print(f"\n🚀 使用建议:")
print(f"  1. 此版本可以清楚分析prod_lag1对趋势预测的独立影响")
print(f"  2. 与原版本和全量版本进行三方对比")
print(f"  3. 如果趋势预测改善明显，说明生产量确实是重要的趋势指标")
print(f"  4. 可以进一步分析生产量对长期趋势的影响机制")

print(f"\n✅ 模型已准备就绪，专门测试prod_lag1对趋势预测的贡献!")