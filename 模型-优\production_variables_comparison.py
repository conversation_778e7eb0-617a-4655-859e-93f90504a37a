"""
X11融合预测模型 - 产量变量对比分析
对比三个版本：
版本A: 仅使用prod_lag1 (绝对产量)
版本B: 仅使用productivity_lag1 (产能)  
版本C: 同时使用prod_lag1和productivity_lag1 (双变量)

分析目的：
1. 评估不同产量指标对预测效果的影响
2. 确定最优的产量变量配置
3. 分析多重共线性的影响
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("=== X11融合预测模型 - 产量变量对比分析 ===")
print("对比版本：A(prod_lag1) vs B(productivity_lag1) vs C(both)")

# 1. 数据加载
print("\n1. 性能数据加载")
print("-" * 50)

# 加载三个版本的性能数据
version_files = {
    'A': r"D:\埃塞俄比亚咖啡\模型\version_A_performance.csv",
    'B': r"D:\埃塞俄比亚咖啡\模型\version_B_performance.csv", 
    'C': r"D:\埃塞俄比亚咖啡\模型\version_C_performance.csv"
}

# 检查文件是否存在
existing_files = {}
for version, file_path in version_files.items():
    if Path(file_path).exists():
        existing_files[version] = file_path
        print(f"✓ 找到版本{version}性能文件")
    else:
        print(f"❌ 未找到版本{version}性能文件: {file_path}")

if len(existing_files) == 0:
    print("❌ 未找到任何版本的性能文件，请先运行各版本的预测模型")
    exit()

# 加载性能数据
performance_data = []
for version, file_path in existing_files.items():
    df = pd.read_csv(file_path)
    df['版本标识'] = version
    performance_data.append(df)

combined_performance = pd.concat(performance_data, ignore_index=True)
print(f"✓ 加载了 {len(existing_files)} 个版本的性能数据")

# 2. 性能对比分析
print("\n2. 性能指标对比")
print("-" * 50)

# 核心指标对比
core_metrics = ['测试集RMSE', '测试集R²', '测试集MAPE', '方向准确性', '过拟合比率']
# 检查实际存在的列名
available_columns = combined_performance.columns.tolist()
print(f"可用列名: {available_columns}")

# 只选择存在的列
columns_to_use = ['版本标识', '产量变量']
for metric in core_metrics:
    if metric in available_columns:
        columns_to_use.append(metric)

comparison_table = combined_performance[columns_to_use].copy()

print("📊 核心性能指标对比:")
print("=" * 80)

# 动态构建表头
header_parts = ["版本", "产量变量"]
for metric in core_metrics:
    if metric in comparison_table.columns:
        if metric == '测试集RMSE':
            header_parts.append('测试RMSE')
        elif metric == '测试集R²':
            header_parts.append('测试R²')
        elif metric == '测试集MAPE':
            header_parts.append('测试MAPE')
        elif metric == '方向准确性':
            header_parts.append('方向准确性')
        elif metric == '过拟合比率':
            header_parts.append('过拟合比率')

print(f"{'版本':<4} {'产量变量':<25} {'测试RMSE':<10} {'测试R²':<8} {'测试MAPE':<10} {'方向准确性':<10} {'过拟合比率':<10}")
print("-" * 80)

for _, row in comparison_table.iterrows():
    version = row['版本标识']
    variables = row['产量变量']
    
    # 安全获取各个指标
    rmse = row.get('测试集RMSE', 0)
    r2 = row.get('测试集R²', 0)
    mape = row.get('测试集MAPE', 0)
    direction = row.get('方向准确性', 0)
    overfitting = row.get('过拟合比率', 0)
    
    print(f"{version:<4} {variables:<25} {rmse:<10.2f} {r2:<8.4f} {mape:<10.2f} {direction:<10.1f} {overfitting:<10.3f}")

# 找出最佳版本
print(f"\n🏆 最佳性能版本:")

if '测试集RMSE' in comparison_table.columns:
    best_rmse_idx = comparison_table['测试集RMSE'].idxmin()
    print(f"  最低RMSE: 版本{comparison_table.loc[best_rmse_idx, '版本标识']} ({comparison_table.loc[best_rmse_idx, '测试集RMSE']:.2f})")

if '测试集R²' in comparison_table.columns:
    best_r2_idx = comparison_table['测试集R²'].idxmax()
    print(f"  最高R²: 版本{comparison_table.loc[best_r2_idx, '版本标识']} ({comparison_table.loc[best_r2_idx, '测试集R²']:.4f})")

if '测试集MAPE' in comparison_table.columns:
    best_mape_idx = comparison_table['测试集MAPE'].idxmin()  
    print(f"  最低MAPE: 版本{comparison_table.loc[best_mape_idx, '版本标识']} ({comparison_table.loc[best_mape_idx, '测试集MAPE']:.2f}%)")

# 3. 特征重要性对比
print("\n3. 产量变量重要性对比")
print("-" * 50)

print("📈 Trend模型中产量变量的重要性:")
for _, row in combined_performance.iterrows():
    version = row['版本标识']
    variables = row['产量变量']
    
    if 'prod_lag1_rank' in row and pd.notna(row['prod_lag1_rank']):
        prod_rank = int(row['prod_lag1_rank'])
        prod_coef = row['prod_lag1_coef']
        print(f"  版本{version} - prod_lag1: 排名{prod_rank}, 系数{prod_coef:.6f}")
    
    if 'productivity_lag1_rank' in row and pd.notna(row['productivity_lag1_rank']):
        productivity_rank = int(row['productivity_lag1_rank'])
        productivity_coef = row['productivity_lag1_coef']
        print(f"  版本{version} - productivity_lag1: 排名{productivity_rank}, 系数{productivity_coef:.6f}")

# 4. 多重共线性分析（如果版本C存在）
if 'C' in existing_files:
    print("\n4. 多重共线性分析（版本C）")
    print("-" * 50)
    
    version_c_data = combined_performance[combined_performance['版本标识'] == 'C'].iloc[0]
    
    if '变量相关系数' in version_c_data and pd.notna(version_c_data['变量相关系数']):
        correlation = version_c_data['变量相关系数']
        prod_vif = version_c_data.get('prod_VIF', np.nan)
        productivity_vif = version_c_data.get('productivity_VIF', np.nan)
        
        print(f"🔍 版本C（双变量）多重共线性分析:")
        print(f"  prod_lag1与productivity_lag1相关系数: {correlation:.4f}")
        if pd.notna(prod_vif):
            print(f"  prod_lag1 VIF: {prod_vif:.2f}")
        if pd.notna(productivity_vif):
            print(f"  productivity_lag1 VIF: {productivity_vif:.2f}")
        
        # 评估多重共线性严重程度
        if abs(correlation) > 0.8:
            print("  ⚠️ 高度相关（|r| > 0.8），存在严重多重共线性")
        elif abs(correlation) > 0.6:
            print("  ⚠️ 中度相关（|r| > 0.6），存在一定多重共线性")
        else:
            print("  ✅ 相关性可接受（|r| <= 0.6）")

# 5. 成本效益分析
print("\n5. 成本效益分析")
print("-" * 50)

print("💰 模型复杂度 vs 性能提升分析:")

# 以版本A为基准进行对比
if 'A' in existing_files:
    baseline_data = combined_performance[combined_performance['版本标识'] == 'A'].iloc[0]
    baseline_rmse = baseline_data['测试集RMSE']
    baseline_r2 = baseline_data['测试集R²']
    baseline_features = baseline_data.get('Trend特征数', 8)  # 默认8个特征
    
    print(f"基准版本A - RMSE: {baseline_rmse:.2f}, R²: {baseline_r2:.4f}, Trend特征数: {baseline_features}")
    
    for _, row in combined_performance.iterrows():
        if row['版本标识'] == 'A':
            continue
            
        version = row['版本标识']
        rmse = row['测试集RMSE']
        r2 = row['测试集R²']
        features = row.get('Trend特征数', 8 if row['版本标识'] != 'C' else 9)  # A和B默认8，C默认9
        
        rmse_improvement = (baseline_rmse - rmse) / baseline_rmse * 100
        r2_improvement = (r2 - baseline_r2) / baseline_r2 * 100
        feature_increase = features - baseline_features
        
        print(f"版本{version} vs 版本A:")
        print(f"  RMSE改善: {rmse_improvement:+.2f}%")
        print(f"  R²改善: {r2_improvement:+.2f}%") 
        print(f"  特征增加: +{feature_increase}")
        
        # 计算性价比
        if feature_increase > 0:
            efficiency = max(rmse_improvement, 0) / feature_increase
            print(f"  改善效率: {efficiency:.2f}%/特征")

# 6. 综合可视化对比
print("\n6. 综合可视化对比")
print("-" * 50)

if len(existing_files) >= 2:
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('产量变量对比分析 - 三版本性能对比', fontsize=16, fontweight='bold')
    
    # 1. RMSE对比
    ax1 = axes[0, 0]
    versions = comparison_table['版本标识'].tolist()
    rmse_values = comparison_table['测试集RMSE'].tolist()
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c'][:len(versions)]
    
    bars1 = ax1.bar(versions, rmse_values, color=colors, alpha=0.7)
    ax1.set_title('测试集RMSE对比')
    ax1.set_ylabel('RMSE (百万美元)')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars1, rmse_values):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{value:.1f}', ha='center', va='bottom', fontweight='bold')
    
    # 2. R²对比
    ax2 = axes[0, 1]
    r2_values = comparison_table['测试集R²'].tolist()
    
    bars2 = ax2.bar(versions, r2_values, color=colors, alpha=0.7)
    ax2.set_title('测试集R²对比')
    ax2.set_ylabel('R²')
    ax2.grid(True, alpha=0.3)
    
    for bar, value in zip(bars2, r2_values):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 3. MAPE对比
    ax3 = axes[0, 2]
    mape_values = comparison_table['测试集MAPE'].tolist()
    
    bars3 = ax3.bar(versions, mape_values, color=colors, alpha=0.7)
    ax3.set_title('测试集MAPE对比')
    ax3.set_ylabel('MAPE (%)')
    ax3.grid(True, alpha=0.3)
    
    for bar, value in zip(bars3, mape_values):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # 4. 特征数量对比
    ax4 = axes[1, 0]
    
    # 检查特征数列是否存在
    if 'Trend特征数' in combined_performance.columns and 'Residual特征数' in combined_performance.columns:
        trend_features = combined_performance['Trend特征数'].tolist()
        residual_features = combined_performance['Residual特征数'].tolist()
    else:
        # 如果没有特征数信息，使用默认值
        trend_features = [8, 8, 9][:len(versions)]  # A=8, B=8, C=9
        residual_features = [13] * len(versions)  # 都是13
    
    x = np.arange(len(versions))
    width = 0.35
    
    bars4a = ax4.bar(x - width/2, trend_features, width, label='Trend特征', color='skyblue', alpha=0.7)
    bars4b = ax4.bar(x + width/2, residual_features, width, label='Residual特征', color='lightcoral', alpha=0.7)
    
    ax4.set_title('特征数量对比')
    ax4.set_ylabel('特征数量')
    ax4.set_xticks(x)
    ax4.set_xticklabels(versions)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 过拟合比率对比
    ax5 = axes[1, 1]
    overfitting_values = comparison_table['过拟合比率'].tolist()
    
    bars5 = ax5.bar(versions, overfitting_values, color=colors, alpha=0.7)
    ax5.set_title('过拟合比率对比')
    ax5.set_ylabel('过拟合比率')
    ax5.axhline(y=1.5, color='red', linestyle='--', alpha=0.7, label='优秀线(1.5)')
    ax5.axhline(y=2.0, color='orange', linestyle='--', alpha=0.7, label='良好线(2.0)')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    for bar, value in zip(bars5, overfitting_values):
        ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
    
    # 6. 方向准确性对比
    ax6 = axes[1, 2]
    direction_values = comparison_table['方向准确性'].tolist()
    
    bars6 = ax6.bar(versions, direction_values, color=colors, alpha=0.7)
    ax6.set_title('方向准确性对比')
    ax6.set_ylabel('方向准确性 (%)')
    ax6.set_ylim(0, 100)
    ax6.grid(True, alpha=0.3)
    
    for bar, value in zip(bars6, direction_values):
        ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.show()

# 7. 决策建议
print("\n7. 决策建议")
print("-" * 50)

print("🎯 基于分析结果的建议:")

# 基于性能和复杂度给出建议
if len(existing_files) >= 2:
    # 以R²为主要指标，如果不存在则使用RMSE
    if '测试集R²' in comparison_table.columns:
        best_overall_idx = comparison_table['测试集R²'].idxmax()
        best_r2 = comparison_table.loc[best_overall_idx, '测试集R²']
    elif '测试集RMSE' in comparison_table.columns:
        best_overall_idx = comparison_table['测试集RMSE'].idxmin()
        best_r2 = comparison_table.loc[best_overall_idx, '测试集R²'] if '测试集R²' in comparison_table.columns else 0
    else:
        best_overall_idx = 0
        best_r2 = 0
        
    best_version = comparison_table.loc[best_overall_idx, '版本标识']
    best_variables = comparison_table.loc[best_overall_idx, '产量变量']
    best_rmse = comparison_table.loc[best_overall_idx, '测试集RMSE'] if '测试集RMSE' in comparison_table.columns else 0
    
    print(f"\n🏆 推荐版本: 版本{best_version}")
    print(f"   使用变量: {best_variables}")
    print(f"   测试集R²: {best_r2:.4f}")
    print(f"   测试集RMSE: {best_rmse:.2f}")
    
    # 具体建议
    if best_version == 'A':
        print(f"\n💡 建议理由:")
        print(f"   • 绝对产量(prod_lag1)提供了足够的预测信息")
        print(f"   • 模型复杂度适中，避免过拟合")
        print(f"   • 实用性强，绝对产量数据容易获得和理解")
        
    elif best_version == 'B':
        print(f"\n💡 建议理由:")
        print(f"   • 产能(productivity_lag1)比绝对产量更能反映生产效率")
        print(f"   • 标准化指标，消除了规模效应的影响")
        print(f"   • 对出口竞争力的预测更有意义")
        
    elif best_version == 'C':
        print(f"\n💡 建议理由:")
        print(f"   • 双变量提供了更全面的生产信息")
        print(f"   • 绝对产量反映规模，产能反映效率")
        print(f"   • 如果多重共线性可控，则信息互补价值高")
        
        # 检查多重共线性警告
        if 'C' in existing_files:
            version_c_data = combined_performance[combined_performance['版本标识'] == 'C'].iloc[0]
            if '变量相关系数' in version_c_data:
                correlation = version_c_data['变量相关系数']
                if abs(correlation) > 0.7:
                    print(f"   ⚠️ 注意：两变量相关性较高({correlation:.3f})，需要谨慎使用")

# 8. 结果保存
print("\n8. 结果保存")
print("-" * 50)

# 保存综合对比结果
summary_results = pd.DataFrame({
    '对比项目': [
        '最佳RMSE版本', '最佳R²版本', '最佳MAPE版本', 
        '推荐版本', '复杂度最低版本', '性价比最高版本'
    ],
    '版本': ['', '', '', '', '', ''],
    '数值': ['', '', '', '', '', ''],
    '说明': ['', '', '', '', '', '']
})

# 填入具体值
if len(existing_files) >= 2:
    if '测试集RMSE' in comparison_table.columns:
        best_rmse_idx = comparison_table['测试集RMSE'].idxmin()
        summary_results.loc[0, '版本'] = comparison_table.loc[best_rmse_idx, '版本标识']
        summary_results.loc[0, '数值'] = f"{comparison_table.loc[best_rmse_idx, '测试集RMSE']:.2f}"
    
    if '测试集R²' in comparison_table.columns:
        best_r2_idx = comparison_table['测试集R²'].idxmax()
        summary_results.loc[1, '版本'] = comparison_table.loc[best_r2_idx, '版本标识']
        summary_results.loc[1, '数值'] = f"{comparison_table.loc[best_r2_idx, '测试集R²']:.4f}"
    
    if '测试集MAPE' in comparison_table.columns:
        best_mape_idx = comparison_table['测试集MAPE'].idxmin()
        summary_results.loc[2, '版本'] = comparison_table.loc[best_mape_idx, '版本标识']
        summary_results.loc[2, '数值'] = f"{comparison_table.loc[best_mape_idx, '测试集MAPE']:.2f}%"
    
    summary_results.loc[3, '版本'] = best_version
    summary_results.loc[3, '数值'] = f"R²={best_r2:.4f}"

# 保存文件
summary_path = r"D:\埃塞俄比亚咖啡\模型\production_variables_comparison_summary.csv"
combined_performance.to_csv(summary_path.replace('_summary.csv', '_detailed.csv'), index=False)
summary_results.to_csv(summary_path, index=False)

print(f"✓ 详细对比结果已保存: {summary_path.replace('_summary.csv', '_detailed.csv')}")
print(f"✓ 对比摘要已保存: {summary_path}")

print(f"\n✅ 产量变量对比分析完成!")
print(f"   运行了 {len(existing_files)} 个版本的对比分析")
if len(existing_files) >= 2:
    print(f"   推荐使用版本{best_version}({best_variables})")
else:
    print(f"   需要运行更多版本进行对比")