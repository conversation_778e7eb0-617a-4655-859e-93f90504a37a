{"cells": [{"cell_type": "markdown", "id": "a87fb1f6-3466-4f65-b236-6d56096f1bfe", "metadata": {}, "source": ["# 先把trend和外生变量做相关性分析，得到确实和汇率、单价高度相关，受汇率影响更高"]}, {"cell_type": "code", "execution_count": 1, "id": "2f8700c8-df9c-4733-a0fb-5a7f0d39e265", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 700x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 1. 读取分解结果\n", "trend_file = r\"D:\\埃塞俄比亚咖啡\\模型\\x11_decomposition_results.csv\"\n", "trend_df = pd.read_csv(trend_file, parse_dates=['parsed_datetime'])\n", "trend_df.set_index('parsed_datetime', inplace=True)\n", "trend_df = trend_df[['Trend']]\n", "\n", "# 2. 读取原始数据\n", "raw_file = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "raw_df = pd.read_csv(raw_file, encoding='gbk')\n", "# 解析时间\n", "def parse_datetime(date_str):\n", "    try:\n", "        month_str, year_str = date_str.split('-')\n", "        year_int = int(year_str)\n", "        if year_int <= 25:  # 假设25以下是2000年后\n", "            full_year = 2000 + year_int\n", "        else:\n", "            full_year = 1900 + year_int\n", "        month_map = {'Jan':1,'Feb':2,'Mar':3,'Apr':4,'May':5,'Jun':6,'Jul':7,'Aug':8,'Sep':9,'Oct':10,'Nov':11,'Dec':12}\n", "        month_int = month_map[month_str]\n", "        return pd.Timestamp(year=full_year, month=month_int, day=1)\n", "    except:\n", "        return pd.NaT\n", "\n", "raw_df['parsed_datetime'] = raw_df['datetime'].apply(parse_datetime)\n", "raw_df.set_index('parsed_datetime', inplace=True)\n", "# 只取三列\n", "cols = ['sales price（usd/lb）', 'IN MILLION USD', 'Exchange(Domestic currency per US Dollar)']\n", "raw_df = raw_df[cols]\n", "\n", "# 3. 合并数据（以trend为基准，inner join）\n", "all_df = trend_df.join(raw_df, how='inner')\n", "\n", "# 4. 相关性分析\n", "corr = all_df.corr()\n", "\n", "# 5. 可视化（中文不乱码）\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "plt.figure(figsize=(7, 5))\n", "sns.heatmap(corr, annot=True, cmap='coolwarm', center=0, fmt=\".2f\")\n", "plt.title('Trend与相关变量相关性热力图')\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 2, "id": "369f493d-3883-4573-a79a-07d0d757e5b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["            Trend  sales price（usd/lb）  \\\n", "count  156.000000           156.000000   \n", "mean    87.893006             1.842905   \n", "std     39.189456             0.407073   \n", "min     50.172083             1.080129   \n", "25%     64.737396             1.550415   \n", "50%     68.706875             1.763439   \n", "75%    105.326146             2.007937   \n", "max    231.314293             3.128276   \n", "\n", "       Exchange(Domestic currency per US Dollar)  \n", "count                                 156.000000  \n", "mean                                   38.673599  \n", "std                                    27.300133  \n", "min                                    17.800005  \n", "25%                                    20.842900  \n", "50%                                    28.044000  \n", "75%                                    50.966125  \n", "max                                   135.671800  \n", "训练集: 124, 测试集: 32\n", "\n", "网格搜索最优ARIMAX(p,d,q)参数：\n", "0阶差分 - ADF p值: 0.7089, KPSS p值: 0.0100\n", "1阶差分 - ADF p值: 0.0177, KPSS p值: 0.1000\n", "选择的差分阶数 d = 1\n", "  ARIMAX(0,1,0): AIC=469.35\n", "  ARIMAX(0,1,1): AIC=387.73\n", "  ARIMAX(0,1,2): AIC=367.97\n", "  ARIMAX(0,1,3): AIC=357.58\n", "  ARIMAX(0,1,4): AIC=347.70\n", "  ARIMAX(0,1,5): AIC=345.08\n", "  ARIMAX(1,1,0): AIC=342.39\n", "  ARIMAX(1,1,1): AIC=342.15\n", "  ARIMAX(1,1,2): AIC=342.39\n", "  ARIMAX(1,1,3): AIC=342.06\n", "  ARIMAX(1,1,4): AIC=343.54\n", "  ARIMAX(1,1,5): AIC=345.43\n", "  ARIMAX(2,1,0): AIC=342.82\n", "  ARIMAX(2,1,1): AIC=339.93\n", "  ARIMAX(2,1,2): AIC=343.49\n", "  ARIMAX(2,1,3): AIC=343.78\n", "  ARIMAX(2,1,4): AIC=345.49\n", "  ARIMAX(2,1,5): AIC=347.43\n", "  ARIMAX(3,1,0): AIC=343.51\n", "  ARIMAX(3,1,1): AIC=343.41\n", "  ARIMAX(3,1,2): AIC=344.56\n", "  ARIMAX(3,1,3): AIC=344.48\n", "  ARIMAX(3,1,4): AIC=341.66\n", "  ARIMAX(3,1,5): AIC=347.86\n", "  ARIMAX(4,1,0): AIC=341.32\n", "  ARIMAX(4,1,1): AIC=343.31\n", "  ARIMAX(4,1,2): AIC=344.74\n", "  ARIMAX(4,1,3): AIC=345.57\n", "  ARIMAX(4,1,4): AIC=346.32\n", "  ARIMAX(4,1,5): AIC=349.03\n", "  ARIMAX(5,1,0): AIC=343.31\n", "  ARIMAX(5,1,1): AIC=345.27\n", "  ARIMAX(5,1,2): AIC=345.74\n", "  ARIMAX(5,1,3): AIC=345.17\n", "  ARIMAX(5,1,4): AIC=345.84\n", "  ARIMAX(5,1,5): AIC=347.35\n", "\n", "最优参数: ARIMAX(2, 1, 1), AIC=339.93\n", "训练集评估： {'MSE': 24.367708536856853, 'RMSE': 4.9363659241244315, 'MAE': 1.0310795569618347, 'MAPE': 1.561189854635988, 'Direction_Accuracy': 82.92682926829268}\n", "测试集评估： {'MSE': 2446.7364486818806, 'RMSE': 49.46449685058851, 'MAE': 35.07472983902258, 'MAPE': 19.53239800556445, 'Direction_Accuracy': 64.51612903225806}\n", "Ljung-Box统计量: 1.9753, p值: 0.9965\n", "Jarque-Bera: 统计量=65559.3463, p值=0.0000\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKUAAAHqCAYAAADVi/1VAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuNSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/xnp5ZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACfQklEQVR4nOzddZhUdRvG8e9sB5sssMDS3ShS0t0NAtKICCIdioQIAgq8NCKCkiIqjUo3CAJKdyws3Wz3zPvHsCMrIbHsbNyf6zrXzpx8zuwi7O3v9xyDyWQyISIiIiIiIiIikohsrF2AiIiIiIiIiIikPgqlREREREREREQk0SmUEhERERERERGRRKdQSkREREREREREEp1CKRERERERERERSXQKpUREREREREREJNEplBIRERERERERkUSnUEpERERERERERBKdQikRERGRhyIiIoiMjMRkMsVbbzKZiIiIICoqyrLu0dfP4+bNmxw4cCBB6rSmK1euWF5HREQwe/Zsbt68acWKREREJLkymP79ry4RERGRFO6NN97g5s2bODg4WNY1adKE4OBgvvvuu6ceN2nSJPr06UNMTAwlS5akevXqjB07lmvXrnHixInH9q9atarlGt9//z1Dhw7l2rVrCX9DiSQyMpKcOXPy3nvvMXLkSKKiosiRIwcdO3Zk9OjRzzz2iy++4KuvviJXrlxP3efBgwcYDAb8/f0TunQRERFJgjRSSkRERF5Kw4YNyZkzZ7x12bNnx2AwYDAYcHJyonjx4ixatOiJxxsMBiZPnhxv3YgRIzAYDPTs2dOyrmfPnhgMBkaMGBFv34kTJ2IwGAgICIi3vl+/fnh5eREaGmpZ16RJEwoUKIDRaARgyZIlbN26la1bt9K/f3/u3btHo0aNmD59OuHh4RiNRlq0aMFHH32EyWTCaDQSFhbGhx9+CICdnR2ffvops2bNolWrVmzYsIF33nmHESNGMGLECAYPHkydOnWIjIzk7t27hIaG4uDgEC8Ei/uc/r1Urlz5uT7/16Vjx4507NjxidvmzZvHzZs3effddwFwcHBgyJAhTJ48mTNnzjzzvM7OzmTKlMly/ictlSpVwtnZOaFvSURERJIohVIiIiLywmJiYti2bRv+/v6cP38+3rZ33nmH/fv3s2bNGt544w3atWvHwoULX+j8R44csbw+fPjwE/fZuHFjvK9xBgwYQHh4OPPnzwfA39+f1atXM3jwYGxszP/0yZcvH8uWLWPPnj18/vnnLFu2jMqVK+Pk5ISTkxMGg4GzZ89SvHhxwBwgOTs7xwuVWrRowf79+xk4cCCOjo68+eab7N27l71797JixQoA7O3tqV+/Pt98881j9e/fv5/9+/fz2WefAbB161b279/PrFmzXuizSizBwcGMHj2ajh07kj9/fsv6rl27kidPHtq3b/+fUxrt7e3x8fF56uLm5va6b0NERESSEDtrFyAiIiLJz969ewkODsbBwYENGzbQvXt3y7Z06dLx1ltvAVCjRg2OHz/O3Llzadeu3XOf/9FQ6ujRo49tj4qKYseOHZbrv/fee5ZtcaNxpk6dSvfu3Zk+fTpZs2a1jO6Jkzt3bjp16kT9+vWpUaMGYO77VKhQIQDu3btHv379+PjjjwHIkycPe/bssRwfGRlJvnz5ADh79iwA27dvZ8iQIcyYMcOyn4ODA46Ojo/dQ9xndOzYMQCKFy+Op6fnc35CiW/AgAEEBQXxxRdfxFtvZ2fH/PnzKVu2LO+++y6LFy+OF97FiYqK4sqVK0yYMOGp1wgMDMTOTv88FRERSS00UkpERERe2MaNG8mZMyf16tV7bKTSv7311lvxmmP/l9KlSxMUFERAQACXLl0iKCjIEuDE2b17N2FhYfTq1YvNmzdbpuXF+fjjjzl//jxLly7l+++/5+OPP7aEHSaTiaioKN555x3Wr18fb9SPvb09d+/e5c8//+TBgwds376dS5cuMW7cOMLCwiz7/frrrxQsWPCxUVw2Njbs3r0bV1dXyzqDwfDc955UrV69mm+//ZZp06bh6+v72PZixYrxyy+/8Ouvv1K9evV43++QkBCioqIYPHgwDx484NChQ09d/P39OXv2LEajkeDg4MS8RREREbEChVIiIiLywjZu3EiVKlWoUqUKW7duJTY29qn73rhxg3Tp0j33uZ2cnMidOzeHDx/myJEj5MyZM17IE3f9XLly0b59e+7evcvBgwfjbc+RIwetW7fmvffew9nZmU6dOlm23bx5E0dHR2xtbalcuTKjR4/Gzs4OOzs7duzYAYCtrS3nzp3jjTfe4Pbt29jY2GBvb285R9WqVSlZsiTlypVj165dlvV2dnY4OTkl2GifuF5LV65coXXr1vj4+HDp0iXL9oiICPr06UOGDBnw8vKiZcuW3L5927I9e/bszJs3j7Fjx+Lr64uXlxc9e/aM93TB+fPnky1bNlxcXOjUqdNjU/D27t1LmzZtaNy4MUWLFuXkyZOcOnXqsSVXrlx8++23nDhxgkKFCrFgwQLA3M/L09MTb29vcuTIQe7cuZ+5ZM2aFU9PT3x8fBLkMxQREZGkS+OjRURE5IUEBgayb98+PvzwQ4oVK8aDBw/Yt28fZcuWjbdfVFQUK1euZPXq1YwdO/aFrlGkSBGOHDmCyWSiSJEi3Lt3L972jRs3UrlyZQoXLoyPjw8bNmygRIkS8fbp3bs3CxcuZMCAAfGmz2XIkIGQkBBcXFxo0aIFhQsXpkmTJpQoUYIyZcpY9gsPD8fe3p5s2bI9Vp+LiwuLFy9m1KhRFClShIsXL1q2OTk5vdC9/pd79+5Rrlw5KlWqxIgRI/D29rZs6969Oxs3bmT69Om4uroyYMAAmjZtys6dOy37TJ48GScnJ7777juOHDnCp59+Sq1atahfvz579uyhY8eOfPjhhzRt2pRZs2axYsUK2rRpA5hDrw4dOlCqVCkaNGhg6bH1NLVr1+bAgQN06tSJ0qVLA/F7fx0/fvy57rl3794pYoSZiIiIPJtGSomIiMgLiRsZ9Wgo9OgUvhkzZmAwGHB0dKR169Z07NiRPn36vNA1ihQpYhkpVaRIkXjb7t27x99//03lypUxGAxUqlTpiVMIt2zZEu9rHIPBgKura7zQ4/bt27Rq1Yo0adJY1oWHh5MhQ4anhiM2NjYMGjQIZ2dny/TBO3fu4OHhYdnn39MKX8aaNWvo168fCxYs4KOPPrI0A7948SLz589n2rRptGjRgrp16/Lll1+ya9cu/P39LcffunWLTZs2Ua9ePQYPHhxv2uGUKVMoVKgQM2bMoFq1aixcuDDeCCUnJye2bdvGqlWraNeuHZGRkYSGhmIwGFiyZAkmk8myVK1alSxZspA9e3a2bt1q6bcVZ9myZSxbtozs2bNbli+++IKgoCDL+/v37zNgwAAFUiIiIqmERkqJiIjIC4kLgLJkyRJv3fDhwwFo2bIln3zyCbNnz2bBggWMGzcu3tS351GkSBF+/PFHTCYTLVu2ZPv27ZZtcT2k2rVrZ2me7ujoSFhYGC4uLoB5hM/EiRNp164dixYtYteuXZQvX/6p16tevTrVq1eP18foypUrZMqUyfL+0SlvcebOncuCBQvo3LkzANevX493TGRk5Avd95MUKlSInj17Prb+6NGjmEwmmjZt+ti2s2fPkiNHDsA8BfDRsC1dunRER0cDcObMGUqVKmXZ5ujoaBnhFCdjxozx3h87dswygu1RV69epVKlSk+9DwcHBw4dOsSAAQMs6x48eMCsWbNwdnYGICwsTI3ORUREUhH9rS8iIiIvZOPGjbRp08YSLqxcuZLRo0dbAh0fHx+KFy/OiBEjmDdvHpMnT2bkyJEvdI0iRYpw+vRpy+t/Xz9fvnwsWbIEMIdHDRo0YPv27dSpUweAOXPmEBISwpQpU7hz5w6jRo1i/fr1/3ndmJgYy+t9+/bFa4L+715LALNnz6Zo0aKUKFECZ2dnNm/eTNasWUmbNi0zZ87EycmJ2NjYJx77vN566y1sbJ4+uH3dunVkyJAh3rqcOXNaXufKleupxxqNRmxtbeOt+/f7f1u5ciUZMmSgQIEC8dZfvXo1XlD5bxEREeTOndsyNRDgiy++oH79+uTJkweA06dPM3fu3GdeX0RERFIOTd8TERGR53bp0iXOnj1r6S9UvHhxWrRoQUxMDFu3bo23b7p06ejevTvTpk0jKCjoha6TK1cunJ2dcXZ2Jnfu3PG2xTVZj7t+/fr1SZcunWUEV3R0NOPHj6dz5854eXnRv39/NmzYwP79+//zujExMWTLlo3o6GiWL19OjRo1AMiUKdNjTwDcv38/hw8fZuDAgZQoUYI2bdqwceNGKlasyLJly+jWrRvOzs5ERka+Uij1NIUKFQLMo7HiPosMGTIwYcKEeM3QnxUy5c6dmwMHDljeR0dHs2/fvqfuf/XqVaZNm0bnzp3jTbELCgoiJCTkmaFUly5d+Pjjj/Hz87Mstra2pEuXzvK+WrVqLFq06Imj0kRERCTl0UgpEREReW5xwU+FChUs6woWLPhYX6k4AwcO5Ouvv2b69Ol8+umnz30dGxsbS+jy6Cihc+fOcfHixXjXj6sn7voLFizg6tWr9O3bF4Bq1apRrFgxvvjiC1atWvXM66ZLl46LFy/St29fHBwcqFy5MjVr1mTcuHGPjeAZPXo0devWpXDhwgAsXryYmzdv4ubmRvv27YmOjub999+3hDyLFi167vt/Hjlz5qRdu3b06NGD4OBgMmXKxNixYzl+/DgzZ858rnP06NGDqlWr0rdvXxo0aMD333/P1atXn7hvQEAAderUwdvbm8GDB8fbFjeq7Wmh1L1792jQoAEuLi7xQrLAwEDmzp1rmb5nNBoJDw/H1taWFi1aPNc9iIiISPKlkVIiIiLy3DZu3EiOHDni9U0yGAyUL1+eDRs2PLZ/hgwZ+OCDD5g0aRJhYWEvdK0iRYo8ceoe8Fh/qIoVK3Ls2DGuXr3Kl19+SbNmzciePbtle//+/VmzZg1HjhwBzAHLnj17OHXqFF5eXpb9oqKi6NWrF9999x0//vgjmTJlomnTplSpUoUZM2ZY9tu8eTOrVq2id+/eAISGhvL555/TuXNn2rVrx4ABA+jbty/nz5+3HBMbG/tC9/88vvnmG5o0aULfvn1p2LAhtra2bNq0ydIM/b9UqVKFhQsX8vvvv1O/fn0iIiJo1qxZvH2ioqL47rvvKFOmDCEhIaxbt85y/h07djB48GC6dOmCj4/PU6cKenp6cujQIU6fPs25c+csi6+vL/Pnz7e8P3/+PBcvXqRRo0av9sGIiIhIsmAwaXy0iIiIpDIbN26kSZMmlC5dmh9++AFfX1/+/PNP3nnnHVxdXVmyZAlFixa17P/nn39anl7Xv39/Ro4cycKFCzlz5gwAzZo1Y+vWrRw/fpxMmTIRGRlJkSJFqFu3LpMnT+azzz7jl19+wd7e3vLku+RiyZIlvPvuu7z77rtMnDiR9OnTW7YFBARQtGhRSpYsyeeff87bb7/92PE//vgjH3zwAd7e3o81Mb9//z5p0qR5rBF+SEgIefLkYefOna/npkRERCRJUCglIiIi8tBvv/1GjRo1cHBweGzb+fPnyZw5M05OTgDcuHEDX19fIiMjGTduHMWKFaNhw4aW/U+ePEnu3Lmxt7fnq6++4ujRo/Tp0+ex3lTJwblz5x7r7SUiIiLyqhRKiYiIiIiIiIhIolNPKRERERERERERSXQKpUREREREREREJNEplBIRERERERERkUSnUEpERERERERERBKdQikREREREREREUl0dtYu4L8YjUauXbuGm5sbBoPB2uWIiIiIiIiIiKRKJpOJ4OBgMmXKhI3Nq49zSvKh1LVr18iSJYu1yxAREREREREREeDy5cv4+fm98nmSfCjl5uYGmG/Y3d3dytWIiIiIiIiIiKROQUFBZMmSxZLVvKokH0rFTdlzd3dXKCUiIiIiIiIiYmUJ1V5Jjc5FRERERERERCTRKZQSEREREREREZFEp1BKREREREREREQSXZLvKfU8jEYjUVFR1i5DEoi9vT22trbWLkNEREREREREXqNkH0pFRUXh7++P0Wi0dimSgDw9PfH19U2w5mkiIiIiIiIikrQk61DKZDJx/fp1bG1tyZIlCzY2mo2Y3JlMJsLCwrh16xYAGTNmtHJFIiIiIiIiIvI6JOtQKiYmhrCwMDJlyoSLi4u1y5EE4uzsDMCtW7dInz69pvKJiIiIiIiIpEDJemhRbGwsAA4ODlauRBJaXMgYHR1t5UpERERERERE5HVI1qFUHPUdSnn0PRURERERERFJ2VJEKJVSREZGvlDD9qtXr9KmTRtCQ0NfY1UiIiIiIiIiIglPoZSVREdHPxZAtWvXji+//DLeupiYGMs0xX9Lnz49mzZtYsmSJa+tThERERERERGR1yFZNzpPzgYPHszOnTuxt7e3rDtw4ACnT5/m999/t6yLiopi6NChNGzYkK5du7Jhw4Z45wkKCqJPnz6MGjUq3vqZM2dSp04dOnbsyPz58x+7vslkSuA7erIRI0Zw8eJF5s2blyjXExEREREREZHkQaGUlUyYMAGAn376yRIQ9ejRg/bt25M5c2YAnJycaNy4seWYBw8e0KVLF4YOHRrvXP7+/qRLl440adIA4OfnZ2kQ/vXXXzN58mRmzpzJb7/9xq+//vq6b01ERERERERE5D9p+p6VdejQgVOnTnHnzh0+//xzHB0duXPnDgcOHKBnz57x9i1btixFihRh06ZN1K1bl6tXrwIwadIk2rZta9mvR48e5MiRAzA/xc7T0xNnZ2fs7Ozw9PTE09Mz0e5PREREREREJDWLiYmxdglJVooaKWUymQgLC7PKtV1cXF7qiXFOTk4sXrwYBweHeOvDw8NxdHSMt65v377ExsYSGRnJunXrKFKkCAsXLgQge/bslv0GDx783NfPnj07c+bMYfPmzcydO5fNmzdTqFAhABYsWMCoUaO4f/8+Xbt2ZfTo0RgMBjp27EiWLFm4d+8e8+fPJ3v27Cxbtox8+fIRHR1N//79WbhwIbly5aJIkSIv/JmIiIiIiIiIpAQBAQFUqFCB4cOH06lTJ2xsNDboUSkqlAoLC7NMYUtsISEhuLq6vtSx7777Lj4+PvHWXbx4kZUrV8ZbFxkZSZkyZejXrx8TJkygXLlyZMmShfDwcPLmzfuypTNs2DBy587NDz/8YAm3duzYQZcuXVi2bBlZs2albt26FCxY0DIia9asWbz33nscO3aMzp07M3r0aBYsWMDMmTNZs2YN27ZtIyAggObNm9O6deuXrk1EREREREQkuerTpw8BAQHMnz+fzp07W7ucJCdFhVLJVWRkJBEREfHWRUVFPbafo6MjH3/8Md27d+fAgQNMmTIFgBs3blC9evWXvr6Hh4dlxFWchQsX0qRJExo0aACYnwy4evVqSyiVJUsWxo4dC0CrVq348ccfAVixYgU9evSgWLFiFCtWjEaNGr10XSIiIiIiIiLJ1W+//caKFSuws7Pj66+/fqnZVSldigqlXFxcCAkJsdq1X9ayZcueOH3v0R/Y2NhYoqOjadmyJW+88QYHDx60bLt69SpZsmSxvI+OjiYmJgZnZ+fnuv6/e1cBXLlyha1bt1r6T0VFRVG0aFHL9kqVKlleOzg4WJq1X79+naxZs1q25cqVi+vXrz9XHSIiIiIiIiIpQXh4uOV37b59+1K4cGErV5Q0pahQymAwvPQUusRkNBqJjY3F3t4egN9//53cuXPH22fv3r2WUUlRUVHs2bOH2rVr4+TkhK2tLQAfffQR0dHRhISE0LBhQ8uxkZGRvPHGG+zYseO56nnSZ+bn58cHH3xA3759AXPQZTQaLdvd3d2feK706dNz7do1y/uAgADLfYqIiIiIiIikBmPHjsXf3x8/Pz+GDx9u7XKSrBQVSiUXmzZtokmTJtja2mJjY8Nbb7311H3d3d2Jjo7mwIEDhIeHx9tmNBpp1aoVmTJlok2bNhQvXjzBAqD27dvTrl07WrdujZ+fH4MGDcJoNLJ06dJnHtewYUNmzJhBtWrVuHr1KsuXL6dly5YJUpOIiIiIiIhIUnfmzBm++uorAKZMmWK13tfJgUIpK6hZsyahoaEAhIaGMnHiRFxdXenXrx8A3377LSdOnKBly5aULVv2iec4fPgw/fr1w9bWllmzZlG1alVCQ0MZM2YMzZs3f+UaK1SowOeff067du24ceMG1apV49tvv/3P43r27Mm5c+eoWLEiOXPmpHHjxq9ci4iIiIiIiEhyYDKZ6NGjB1FRUdSpU4cmTZpYu6QkzWCKawaURAUFBeHh4UFgYOBjU8YiIiLw9/cnR44cODk5WanCl3Pq1CnmzZvHwoULqVatGoMHD6ZAgQIA3L59m4ULFzJhwgQyZszI9OnTKVOmDJs2bWLfvn2sWLGC8+fPM2DAAD7++GPs7OwwmUwsXryYgQMHkjVrVmbMmEGJEiWsfJcvLzl/b0VERERERCR1+vnnn2nZsiWOjo4cP36cXLlyWbukBPWsjOZl2CRATfISIiIiyJAhA4cOHWLBggWWQAogXbp09OvXj3PnztGhQwcKFy6MwWDg0qVL/P333/Tp04fLly8zZMgQ7OzMg90MBgNt2rTh5MmTlC5d2tJ3SkRERERERERev6CgIPr06QPAp59+muICqddBI6UkSdL3VkRERERERJKTfv36MWnSJHLnzs3Ro0dT5O+yGiklIiIiIiIiIpKEHDlyhKlTpwIwY8aMFBlIvQ4KpUREREREREREXpLRaOTDDz8kNjaW5s2bU7NmTWuXlGwolBIREREREREReUkLFy5k9+7duLq6MmnSJGuXk6wolBIREREREREReQlBQUF8/PHHAAwfPhw/Pz8rV5S8KJQSEREREREREXkJI0eO5ObNm+TNm9fy5D15fgqlrCgmJibBzvU8D1GcPXs2f//9NwB///03I0aMSLDrP4vJZOL+/fuJci0RERERERGRxHDy5EmmTJkCwJQpU3BwcLByRcmPQikr2b17N2+//Ta3b9+2rAsLC3tsv/DwcC5dumR5v2nTJtatW/fYfpUqVWLPnj1Pvd7du3f55JNPiI2NBcDPz48ff/zRElI9TaVKlfjll18s700mE++//z7Xrl0DYNy4caxateqZ51i0aBH58+cnMjLymfuJiIiIiIiIJAcmk4nevXsTExNDw4YNqV27trVLSpYUSllJuXLlqFmzJpUrV+bOnTsAtGrVih9//BGTycScOXMAOHbsGB07drQc99VXX3Hq1CkAIiMjMZlM+Pv7c/LkSd544w3APAIrKioq3vUGDBhAaGgonTp1onDhwlStWhUbGxvat29P4cKFKVSoELlz52b//v3xjrO3t8fd3R2AWbNmsXz5ctKkSUPPnj0BOHjwIFevXn3qfV67do2PP/6YN998k4EDB77CJyYiIiIiIiKSNKxcuZKNGzfi6OjIxIkTrV1OsmVn7QJSsy+++ILIyEjOnj2Lj48Pzs7OODk5YTAY6NatG126dMHe3h5bW1sAjh8/zv379+nZsye3bt2iRIkSGAwGHjx4gJ2dHfnz5wcgNjaWxo0bM23aNACWLVvGvHnzWLhwIW3btgVgzpw5/Prrr6xcuRIwp7wmkwkbG3NO+eGHH7Jy5Uru37/PoUOHqFu3Ls7OzuTLl4+hQ4eSM2dONm/ejJ2dHfb29k+8v5CQEJo2bUrv3r3p378/tWvXZtiwYYwaNep1fqwiIiIiIiIir014eDj9+vUDzANAcuXKZeWKki+NlLKC4OBgAgICuHHjBsOHD6ds2bIAODo6YjAYALCzezwv/OKLL5g8eTJhYWEULFiQ33//nUuXLpE1a1Z27NjBxYsXuXjxIpcvX7YEUv7+/rz//vvky5ePDh06YGdnh52dHR988AGrV6+2vLexsWH16tWWa3399ddcu3aNSpUqsXTpUhYsWIC9vT0uLi6kTZuWX3/9lTJlygBgNBofq/XevXvUrVuXsmXL8vHHH2NnZ8eKFSvYvHkzbdq0UY8pERERERERSZbGjx/PxYsXyZIlC4MHD7Z2OcmaQikr2L59OzVq1KBo0aIMGzbMsv5Zjc+XLVvGwYMHOXz4MB06dKBevXp069aNXLlycfHiRdq2bfvEdDYgIIDu3btTpkwZ5s+fT0xMDDExMcyaNYuGDRta3oeEhFCzZs2nXv/8+fPx3leoUAFXV1eMRqNldFWcDRs2UKpUKcqVK8eECRMs693c3Fi/fj3R0dHkyZOHUaNGERAQ8J+fl4iIiIiIiEhScOnSJcaOHQvAhAkTcHV1tXJFyVuKnL4XGvr0bba24OT0fPva2ICz83/v+6I/g/Xr16d+/fqMGDGC0EdOGh4e/sQRUgAeHh5UqFCBixcvsnnzZk6fPo2vry/vvfcelStXpkWLFmTIkOGx4ypVqkSlSpXo2LEjQ4YMsYRE9+7dIzAwkOLFiwMQFRVFp06d4vV9unnzJjdv3qRNmza8//778c5rMpkwGAyEh4fj4uJiWT9y5EimTp3Kt99+y4wZM/Dy8ooXWsXGxtKhQwcWLFjAp59+ioeHB7169XqxD1BERERERETECvr3709ERARVqlShRYsW1i4n2UuRoVSaNE/fVrcu/PbbP+/Tp4cnPPQOgEqVYNu2f95nzw4Pe5LHYzK9TJVmcf2iAG7duoWbm9sT96tevTrVq1enffv2jBo1Cl9fX06dOsX69euZMWMGgGXqX3R0NAaDIV7AFRISQoECBSx9p44fP86FCxeoXLnyw3swceHCBUJCQkiTJg3Dhg1j1qxZ2NjY8M0339C4cWM++ugjAE6cOMGXX37JggULuH79OhkzZrRcp0+fPnTt2hVfX1+aNm36zHuvU6fOE6f+iYiIiIiIiCQ1mzdvZtmyZdja2jJlyhTL7+Dy8lJkKJVcnTx58pkN0lavXs2lS5eYN28eYWFhrFmzhpCQEPLnz4/JZCIoKIjs2bMTERHBsGHD6NGjh+XYhg0bkj17dvLmzQtA586dmThxIqVLlwbMoVRkZKRl1FObNm0YMGAALVu2xNPT03Key5cvU69ePcaMGUNkZCTHjh2jaNGilu3u7u64u7vTpEkT9u3b98Qm6GFhYQwaNIgBAwbEC+VEREREREREkqKoqCjLQI0PP/yQIkWKWLmilCFFhlIhIU/f9u8M5Natp+/7r1ZJXLz40iU909KlS8mfPz+urq74+fk9cZ/Lly/Tpk0b8ubNS968eTEYDJw4cYKPPvqI0NBQ0qRJg6+vLxcuXODq1atkyZIFMM93rVGjBg4ODvHOd+HCBU6ePBlv/mtUVBQNGzZkwoQJlhFVj7p79y7ffPMN33//Pa1bt+brr78mJCSEX375he7du8fbNyoqiilTptC8efPHztOxY0dMrzK8TERERERERCQRTZw4kVOnTpE+fXpGjhxp7XJSjBQZSr1Ij6fXte/zMJlMLFu2jJ07d1K2bFmaNGli2fbvpudZsmRh3LhxFCxYkBIlSgDm6Xr9+/fnzp07LFiwAICNGzfSo0cPDh48iJubG9myZePMmTOA+Ul8nTt3pmzZsmzZsoVevXoxadIkatasSf/+/fH29n5mvc7OzkyaNIn27dvz66+/MmLECJYtW0bHjh0pW7aspT9VXG3PomGOIiIiIiIikhxcunTJEkT973//izebSF6Nnr5nRf7+/gQFBTF69GjmzJlDz549AXMz8LheT9HR0cTGxmIymahQoQLHjh3jnXfeIW/evEyePJnNmzczffp0y8ijWrVqUapUKfr06QOYG5pPnz6d2rVrU6FCBbp06cKYMWMwmUz4+vqyc+dO7OzsyJ8/P9WrV2fKlCmWGhYuXMjRo0dxdHQE4Pvvv6d9+/b069ePzp07s3z5cpo2bUrfvn3p2rVrvHszmUx8+OGHZM+e/bFl6dKlGiklIiIiIiIiyULv3r0JDw+nUqVKtGnTxtrlpCgpcqRUcmEwGJg/fz4BAQF89NFH5MmTBzA3P9+0aRMAkZGRREVFsWDBAj766CPatGnDp59+SkxMDB07dmT79u2cPHmSNWvWWEY6TZo0iZIlS3Lr1i1cXFzYsGED9evXZ/ny5ZaeUdHR0QQHB+Pk5MTnn3/OwIEDWbFiheUctra27Nu3j169elGqVClLze3btycyMpL9+/eTLVs2AAYNGkSFChXi3Vt0dDRff/31U6fvRUZGJvCnKSIiIiIiIpKw1qxZw6pVq7Czs+Prr7/WrJ8EZjAl8SErQUFBeHh4EBgYiLu7e7xtERER+Pv7kyNHDpycnKxUYeIIDQ3FaDRans5nNBq5cuUKWbNmZc+ePSxevJg2bdpQpkwZwNzT6d99pBLC6zrvv6Wm762IiIiIiIgkPWFhYRQqVIiLFy/y8ccf8+WXX1q7JKt7VkbzMjRSKplw/VdDKxsbG7JmzQpA2bJlKVu2bLztrys4SoxASkRERERERMTaxowZw8WLF8mSJQvDhg2zdjkpknpKiYiIiIiIiEiyNXXqVEaNGpWgvYtPnz7NuHHjLOf/90ARSRgaKSUiIiIiIiIiydKNGzfo3bs3AI0bN6ZIkSKvfE6TyUSPHj2Ijo6mXr16NGrU6JXPKU+mkVIiIiIiIiIikixt2bLF8nrz5s0Jcs6ff/6ZzZs34+TkxNSpU9Xc/DVSKCUiIiIiIiIiyVLck+v//fplBQUF0bdvXwCGDBlCzpw5X/mc8nQKpUREREREREQk2TGZTPGCqG3bthEdHf1K5xw9ejTXr18nT548DBw48FVLlP+gUMoKQkNDn2u/O3fu8Ndff/3nfkaj8T/3mT17Nn///TcAf//9NyNGjHiuGuLExsYSHh7+WOO4qKgooqKiLO+f994OHDjA3bt3X6gGERERERERkThnz57l8uXLODg44O3tTWhoKH/++edLny8gIIApU6YAMHHiRBwdHROqVHkKNTq3gqZNm+Ln58eMGTP47rvvuH//vmVb1qxZad++PQC7d+9m/Pjx7Nq1iyNHjpAxY0bSpUv32Pm6detGtmzZyJw5M3fv3qV///7xtt+9e5dPPvmEdevWAeDn58ePP/5Iw4YNefPNNx87348//sigQYPw9PQE4P79+3z22WeMHz8eJycn/P39SZs2LZ6enkRGRtKtWzd69+7Nnj17aN68OT/88ANp06Zl1apV8c7btm1bsmfPDsD777/P+PHjqV69+kt/jiIiIiIiIpJ6xY2SKl++POnSpeOnn35i06ZNlC9f/qXON3ToUCIjI6lcuTL16tVLyFLlKTRSygoWLFjAuXPnWLlyJV9//TWOjo74+voSGBjIokWLOHfuHN9//z2Ojo7Y2Zlzwx9//JHatWsTFhb22PkcHByIjo6mUqVKjB07ll9//TXe9gEDBhAaGkqnTp0oXLgwVatWxcbGhvbt21O4cGEKFSpE7ty52b9/PwCtW7fm8uXLHD16lFatWlG4cGE6derEmTNnOHLkCEWLFuX777/n0KFDnDx50vKkg7Jly/LFF18waNAgjh07xvr16/H19cXX15eFCxdy7do15syZw4ULF+Ldm4iIiIiIiMiLigulqlevbhnw8LJ9pQ4ePMiiRYsAmDBhgpqbJxKFUlaQIUMGtmzZQqtWrbC3t6d58+Y4OztTrVo1bGxsCAgI4Icffoh3zJgxY8iZMydr164lIiIi3pQ9JycnAHLkyMHq1aupWLGiZduyZcuYN28ec+bM4dixYxw7dow+ffqQL18+y/tjx45x5swZSpYsaTlu3bp1TJ48mW+//Zb58+fHC5Bu3rxJ7ty5n3hvnTp1Ys+ePTg7O5MvXz6KFClCiRIlyJgxIzY2NixYsICrV68myOcoIiIiIiIiqVNsbKzlyXuPhlJ79+4lKCjohc5lMpkYOHAgJpOJd999lxIlSiR4vfJkGqqSyO7du8fnn3/O2LFjcXFxAcDGxoa2bduyfPlyy/t/p7IGg4GffvoJk8lEhgwZcHZ2xtbWFjA/HSAmJoZ58+ZhNBpp1KgR06ZNw9/fn/fff598+fLRoUMHOnbsCJj/wJlMJkvQFBsby4oVK2jcuLHletmyZeO9996jUaNGZMiQAYBevXqxbt067t+/T506dTAYDAQFBREQEADAN998Q65cuahRo4blPL/99huBgYGW90+6NxEREREREZEX8ddffxEYGIinpydvvvkmtra25MqVi/Pnz7Njxw7q16//3Odat24dmzdvxsHBgdGjR7/GquXfNFIqkdnb23P+/HmqVKliCWtsbW2xtbXF3d39icesWLGC5s2bU6NGDW7cuMGdO3e4fPkyFy9e5OLFi3z55Zc0b96cixcvEhAQwLRp0wBzk7bu3btTpkwZ5s+fT0xMDDExMcyaNYuGDRta3oeEhFCzZk3L9SIjI8mbNy8bN26kePHilvVhYWF8+umnnDx5kuXLl3Po0CFu375t2Z4pUybeeecdFi5cGO9+PTw8EvIjFBERERERkVQubppe1apVLQM2XmYKX2xsLIMGDQLMAzHi+iBL4kiZoVRo6NOXiIjn3zc8/Pn2fQFubm6sXLmSJk2akCZNGsA8csne3t7yB+nfmjRpwtKlSzl//jyxsbHs2LEDFxcXPDw8cHZ2xsbGhsuXLz92XKVKlSwp75AhQyhevDjFixdn5MiRbN261fK+ZMmSzJgxw3LciBEjKFy4MJUqVeKzzz7Dz8+PFi1aWEZW/f7775ZHYz5ac8OGDVm2bFm8P8SxsbE4Ozu/0GckIiIiIiIi8iyP9pOK8zKh1Lx58zh27BheXl58+umnCVuk/KeUOX3vYdjzRHXrwm+//fM+fXp4QvNwACpVgm3b/nmfPTvcufP4fibTC5VnZ2dH69atiY2NJTY2ltu3b5M2bVrAHOI8i4ODA+XLlycwMBBnZ2fCwsIICAhg2LBhTz0mJCSEAgUKkD9/fgCOHz/OhQsXqFy58sPyTVy4cIGQkBDSpEnD2LFjGTt2LJ988gm+vr7cunWLtGnTcvbsWQBCQ0PJmTPnE69VtWpVzp49y61btwC4deuW5Ql//3VvIiIiIiIiIv8lLCyM3bt3A/FDqSpVqmAwGDh+/DjXr18nY8aMzzxPaGgow4cPB8xP3vPy8np9RcsTpcxQKhno168fRYoUITw8nBMnTpApUyYASxPzZwU4NjY22NiYB7k5ODiQO3duYmJiOHPmDHnz5n1s/4YNG5I9e3bLts6dOzNx4kRKly4NmEOpyMhIS4+rf+vYsSNeXl4MHToUgNu3b1vqfbThOsDRo0d5++23GT9+PAAnTpywzOV9nnsTEREREREReZZdu3YRFRVFlixZ4j2EK23atLz55pv89ddfbN68mbZt2z7zPJMmTeLatWvkyJGDHj16vO6y5QlSZigVEvL0bf+eIvdwRM8T2fxrduPFiy9d0qMCAgJYt24dU6ZMoUePHowdO5Zy5cqRPn16NmzYwM6dO4mKinrisbdv32bcuHFMnDgx3vpy5cqxadMm8ubNS2xsLLa2tly6dIkaNWrg4OAQb98LFy5w8uRJXF1dLeuioqJo2LAhEyZMeOyacWFWZGQkAPv27aN79+7x1sWZOnUq7du3p0OHDtSpU4cCBQrg6+vLihUrcHd3Z9iwYU+9NxEREREREZH/8ujUvX8/SKt69er89ddfbNq06Zmh1M2bN/nqq68A89PuHR0dX1/B8lQps6eUq+vTFyen59/3372QnrbfCxo5ciSdOnXCz88POzs75s6dS/HixSlfvjzHjh2jZs2alqGIjzIajdSuXRuj0cilS5cwmUx06NCB+fPn07FjR+bPn09wcDD16tUjPDycbNmycebMGY4dO8aaNWtIly4dDRs2pGjRoowePRpnZ2caNWrEjh07OHPmzBMDqUd9/PHH5MyZk927d5M5c2YOHTrE6dOnLdvPnTvHzz//zODBg3F2dub777+ncuXKTJs2ja5du2Jra8sff/wR7+l8IiIiIiIiIi/iSf2k4jzaV8r0jFY7Q4YMISQkhJIlS9KyZcvXU6j8p5QZSiVhW7ZsYcGCBQwYMACA/v37U758eTp06MBXX31Fq1atCA4OJjo6muvXr1tS34CAAK5du0bv3r3JkiULRYsWpW3bttSuXZsGDRrQoEEDbt68Sd++ffnjjz9wdHTk3r17TJ8+ndq1a1OhQgW6dOnCmDFjMJlM+Pr6snPnTuzs7MifPz/Vq1dnypQpgDn8evDgAbdv37Y0NwdwdnamW7dujB49mvDwcFq0aMGGDRsA8xTA999/n+bNm+Pn58fx48eZPHkyI0eOZPr06Rw+fNjSTD04OJgHDx48lmiLiIiIiIiIPMudO3c4ePAgANWqVXtse7ly5XB0dOTq1avxBlE86o8//uC7774DzFP49Lup9aTM6XtJ2BtvvMGkSZPInj07e/bsYePGjfzxxx8AdOnShdOnTxMWFsaiRYvo1asXo0aNAiBLliysXr2aunXrEh4eTo8ePXD616iv7777jvr169O+fXtsbGxwcHBgw4YN1K9fn+XLl1t6RkVHRxMcHIyTkxOff/45AwcOZMWKFXh7e1vOVapUKVxdXRk8eDAA48ePZ9KkSYwcOZIuXboA5ml8derUISQkhEGDBtGrVy/LVL/Bgwfz4Ycf8tZbbwHwzTffcOnSJYxGI/ny5cPZ2ZkCBQq8xk9aREREREREUpotW7YAUKRIETJkyPDYdmdnZ8qXL8/mzZvZtGmT5YFfcWJiYiztaDp37ky5cuVef9HyVAbTs8azJQFBQUF4eHgQGBiIu7t7vG0RERH4+/uTI0eOxwKa5CIiIuKJtUdERBATE0OaZz1J8AmCgoJwc3NL8KT38uXLgDkce1RwcDBGoxEPD49466Ojo7G1tbU0ZH/U3bt3LU8bfJqU8L0VERERERGRhNW1a1dmz55N3759H+u1HOfLL79k8ODBNGrUiJUrV8bbNmXKFPr06YO3tzenT5/Gx8cnEapOOZ6V0byMF56+t2rVKnLmzImdnR3Fixfn5MmTABw7doySJUvi5eXFwIED483d3L59OwUKFMDHx+epPzSp1dMCFycnpxcOpADc3d1fy9DDLFmyPBZIAbi5uT0WSAHY29s/MZAC/jOQEhEREREREXmSZ/WTihO3bevWrcTExFjWX7t2jWHDhgHm4EqBlPW9UCh1/vx5OnXqxJdffsnVq1fJmzcvXbp0ITIykgYNGlCiRAkOHDjAiRMnmDdvHmB+WlzDhg1p3bo1e/bs4YcffmDr1q2v415EREREREREJIW6cOEC/v7+2NnZUbFixafu98Ybb+Dl5UVQUBAHDhywrO/fvz/BwcGULl2a9957LzFKlv/wQqHUyZMn+fLLL3nnnXfIkCED3bt35+DBg6xdu5bAwEAmTpxIrly5GDNmjKVp2A8//ECmTJkYNmwYefLkYfjw4ZZtIiIiIiIiIiLPY/PmzQCULVv2mTOLbG1tqVq1KvDPyKpNmzaxZMkSbGxsmDlz5lNn9kjieqHvQv369enatavl/enTp8mTJw+HDx+mTJkylkbaRYsW5cSJEwAcPnyYKlWqWKaUlSpVir/++uup14iMjCQoKCje8l+SeFsseQn6noqIiIiIiMijnmfqXpy4fTZt2kRkZCQ9evQA4KOPPuKNN954fUXKC3npaDAqKor//e9/dOvWjaCgIHLkyGHZZjAYsLW15f79+49tc3d359q1a08979ixY/Hw8LAsT+pjFMfW1tZSi6QsYWFhgLk3lYiIiIiIiKRuRqPRMlLqeUKpGjVqAPDHH3/w+eefc+bMGXx9fRk5cuRrrVNejN3LHvjZZ5/h6upKly5dGDp0KI6OjvG2Ozk5ERYWhp2dXbxtceufZvDgwfTr18/yPigo6KnBlJ2dHS4uLty+ffuZjbUl+TCZTISFhXHr1i08PT0twaOIiIiIiIikXocPH+bu3bu4ublRsmTJ/9w/Z86cZM+enYsXLzJ27FgAJk6c+MQHdYn1vFQotWXLFmbMmMHevXuxt7fH29ubY8eOxdsnODgYBwcHvL29uX379mPrn8bR0fGxgOtpDAYDGTNmxN/fn0uXLr3MrUgS5enpia+vr7XLEBERERERkSRg48aNAFSuXPm5ZtQYDAaqV6/OnDlzAKhatSqtWrV6rTXKi3vhUMrf35/WrVszY8YMChYsCEDJkiWZPXt2vH0iIyPx9vamZMmSLF682LLt4MGDZM6cOQFKN3NwcCBPnjyawpeC2Nvba4SUiIiIiIiIWMSFUtWqVXvuY+JCKXt7e2bMmGHpdS1Jh8H0Ah2lw8PDeeuttyhXrhwTJ060rHd0dCRz5sx89dVXdOrUiffff58bN26wZs0a7ty5Q5YsWVizZg2VKlWiYcOG5M6dm2nTpj3XNYOCgvDw8CAwMBB3d/cXv0MRERERERERSbbCw8Px8vIiMjKSEydOUKBAgec6LjIykt69e1OxYkXefffd11xl6pDQGc0LhVKrVq2icePGj6339/fnyJEjtG7dGmdnZ2xsbNi2bZtlJNU333xDr169SJMmDZ6enuzZs4cMGTI81zUVSomIiIiIiIikXhs3bqRmzZpkzpyZy5cva8STFSV0RvNC0/caNWrE0zKs7Nmzc/78ef766y/KlClD2rRpLdu6detGrVq1OHXqFBUqVCBNmjSvVrWIiIiIiIiIpApxU/dq1KihQCqFeemn7z2Jr68v9erVe+K2HDlykCNHjoS8nIiIiIiIiIikcI+GUpKy2Fi7ABERERERERGRJ7l58yaHDh0CzI3LJWVRKCUiIiIiIiIiSdLmzZsBKF68OOnTp7dyNZLQFEqJiIiIiIiISJKkqXspm0IpEREREREREUlyTCaTQqkUTqGUiIiIiIiIiCQ5p06d4urVqzg6OlK+fHlrlyOvgUIpEREREREREUly4kZJVahQAWdnZytXI6+DQikRERERERERSXI2bNgAQM2aNa1cibwuCqVEREREREREJEmJiopi27ZtgPpJpWQKpUREREREREQkSdm7dy+hoaGkS5eOokWLWrsceU0USomIiIiIiIhIkhLXT6p69erY2Ci6SKn0nRURERERERGRJCUulNLUvZRNoZSIiIiIiIiIJBn3799n//79gEKplE6hlIiIiIiIiIgkGVu2bMFoNFKgQAH8/PysXY68RgqlRERERERERCTJ0NS91EOhlIiIiIiIiIgkGQqlUg+FUiIiIiIiIiKSJFy4cIELFy5gZ2dHpUqVrF2OvGYKpUREREREREQkSYgbJVW2bFnc3NysXI28bgqlRERERERERCRJ2LBhA6Cpe6mFQikRERERERERsbrY2Fi2bNkCQM2aNa1cjSQGhVIiIiIiIiIiYnUHDhzgwYMHeHp68tZbb1m7HEkECqVERERERERExOo2bdoEQJUqVbC1tbVyNZIYFEqJiIiIiIiIiNVt3rwZUD+p1EShlIiIiIiIiIhYVVhYGLt37wagWrVqVq5GEotCKRERERERERGxql27dhEVFUWWLFnIkyePtcuRRKJQSkRERERERESsKq6fVPXq1TEYDFauRhKLQikRERERERERsaq4flLVq1e3ciWSmBRKiYiIiIiIiIjV3Llzh4MHDwJQtWpVK1cjiUmhlIiIiIiIiIhYzdatWzGZTBQuXBhfX19rlyOJSKGUiIiIiIiIiFjNo/2kJHVRKCUiIiIiIiIiVqNQKvVSKCUiIiIiIiIiVuHv78+FCxews7OjYsWK1i5HEplCKRERERERERGxirin7pUuXRo3NzcrVyOJTaGUiIiIiIiIiFiFpu6lbgqlRERERERERCTRGY1Gy0gphVKpk0IpEREREREREUl0R44c4c6dO6RJk4bSpUtbuxyxAoVSIiIiIiIiIpLo4kZJVaxYEXt7eytXI9agUEpEREREREREEp36SYlCKRERERERERFJVJGRkezYsQNQKJWaKZQSERERERERkUS1d+9ewsLCSJ8+PYULF7Z2OWIlCqVEREREREREJFE9+tQ9g8Fg5WrEWhRKiYiIiIiIiEiiiusnVa1aNStXItakUEpEREREREREEk1gYCD79u0D1E8qtVMoJSIiIiIiIiKJZvv27cTGxpInTx6yZs1q7XLEihRKiYiIiIiIiEiiiZu6p1FSolBKRERERERERBKFyWRi7dq1gPpJiUIpEREREREREUkkBw8e5Ny5czg7O1OrVi1rlyNWplBKRERERERERBLFTz/9BEC9evVIkyaNlasRa1MoJSIiIiIiIiKvnclk4ueffwagZcuWVq5GkgKFUiIiIiIiIiLy2u3bt4+LFy/i6upK3bp1rV2OJAEKpURERERERETktYsbJdWgQQNcXFysXI0kBQqlREREREREROS1MhqNmronj1EoJSIiIiIiIiKv1Z49e7hy5Qru7u7Url3b2uVIEqFQSkREREREREReq7in7jVq1AgnJycrVyNJhUIpEREREREREXltYmNj+eWXXwBN3ZP4FEqJiIiIiIiIyGuzc+dObty4gZeXFzVq1LB2OZKEKJQSERERERERkdcmbupekyZNcHBwsHI1kpQolBIRERERERGR1yImJoZly5YB8M4771i5GklqFEqJiIiIiIiIyGuxbds2bt++Tdq0aalataq1y5EkRqGUiIiIiIiIiLwWcVP3mjVrhr29vZWrkaRGoZSIiIiIiIiIJLjo6GiWL18O6Kl78mQKpUREREREREQkwW3atIl79+6RIUMGKlWqZO1yJAlSKCUiIiIiIiIiCS5u6l7z5s2xtbW1cjWSFCmUEhEREREREZEEFRkZycqVKwFN3ZOnUyglIiIiIiIiIglq/fr1BAYGkilTJsqVK2ftciSJUiglIiIiIiIiIgnGZDIxfvx4AN555x1sbBQ9yJPpJ0NEREREREREEszy5cvZtWsXzs7O9O/f39rlSBKmUEpEREREREREEkRkZCSDBg0CYODAgfj5+Vm5IknKFEqJiIiIiIiISIKYOnUqFy5cIGPGjAwcONDa5UgSp1BKRERERERERF7ZrVu3+OKLLwAYM2YMadKksXJFktQplBIRERERERGRV/bZZ58RFBTEm2++Sfv27a1djiQDLxxK3blzhxw5cnDx4kXLul69emEwGCxL7ty5LduOHTtGyZIl8fLyYuDAgZhMpgQpXERERERERESShuPHj/Ptt98CMGnSJD1xT57LC/2U3Llzh/r168cLpAAOHDjAb7/9xv3797l//z4HDx4EzA3OGjRoQIkSJThw4AAnTpxg3rx5CVW7iIiIiIiIiCQB/fv3x2g00rRpUypWrGjtciSZeKFQqlWrVrz77rvx1sXExHD8+HEqVqyIp6cnnp6euLm5AbB27VoCAwOZOHEiuXLlYsyYMXz33XcJV72IiIiIiIiIWNXatWtZv3499vb2fPXVV9YuR5KRFwqlZs+eTa9eveKtO3r0KEajkeLFi+Ps7Ezt2rUJCAgA4PDhw5QpUwYXFxcAihYtyokTJxKodBERERERERGxpujoaPr37w+YW/s82s5H5L+8UCiVI0eOx9adOHGCfPnysXDhQo4cOYKdnR1du3YFICgoKN4xBoMBW1tb7t+//9RrREZGEhQUFG8RERERERERkaTn22+/5eTJk/j4+DB06FBrlyPJjN2rnqBNmza0adPG8v7rr78mR44cBAUFYWdnh6OjY7z9nZycCAsLw8vL64nnGzt2LJ9//vmrliUiIiIiIiIir9GdO3f47LPPAPj888/x9PS0bkGS7CR4O/z06dNjNBq5fv063t7e3L59O9724OBgHBwcnnr84MGDCQwMtCyXL19O6BJFRERERERE5BUEBwdTt25d7t69S4ECBSwzpkRexCuHUgMHDmTx4sWW93v27MHGxoYsWbJQsmRJ9uzZY9nm7+9PZGQk3t7eTz2fo6Mj7u7u8RYRERERERERSRoiIiJo1KgR+/fvJ23atCxduhQ7u1eeiCWp0Cv/1BQrVoyhQ4eSIUMGYmNj6dmzJ+3bt8fFxYWKFSsSFBTE3Llz6dSpE2PGjKF69erY2tomRO0iIiIiIiIikohiYmJo1aoVW7duxc3NjXXr1lGwYEFrlyXJ1CuHUm3btuX48eM0a9YMW1tb2rZty5gxY8wnt7Njzpw5tG7dmoEDB2JjY8O2bdte9ZIiIiIiIiIiksiMRiOdO3dm1apVODo6snr1at566y1rlyXJmMFkMple90Vu3LjBX3/9RZkyZUibNu0LHRsUFISHhweBgYGayiciIiIiIiJiBSaTid69ezNt2jRsbW1ZsWIFDRo0sHZZksgSOqNJlEmfvr6+1KtXLzEuJSIiIiIiIiIJbMSIEUybNg2AefPmKZCSBJHgT98TERERERERkZRjypQpjBw5EoDp06fTtm1bK1ckKYVCKRERERERERF5oiVLltCnTx8ARo0aRY8ePaxbkKQoCqVERERERERE5DFbt26lQ4cOAHz00UcMGTLEyhVJSqNQSkRERERERETiOXr0KI0bNyYqKopmzZoxefJkDAaDtcuSFEahlIiIiIiIiIhYXL58mTp16hAUFESFChVYtGgRtra21i5LUiCFUiIiIiIiIiICwP3796lTpw5Xr16lYMGCrFq1CicnJ2uXJSmUQikRERERERERISIigsaNG3P8+HEyZcrE2rVr8fLysnZZkoIplBIRERERERFJ5YxGI+3atWPHjh24u7uzdu1asmbNau2yJIVTKCUiIiIiIiKSyn388ccsXboUe3t7Vq5cSdGiRa1dkqQCCqVEREREREREUrF58+YxYcIEAObPn0+VKlWsXJGkFgqlRERERERERFKp3bt388EHHwAwfPhwWrdubeWKJDVRKCUiIiIiIiKSCgUEBNC0aVOioqJo1qwZn332mbVLklRGoZSIiIiIiIhIKhMSEkLDhg25desWxYoVY/78+djYKCKQxKWfOBEREREREZFUxGg00qFDBw4fPkz69OlZvXo1rq6u1i5LUiGFUiIiIiIiIiKpyIgRI1i+fDkODg6sWLGCrFmzWrskSaUUSomIiIiIiIikEj/99BOjRo0C4Ntvv+Xtt9+2ckWSmimUEhEREREREUkFDhw4QMeOHQEYMGAAHTp0sG5BkuoplBIRERERERFJ4a5du0ajRo2IiIigbt26fPnll9YuSUShlIiIiIiIiEhKFh4eTuPGjbl27RoFChTgxx9/xNbW1tpliSiUEhEREREREUmpTCYTXbp0Yf/+/Xh7e7N69Wrc3d2tXZYIoFBKREREREREJMX66quvWLx4MXZ2dixdupTcuXNbuyQRC4VSIiIiIiIiIinQqlWr+PTTTwGYOnUqVapUsXJFIvEplBIRERERERFJYY4cOUKbNm0wmUx8+OGHdO/e3doliTxGoZSIiIiIiIhICnL79m0aNmxIaGgoVatWZfLkydYuSeSJFEqJiIiIiIiIpBBRUVE0a9aMS5cukStXLn755Rfs7e2tXZbIEymUEhEREREREUkB4qbq7dy5E3d3d9asWYO3t7e1yxJ5KoVSIiIiIiIiIinA1KlT+e6777CxsWHJkiUUKFDA2iWJPJNCKRERERERESszmWD3bti0ydqVSHK1fv16+vXrB8D48eOpU6eOlSsS+W8KpURERERERKwkOBi++QaKF4fy5aFvX3NABRAbC717w4EDVi1RkoHTp0/TsmVLjEYjHTt2pG/fvtYuSeS5KJQSERERERFJRP7+l3j//a8pUGAz3t7hdO8OR46AjU0k9++vp0GDlvzyyy+sWhXL1KlQsiRUqgSrVpmDKpFH3b9/nwYNGhAYGMjbb7/NN998g8FgsHZZIs/FYDLF5fBJU1BQEB4eHgQGBuLu7m7tckRERERERF5ITAyAib17dzN58mSWLSsNDHxkjzPA18B84IFlrZ9fTTJmnMrBg3mJiTGHDPnywbffQsWKiVW9JGUxMTHUqVOHTZs2kTVrVvbt20eGDBmsXZakYAmd0dglQE0iIiIiIiLyiMBAWLQIVq82smNHDFmy9ODs2TkPt5qwsQmnQIEAypc/SoEC13FwyIe9/Xjs7e05e/YsM2fO5MqVDVy5kh8vr8IUKjSTo0ff5vRpGypXhp494auvwMnJmncp1tavXz82bdqEi4sLq1evViAlyY5CKRERERERkQRy7BjMmAELF5oIDTVg7pjiwNmz6XFycqJdu3Z0796bwoWdsbfPB+R74nkGDx7M3Llz+d///sfFi8fYtasCTk4ZKFBgGSdPluOPP8BOv82lapMnT2batGkALFq0iGLFilm5olQoJgYOHoStW2H7dnMa7eRkXlxd4aefrF1hkqfpeyIiIiIiIq/o2jVo3Rp27Hh07QlgDunSHaV372p88EEXfHx8Xui8MTExLFu2jPHjx/PXX38BYDDUoUaN/HzxRWtKlixJZKS515SLS4LdjiRxP//8M61atcJkMjF27Fg++eQTa5eU8plMEBQEbm5g87A9d8WKsHPnk/d3cYHQ0MSrL5EkdEajUEpEREREROQV3b59n9y5DQQFpQFWAjPIl+8GQ4cOoWXLltjb27/S+U0mE1u3bmX8+PGsW7fOsr5y5cqkSzeHgwdzMnOmgerVX+kykgxs376dmjVrEhUVRY8ePZg2bZoamyekP/6ADRvg6lW4eTP+EhEBN25A3DTJAQNgzhzzkwiqVIGsWSEy0ryf0QjvvWfde3kNFEqJiIiIiIgkATdugLd3NOPGfcW4ceMIDi4OXKBQIU+GDRtG8+bNsbW1TfDrHjlyhAkTJvDjjz8SE2OPeURWdgCqV49l3Dhb3ngjwS8rScDRo0epUKECgYGBNG3alJ9//vm1/IylGoGBsGsX1KwJccFxt24wa9bTjzlyBIoUMb8ODjaPiEpF3wOFUiIiIiIiIlZ24ADUrBmLq+v3XLnSFYAiRYowfPhwmjZtik3c9J7X6PLly0yePJlZs5YQGjoI6A44ANC4cQgTJ6YhR47XXoYkksuXL1O2bFmuXr1K+fLl2bBhA87OztYuK/mIiIDDh2H/fvNy4ACcPGmelrdnD5QpY97vt9/g558hd27ziKhHl/Tpzb2iUjGFUiIiIiIiIla0bRvUrRtNeLg9sB939/rMmPE/3n333UQJo/7twYMHzJ49m8mTV3PtWjegDQA2NtEMHXqKESMKa3pXMnf//n0qVKjA8ePHKVCgALt27cLb29vaZSUNRiNcuABXrsCDB+bRTw8eQPXqUKiQeZ+FC6FzZ3Nj8n/LnRumToU6dRKz6mRLoZSIiIiIiIiV/PxzOK1b22E02gNbKF16LEuWzCZ79uzWLo3Y2Fh+/fVXRo9ey/79zYC3gdwUKpSWMWPGUKtWAxwdFU4lNxEREdSqVYsdO3aQKVMm9uzZQ9asWa1dlnWYTBAXsG7bBp98AkePQljY4/t++y28/7759a5dUKEC+PhAyZLxl7j+UPJcEjqj0UNERUREREREnsOoUf4MH54F869Rq/j00yN8/vla7OySxq9Vtra2NGrUiEaNGnHixAnGjPmSlSuDOX78Bo0aNSJt2kMULpybgQNdqVPnnweISdIVGRlJ69at2bFjB+7u7qxduzZ1BFIxMXD2rHm63ZEj/3wdORI6dTLvY2cHf/5pfu3kBNmygZcXeHqCh4f5fZySJeHiRXMjco0aTFI0UkpEREREROQ/dO68k7lzKwDg4rKUNWvSU7VqRStX9d8CAwP58ssvmTDhF2JizgDmJCpHDiM9etjQqRNoFljSFBISQtOmTdm4cSMODg6sW7eOKlWqWLus1+vwYeja1RxARUQ8vr1PH5g0yfw6JAR+/x2KFjVPwUsi4XBKp+l7IiIiIiIiiWjTpk3UrPk3JlNfcubcyJ9/lsHHJ3klOadPn6ZLl7Hs2lUYeA/wAsDNzcTs2QZatrRqefIvd+/epW7duuzbtw9XV1dWrFhBjRo1rF1WwjGZzE3GN24EPz9o1sy8/uZN8PU1v3Z1NT/lrmhRKFbM/LVoUVAuYFUKpURERERERBLJuXPnKFWqFPfv36dZs/789NN4bG2T5/Qfk8nEqlWr6NXrEy5fLgf0BooCsGwZNG1q1fLkoStXrlCzZk1OnjyJt7c3a9eupVSpUtYuK2FERMAPP8DkyXDsmHldtWqwadM/+6xYAYULQ65cmmOaBCV0RqPvsIiIiIiIyBMEBQXRqFEj7t+/T+nSpVm06ItkG0gBGAwGGjduzKlTfzN8uB8ODmWBMRgMezh5chzR0dHWLjHVO3PmDOXKlePkyZNkzpyZnTt3poxA6uZN+Owzc0+nLl3MgZSTE9SsCY0axd+3SRPIk0eBVCqhkVIiIiIiIiL/YjQaady4MWvW+JAu3WUOH15AxowZrV1Wgjp//jzdunVj06ZtQAzFihVj5sw5hIe/RdWq1q4u9fn777+pXbs2t2/fJm/evGzYsIFsjzbrTs5q1PhnNFTWrNCzpzmc8vS0alny4jRSSkRERERE5DUbPnw4a9YcAmZy+/ZG7txJWYEUQK5cudiwYQPz53+Ht7c3hw8f5u23t1GtGvTsGYUGTiWeHTt2ULlyZW7fvs2bb77Jzp07U04gBdCjB5QpAz/9BOfPw4ABCqQEUCglIiIiIiISz88//8zo0aOBYYAjlSubW9ykRAaDgfbt23Pq1CnefbeNZf3MmQ9YuPA3kvjEmhRh27Zt1KlTh+DgYCpVqsTWrVtJnz69tct6NSYTHDpk/grmKXp79sA77+gpeRKPQikREREREZGHDh48SMeOHYHcGAydARg9GgzJt5XUc0mXLh0//LCIdeuKkD59V2JjW/Dee/UpVaoUGzZsUDj1mmzdupV69eoRFhZG7dq1Wbt2bcpoW7N3L7zxBrz9NhiNKf8PkLw0hVIiIiIiIiLArVu3aNy4MeHh4WTMOAuTyZa6dc2/V6cWtWrV4sKFSQwdWhFXV1cOHDhArVq1qFy5Mrt27bJ2eSnKli1bLIFUnTp1WLFiBc7OztYuK2F89535a758alguz6SfDhERERERSfWMRiNt27YlICCArFnrcuNGFQC++MLKhVmBq6sro0aN4sKFC/Tt2xdHR0d27NhBhQoVqFu3LsdXrIB168wjYOSlbN68mfr16xMeHk7dunVZvnw5Tk5O1i4rYQQHw5Il5tfvvWfdWiTJUyglIiIiIiKp3vjx49m4cSPOzs7kzv0jJpOB5s3NM5BSq/Tp0zNx4kTOnTtH165dsbW1Ze3atexp2hTq1MFUsCDMmAEhIdYuNVnZtGmTJZCqV69eygqkAH7+GUJDIW9eKF/e2tVIEqdQSkREREREUrU9e/YwZMgQAKZOnUbDhu74+sLIkVYuLInw8/Nj1qxZD5uhv8ttIAgwnD4NH30Efn7Qvz9cuGDtUpO8jRs30qBBAyIiIqhfvz7Lli3D0dHR2mUlrLipe++9p15S8p8MpiTesS4oKAgPDw8CAwNTRsM3ERERERFJMh48eEDx4sW5dOkSrVq1YvHixRgMBqKjwd7e2tUlTUuWLKFXp060jIign709OaKjzRsMBujVCyZPtmp9SdWBAweoUKECERERNGjQgF9++SXlBVInTkChQmBrC1eugK+vtSuSBJbQGY1GSomIiIiISKpkMpno0qULly5dImfOnHz11TcYHo7sUCD1dK1atWLDnj2syZaNXNHRNHNy4maxYmAywaO/pAYFwQcfwIoV5tep2K1bt2jSpAkRERHUqVOHpUuXprxACv7pJVW/vgIpeS4KpUREREREJFX69ttvWbZsGXZ2dvTsuZZixTzYtMnaVSUPxYsX58CBA1SpWpXlERH4Hj7M5O7dMT7a2HrzZvj2W2jaFNKmhTJlYMAAWLkS7tyxWu2JLTo6mhYtWnDlyhXy5cvHjz/+iIODg7XLej0++wx+/x0+/dTalUgyoel7IiIiIiKS6hw9epRSpUoRERFB27ZrWLKkPjEx8M478NNP1q4u+YiJiWHQoEFMmjQJgPr167No0SI8PDzgyBGYMwfWr4czZx4/+OefoUUL8+s7dyAqyjy6xiZljZ3o1asX06ZNw83NjX379pE/f35rlyTy0hI6o1EoJSIiIiIiqUpoaCglS5bk5MmTFCo0lRMnPsJkMtC6Ncyfr6l7L2PhwoV07dqViIgI8ubNy6pVq+KHLxcvwq5dsHOn+euJE3D2LOTObd4+ZgwMGQIODpAtG2TPbl6yZTMvdeuCt7cV7uzVzJs3j06dOgGwatUqGjZsaOWKXiOjMcUFivK4hM5o7BKgJhERERERkWQhIiKCDz74gJMnT+LmNprjx3sC0K0bTJ9u7s8sL65du3YULFiQJk2acObMGUqVKsWiRYv+CWHiQqa2bc3v796NHzIFBpo//Kgoc1h19mz8Cxw79s/+M2fC4sWPh1fZs0PWrJBEejXt37+fbt26AfDZZ5+l7EAqIADefhvat4cvvlA4Jc9NI6VERERERCTFM5lMrFmzhr59+3LhwgXgK2AQYG5/88UXenp9Qrh16xYtWrRgx44dAIwYMYJhw4Zh8zwhRXQ0XL1qHlXl72/+eumSOfBYtQrc3Mz7ffCBuVfV0xw7Zn4CHMCff8Lly+YRWbly/XOO1+zWrVuUKFGCK1eu0LBhQ1asWPF8n0FyNXKkuZ9UlSqwZYu1q5HXSNP3RERERERE/sOJE+YHgYWFwa1b99m6dQ9XrswCVpMpkx958uxi+/ZsjB9v7r0tCSc6Opp+/foxffp0ABo1asSCBQsS7ve5kyfN/aouXfonuLp40byEhUFwMKRJY963WzeYNeufY11cIF26f5YFC8DHx7zt1CkICYEcOcyjsl4ypYyOjqZ69ers2LGDfPnysW/fvpT9u6zRCDlzmr8PixZBmzbWrkheI4VSIiIiIiIiz3DwIFSuDEFB8dfb2Ixl0KAghgwZgqNjGjZvhtq1rVJiqjB37ly6detGVFQUBQoUYPHixRQvXvz1XdBkMk8LjAuZAL76yjzK6tw5uH378WOCgv4ZPdW1K8yebX6dJs0/UwMzZYL06WHQoH/2vXXLfD0fn3hzPmNjY+nevTuzZ89OPY3NN26EmjXB0xOuXQNnZ2tXJK+RQikREREREZGnOHMGypc3cfu2AXv7Q0RHbwQiyZMnG8OHV6FtWz9rl5iq/PnnnzRt2pRr164BUK1aNfr27UudOnUSfzpbcLA5TLp927zcuQMdO/4zIqp3b/MTAW/cePLxISHg6mp+/d578P335t5J6dKBry9R3t5sP3WKg9ev8wWwKK6x+f375j5XLi6v795iYyE0NP5SuPA/gdm9exAebq7DwcFct8lkHuVkNIK7+z/7hoSYjzeZzAuYv9rZgYfH4z27WrY0f249epgbs0mKplBKRERERETkCaKjo6lT5wybNxcC/gaqkCtXOiZPnkz9+vWtXV6qdePGDXr37s3SpUsxGo0A5M2bl969e9OhQwdc44KepCIiwtzHKm5K4I0b5lBn8uR/9nn3XfP80Kf8Or1swQKatWtnfhMXYHl6mkddpU1rfu3hYV6++uqfsOvXX809sSIiIDLS/DUiwjwtMSTEfM24x0P26WOeLhcSYt73327f/mfUWO/eMHXq0+/5wgXztEWAjz+GceOevu/+/fDWW+bX33wD3bubXx88CK9zJJwkCXr6noiIiIiIyCOio6OZP38+Y8aMwd//EvAZadMu4ZNPhvHRRx/h5ORk7RJTNV9fX3766ScuXbrE9OnTmT17NmfOnKFHjx4MGTKE999/n7Zt21KkSBEMSaHbvJMT5M1rXp5m8WJzP6o7d1g7dy4zR4zAKyqKgt7edG7U6J9ACswjsgAePDAv/zZhQvzz/vjj068bHPzPUwgjIszTFR9lMJgDLlfXx4MqOzuIiXnyeR+GhY+dK+6rwWAejQXmIC3O5cvmr6VKKZCSl6KRUiIiIiIikiwZjUbmzp3LyJETCAg4DZhInz49gwYNolu3bklvBI4AEBwczPz585kyZQrnzp2zrM+ZMydNmzalSZMmlClTJsk/rS4mJoZPPvmE//3vfwDUqlWLxYsX4x0XGsUxmcxh0rVr5qcL3rsHgYHmJSgIPv/8n31nzTI/MdDJybw4Opq/Ojub+1m1b//PqKrLl83Hp0nzTxDl5PTsBu1GI0RFmb/a2JgXg8EcWP1XIBgba74PN7d/pvodPQrHj0OlSpAx4wt+gpIcafqeiIiIiIikemFhYXTs2JFfflkN/I6T021GjbrOhx92xeV19u6RBGM0Gvntt9+YM2cOGzZsICIiwrItY8aMNGrUiA4dOlC6dOmkMYIKCA8P5+TJkxw/fpy5c+eydetWAAYPHsyoUaOwfaTpuUhKpFBKRERERERStevXr9OoUSP27z+MwfATJlNj0qQxsX+/gZT+oLOUKiQkhHXr1rFixQp+/fVXgh55dGLx4sXp1q0bbdq0IU2aNIlST2xsLGfPnuXQoUMcPXqU48ePc+zYMS5cuMCjv0K7uroyb948mjdvnih1iVibQikREREREUm1Dh06RIMGDbhyxQtb28XExhbG0RHWroUqVaxdnSSEyMhItm7dyuLFi/n555+JfNgbyc3NjbZt29KtWzeKFi2aoNc7cuQIBw8e5ODBgxw6dIgjR44QFhb2xP19fHwoVKgQRYoU4cMPP6RAgQIJVotIUqdQSkREREREUqXVq1fTunVbwsK6YTB8gcnkgI+P+QFktWpZuzp5He7du8f8+fP55ptvOHPmjGV9mTJlaNy4MXXr1qVw4cIvNL0vNjaWQ4cOsWnTJjZv3szOnTvjTR2M4+LiQtGiRSlatCiFCxemUKFCFC5cmPTp0yfIvYkkRwqlREREREQkVTGZTEycOJGBAwdiMs0BOgPQoAHMng0ZMli3Pnn9TCYTW7du5ZtvvmHFihXEPPIUOT8/P+rWrUvdunWpVq0aadKkISYmhnv37nHnzh3u3r3L3bt3uXLlCtu2bWPLli3cv38/3vl9fHx48803KV68OG+88QbFixcnT5486hEl8i8KpUREREREJNUIDQ2ld+/efPfddwA0aTKWbds+ZsIEA506/fcDwyTluXHjBsuWLWPt2rVs2bKF8PBwyzZ7e3tcXFwIDAx85jnc3NyoUqUK1atXp1q1ahQoUCDJNFMXScqsHkrduXOHkiVLsnXrVrJnzw7AsWPH6NSpE+fOnaNLly6MGzfO8gd6+/btdOvWjdu3b/Ppp5/Sr1+/FypQoZSIiIiISOq0e/cfvPPO11y7lhYbm+lMnDiRXr16ERJiwM3N2tVJUhAeHs62bdv4/fff+e233/D394+33cvLi7Rp0+Lj44OPjw+lS5emevXqvPXWW9jZ2VmpapHky6qh1J07d6hfvz5//vkn/v7+ZM+encjISPLnz0+tWrUYOHAgvXr1onnz5nTq1Inbt2+TO3du+vfvT+vWrWnVqhUTJkygygt0IFQoJSIiIiKSujx4EEnr1mtYty4PUAyIYsmSvbRsWdHapUkSZjKZ8Pf3JyoqirRp0+Ll5aXgSSSBJXRGY/MiO7dq1Yp333033rq1a9cSGBjIxIkTyZUrF2PGjLEMrf3hhx/IlCkTw4YNI0+ePAwfPtyyTURERERE5FFXr8J7793AxyeMdeuaA8Wws4ukc2cTFSookJJnMxgM5MyZk/z585MuXToFUiLJwAuFUrNnz6ZXr17x1h0+fJgyZcrg4uICQNGiRTlx4oRlW5UqVSxT+UqVKsVff/31zGtERkYSFBQUbxERERERkZRt5sxYsmaN5fvvfYmN9cLG5jIdOhzj1i1HvvvOkUyZrF2hiIgktBcKpXLkyPHYuqCgoHjrDQYDtra23L9//7Ft7u7uXLt27ZnXGDt2LB4eHpYlS5YsL1KiiIiIiIgkM/v27ePrr2thNF4HdlCq1DguX3Zk3rzCeHlZuzoREXldXiiUehI7OzscHR3jrXNyciIsLOyxbXHrn2Xw4MEEBgZalsuXL79qiSIiIiIikgQdPhxI9+7dKVOmDMeObcbD420WLgxg796BZMqU3trliYjIa/bKk2y9vb05duxYvHXBwcE4ODjg7e3N7du3H1v/LI6Ojo+FXCIiIiIiknIYjSY6d97H/PlvAEGAiXbt2jF+/HgyZMhg7fJERCSRvPJIqZIlS7Jnzx7Le39/fyIjI/H29n5s28GDB8mcOfOrXlJERERERJKpQ4dOkTnzOubPLw044OHRnK1bt7JgwQIFUiIiqcwrh1IVK1YkKCiIuXPnAjBmzBiqV6+Ora0tDRs2ZPfu3WzatIno6GjGjRtHrVq1XrloERERERFJfr7++hfefDOEGzfqALHUrr2FmzfrUblyZWuXJiIiVvDK0/fs7OyYM2cOrVu3ZuDAgdjY2LBt2zYAfHx8mDRpEnXr1iVNmjR4enoyb968V72kiIiIiIgkIyaTidGjJzNs2NvAW9jbB/Ldd2G0a1fV2qWJiIgVGUwmkykhTnTjxg3++usvypQpQ9q0aeNt8/f359SpU1SoUIE0adK80HmDgoLw8PAgMDAQd3f3hChVREREREQSidFopHfvfkyfXgVohJNTKH/95UzBgq88aUNERBJZQmc0rzxSKo6vry/16tV74rYcOXKQI0eOhLqUiIiIiIgkAxEREbRv355ffvkF8MbOrh6bNrlSsKC1KxMRkaQgwUIpERERERGROA8ePKBx48Zs374de3t7FizIS5kydmTPbu3KREQkqVAoJSIiIiIiCerq1avUqVOHo0e9SJMmHatWLaFqVfWPEhGR+BRKiYiIiIhIgrl48SKVKlUiICAHsIGcOaMpUcLV2mWJiEgSpFBKREREREQSxOXLl6latSoBAWmwsVmN0ehA3rwOuLlZuzIREUmK9MgLERERERF5ZdevX6datWr4+3tha7sZo9GdcuVg4UKw0W8dIiLyBPrrQUREREREXsmtW7eoVq0aZ8++DfxBbKwvBQrAqlXg5GTt6kREJKlSKCUiIiIiIi/t7t27VK9enZMn6wHzAEcaNIA9eyBtWisXJyIiSZp6SomIiIiIyEt58OABNWvW5OjRo/j4OBMTE0vfvrYMHaopeyIi8t8USomIiIiIyAsLCgqiatU2HDz4N+nSpWPbtrn4+tri7W3tykREJLnQ/78QEREREZEXcu3aNYoUmcTBg8tIk6YFmzZtomDBggqkRETkhWiklIiIiIiIPLdDhw5TseIWgoM/A6BmzRkULZrOylWJiEhypJFSIiIiIiLyXH79dT0lSx4hOLgvAD173mfpUgVSIiLychRKiYiIiIjIf5o8eQ4NGpiIiWkHxDJxYihTp3phMFi7MhERSa40fU9ERERERJ7KaDTSp88wpk1rAryFnV0kP/9sQ5MmrtYuTUREkjmFUiIiIiIi8kQRERG0adOG5cuXA1lxdS3A5s0ulC6t4VEiIvLqFEqJiIiIiMhjoqKiaNasGb///jsODg7MmeNO5cquZMli7cpERCSlUCglIiIiIiLxxMTEULfuIDZvboCT005+/30VVapUsXZZIiKSwiiUEhERERERi9jYWBo3/pjNmwcDGWjcuBZVquSwdlkiIpIC6el7IiIiIiICmJuat2r1Kb/91hfIQM6cgXz9tQIpERF5PRRKiYiIiIgIJpOJjh2HsXRpN8APP78g9u71wMvL2pWJiEhKpVBKRERERCSVM5lMdOv2OQsXdgRy4OsbxL597qRLZ+3KREQkJVMoJSIiIiKSyg0dOpxvv60P5MHHJ5h9+9zJmNHaVYmISEqnUEpEREREJBUbPXo0Y8Z8AQwiQ4b77NvnRpYs1q5KRERSA4VSIiIiIiKp1MSJExk6dCgA48fX5coVL3Kor7mIiCQShVIiIiJJzOzZUKoUZMoEXbvC7t1gMlm7KhFJaWbOnEn//oeAvIwcOZIBAwZgZ2ftqkREJDXRXzsiIiJWYjQa+esvf9avN1G27G3s7KKIjo5m48Yc7N+fCzAHVLNnQ+7c0L69ecmWzcqFi0iyN3fuXD78cAWwFkfHKNq2dbJ2SSIikgoZTKak/f9eg4KC8PDwIDAwEHd3d2uXIyIi8tICAwP544/9LF16he3bHbh4MS+xsW8AtkA94PeHexYCSgFXgdYYDC0wmVwBcHWN4fLlKLy8XKxxCyKSAvz444+8++7nwF7Ak06dTHz3nQGDwdqViYhIUpfQGY1GSomIiLwmwcHBbNmyhXXr1rFx403On+8AVAGqx9vP3v4UadNmwdMzP/b29g+XEwQGBnL6dGdMpo+ApkAHQkP9SZeuG+XKlWPgwEGMHl2XQoUMVK0KVauCr68VblREko3ly5fTtm1P4A/Ak/LlTcycqUBKRESsQyOlREREEojJZGLHjmPMm3eSrVvDuXx5DUbjsodb8wGnAHB0DKJw4RvUr+9Ax46ZyZ7d/qnnDAwM5K+//uLPP/9k37597N27nxs3rj7cmh84GW//ggXhvfegVy/UG0ZE4tm8eTO1a9cnJmY1UIOsWU3s328gfXprVyYiIslFQmc0CqVERERekslk4syZM/z889/89JMTp04VITY29yN7/ETu3EOpVasWVatW48SJatSt607x4mDzko8aMZlMXLp0iRkzZvD11/MIC3sLqIaTU10iIwtgMpmHO5QqBXPnmkMqEZEjR45QoUIFgoJGAb1wdTWxe7eBYsWsXZmIiCQnCqVERESsxGQycfLkSbZv38727dvZtm0PN2/Owzwl7x+urlcoVCiEli096dfv9c2nu3PnDpMnT2batGkEBQUB3qRP/xHBwUMID3dg0yaoVu21XV5EkonLly9TpkwZrl27j7v7PoKCCrNiBTRubO3KREQkuVEoJSIikkiioqL4+++/2bVrF9u27WH3bn8ePDABhyz7GAzbMZnKkzv3eVq3jqJbt9xkyuSYqHU+ePCAadOmMXnyZO7duwdkIm3aTowZk5UOHTrg6OhIcDC4uSVqWSKSBDx48IDy5ctz/PhxChYsyNatuzh0yIuaNa1dmYiIJEcKpURERBJIZCRcvw537kDRohAREcSePXuYM+cuu3Z5cOuWHUajL5AJSPfwqGjKl29FzZrFqVSpEs7OpcmY0RE/PyveyEPBwcHMnDmT//3vf9y6dQsAPz8/unQZxdSpHejVy8DHH4OTnvwukipERkZSq1Yttm/fQaZMGdmzZw9Zs2a1dlkiIpKMKZQSERF5SSYTHD0KGzbAxo2wY4eJiAhzD6aCBetz6tRajEYjMAYY/NjxDg5GMmc28PXXBmrXTtzaX0RYWBizZ89m3LhxXLt2DRgCfAGAn18sY8fa8u67L9/XSkSSPqPRSJs2bViyZAl2dot4990azJmTHvunP1dBRETkPymUEhEReQnffw9Dhpi4cePfzz2PBG4DtYHj5MyZk9y5u+DsXIMSJTJTooQvfn4GMmcGb2+S1WPTIyIimDdvHmPHfklAQBlgAmAe0pU3byjTprlQs2YyuiEReW4DBw5kwoQJ2Nh0wWicjY0N7NljfgiCiIjIy1IoJSIi8gIiIyPZsmULkydfYcOG94FQYBuwEdhAsWKOVKxYgfLly1O+fHkyZcpk1Xpfh+joaBYuXMjEid9w/HhVzKPAPAAoXtyf7dt9cHdXwymRlGLKlCn06dMHKIy9/UGio+0YPRo+/dTalYmISHKnUEpEROQ/GI0walQ0x479wR9/vPtwCps78AZOToeoWbMSDRo0oF69emTMmNHa5SYak8nE3r17mTx5EcuW5SM2thswljRpJtC+fXsGDhxEtmzZktVoMEk5YmMhMBDu3YO7dyE8HAoXBh8fa1eWvEybNo1evXoBafDxucidO2mpXRt++01TdkVE5NUplBIREXmGK1ciqFnzJidPZgOigAL4+obRqFEjGjRoQNWqVXF2drZ2mVZ37949Jk5cwU8/zeDcuYMA2Ng0wdt7IgMGuNCrV3r0MUliqlcPfv/98fV58kCZMjB1Knh6JnpZycqECRMYOHAgAPnz/82pU2/g5wcHDyrcExGRhKFQSkRE5AkiIyMZMuRXJk8uS2xsJiAcL6+hjBmTh06dOuHo6GjtEpMkk8nEli1b+PLLL9m0qQ9QDwAHhxDato1iyBBvcua0aomSAh04EMN77wVTosRXBAWdw8bGhn37enLpUgXs7MJxdAzFxsZEcLD5qZdubibu3zdga2s+/n//g7RpoXlzSJPGijeShIwePZqhQ4cCULfuCn7/vTF2drB9O7z9tpWLExGRFEOhlIiIyL+sXr2GDh3+5sGDTwF77OzOM2jQAYYPb6ww6gX8/vs++vY9xpkzVYHsD9caKVfuHl984UmlSnaa2icvLSoqis2bNzN+fABbt7YDXID5QMeHe7hiHt0Y/chRXkBpDIZMFClygLfeeouSJUsyZkwnLl92xMXFHEx16ACVK6fO6Wkmk4nPPvuMUaNGATBy5EiqVh1G1aowejQMGGDlAkVEJEVRKCUiIvJQYGAgvXv3Yf78ykAHAN588yzr1mUlXTqFUS9r79799Oq1jv37SwG1ALC1PUqbNv+jceNG1KxZE1dXV+sWKcnG9u3b+f7771m5cg1BQQMAc7dte/sttG69mlKl8mAymTAajZYlNjaW4OBgDh48yP79+7l58+a/zvoJdnZdiInJZVmTNSu0awedO5NqRveZTCY++eQTxo0bB8BXX33FoEGDANi/H956K3k9MVRERJI+hVIiIiLAhg0beO+997hy5QowCINhDBMmxNK3r4N+CUsgBw8eZOTIX1i7Nj+Rkb8BPwPg6OhDnjxf0KWLPYUKZcXPzw8/Pz/SaB6VPOLAgQN8+umnbNy4EfODBn4A6gPQsmUA8+dnwtHR7j/PYzKZuHr1KgcOHODAgQPs37+fXbt2ERYWBpQBOmAwtMZkMj9RsnVrI4sXp/whU0ajkX79+jFlyhTAjeLFDzB/fl6KFrV2ZSIikpIplBIRkVQtJCSEgQMH8s033wCQO3du5s6dh5dXOQoVsnJxKVRMTAy7d+9m1apVrFy5En//hsBkIAw4D1wCLuHoeBMfn1Dy5r1IunR2ltEvj46CcXJyIkOGrDg5FSE2NjfNmhnImjULvr6+bNpkx99/Q82a8MYbWPoHSfJy6tQphg0bxtKlSwGwtc1NmjQ7CQz0xcnJxJw5Btq0ebVrhIWFsX79epYtW8aaNWsICooEGgGdcXf/nGbN8tOsWTNy5apOaKgjJUq88m0lGcHBwcybN49p06Zx9uxZwI/MmQ9x9Wpa8uWD48f1Z0dERF4fhVIiIpJq7dixg44dO+LvnxMYxgcf/Mr//jdCU8kSkclkYvz4AMaNS8Pdu2mfsldR4OjD18OB/kDgw8UByAnEjZDJBFzHxsYGN7c1BAbWBcDb20T16gZq1DCHVFmzvq47koQSEBDA559/zty58zGZcmAwnKdNmzb07z+Kd97JTng4rFhhnlKWkCIjI9myZQvLli1j5cqV3L1717LN3n4R0dFtKF78BmPGeFCjhjN2/z04K0k6f/4806dP5/vvvycoKAgAV9eK2Nuv5cEDF3x9Yc2ahP98RUREHqVQSkSswmSCU6dg1y5zn4q8eeGDD8DNzdqVSWpgNBr58ssvGTZsGEZjF2AGYMfgwTBmjLWrS52MRjhzBi5dMi9nzkRy+nQEFy8a6d59LTExd7CxsWH58gps3VrssePt7cNwdPTHza0Xt2/vICYmBmgHNAWqAB7x9q9c2cSaNQY9aS0JuX79Ort372b37t3s2HGIQ4ecMBrrA42xtfXhjz/OUqpUYQDOnjX/feHr+3priomJYefOnSxbtoxly5Zz48ZooD1gHjpkYxOOn99VypaNoWVLX5o08Xy9Bb2i+/cfsHDhUZYv/4UdO6Zj/me7N66uy/D2Lsjt2+mIiDBQuDD89pvCWxERef0USonIc4uNhbt34eZNiIgw/xKZJw94e0NsbCxnzwZx4EAYQUH3CAq6w4MHd7h//xb37t3k3r2bwCXy5s1Bvnz52Lu3AT/9lCve+b28oHdv6NXL/FrkdXjw4AHt27dnzZrfgXFAPwDatoU5c0AP10vaAgPh1i3z18BA89PR8uWDjBn/acAcGxvLjRs32LVrF+vXr2fdus1cv+4H1Hi4lMHB4TDt2n1NrVq1qFatGra23nh4POPCkiCMRiO3bt3iypUrXL58mYCAAP766y92797NhQu5MAc+bwH54x3n5gabNkGpUtao2sxoNLJ3716+/347P/2UnZCQ2pif5hdnF3nzvke5cuUoXrw4trZ5eeMNDzJnzkTGjBlxcHBI1HojIiI4dOgQ+/btY8+eQ2zdmoGbN5sDJYDvgC7Url2brl370rRpTctxtWrBzz+D/pksIiKJQaGUiDxReHg4p06dYvlyWLgwG/fuORIS4ozJFL/Zq4/PB0RFLXk49L8V8OMzzlod2PzwdSPgRwyGfaRNe5GYmNo8eJABAA8PuHDBHHaJJKTDhw/TrFkzzp9/gMHwMyZTVQBGjoShQ/VUqZTKZDJx4sQJ1q9fz/r169m27TxRUW7AIQAMBm8MhgBy575Mhw5GGjfORv78rtik/N7WLywqKgp//2DOnIkgNDTsX0s49+7F4OXlj9F4i+DgYM6d8+X06ZKEhtoQGmokPNwGk8kJcAZcgF788/dCD2C65VqZM8dQu7YdTZtCtWpJKzA2mUycOXOOpUuPs3ZtMEePehAUtB2Y+HAPD+AmEAAsAZaQLt1tMmbMiI+PD97e3nh5eeHt7U1kZF5u3MhPVJQXvr4msmSxIVs2W3LmdCRHDhc8PNywsbEhNjaWwMBYrl83ceuWidu3Tdy7F4vJdAOj8TwhISe4fPk8/v7+XLx4EX9/f2JiMgHdgS6ADwAGQwTFi+9k8eIs5M9vDv/mzoV06SBzZiheXP8tFBGRxKNQSqwiIADSpwcnJ2tXInGMRiMrVpxm0qRQYmNncfDgQiIjI3k8aDICd4HQh697AOsebquNwTAROzsnbGwcsbFxAOwBe0wmO+rVO0q+fMs5ffo0J06c49y5c0RHhz481gZohpPTF+TJY2DjRncyZDCHVIGBvPIIhqioKG7dusWdO3cIDnZk69Z05MtnoGBBO/LkccbR0R7DE/4VHh4O9+/HH4UhydOCBQvo1q0b4eE5sLVdT2ysHy4u5l/G3nnH2tVJYgoPD2fHjh2WkOrEicLAT/H2MRhC8fS8QbZs4bRrF0rbtjlInz69dQpOYLdvw4YN4eTIcY7o6HsEBQVx+rSJv//2wGgMIiYmiNDQUG7ccOLOHS8CA9ORJs1QgoP/JCQkBPgA+OYZV6jDP38vdADmPXXP0qUnUbPmfcqVK4e399usW+fGW29BiRLmfyckJ/fu3WPPnj0PpyA6sGvXJxiNj/5D5wiwCvAGRgPXH64fAXz2lLNGA9WAnQ/ffwEMecq+sZinqsbtOxN4n7iphunTh9Gtm4levVxJ+7T2bSIiIolMoZQkuvXroW5daNAAVq60djWp261bt1i5cj1z5wZz4MCbxMSUebhlBvARadOmJV26ojg6liZDBhOZM9uSJYsTvr4+pE2bFk9PTzw9PfHy8sLT0xMPD48Xmp4QGxvLpUuXOHLkCD/99BMrVqwgMjIKcMPWNpS6detSq1Y3BgyoTYsWMGiQDYULP/lcYWFhnDhxgmPHjnHs2DGuXLnC9es3uXgxCzdu1CQqajGw9uHe1YGNjxwdDpzH1tYOGxt3ChacQ7Fi5/H19eXWrXLMm9cQd/dI8ucPpEiRMN54I4q33jKSIYMTzs7OuLi44OzsTFSUHdeuwY0bcOeOOcgqXBicnGIJDg4mKCiIoKAgwsLC4j1B7NYtW06dSsPJk2kAZ/Lm9aRnT0dLEBcSAg4O5kVeXGRkJH379mXmzJkAVK7cmpMnF5EmjQ0rV/LUnylJPS5fvsz8+X+yYIEz/v65iYnJgbmBepzmwDLc3d3x8alBTExb8uW7zFtvhZI7d3r8/Pzw9PTE3d0dd3d33NzccHV1xcbKQ61MJhP+/v6sX3+crVtjOXzYg8uXsxMenuPhHo+GRx2Buc84m/kzMGuDwfAVBgPY2BgwGAzY2JhfOzhEUrbsUvLnv0KaNGkICcnB2bNF8PS0JWNGdzJl8iRjRk/c3e1wcYFChcwjdFKikBBzo/DFi02sXw/R0f/8n41u3VaTMeMh7t+/z9GjmTl2rDwGw3XCw92JiPAhOjo9RmN6zIFSfuD0wyP7ACOBO8BtIAg7uywYjdkwGp3o0WMSJUp4kj17dlaufJOpUz2oVg169oT69fUUPRERSXoUSkmiiomBYsXgxAnz+717oXRp69aU2pw5c4YVK1Yydaof1675ACX5pydGDBky7KNZs+t89FFB8ufP/8TRQ6/LgwcP+Omnn5g3bx579+59uLY35kfFm3l776FkyW2ULh2JyWTk6NGjHDt2jPPnzz9s2OqC+UlczTH3Jon75WsltrbN8fHxISLiTUJCehAbmwvIhXk016O6ArMfvq4KbCDu/zTHdxLzNMSzANjYfIrROPpf+xiBC5ifHDYKOPhwfSugBebPP8tjZ86QoRD586cjT548HD/+IXv2vEHevNFUrWpPpUpQsSJkyvTEj1EesWPHDrp3786JEycxGGD48OEMHz6cY8dsyJJFvcvkya5evcXatWfYvv0Whw9HERo6C3//7Q//GzMMcygAEAPsA3ZhHu3pBfQEwjEYDNjbT8JobIGbWxgZM5rIlcuFwoXTkjOnE5kywdtvg6dnwtUdFBTE/v372bt3L3v37mXnTlsCA8cBeZ+w9xHc3MaTOfMBPDw8iI6uzPXrTTEYXDGZXDAY7MmQIRQ/vyhy5TJRuXIshQq54e3tjYeHB7ZKN17I/fvmJwVu3WqeIteunTmQe5aYGLhyJQY3t1BsbIzY2tpiY2OLnZ0tNjY2D9/bYDAYMJnM/R7TpfsneLp1y/w/SAoWfP33JyIi8rIUSkmimjMH3n//n/f16sGvv1qvntQgMNBEx453OHnyOgZDS06dOvVwyxkgDwDu7g9o0eIBQ4dmInv2pDEc59SpU8yfP58VK1Zw7pw3sbF9gWaYf/ED8y+Cs4DvLcfY2p4hNjZPvPO4uMRQu3YwnTsbqFPHPd7IBZPJRFhYFKdPR3L6dDTBwQ+IiLiFre0lQkMDuHHjxsPlPrdvZ+L+/XwEBRUgLKwosbHZHp6lFubQCszTVGZgnpJxF8gKZHykvrJ4eZ3DxcWFwMAhBAZ2fbjFiKPjOZycjhEZeYOIiDSYRy3E/ef0J+Dx+WW5cpmoVMnAlCnoCWL/cvPmTQYNGsSCBQuAjNjbL6NvXye++n97dx4eVX33ffwzS1ZIJnsAgSRElH0RwRQUVMCKIC5oxaXuCopalzsWWryRG26oDwpSURQRoZeKPBe1UM1D68IiAhUSayCGLSSENUAgyWRfJuf5Y0hKICrgzJws79d1zTWZ7Zzv4foS5nz4/X7nlf5ml4ZmqqysTAcOHFBKSok++6ytduyI1smT586BslrjVFt74PSj11S3kH5jnn32fQ0fHq2ePXuqpCROLpdVvXv//GgWwzCUm5urHTt2aMeOHfr++5369lurDhy4XNIWSXX/sHaTOzx3KTIyV926HdevflWtUaNCNWBAvBys7A4AAExGKAWfKS11X6nt6FFp0iRp4ULpxhulTz5pWouXthQFBQV67bXP9OqrQ1RZ2UVSoaRw2e12XXfddYqOnqx+/fpp6NAIXXll0x7SX1tbe/pKWnl69922Wr8+QTU1fnI48jR9+gr17t1bvXr10k03xSgtzX2VpsGDpQcekG65RQoO9nxNx49L27ZJPXoYateuQmVl7kV+KyrKVFZWpvLycgUFBam6OkwHD4YpO7utnnzSXl/Lli3Spk3uK0ldcUXDUKmgoEB79+7Vnj17Tt9nKT39qHbuDJU0VNIwSf0k2eTv79Rbb63U9ddfp/j4eO3da1FCguR39uCvVsLlcmnRokX6wx/+oMLCCkkvyG6fqpqaQLVr515APyjI7CrRUuTmSl99JaWluX/PhIVJEycaCgoql9Pp1N69ZUpPz9e2bQeVkZGvffsqVFQUJKmD3OsKXSP3aErJZvtQLtc98vMrV+fOR9W+/VFFRh5UWNh+BQYeVGVlmSoqKnTgwAFt356hkpJ4uacij5T794L7l0tw8KcaO/YjJSUl6aqrkpSXd4Wuu86PKwsCAIAmiVAKPlNSIs2c6V5f4bvv3OFUfLzZVbUshmFow4YNWrx4sVasqFBNzRJJoZIOaciQ5XryyU4aPXpUs//f8ePHpbfflo4ccd/XOXDAvSB6Mz+8H3Xs2DH985//1Jo1a/SPf2xWYWEPua+m9IEkqWPHzjp16jvZbMG67bZKPfWUQ1deaWk1C7SnpqbqiSeeUGpqqqTx8vefq6oq90i1q66Sli2TLr/c3BqBEydOKC0tTampqcrIyFBmZqZ2796tqqoFco+IbOwXmFNSpNzTBQPknhLccP5ueHiFrr/e0PjxQbrjDi8fBAAAgIcQSsFrNm7cqPLycg0fPrzB2hM1NZLdbmJhLVBxcbEWL16st956S1lZ2XJfyeclSVJi4mGlpLTR5ZeHmVghPM3lcmnr1q1as2aNvvzyS23btk01NXGSNkv6zyWr2rQ5of79SzVuXIzGjg1Wly6mlexxhmEoIyNDq1ev1urVq0+HUb1lsy2WyzVIktSpk/SnP0l3383VE9F01dTUaN++fdqxI1Pr1p3Qv/8doMLCOBUUxOnkyUsUFlao559foqCgIEVHR2v27FuVkxOkYcMsGjFCGjnSvT4RPQ4AAJobQil4xfz58/Xss89KkuLj4zVhwgQ9/PDDjV5O+8gR6csvpfvv93GRLUBeXp7+/Oc/a+HChSosLJQk2WyfyOW6TZL09NOGXnvN0mqncrUmpaWl2rx5s9au3ai//71cO3deKcMYK+k/c9Xi4pbqqafyNXr0aMXHd1NWlkW9ejWvE9mamhpt2rSpPojKzj4sKVrSIVksFt1442T94x//q+BgiyZPlp5/3jvTNwFfcbnci1XHxv7nudxc99U9uSInAABo7gilminDMPT555+rc+fOiouLU3ATOesyDEPTpk3TjBkzJEnBwcEqK7tE0juy2f5bd93VWU888YSGDBkii8WivDwpIUGqrHRfka9bN3Prb8rqTkyOHZO2bz+gDz/8q778cpNqamySjumyy47qhRdekJ/f/Zo0KVCLFkn33Wd21TBLeXm51q7dqqVL92j9+hrl518u99X/1kuSYmMf0bFjixUVVawbb6zRffeF6dprLU1qfTen06kdO3YoPT1dmzYdUnr6Se3bV6qKinhJvU/fLpPDka05czbo5ptvVrt27bR0qXTDDVydEAAAAGjqCKWaqRMnTjQYdRQdHa24uDjFxcUpMrK3/vGPifLzC1BCgtSjR6C6dw9Uly7uACguzjv/u1pbW6vf/e53WrBggSRp5syZeu655zR06HGlpcVLSpE0RpLUq1cvTZs2TXfccYduuUX6+9/dl0f+y188X1dzUVsr5eRIO3ZIGRlSu3bSo4/WTVHap759u8gwrI1+dsCAPG3dGlN/ZbkjRzghR0PZ2dlKSUlRSkqK1q1bp6qqxyTN0Zkjqez2cnXvflAjRtRo2LDjCgqqUm1trVwul1wul2pra+Xn56eQkBCFhoYqNDRUgYEhiowMVcDPpFnV1dUqLi5ucNu1q0bZ2RU6dKhSR4+6lJ8vFRT4yekMVHl5gUpLz1wY599yL+5+ro4d3euJNacRXwAAAAAIpcwu56JlZWXp9ttvV25urpxO51mvjpP0ttwLIJ8rPn6HJk36p+Lj4xUXF6egoHj17Bklyy84o6uurtZDDz2kDz/8UBaLRQsWLNCTTz6pLVvcV0GzWqUPP8zQF1/M0/Lly1VeXi5JGj9+vB55ZKFGjgyTzSbt3i0lJl50Gc1KdbU7hNuyxR1E/fCD+wqFdRITdysh4SmlpqaenprnlNRGUr6kU3I4/NWxY5Sio0OVlCTNnm3KYaAZKikp0YYNG7Ru3bdas6Zau3Z1VW3tKEntz3hXB0lHT//8rKThkvZJcknqfMYtRFJb2Ww2Wa1WuVzTJQ2WxbJHNtteWa175HKVq7o6Qe7+nXvGPjIk9fyRKgskRahjx47q27ev9u59RYWFcerQwaoePQLVr59VvXtLffq4pzERSAEAAADND6FUC1BYWKjc3Fzt379fubm5ys3N1Z49p7RzZ4Ty8oJUWhotqYukhNO3/5H0f05/upOkA7JY9ikkZIeio4sVFuZQWFiYwsIcGjlSGj06Uu3bt5dh2JSbK3Xp0vAEsLy8XHfeeadSUlJkt9u1bNky3XPPPTIM6Zpr3Je9f+QRafFi9/sLCgr02muv6U9/+pNcLpfatWunSy5JV1paTIP3tXQbN0rDhxuqrj7zbLpC0k5JOyR9Jck9dCwgIEC9el2vpKTLdNVVA3T11VcrISHB90WjRaqsrFRq6ndasWKvPv/cX4cPd1Bc3FOy262y2WzKzp6jwsLrf2ILUZJOnv75S7kDrMYUKyAgRqGhIQoJCVFBwXxVVPRTmzYlCg2tVHh4jaKjpfbtbercOVBPPx2jyMgIjx4rAAAAgKaDUKoVKC0trQ+r9u/PVU7OQR08mK3c3Fzt3t1Vp069L6nxaWHSc5Jel91uV0zMjTpy5FMFBBSrc+ej6tmzRIMGWbRq1f9o69a1Cghw6a9/XaHRo0dLklatkm67TQoKkvbulS65pOGWt27dqgceeEC7du2SlCRpi+x2Q1lZFsXFee2Po0koLCzUyy+/rAULvpfLdaukTXIHUVmyWg316tVLAwcOrL/17t1bfqxWDpN8+62Uni7t2+eeZtq5839uHTq45O9frLKy0tNTTW3audNPWVl2ZWfblZXlL5fLoq5dDfXoYdPs2TYFBpp9RAAAAACaAkIp6NixSqWknNTatZU6fLhCxcUl9Wu+1Nb+X+Xnr5DL5ZJ7WuBHkhpfkOq//3uXpk93r1T+6afSnXe6FzD/4x+lmTMb33d5ebmmTp2qefPmyTA+lzRCv/51rtas6fyLphM2VS6XS4sXL9HUqX9Qfn6+JCkxMVGDBg2qD6D69++vNm3amFwpAAAAAADeRSjVAk2aJG3fLr30kvsKVL9UTU2Njhw5otzcXGVlHdS331Zq+/ZA5eTE6OTJRFVXd5Zk1SefuEdGSdJHH0n33useHZWZKf3cH/XXX3+t8ePf1NGjyyXdrl699ik5OVl33DFeQUH+LWK9mE2bNumRR97Q7t1TJd2t7t1dmj9/vkaOHGl2aQAAAAAA+ByhVAs0eLB78ewzQyJvMgz3ot1Wq2S3u58rKnJfDatzZ8nhOL/tlJSU6LHHPtCnn/5epaXuxdtDQ+fI4bhXL7/s0G9/G6zmOIPt8OHDevHFF/XRR2slfSups/r0yVJqahxT8gAAAAAArZanM5ofW5gIPnR6VpgiI32zP4tF8vf/TyAluYOo3r3PP5CSpLZt22r58ok6dChXs2fPVmxsezmd43TwYHs98kiwIiKK9MorJaqu9vwxeMu6devUp08fffTRJ5JWSeqsSy+t0YYNlxJIAQAAAADgQYRSTcDJ0xfBiooyt46LFRYWpsmTJys3N0dz525WZOR8SXkqKXFo8uS26tzZqU8+MdS0x+RJb731lkaOHKlTp04pPHyVpKsUESGtWWNXWJjJxQEAAAAA0MIQSpnM5ZIKCtw/+2qklLcEBATouefu1fHjT2vlylS1bz9T0nHl5YVq3DiLnnqq0OwSG1VVVaWJEydq0qRJcrlc6tFjtQoKfi27XfrrX6VLLzW7QgAAAAAAWh5CKZMVFKh+BFFEhLm1eIrVatW4cWO0f/+L+uMfl8pqnSWpSEuWXKt58+bJ5XI1mVFTJ06c0MiRI/XOO+/IYrHo1lv/qczMsZKkt9+Wrr3W3PoAAAAAAGipWOjcZLt2Sd27u9dyKiw0uxrv2L17tx5++HfavPmfkqQrr7xS8fFr1LFjlF56ybwwbvv27Ro7dqxyc3MVEhKi5cuX6/rrR+u666QHH5QmTjSnLgAAAAAAmiIWOm9hysulDh2kSy4xuxLvufzyy7Vx4//TokWL5HA4lJp6SitXRuj116WEhFrNnStVVvquHsMwtGzZMg0ePFi5ubm65JLx2rLlW40ePVpBQdKmTQRSAAAAAAB4G6GUyfr3lw4fln74wexKvMtqteqxxx7Tzp079ZvfXCnpRknb5XRa9cILUrduLq1cKa9P69u7d69GjhypBx98UKWlFYqLW67Dh5dr5cru9e+x2bxbAwAAAAAA8GAo9cwzz8hisdTfLj29OnRGRoYGDhyo8PBwJScnq4nPFoSXtW/fXitWrNCWLf+ja675naRHJB3V/v023XmnNGSIS7t3e36/VVVVmjlzpnr37q2vvvpKAQGdlJiYpdzc8ZKkkhLvB2IAAAAAAOA/PBZKpaamKiUlRQUFBSooKNC///1vVVZW6uabb9aAAQOUmpqqzMxMLV261FO7RDOWlJSkDRvWKiVlnLp3v0XSdEll+vbbQi1Z8qrWrFmj48ePe2RfmzZtUv/+/fXSSy+psrJa3bu/rsDAHO3bF682baSPP5bmzJEsFo/sDgAAAAAAnAePLHReU1OjyMhIHT58WG3btq1/ftWqVXr44Yd16NAhBQcHKz09XZMmTdI333xz3ttu6Qudz50rffKJ9PDD7ltr5HK59MEHH2jKlAU6etRf0ub612JjhykpKUwDBgxQ37591aFDB8XGxiomJkYBAQH17zMMd6hUWlqqffv21d/S0tL08ccfS5LCw4coPHyVsrOjJEl9+0offCD16uXTwwUAAAAAoFnydEZj90BN2rFjh2pra9WvXz8dPnxYw4YN06JFi5Senq6kpCQFBwdLkvr06aPMzExP7LLFyMx0L6w9apTZlZjHZrPpgQce0F133aVly5Zp48YEpaWladeuXjp2bIVWr35dq1dPlVTe4HMOh0OxsbFyuR5XTs4Tstu3qarqS0lfS9oqqaL+vQ8//LAmTXpVQ4aEKzRUmjFDevJJye6RvwEAAAAAAOBCeeSUPDMzU5dffrneeOMNRUVF6bnnntPjjz+unj17KiEhof59FotFNptNBQUFCg8Pb3RblZWVqjzjUmxOp9MTJTZZJ0+67yMjza2jKQgMDNSECRM0YcIESdIzz1TpjTeskp5XSMg9ioycp5MnI1VScrUM4x0VFf1FRUVFkt6V9IKqqoZJGiZJsliqFBmZrR49DmvWrCANGTJYknuq3qBBUvv2phwiAAAAAAA4zSOh1L333qt77723/vFbb72lhIQEde/evcEUK8kdPJSVlf1oKDV79mxNnz7dE2U1C/n57vuoKHPraIr+/Gd/3Xij9Pjj0uHD7VRc/Er9a2PHXqlXXpmiY8eO6dix48rJ+VyFhZdrz572+te//HXkiL/y87vp66+7NdjmLbf4+igAAAAAAEBjvDJ5KSYmRrW1tWrXrp0yMjIavFZcXCx/f/8f/eyUKVP0/PPP1z92Op3q1KmTN8psEhgp9dNuukn64Qfp97+X1qyR+vd3T3UcNcpfnTt3U7du3c75jGFIOTnS119L334rHT1qQuEAAAAAAOAneSSUSk5OVv/+/XXPPfdIkrZs2SKr1arevXvr3XffrX9fTk6OKisrFRER8aPbCggIOGd0VUvGSKmf53BIb799/u+3WKQuXdy3Bx/0WlkAAAAAAOAX8Ego1bdvX02dOvX0otMuPf3007r//vt1ww03yOl06v3339dDDz2kWbNmacSIEbLZbJ7YbbNXWyudOuX+mZFSAAAAAACgNfFIKHXffffphx9+0Lhx42Sz2XTfffdp1qxZstvtWrx4se6++24lJyfLarVq/fr1nthli1BaKrVr5x4tRSgFAAAAAABaE4thGIa3d5KXl6e0tDQlJSUp8gLTF6fTKYfDoaKiIoWGhnqpQnMZhnvKGQAAAAAAQFPl6YzGKwudn61du3YaPXq0L3bVLBFIAQAAAACA1sZqdgEAAAAAAABofQilTLR6tTRkiPTyy2ZXAgAAAAAA4Fs+mb6Hxu3bJ23eLMXHm10JAAAAAACAbzFSykQnT7rvufIeAAAAAABobQilTJSf776PijK3DgAAAAAAAF8jlDIRI6UAAAAAAEBrRShlorqRUoRSAAAAAACgtSGUMlHdSCmm7wEAAAAAgNaGUMpE/v5SYCAjpQAAAAAAQOtjN7uA1iwtTTIMs6sAAAAAAADwPUIpk1ksZlcAAAAAAADge0zfAwAAAAAAgM8RSpkkI0MaMkR69FGzKwEAAAAAAPA9pu+Z5NAhafNmqazM7EoAAAAAAAB8j5FSJjl50n3PlfcAAAAAAEBrRChlkvx89z2hFAAAAAAAaI0IpUxSN1IqKsrcOgAAAAAAAMxAKGUSRkoBAAAAAIDWjFDKJIyUAgAAAAAArRmhlEksFikoiJFSAAAAAACgdbKbXUBr9fHH7nvDMLcOAAAAAAAAMzBSymQWi9kVAAAAAAAA+B6hFAAAAAAAAHyOUMoE5eXS4MHSzTdLlZVmVwMAAAAAAOB7rCllgvx8acsWyd/ffQMAAAAAAGhtGCllgvx8931kJGtKAQAAAACA1olQygQnT7rvIyPNrQMAAAAAAMAshFImqBspFRVlbh0AAAAAAABmIZQyASOlAAAAAABAa0coZYIz15QCAAAAAABojQilTFBTIwUFMX0PAAAAAAC0XhbDMAyzi/gpTqdTDodDRUVFCg0NNbscj3K5JJvN7CoAAAAAAAB+nqczGkZKmYhACgAAAAAAtFaEUgAAAAAAAPA5QikT3HabdPPNUna22ZUAAAAAAACYg1DKBF98IX32mVRba3YlAAAAAAAA5iCU8rGKCqm01P0zV98DAAAAAACtFaGUj5086b632SSHw9xaAAAAAAAAzEIo5WN1oVREhGSxmFsLAAAAAACAWQilfCw/333P1D0AAAAAANCaEUr5WN1IqchIc+sAAAAAAAAwE6GUj1VUSMHBjJQCAAAAAACtm8UwDMPsIn6K0+mUw+FQUVGRQkNDzS7HY2pqJLvd7CoAAAAAAADOj6czGkZKmYRACgAAAAAAtGaEUgAAAAAAAPA5Qikfe+EFacwYaeNGsysBAAAAAAAwD6GUj23cKKWkSAUFZlcCAAAAAABgHkIpHzt50n3P1fcAAAAAAEBrRijlY/n57vvISHPrAAAAAAAAMBOhlA9VV0tOp/tnQikAAAAAANCaEUr50KlT7nuLRQoPN7cWAAAAAAAAMxFK+VDd1L3wcMlmM7cWAAAAAAAAMxFK+VBxsRQczNQ9AAAAAAAAu9kFtCZJSVJpqVRVZXYlAAAAAAAA5mKklAn8/c2uAAAAAAAAwFyEUgAAAAAAAPA5QikfWrBAGjNG+vhjsysBAAAAAAAwF6GUD6WmSikp0v79ZlcCAAAAAABgLkIpHzp50n3P1fcAAAAAAEBrRyjlQ3WhVFSUuXUAAAAAAACYjVDKh/Lz3feMlAIAAAAAAK0doZQPMX0PAAAAAADAjVDKR1wuqaDA/TPT9wAAAAAAQGtHKOUjTqcUHOz+OSLC3FoAAAAAAADMZje7gNYiPFwqKZEqKiQ/P7OrAQAAAAAAMBcjpXwsMNDsCgAAAAAAAMxHKAUAAAAAAACfI5QCAAAAAACAzxFKAQAAAAAAwOcIpQAAAAAAAOBzhFIAAAAAAADwOZ+EUhkZGRo4cKDCw8OVnJwswzB8sVsAAAAAAAA0UV4PpSorK3XzzTdrwIABSk1NVWZmppYuXert3QIAAAAAAKAJ83ootWbNGhUVFWnu3LlKTEzUrFmz9N5773l7twAAAAAAAGjC7N7eQXp6upKSkhQcHCxJ6tOnjzIzM3/0/ZWVlaqsrKx/7HQ6vV0iAAAAAAAAfMzrI6WcTqcSEhLqH1ssFtlsNhUUFDT6/tmzZ8vhcNTfOnXq5O0SAQAAAAAA4GNeD6XsdrsCAgIaPBcYGKiysrJG3z9lyhQVFRXV3w4ePOjtEgEAAAAAAOBjXp++FxERoYyMjAbPFRcXy9/fv9H3BwQEnBNiAQAAAAAAoGXx+kipgQMHasuWLfWPc3JyVFlZqYiICG/vGgAAAAAAAE2U10OpoUOHyul06v3335ckzZo1SyNGjJDNZvP2rgEAAAAAANBEeX36nt1u1+LFi3X33XcrOTlZVqtV69ev9/ZuAQAAAAAA0IR5PZSSpLFjx2rfvn1KS0tTUlKSIiMjfbFbAAAAAAAANFE+CaUkqV27dho9evQFf84wDEmS0+n0dEkAAAAAAAA4T3XZTF1W80v5LJS6WMXFxZKkTp06mVwJAAAAAAAAiouL5XA4fvF2LIan4i0vqa2t1ZEjRxQSEiKLxfKT73U6nerUqZMOHjyo0NBQH1UI0HswD70Hs9B7MAu9B7PQezALvQezNNZ7hmGouLhYHTp0kNX6y6+d1+RHSlmtVnXs2PGCPhMaGspfVpiC3oNZ6D2Yhd6DWeg9mIXeg1noPZjl7N7zxAipOr881gIAAAAAAAAuEKEUAAAAAAAAfK5FhVIBAQGaNm2aAgICzC4FrQy9B7PQezALvQez0HswC70Hs9B7MIsveq/JL3QOAAAAAACAlqdFjZQCAAAAAABA80AoBQAAAAAAAJ8jlAIAAAAAAIDPNflQavXq1erSpYvsdrv69eunnTt3SpIyMjI0cOBAhYeHKzk5WWcvjZWVlaWIiIhztrdo0SK1b99efn5+GjZsmI4ePeqT40Dz4+neq1NdXa3evXtr/fr13iwfzZi3eu+uu+7S008/7dXa0bx5uvdmzJih2NhYtW3bVmPHjlV+fr5PjgPNz8X03k99p9uwYYO6d++uqKgozZ071+fHg+bD073HuQbOl6d7rw7nGvg53uq9iz7XMJqwrKwsIzw83FixYoWRl5dn3HnnncbgwYONiooKIz4+3pgwYYKRlZVl3HTTTcaSJUvqP7dv3z6ja9euxtmHt3HjRiMmJsb44osvjIMHDxrXXHONcc899/j6sNAMeLr3zjRz5kxDkrFu3TofHAmaG2/1XkpKihETE2MUFBT46EjQ3Hi69zZs2GD07NnT2LVrl7F3717jpptuMn7729/6+rDQDFxM7/3Ud7rjx48boaGhxvTp0409e/YYV1xxhbF27VozDxFNlKd7j3MNnC9P996ZONfAT/FW7/2Sc40mHUp9+umnxjvvvFP/eO3atUZQUJDxt7/9zQgPDzdKS0sNwzCM77//3hgyZEj9+3r06GHMmTPnnC/IS5YsMf72t781eNy9e3fvHgSaJU/3Xp09e/YYYWFhRnx8PP9QoFHe6L2SkhIjLi7OeO+997x/AGi2PN17c+bMMZKTk+sff/DBB8avfvUrLx8FmqOL6b2f+k43b948o1u3bkZtba1hGIaxatUq49577/XR0aA58XTvca6B8+Xp3qvDuQZ+jjd675eea9gvfGyV74wZM6bB4927d6tr165KT09XUlKSgoODJUl9+vRRZmZm/fs+++wzWSwWJScnN/j8Qw891Oj2gLN5uvfqTJgwQZMnT9aaNWu8VzyaNW/03vTp01VVVSW73a4vvvhCw4cPl9Xa5Gdvw8c83Xs9e/bUO++8o4kTJ6pt27Z67733NHLkSO8fCJqdi+m9n/pOl56eruuuu04Wi0WSNGjQIE2ePNnbh4FmyNO9x7kGzpene68O5xr4Od7ovV96rtFszkqqqqr02muvaeLEiXI6nUpISKh/zWKxyGazqaCgQJIavPZjTp06Vf9lGfgpnuq9999/X0VFRfqv//ovr9eMlsETvZebm6v58+crISFB2dnZ+v3vf69bb71VtbW1PjkGNE+e6L1Ro0YpMTFRiYmJio2NVUlJCcEAftaF9F6ds7/Tnf250NBQHTlyxDcHgGbLE713vq8BZ/JU73GugQvlid7zxLlGswmlpk2bpjZt2ujRRx+V3W5XQEBAg9cDAwNVVlZ23tubNGmSBg8erFGjRnm6VLQwnui9EydOaMqUKVqyZIlsNps3y0UL4oneW7ZsmWJjY/XVV1/p5Zdf1oYNG/TNN9/oyy+/9GbpaOY80XsrV67UgQMHlJmZqePHj6tnz5667777vFk2WoCL6b2zv9Od/bkL/Y6I1skTvXe+rwFn8kTvca6Bi+GJ3vPEuUaTnr5XZ+3atXrzzTf1r3/9S35+foqIiFBGRkaD9xQXF8vf3/+8trds2TKtW7dO6enp3igXLYineu/ZZ5/VI488or59+3qzXLQgnuq9Q4cOacSIEQoMDJQkhYSEqGvXrsrKytINN9zgtfrRfHmq9z788EM98cQT6t69uyTp9ddfV1hYmAoLCxUWFuat8tGMXUzvNfadLiIiQidOnPjRzwBn81Tvnc9rwJk81Xuca+BCear3PHKucVErUflQdna2ERMTY/zlL3+pf+6rr74yEhMTG7wnMDDQqKmpqX8uJyen0QV/t23bZoSEhLDwG36WJ3tPkhESEmI4HA7D4XAYNpvNaNOmjTF79mzvHwiaHU/23vTp043x48fXP3a5XEaHDh2MVatWefEI0Fx5svfGjh1rTJ06tf7xrl27DElGfn6+F48AzdXF9N6Pfad77733jOHDh9c/Xrt2rdG1a1fvHgCaLU/23s+9BpzJk73HuQYuhCd7zxPnGk06lCorKzN69OhhPPbYY0ZxcXH9raqqyoiOjq6/ROGjjz5qjBkzpsFnG/uCfOzYMaNdu3bGjBkzGmwPOJuney8nJ6fB7aqrrjKWL19+UZfMRMvm6d7LzMw0goODjZUrVxoHDx40XnzxRSM6OtooKSnx2TGhefB0782ZM8eIiYkxFi5caCxdutTo16+fMXjwYJ8dD5qPi+m9n/pOd+LECSMwMND44osvjKqqKuPGG280nnrqKdOOD02Xp3uPcw2cL0/3HucaOF+e7j1PnGs06VBq1apVhqRzbjk5Ocbq1auN4OBgIzIy0oiOjjZ++OGHBp9t7Avy66+/3uj2gLN5uvfONmzYMP4HDY3yRu+tXr3a6NOnjxEYGGj06tXL2Lx5s68OB82Ip3uvoqLCeOaZZ4wOHToY/v7+xrBhw4zs7GxfHhKaiYvpvZ/7Trdw4ULDz8/PCA8PNxISEoy8vDyzDg9NmKd7j3MNnC9v/N47E+ca+DHe6L1feq5hMQzDOL+Jfk1PXl6e0tLSlJSUpMjISLPLQStC78Es9B7MQu/BLBfbezk5Odq1a5euueYatW3b1osVoqXi9x7MQu/BLGb0XrMOpQAAAAAAANA8Wc0uAAAAAAAAAK0PoRQAAAAAAAB8jlAKAAAAAAAAPkcoBQAA4EPz58/Xq6++KkmqqalRZWWlamtrTa4KAADA9wilAAAAvGDatGm64447znm+f//++uMf/6ht27bpo48+ksPhkMPhUFhYWP3Nz89PCxYsMKFqAAAA3yGUAgAAuEA2m02hoaH1AdIbb7xxznv8/f3l7+9/zvNDhw7Vk08+KafTqfvvv18VFRUqLi5WYWFh/e3Xv/61AgMDfXEoAAAAprGbXQAAAEBz4+fnp++++06XXnqpxowZI7vdrpdffll+fn6yWq2yWCzatGmT8vLyNGPGDN1yyy3q06ePEhMT9eabb2revHk/uw+7na9pAACgZePbDgAAwAU6OzDy8/NTjx49ZLfbZbfbZbFYtHfvXlVWVqpnz55yOByS3KOnAgICJEl9+vSR0+lssJ0JEyZoypQpvjkIAAAAkxFKAQAAXCCr1aorrrhCVqtVpaWlGjdunH7zm9/o+++/V79+/SRJu3fvlsvl0u23317/OZvNJovFIknKy8vT1q1bFR8fL0maPHmyysvLfX0oAAAApmFNKQAAgAtktVr13Xff1a//ZLFYtGfPHiUlJSk7O/u8t3E+zwEAALRUfPMBAAC4QLW1tec8d9lll+mOO+7QjBkzzns7V199teLj4xUfH6+FCxd6skQAAIAmj1AKAADgArlcrgaPDcOQJD3++OMKCgo67+1888032r9/v/bv368nnnjCozUCAAA0dawpBQAAcIFcLpcGDRokq9Uqp9OpW265RZI0dOhQDR069Ly2URdknamxEVgAAAAtFaEUAADABaqpqdHWrVt16aWXasyYMaqurr7gbcTGxuraa69t8NyECRM8VCEAAEDTRygFAABwgaKjo2W3u79GvfvuuwoODj7nPU6ns/5Ke3XKyspUU1MjSdq+fXuj2zYMQ/n5+Y1uEwAAoCUhlAIAALhAR48erf+5ffv2DV4rKSlRUlKS9uzZo7fffvuc1yoqKn50u/n5+eratatiY2N19dVXe7ZoAACAJsZiNLagAQAAAC5aZmamYmJiFBUVdcGfPXLkiDp06OCFqgAAAJoWQikAAAAAAAD4nNXsAgAAAAAAAND6EEoBAAAAAADA5wilAAAAAAAA4HOEUgAAAAAAAPA5QikAAAAAAAD4HKEUAAAAAAAAfI5QCgAAAAAAAD5HKAUAAAAAAACfI5QCAAAAAACAzxFKAQAAAAAAwOf+PyJ46p5RrA33AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from statsmodels.tsa.arima.model import ARIMA\n", "from statsmodels.tsa.stattools import adfuller, kpss\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "from statsmodels.stats.diagnostic import acorr_ljungbox\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "class TrendARIMAXForecaster:\n", "    def __init__(self):\n", "        self.model = None\n", "        self.fitted_model = None\n", "        self.data = None\n", "        self.target_col = 'Trend'\n", "        self.exog_cols = ['sales price（usd/lb）', 'Exchange(Domestic currency per US Dollar)']\n", "\n", "    def load_and_merge_data(self, trend_path, raw_path):\n", "        # 1. 读取Trend\n", "        trend_df = pd.read_csv(trend_path, parse_dates=['parsed_datetime'])\n", "        trend_df.set_index('parsed_datetime', inplace=True)\n", "        trend_df = trend_df[['Trend']]\n", "\n", "        # 2. 读取原始数据\n", "        def parse_datetime(date_str):\n", "            try:\n", "                month_str, year_str = date_str.split('-')\n", "                year_int = int(year_str)\n", "                if year_int <= 25:\n", "                    full_year = 2000 + year_int\n", "                else:\n", "                    full_year = 1900 + year_int\n", "                month_map = {'Jan':1,'Feb':2,'Mar':3,'Apr':4,'May':5,'Jun':6,\n", "                             'Jul':7,'Aug':8,'Sep':9,'Oct':10,'Nov':11,'Dec':12}\n", "                month_int = month_map[month_str]\n", "                return pd.Timestamp(year=full_year, month=month_int, day=1)\n", "            except:\n", "                return pd.NaT\n", "\n", "        raw_df = pd.read_csv(raw_path, encoding='gbk')\n", "        raw_df['parsed_datetime'] = raw_df['datetime'].apply(parse_datetime)\n", "        raw_df.set_index('parsed_datetime', inplace=True)\n", "        exog = raw_df[self.exog_cols]\n", "\n", "        # 合并\n", "        merged = trend_df.join(exog, how='inner')\n", "        merged = merged.dropna()\n", "        self.data = merged\n", "        return merged\n", "\n", "    def check_stationarity(self, series, name=\"序列\"):\n", "        adf_result = adfuller(series)\n", "        kpss_result = kpss(series, nlags=\"auto\")\n", "        print(f\"{name} - ADF p值: {adf_result[1]:.4f}, KPSS p值: {kpss_result[1]:.4f}\")\n", "        return adf_result[1] <= 0.05 and kpss_result[1] > 0.05\n", "\n", "    def difference_series(self, series, max_diff=2):\n", "        original_series = series.copy()\n", "        diff_order = 0\n", "        for d in range(max_diff + 1):\n", "            if d == 0:\n", "                current_series = original_series\n", "            else:\n", "                current_series = original_series.diff(d).dropna()\n", "            is_stationary = self.check_stationarity(current_series, f\"{d}阶差分\")\n", "            if is_stationary:\n", "                diff_order = d\n", "                break\n", "        return diff_order\n", "\n", "    def auto_arima_selection(self, y, exog=None, max_p=5, max_q=5, max_d=2):\n", "        print(\"\\n网格搜索最优ARIMAX(p,d,q)参数：\")\n", "        d = self.difference_series(y, max_d)\n", "        print(f\"选择的差分阶数 d = {d}\")\n", "        best_aic = float('inf')\n", "        best_params = None\n", "        results = []\n", "        for p in range(max_p + 1):\n", "            for q in range(max_q + 1):\n", "                try:\n", "                    model = ARIMA(y, exog=exog, order=(p, d, q))\n", "                    fit = model.fit()\n", "                    aic = fit.aic\n", "                    results.append({'p': p, 'd': d, 'q': q, 'AIC': aic})\n", "                    if aic < best_aic:\n", "                        best_aic = aic\n", "                        best_params = (p, d, q)\n", "                    print(f\"  ARIMAX({p},{d},{q}): AIC={aic:.2f}\")\n", "                except Exception as e:\n", "                    print(f\"  ARIMAX({p},{d},{q}): 拟合失败\")\n", "        results_df = pd.DataFrame(results)\n", "        print(f\"\\n最优参数: ARIMAX{best_params}, AIC={best_aic:.2f}\")\n", "        return best_params, results_df\n", "\n", "    def fit_and_predict(self, train_data, test_data, order):\n", "        y_train = train_data[self.target_col]\n", "        x_train = train_data[self.exog_cols]\n", "        y_test = test_data[self.target_col]\n", "        x_test = test_data[self.exog_cols]\n", "        model = ARIMA(y_train, exog=x_train, order=order)\n", "        fit = model.fit()\n", "        self.fitted_model = fit\n", "        train_pred = fit.fittedvalues\n", "        test_pred = fit.forecast(steps=len(test_data), exog=x_test)\n", "        return y_train, train_pred, y_test, test_pred\n", "\n", "    def calculate_metrics(self, y_true, y_pred):\n", "        mse = mean_squared_error(y_true, y_pred)\n", "        rmse = np.sqrt(mse)\n", "        mae = mean_absolute_error(y_true, y_pred)\n", "        mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100\n", "        direction_accuracy = np.mean(\n", "            np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred))\n", "        ) * 100 if len(y_true) > 1 else 0\n", "        return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'MAPE': mape, 'Direction_Accuracy': direction_accuracy}\n", "\n", "    def diagnostics(self):\n", "        residuals = self.fitted_model.resid\n", "        lb_result = acorr_ljungbox(residuals, lags=10)\n", "        lb_stat = lb_result['lb_stat'].iloc[-1]\n", "        lb_pvalue = lb_result['lb_pvalue'].iloc[-1]\n", "        print(f\"Ljung-Box统计量: {lb_stat:.4f}, p值: {lb_pvalue:.4f}\")\n", "        from scipy.stats import jarque_bera, probplot\n", "        jb_stat, jb_pvalue = jarque_bera(residuals)\n", "        print(f\"Jarque-Bera: 统计量={jb_stat:.4f}, p值={jb_pvalue:.4f}\")\n", "\n", "        fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "        axes[0,0].plot(residuals); axes[0,0].set_title(\"残差序列\")\n", "        axes[0,1].hist(residuals, bins=20); axes[0,1].set_title(\"残差直方图\")\n", "        plot_acf(residuals, ax=axes[1,0], lags=20)\n", "        axes[1,0].set_title(\"残差ACF\")\n", "        probplot(residuals, dist=\"norm\", plot=axes[1,1]); axes[1,1].set_title(\"残差Q-Q图\")\n", "        plt.tight_layout(); plt.show()\n", "\n", "if __name__ == \"__main__\":\n", "    trend_path = r\"D:\\埃塞俄比亚咖啡\\模型\\x11_decomposition_results.csv\"\n", "    raw_path = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "    forecaster = TrendARIMAXForecaster()\n", "    merged = forecaster.load_and_merge_data(trend_path, raw_path)\n", "    print(merged.describe())\n", "\n", "    # 训练测试集划分\n", "    split_ratio = 0.8\n", "    split_idx = int(len(merged) * split_ratio)\n", "    train, test = merged.iloc[:split_idx], merged.iloc[split_idx:]\n", "    print(f\"训练集: {train.shape[0]}, 测试集: {test.shape[0]}\")\n", "\n", "    # 自动调参\n", "    best_order, param_df = forecaster.auto_arima_selection(train['Trend'], train[forecaster.exog_cols])\n", "\n", "    # 拟合与预测\n", "    y_train, train_pred, y_test, test_pred = forecaster.fit_and_predict(train, test, best_order)\n", "\n", "    # 评估\n", "    train_metrics = forecaster.calculate_metrics(y_train, train_pred)\n", "    test_metrics = forecaster.calculate_metrics(y_test, test_pred)\n", "\n", "    print(\"训练集评估：\", train_metrics)\n", "    print(\"测试集评估：\", test_metrics)\n", "\n", "    # 诊断\n", "    forecaster.diagnostics()\n", "\n", "    # 可视化\n", "    plt.figure(figsize=(12,5))\n", "    plt.plot(merged.index, merged['Trend'], label='真实Trend', color='black')\n", "    plt.plot(train.index, train_pred, '--b', label='训练集拟合')\n", "    plt.plot(test.index, test_pred, '--r', label='测试集预测')\n", "    plt.legend(); plt.title('ARIMAX预测Trend分量'); plt.xlabel('时间'); plt.tight_layout(); plt.show()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "04fdb745-e448-4d67-b0e0-d620ff6636b0", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集样本数：124，测试集样本数：32\n", "                     Trend  sales price（usd/lb）  \\\n", "parsed_datetime                                   \n", "2012-07-01       60.783485             2.003427   \n", "2012-08-01       60.291183             1.857312   \n", "2012-09-01       59.798882             1.853827   \n", "2012-10-01       59.306580             1.681170   \n", "2012-11-01       58.814278             1.671289   \n", "\n", "                 Exchange(Domestic currency per US Dollar)  \n", "parsed_datetime                                             \n", "2012-07-01                                       17.800005  \n", "2012-08-01                                       17.878167  \n", "2012-09-01                                       17.935412  \n", "2012-10-01                                       18.003773  \n", "2012-11-01                                       18.075355  \n", "\n", "--- 差分阶数自动选择 ---\n", "  1阶差分: 平稳\n", "\n", "--- 网格搜索p,q ---\n", "ARIMA(0,1,0): AIC=469.35\n", "ARIMA(0,1,1): AIC=387.73\n", "ARIMA(0,1,2): AIC=367.97\n", "ARIMA(0,1,3): AIC=357.58\n", "ARIMA(0,1,4): AIC=347.70\n", "ARIMA(0,1,5): AIC=345.08\n", "ARIMA(1,1,0): AIC=342.39\n", "ARIMA(1,1,1): AIC=342.15\n", "ARIMA(1,1,2): AIC=342.39\n", "ARIMA(1,1,3): AIC=342.06\n", "ARIMA(1,1,4): AIC=343.54\n", "ARIMA(1,1,5): AIC=345.43\n", "ARIMA(2,1,0): AIC=342.82\n", "ARIMA(2,1,1): AIC=339.93\n", "ARIMA(2,1,2): AIC=343.49\n", "ARIMA(2,1,3): AIC=343.78\n", "ARIMA(2,1,4): AIC=345.49\n", "ARIMA(2,1,5): AIC=347.43\n", "ARIMA(3,1,0): AIC=343.51\n", "ARIMA(3,1,1): AIC=343.41\n", "ARIMA(3,1,2): AIC=344.56\n", "ARIMA(3,1,3): AIC=344.48\n", "ARIMA(3,1,4): AIC=341.66\n", "ARIMA(3,1,5): AIC=347.86\n", "ARIMA(4,1,0): AIC=341.32\n", "ARIMA(4,1,1): AIC=343.31\n", "ARIMA(4,1,2): AIC=344.74\n", "ARIMA(4,1,3): AIC=345.57\n", "ARIMA(4,1,4): AIC=346.32\n", "ARIMA(4,1,5): AIC=349.03\n", "ARIMA(5,1,0): AIC=343.31\n", "ARIMA(5,1,1): AIC=345.27\n", "ARIMA(5,1,2): AIC=345.74\n", "ARIMA(5,1,3): AIC=345.17\n", "ARIMA(5,1,4): AIC=345.84\n", "ARIMA(5,1,5): AIC=347.35\n", "\n", "最优模型参数：ARIMA(2, 1, 1)，AIC=339.93\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:                  Trend   No. Observations:                  124\n", "Model:                 ARIMA(2, 1, 1)   Log Likelihood                -163.965\n", "Date:                Thu, 24 Jul 2025   AIC                            339.929\n", "Time:                        15:40:30   BIC                            356.803\n", "Sample:                    07-01-2012   HQIC                           346.783\n", "                         - 10-01-2022                                         \n", "Covariance Type:                  opg                                         \n", "=============================================================================================================\n", "                                                coef    std err          z      P>|z|      [0.025      0.975]\n", "-------------------------------------------------------------------------------------------------------------\n", "sales price（usd/lb）                           1.4730      0.708      2.079      0.038       0.084       2.862\n", "Exchange(Domestic currency per US Dollar)     0.2146      0.279      0.770      0.441      -0.332       0.761\n", "ar.L1                                        -0.0042      0.079     -0.053      0.958      -0.159       0.151\n", "ar.L2                                         0.6737      0.113      5.969      0.000       0.452       0.895\n", "ma.L1                                         0.9995      2.321      0.431      0.667      -3.549       5.548\n", "sigma2                                        0.8168      1.904      0.429      0.668      -2.915       4.548\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.00   Jar<PERSON><PERSON><PERSON>ra (JB):               447.84\n", "Prob(Q):                              1.00   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               0.59   Skew:                            -0.50\n", "Prob(H) (two-sided):                  0.09   Kurtosis:                        12.29\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n", "\n", "Ljung-Box检验 p值: 0.9965256344887221\n", "\n", "测试集 MSE: 2446.74  MAE: 35.07  MAPE: 19.53%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from statsmodels.tsa.arima.model import ARIMA\n", "from statsmodels.tsa.stattools import adfuller, kpss\n", "from statsmodels.stats.diagnostic import acorr_ljungbox\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 1. 读取Trend和外生变量数据\n", "trend_path = r\"D:\\埃塞俄比亚咖啡\\模型\\x11_decomposition_results.csv\"\n", "raw_path = r\"D:\\埃塞俄比亚咖啡\\2005(2013)-2017(2025) monthly data(1).csv\"\n", "\n", "# Trend文件\n", "trend_df = pd.read_csv(trend_path)\n", "trend_df['parsed_datetime'] = pd.to_datetime(trend_df['parsed_datetime'])\n", "trend_df.set_index('parsed_datetime', inplace=True)\n", "\n", "# 外生变量\n", "raw_df = pd.read_csv(raw_path, encoding='gbk')\n", "def parse_datetime(date_str):\n", "    try:\n", "        month_str, year_str = date_str.split('-')\n", "        year_int = int(year_str)\n", "        full_year = 2000 + year_int if year_int <= 25 else 1900 + year_int\n", "        month_map = {'Jan':1,'Feb':2,'Mar':3,'Apr':4,'May':5,'Jun':6,\n", "                     'Jul':7,'Aug':8,'Sep':9,'Oct':10,'Nov':11,'Dec':12}\n", "        month_int = month_map[month_str]\n", "        return pd.Timestamp(year=full_year, month=month_int, day=1)\n", "    except:\n", "        return pd.NaT\n", "\n", "raw_df['parsed_datetime'] = raw_df['datetime'].apply(parse_datetime)\n", "raw_df = raw_df.dropna(subset=['parsed_datetime'])\n", "raw_df.set_index('parsed_datetime', inplace=True)\n", "raw_df = raw_df.sort_index()\n", "\n", "# 只保留与Trend时间重合的数据\n", "exog_cols = ['sales price（usd/lb）', 'Exchange(Domestic currency per US Dollar)']\n", "exog_data = raw_df[exog_cols].reindex(trend_df.index)\n", "# 如果有缺失先填充（可选，也可dropna）\n", "exog_data = exog_data.fillna(method='ffill').fillna(method='bfill')\n", "\n", "# 合并\n", "full_data = trend_df[['Trend']].join(exog_data)\n", "full_data = full_data.dropna()\n", "\n", "# 2. 分割训练/测试集\n", "split_ratio = 0.8\n", "split_idx = int(len(full_data) * split_ratio)\n", "train = full_data.iloc[:split_idx]\n", "test = full_data.iloc[split_idx:]\n", "\n", "y_train = train['Trend']\n", "X_train = train[exog_cols]\n", "y_test = test['Trend']\n", "X_test = test[exog_cols]\n", "\n", "print(f\"训练集样本数：{len(train)}，测试集样本数：{len(test)}\")\n", "print(train.head())\n", "\n", "# 3. 自动选择差分d\n", "def auto_difference(y, max_diff=2):\n", "    for d in range(max_diff+1):\n", "        s = y.diff(d).dropna() if d>0 else y\n", "        adf = adfuller(s)[1] <= 0.05\n", "        kpss_ok = kpss(s, nlags='auto')[1] > 0.05\n", "        if adf and kpss_ok:\n", "            print(f\"  {d}阶差分: 平稳\")\n", "            return d\n", "    print(\"  警告：未检出完全平稳，采用最大差分\")\n", "    return max_diff\n", "\n", "print(\"\\n--- 差分阶数自动选择 ---\")\n", "d = auto_difference(y_train, max_diff=2)\n", "\n", "# 4. 网格搜参\n", "print(\"\\n--- 网格搜索p,q ---\")\n", "best_aic = float('inf')\n", "best_order = (0, d, 0)\n", "results = []\n", "for p in range(0, 6):\n", "    for q in range(0, 6):\n", "        try:\n", "            m = ARIMA(y_train, exog=X_train, order=(p, d, q)).fit()\n", "            aic = m.aic\n", "            results.append({'p':p,'d':d,'q':q,'AIC':aic})\n", "            if aic < best_aic:\n", "                best_aic = aic\n", "                best_order = (p, d, q)\n", "            print(f\"ARIMA({p},{d},{q}): AIC={aic:.2f}\")\n", "        except:\n", "            print(f\"ARIMA({p},{d},{q}) 拟合失败\")\n", "results_df = pd.DataFrame(results)\n", "\n", "print(f\"\\n最优模型参数：ARIMA{best_order}，AIC={best_aic:.2f}\")\n", "\n", "# 5. 拟合最优模型\n", "model = ARIMA(y_train, exog=X_train, order=best_order)\n", "fitted = model.fit()\n", "print(fitted.summary())\n", "\n", "# 6. 诊断与预测\n", "resid = fitted.resid\n", "lb_res = acorr_ljungbox(resid, lags=[10], return_df=True)\n", "print(\"\\nLjung-Box检验 p值:\", lb_res['lb_pvalue'].values[0])\n", "\n", "# 样本内预测\n", "train_pred = fitted.fittedvalues\n", "\n", "# 样本外预测（使用真实的测试集外生变量）\n", "test_pred = fitted.forecast(steps=len(y_test), exog=X_test)\n", "mse = mean_squared_error(y_test, test_pred)\n", "mae = mean_absolute_error(y_test, test_pred)\n", "mape = np.mean(np.abs((y_test - test_pred)/y_test))*100\n", "\n", "print(f\"\\n测试集 MSE: {mse:.2f}  MAE: {mae:.2f}  MAPE: {mape:.2f}%\")\n", "\n", "# 7. 可视化\n", "plt.figure(figsize=(15,5))\n", "plt.plot(full_data.index, full_data['Trend'], label='True Trend')\n", "plt.plot(train.index, train_pred, 'b--', label='Train Fit')\n", "plt.plot(test.index, test_pred, 'g-', label='Test Predict')\n", "plt.title(f'ARIMAX{best_order}预测Trend（测试集用真实外生变量）')\n", "plt.legend()\n", "plt.grid(alpha=0.3)\n", "plt.show()\n", "\n", "# 可选：结果DataFrame导出\n", "results_out = test.copy()\n", "results_out['pred'] = test_pred\n", "results_out.to_csv(r\"D:\\埃塞俄比亚咖啡\\模型\\arimax_trend_test_predict.csv\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "147ef39c-3913-44c4-b5f9-8d5ac0c368c2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38env", "language": "python", "name": "py38env"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}