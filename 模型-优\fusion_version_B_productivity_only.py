"""
版本B：X11分解融合预测模型 - 仅使用productivity_lag1
Trend: 线性回归8特征模型 (添加productivity_lag1)
Seasonal: 完全周期性模式
Residual: 梯度提升13特征模型 (不添加产量变量)
最终预测 = Trend预测 + Seasonal预测 + Residual预测

实验目的: 测试产能(productivity_lag1)对预测效果的影响
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 配置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

print("=== 版本B：X11分解融合预测模型 - 仅使用productivity_lag1 ===")
print("Trend: 线性回归8特征模型 (添加productivity_lag1)")
print("Seasonal: 完全周期性模式")
print("Residual: 梯度提升13特征模型 (不添加产量变量)")
print("实验目的: 测试产能(productivity_lag1)对预测效果的影响")

# 1. 数据加载
print("\n1. 数据加载")
print("-" * 50)

# 加载X11分解结果
decomp_path = r"D:\埃塞俄比亚咖啡\模型\x11_decomposition_results.csv"
decomp_data = pd.read_csv(decomp_path)

# 加载外生变量（包含prod_lag1和productivity_lag1）
exog_path = r"D:\埃塞俄比亚咖啡\2005(2013)-2017(2025) monthly data(1).csv"
exog_data = pd.read_csv(exog_path, encoding='gbk')

print(f"X11分解数据: {decomp_data.shape}")
print(f"外生变量数据: {exog_data.shape}")

# 检查数据列
if 'prod_lag1' in exog_data.columns:
    print("✓ 找到prod_lag1列")
if 'productivity_lag1' in exog_data.columns:
    print("✓ 找到productivity_lag1列")
    print(f"productivity_lag1样本值: {exog_data['productivity_lag1'].dropna().head().tolist()}")

# 2. 数据合并和预处理
print("\n2. 数据合并和预处理")
print("-" * 50)

# 合并数据
min_len = min(len(decomp_data), len(exog_data))

# 处理productivity_lag1变量
productivity_lag1_col = exog_data['productivity_lag1'].iloc[:min_len]
if productivity_lag1_col.dtype == 'object':
    productivity_lag1_col = productivity_lag1_col.astype(str).str.replace(',', '').str.replace('"', '')
    productivity_lag1_col = pd.to_numeric(productivity_lag1_col, errors='coerce')
    print("✓ productivity_lag1已转换为数值格式")

data = pd.DataFrame({
    'date': pd.to_datetime(decomp_data['parsed_datetime'].iloc[:min_len]),
    'original': decomp_data['Original'].iloc[:min_len],
    'trend': decomp_data['Trend'].iloc[:min_len],
    'seasonal': decomp_data['Seasonal'].iloc[:min_len],
    'residual': decomp_data['Residual'].iloc[:min_len],
    'sales_price': exog_data['sales price（usd/lb）'].iloc[:min_len],
    'exchange_rate': exog_data['Exchange(Domestic currency per US Dollar)'].iloc[:min_len],
    'productivity_lag1': productivity_lag1_col  # 仅使用产能
})

data.set_index('date', inplace=True)
data = data.dropna()

print(f"合并后数据量: {len(data)} 样本")
print(f"时间范围: {data.index[0]} - {data.index[-1]}")
print(f"productivity_lag1统计: 最小值={data['productivity_lag1'].min():.2f}, 最大值={data['productivity_lag1'].max():.2f}")

# 3. 数据分割
print("\n3. 数据分割")
print("-" * 50)

split_date = '2022-12-01'
train_data = data[data.index < split_date]
test_data = data[data.index >= split_date]

print(f"训练集: {len(train_data)} 样本 ({train_data.index[0]} - {train_data.index[-1]})")
print(f"测试集: {len(test_data)} 样本 ({test_data.index[0]} - {test_data.index[-1]})")

# 4. Trend预测 - 添加productivity_lag1
print("\n4. Trend预测 - 线性回归8特征模型（添加productivity_lag1）")
print("-" * 50)

def create_trend_features(data, start_index=0):
    """创建Trend预测的8个特征（添加productivity_lag1）"""
    features_data = data.copy()
    
    # 时间特征 - 保持连续的时间索引
    features_data['year'] = features_data.index.year
    features_data['month'] = features_data.index.month
    features_data['time_index'] = range(start_index, start_index + len(features_data))
    
    # Trend历史特征
    features_data['trend_lag_1'] = features_data['trend'].shift(1)
    features_data['trend_lag_12'] = features_data['trend'].shift(12)
    
    # 外生变量
    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)
    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)
    
    # 版本B：仅使用产能
    features_data['productivity_lag1'] = features_data['productivity_lag1']
    
    return features_data.dropna()

# Trend特征列表（8个特征）
trend_features_list = [
    'time_index', 'trend_lag_1', 'trend_lag_12', 'sales_price_lag_1', 
    'exchange_rate_lag_1', 'year', 'month', 'productivity_lag1'
]

# 为了保证连续性，需要使用全部数据创建特征，然后再分割
full_data_for_trend = pd.concat([train_data, test_data])
full_trend_features = create_trend_features(full_data_for_trend)

# 根据日期分割训练集和测试集
trend_train_data = full_trend_features[full_trend_features.index < pd.to_datetime('2022-12-01')]
trend_test_data = full_trend_features[full_trend_features.index >= pd.to_datetime('2022-12-01')]

if len(trend_test_data) == 0:
    full_trend_data = create_trend_features(data)
    trend_split_idx = int(len(full_trend_data) * 0.8)
    trend_train_data = full_trend_data.iloc[:trend_split_idx]
    trend_test_data = full_trend_data.iloc[trend_split_idx:]

X_trend_train = trend_train_data[trend_features_list]
y_trend_train = trend_train_data['trend']
X_trend_test = trend_test_data[trend_features_list]
y_trend_test = trend_test_data['trend']

# 标准化和训练
trend_scaler = StandardScaler()
X_trend_train_scaled = trend_scaler.fit_transform(X_trend_train)
X_trend_test_scaled = trend_scaler.transform(X_trend_test)

trend_model = LinearRegression()
trend_model.fit(X_trend_train_scaled, y_trend_train)

trend_train_pred = trend_model.predict(X_trend_train_scaled)
trend_test_pred = trend_model.predict(X_trend_test_scaled)

print(f"✓ Trend模型训练完成（版本B：含productivity_lag1）")
print(f"  训练集RMSE: {np.sqrt(mean_squared_error(y_trend_train, trend_train_pred)):.2f}")
print(f"  测试集RMSE: {np.sqrt(mean_squared_error(y_trend_test, trend_test_pred)):.2f}")
print(f"  测试集R²: {r2_score(y_trend_test, trend_test_pred):.4f}")

# 打印Trend线性公式和系数
print("\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
print("Trend线性回归公式:")
print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

# 获取截距和系数
intercept = trend_model.intercept_
coefficients = trend_model.coef_

# 创建系数映射
coef_mapping = {
    'time_index': 'β₁', 
    'trend_lag_1': 'β₂', 
    'trend_lag_12': 'β₃', 
    'sales_price_lag_1': 'β₄',
    'exchange_rate_lag_1': 'β₅', 
    'year': 'β₆', 
    'month': 'β₇', 
    'productivity_lag1': 'β₈'
}

# 打印公式
print(f"\nTrend = {intercept:.4f} +")
for i, feature in enumerate(trend_features_list):
    beta = coef_mapping[feature]
    coef = coefficients[i]
    sign = "+" if coef >= 0 else ""
    if i == len(trend_features_list) - 1:
        print(f"        {sign}{coef:.6f} × {feature}")
    else:
        print(f"        {sign}{coef:.6f} × {feature} +")

# 打印系数表格
print("\n┌──────────────────────────┬────────────┬─────────────┐")
print("│ 特征名称                 │ 系数符号   │ 系数值      │")
print("├──────────────────────────┼────────────┼─────────────┤")
print(f"│ 截距项                   │ β₀         │ {intercept:11.4f} │")
for i, feature in enumerate(trend_features_list):
    beta = coef_mapping[feature]
    coef = coefficients[i]
    print(f"│ {feature:<24} │ {beta:<10} │ {coef:11.6f} │")
print("└──────────────────────────┴────────────┴─────────────┘")

# 特征重要性分析
trend_feature_importance = pd.DataFrame({
    'feature': trend_features_list,
    'coefficient': trend_model.coef_,
    'abs_coefficient': np.abs(trend_model.coef_)
}).sort_values('abs_coefficient', ascending=False)

# 分析8个因素对trend的影响
print("\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
print("Trend影响因素分析（按绝对值重要性排序）:")
print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

for idx, row in trend_feature_importance.iterrows():
    feature = row['feature']
    coef = row['coefficient']
    abs_coef = row['abs_coefficient']
    beta = coef_mapping[feature]
    
    # 根据系数正负判断影响方向
    if coef > 0:
        direction = "正向影响"
        explanation = {
            'time_index': "随时间推移，出口额呈上升趋势",
            'trend_lag_1': "前期趋势越高，当期趋势越高（动量效应）",
            'trend_lag_12': "去年同期趋势越高，当期趋势越高（年度周期）",
            'sales_price_lag_1': "前期价格越高，趋势越强",
            'exchange_rate_lag_1': "前期汇率越高（本币贬值），趋势越强",
            'year': "年份越晚，趋势值越高",
            'month': "月份数值越大，趋势越高",
            'productivity_lag1': "前期产能越高，趋势越强"
        }[feature]
    else:
        direction = "负向影响"
        explanation = {
            'time_index': "随时间推移，出口额呈下降趋势",
            'trend_lag_1': "前期趋势越高，当期趋势越低（均值回归）",
            'trend_lag_12': "去年同期趋势越高，当期趋势越低",
            'sales_price_lag_1': "前期价格越高，趋势越弱",
            'exchange_rate_lag_1': "前期汇率越高（本币贬值），趋势越弱",
            'year': "年份越晚，趋势值越低",
            'month': "月份数值越大，趋势越低",
            'productivity_lag1': "前期产能越高，趋势越弱"
        }[feature]
    
    print(f"\n{idx+1}. {feature} ({beta})")
    print(f"   系数: {coef:.6f} | 绝对值: {abs_coef:.6f}")
    print(f"   影响: {direction}")
    print(f"   解释: {explanation}")

productivity_importance_rank = trend_feature_importance[trend_feature_importance['feature'] == 'productivity_lag1'].index[0] + 1
productivity_coefficient = trend_feature_importance[trend_feature_importance['feature'] == 'productivity_lag1']['coefficient'].iloc[0]
print(f"\n特别说明: productivity_lag1重要性排名: {productivity_importance_rank}/8")
print(f"         productivity_lag1系数: {productivity_coefficient:.6f}")

# 5. Seasonal预测 - 不变
print("\n5. Seasonal预测 - 完全周期性模式")
print("-" * 50)

seasonal_pattern = train_data.groupby(train_data.index.month)['seasonal'].mean()
print(f"✓ 季节性模式计算完成")

def predict_seasonal(dates, pattern):
    months = [date.month for date in dates]
    return np.array([pattern[month] for month in months])

seasonal_train_pred = predict_seasonal(trend_train_data.index, seasonal_pattern)
seasonal_test_pred = predict_seasonal(trend_test_data.index, seasonal_pattern)

seasonal_train_actual = trend_train_data['seasonal'].values
seasonal_test_actual = trend_test_data['seasonal'].values

print(f"  测试集RMSE: {np.sqrt(mean_squared_error(seasonal_test_actual, seasonal_test_pred)):.4f}")
print(f"  测试集R²: {r2_score(seasonal_test_actual, seasonal_test_pred):.4f}")

# 6. Residual预测 - 不添加产量变量
print("\n6. Residual预测 - 梯度提升13特征模型（不添加产量变量）")
print("-" * 50)

def create_residual_features(data):
    """创建Residual预测的13个特征（不添加产量变量）"""
    features_data = data.copy()

    features_data['month'] = features_data.index.month
    
    for lag in [1, 3, 6]:
        features_data[f'residual_lag_{lag}'] = features_data['residual'].shift(lag)

    features_data['residual_volatility'] = features_data['residual'].rolling(window=6).std()
    features_data['trend_change'] = features_data['trend'].pct_change()
    features_data['seasonal_abs'] = np.abs(features_data['seasonal'])
    
    features_data['sales_price_lag_1'] = features_data['sales_price'].shift(1)
    features_data['exchange_rate_lag_1'] = features_data['exchange_rate'].shift(1)
    features_data['sales_price_change'] = features_data['sales_price'].pct_change()
    features_data['exchange_rate_change'] = features_data['exchange_rate'].pct_change()
    features_data['exchange_sales_interaction'] = features_data['exchange_rate'] * features_data['sales_price']
    
    features_data['exchange_rate'] = features_data['exchange_rate']
    features_data['sales_price'] = features_data['sales_price']

    return features_data.dropna()

# Residual特征列表（13个特征，不含产量变量）
residual_features_list = [
    'residual_lag_1', 'residual_lag_6', 'residual_volatility',
    'exchange_rate', 'sales_price',
    'exchange_rate_lag_1', 'sales_price_lag_1',
    'exchange_rate_change', 'sales_price_change',
    'exchange_sales_interaction',
    'seasonal_abs', 'trend_change',
    'month'
]

# 为了保证连续性，使用全部数据创建特征，然后再分割
full_data_for_residual = pd.concat([train_data, test_data])
full_residual_features = create_residual_features(full_data_for_residual)

# 根据日期分割训练集和测试集
residual_train_data = full_residual_features[full_residual_features.index < pd.to_datetime('2022-12-01')]
residual_test_data = full_residual_features[full_residual_features.index >= pd.to_datetime('2022-12-01')]

if len(residual_test_data) == 0:
    full_residual_data = create_residual_features(data)
    residual_split_idx = int(len(full_residual_data) * 0.8)
    residual_train_data = full_residual_data.iloc[:residual_split_idx]
    residual_test_data = full_residual_data.iloc[residual_split_idx:]

available_features = [f for f in residual_features_list if f in residual_train_data.columns]
print(f"可用Residual特征: {len(available_features)}/{len(residual_features_list)}")

X_residual_train = residual_train_data[available_features]
y_residual_train = residual_train_data['residual']
X_residual_test = residual_test_data[available_features]
y_residual_test = residual_test_data['residual']

residual_model = GradientBoostingRegressor(
    n_estimators=50, max_depth=3, learning_rate=0.05, subsample=0.8,
    min_samples_split=10, min_samples_leaf=5, max_features='sqrt', random_state=42
)

residual_model.fit(X_residual_train, y_residual_train)

residual_train_pred = residual_model.predict(X_residual_train)
residual_test_pred = residual_model.predict(X_residual_test)

print(f"✓ Residual模型训练完成（版本B：不含产量变量）")
print(f"  训练集RMSE: {np.sqrt(mean_squared_error(y_residual_train, residual_train_pred)):.2f}")
print(f"  测试集RMSE: {np.sqrt(mean_squared_error(y_residual_test, residual_test_pred)):.2f}")
print(f"  测试集R²: {r2_score(y_residual_test, residual_test_pred):.4f}")

# Residual特征重要性分析
residual_feature_importance = pd.DataFrame({
    'feature': available_features,
    'importance': residual_model.feature_importances_
}).sort_values('importance', ascending=False)

print(f"\nResidual模型特征重要性排名:")
for i, row in residual_feature_importance.iterrows():
    print(f"  {row.name+1}. {row['feature']}: {row['importance']:.4f}")

# 7. 最终融合预测
print("\n7. 最终融合预测")
print("-" * 50)

split_date = pd.to_datetime('2022-12-01')
original_train_data = data[data.index < split_date]
original_test_data = data[data.index >= split_date]

def predict_step_by_step(test_data, train_data, models, scalers, seasonal_pattern):
    trend_model, residual_model = models
    trend_scaler = scalers

    trend_predictions = []
    seasonal_predictions = []
    residual_predictions = []
    final_predictions = []

    full_data = pd.concat([train_data, test_data])

    for i, test_date in enumerate(test_data.index):
        print(f"  预测 {test_date.strftime('%Y-%m')}...")

        current_data = full_data[full_data.index <= test_date]

        # Trend预测 - 保持时间索引连续
        trend_features_data = create_trend_features(current_data, start_index=0)
        if len(trend_features_data) > 0 and test_date in trend_features_data.index:
            trend_X = trend_features_data.loc[[test_date], trend_features_list]
            trend_X_scaled = trend_scaler.transform(trend_X)
            trend_pred = trend_model.predict(trend_X_scaled)[0]
        else:
            trend_pred = current_data['trend'].iloc[-1]

        # Seasonal预测
        seasonal_pred = seasonal_pattern[test_date.month]

        # Residual预测
        residual_features_data = create_residual_features(current_data)
        if len(residual_features_data) > 0 and test_date in residual_features_data.index:
            residual_X = residual_features_data.loc[[test_date], available_features]
            residual_pred = residual_model.predict(residual_X)[0]
        else:
            residual_pred = 0.0

        final_pred = trend_pred + seasonal_pred + residual_pred

        trend_predictions.append(trend_pred)
        seasonal_predictions.append(seasonal_pred)
        residual_predictions.append(residual_pred)
        final_predictions.append(final_pred)

    return (np.array(trend_predictions), np.array(seasonal_predictions),
            np.array(residual_predictions), np.array(final_predictions))

print("开始逐步预测测试集...")

models = (trend_model, residual_model)
scalers = trend_scaler

(trend_test_pred_full, seasonal_test_pred_full,
 residual_test_pred_full, final_test_pred_full) = predict_step_by_step(
    original_test_data, original_train_data, models, scalers, seasonal_pattern)

# 训练集预测对齐
train_available_mask = original_train_data.index.isin(trend_train_data.index)
final_train_dates = original_train_data.index[train_available_mask]
final_train_actual = original_train_data['original'][train_available_mask].values

train_trend_aligned = []
train_seasonal_aligned = []
train_residual_aligned = []

for date in final_train_dates:
    if date in trend_train_data.index:
        idx = trend_train_data.index.get_loc(date)
        train_trend_aligned.append(trend_train_pred[idx])
        train_seasonal_aligned.append(predict_seasonal([date], seasonal_pattern)[0])

        if date in residual_train_data.index:
            residual_idx = residual_train_data.index.get_loc(date)
            train_residual_aligned.append(residual_train_pred[residual_idx])
        else:
            train_residual_aligned.append(0.0)

final_train_pred = np.array(train_trend_aligned) + np.array(train_seasonal_aligned) + np.array(train_residual_aligned)

final_test_dates = original_test_data.index
final_test_actual = original_test_data['original'].values
final_test_pred = final_test_pred_full

print(f"✓ 版本B预测完成!")

# 8. 性能评估
def calculate_metrics(y_true, y_pred):
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100

    if len(y_true) > 1:
        direction_accuracy = np.mean(np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred))) * 100
    else:
        direction_accuracy = 0

    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2, 'MAPE': mape, 'Direction_Accuracy': direction_accuracy}

final_train_metrics = calculate_metrics(final_train_actual, final_train_pred)
final_test_metrics = calculate_metrics(final_test_actual, final_test_pred)
final_overfitting_ratio = final_test_metrics['RMSE'] / final_train_metrics['RMSE']

print(f"\n🎉 版本B：仅使用productivity_lag1的融合预测完成!")
print(f"\n训练集性能:")
print(f"  RMSE: {final_train_metrics['RMSE']:.2f} 百万美元")
print(f"  R²: {final_train_metrics['R2']:.4f}")
print(f"  MAPE: {final_train_metrics['MAPE']:.2f}%")

print(f"\n测试集性能:")
print(f"  RMSE: {final_test_metrics['RMSE']:.2f} 百万美元")
print(f"  R²: {final_test_metrics['R2']:.4f}")
print(f"  MAPE: {final_test_metrics['MAPE']:.2f}%")
print(f"  方向准确性: {final_test_metrics['Direction_Accuracy']:.1f}%")
print(f"  过拟合比率: {final_overfitting_ratio:.3f}")

# 9. 结果保存
print("\n9. 结果保存")
print("-" * 50)

# 保存详细结果
results_df = pd.DataFrame({
    'date': np.concatenate([final_train_dates, final_test_dates]),
    'actual': np.concatenate([final_train_actual, final_test_actual]),
    'predicted': np.concatenate([final_train_pred, final_test_pred]),
    'trend_pred': np.concatenate([train_trend_aligned, trend_test_pred_full]),
    'seasonal_pred': np.concatenate([train_seasonal_aligned, seasonal_test_pred_full]),
    'residual_pred': np.concatenate([train_residual_aligned, residual_test_pred_full]),
    'dataset': ['train'] * len(final_train_dates) + ['test'] * len(final_test_dates)
})

results_path = r"D:\埃塞俄比亚咖啡\模型\version_B_productivity_only_results.csv"
results_df.to_csv(results_path, index=False)
print(f"✓ 版本B结果已保存: {results_path}")

# 保存性能指标
performance_df = pd.DataFrame({
    '版本': ['B_productivity_only'],
    '产量变量': ['productivity_lag1'],
    'Trend特征数': [8],
    'Residual特征数': [len(available_features)],
    '测试集RMSE': [final_test_metrics['RMSE']],
    '测试集R²': [final_test_metrics['R2']],
    '测试集MAPE': [final_test_metrics['MAPE']],
    '方向准确性': [final_test_metrics['Direction_Accuracy']],
    '过拟合比率': [final_overfitting_ratio],
    'productivity_lag1_rank': [productivity_importance_rank],
    'productivity_lag1_coef': [productivity_coefficient]
})

performance_path = r"D:\埃塞俄比亚咖啡\模型\version_B_performance.csv"
performance_df.to_csv(performance_path, index=False)
print(f"✓ 版本B性能指标已保存: {performance_path}")

# 10. 可视化对比分析
print("\n10. 可视化对比分析")
print("-" * 50)

# ===== 第一组图：三个组件的原始数据可视化 =====
fig1, axes1 = plt.subplots(3, 1, figsize=(16, 12))
fig1.suptitle('X11分解三个组件的时间序列', fontsize=16, fontweight='bold')

# Trend组件
ax1 = axes1[0]
ax1.plot(data.index, data['trend'], 'blue', linewidth=2)
ax1.set_title('Trend组件（长期趋势）', fontsize=14)
ax1.set_ylabel('趋势值 (百万美元)')
ax1.grid(True, alpha=0.3)
ax1.axvline(x=pd.to_datetime('2022-12-01'), color='red', linestyle='--', alpha=0.5, label='训练/测试分割点')
ax1.legend()

# Seasonal组件
ax2 = axes1[1]
ax2.plot(data.index, data['seasonal'], 'green', linewidth=2)
ax2.set_title('Seasonal组件（季节性模式）', fontsize=14)
ax2.set_ylabel('季节性值 (百万美元)')
ax2.grid(True, alpha=0.3)
ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
ax2.axvline(x=pd.to_datetime('2022-12-01'), color='red', linestyle='--', alpha=0.5, label='训练/测试分割点')
ax2.legend()

# Residual组件
ax3 = axes1[2]
ax3.plot(data.index, data['residual'], 'orange', linewidth=1)
ax3.set_title('Residual组件（残差/不规则变动）', fontsize=14)
ax3.set_ylabel('残差值 (百万美元)')
ax3.set_xlabel('日期')
ax3.grid(True, alpha=0.3)
ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
ax3.axvline(x=pd.to_datetime('2022-12-01'), color='red', linestyle='--', alpha=0.5, label='训练/测试分割点')
ax3.legend()

plt.tight_layout()
plt.show()

# ===== 第二组图：Trend预测效果 =====
fig2, axes2 = plt.subplots(2, 2, figsize=(16, 10))
fig2.suptitle('Trend组件预测效果分析', fontsize=16, fontweight='bold')

# Trend训练集与测试集预测
ax4 = axes2[0, 0]
# 获取原始trend数据用于显示完整的时间序列
train_trend_full = train_data['trend']
test_trend_full = test_data['trend']
ax4.plot(train_trend_full.index, train_trend_full.values, 'black', label='训练集真实Trend', linewidth=2, alpha=0.8)
ax4.plot(test_trend_full.index, test_trend_full.values, 'black', label='测试集真实Trend', linewidth=2)
# 只在有预测值的地方画预测线
ax4.plot(trend_train_data.index, trend_train_pred, 'blue', label='训练集预测', linewidth=1.5, alpha=0.7)
ax4.plot(trend_test_data.index, trend_test_pred, 'red', label='测试集预测', linewidth=2)
ax4.axvline(x=pd.to_datetime('2022-12-01'), color='gray', linestyle=':', alpha=0.7, label='训练/测试分割')
ax4.set_title('Trend预测结果')
ax4.set_ylabel('Trend值 (百万美元)')
ax4.legend()
ax4.grid(True, alpha=0.3)

# Trend预测散点图
ax5 = axes2[0, 1]
ax5.scatter(y_trend_train, trend_train_pred, alpha=0.5, s=30, color='blue', label='训练集')
ax5.scatter(y_trend_test, trend_test_pred, alpha=0.7, s=50, color='red', label='测试集')
min_val = min(y_trend_train.min(), y_trend_test.min())
max_val = max(y_trend_train.max(), y_trend_test.max())
ax5.plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, label='完美预测线')
ax5.set_xlabel('真实Trend值')
ax5.set_ylabel('预测Trend值')
ax5.set_title(f'Trend预测准确性\n训练R²={r2_score(y_trend_train, trend_train_pred):.3f}, 测试R²={r2_score(y_trend_test, trend_test_pred):.3f}')
ax5.legend()
ax5.grid(True, alpha=0.3)

# Trend预测误差时间序列
ax6 = axes2[1, 0]
train_errors = y_trend_train - trend_train_pred
test_errors = y_trend_test - trend_test_pred
ax6.plot(trend_train_data.index, train_errors, 'b-', linewidth=1, alpha=0.5, label='训练集误差')
ax6.plot(trend_test_data.index, test_errors, 'ro-', markersize=5, linewidth=1.5, label='测试集误差')
ax6.axhline(y=0, color='black', linestyle='--', alpha=0.7)
ax6.axhline(y=test_errors.std(), color='red', linestyle=':', alpha=0.5, label='±1σ')
ax6.axhline(y=-test_errors.std(), color='red', linestyle=':', alpha=0.5)
ax6.set_ylabel('预测误差')
ax6.set_title('Trend预测误差分析')
ax6.legend()
ax6.grid(True, alpha=0.3)

# Trend特征贡献度
ax7 = axes2[1, 1]
trend_feature_importance_plot = trend_feature_importance.sort_values('abs_coefficient', ascending=True)
colors = ['red' if x < 0 else 'green' for x in trend_feature_importance_plot['coefficient']]
ax7.barh(range(len(trend_feature_importance_plot)), trend_feature_importance_plot['coefficient'], color=colors)
ax7.set_yticks(range(len(trend_feature_importance_plot)))
ax7.set_yticklabels(trend_feature_importance_plot['feature'])
ax7.set_xlabel('系数值')
ax7.set_title('Trend模型特征系数')
ax7.grid(True, alpha=0.3, axis='x')

plt.tight_layout()
plt.show()

# ===== 第三组图：Residual预测效果 =====
fig3, axes3 = plt.subplots(2, 2, figsize=(16, 10))
fig3.suptitle('Residual组件预测效果分析', fontsize=16, fontweight='bold')

# Residual训练集与测试集预测
ax8 = axes3[0, 0]
# 获取原始residual数据用于显示完整的时间序列
train_residual_full = train_data['residual']
test_residual_full = test_data['residual']
ax8.plot(train_residual_full.index, train_residual_full.values, 'black', label='训练集真实Residual', linewidth=1, alpha=0.8)
ax8.plot(test_residual_full.index, test_residual_full.values, 'black', label='测试集真实Residual', linewidth=1.5)
# 只在有预测值的地方画预测线
ax8.plot(residual_train_data.index, residual_train_pred, 'blue', label='训练集预测', linewidth=1, alpha=0.7)
ax8.plot(residual_test_data.index, residual_test_pred, 'red', label='测试集预测', linewidth=1.5)
ax8.axvline(x=pd.to_datetime('2022-12-01'), color='gray', linestyle=':', alpha=0.7, label='训练/测试分割')
ax8.axhline(y=0, color='black', linestyle='-', alpha=0.3)
ax8.set_title('Residual预测结果')
ax8.set_ylabel('Residual值 (百万美元)')
ax8.legend()
ax8.grid(True, alpha=0.3)

# Residual预测散点图
ax9 = axes3[0, 1]
ax9.scatter(y_residual_train, residual_train_pred, alpha=0.5, s=30, color='blue', label='训练集')
ax9.scatter(y_residual_test, residual_test_pred, alpha=0.7, s=50, color='red', label='测试集')
min_val = min(y_residual_train.min(), y_residual_test.min())
max_val = max(y_residual_train.max(), y_residual_test.max())
ax9.plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, label='完美预测线')
ax9.set_xlabel('真实Residual值')
ax9.set_ylabel('预测Residual值')
ax9.set_title(f'Residual预测准确性\n训练R²={r2_score(y_residual_train, residual_train_pred):.3f}, 测试R²={r2_score(y_residual_test, residual_test_pred):.3f}')
ax9.legend()
ax9.grid(True, alpha=0.3)

# Residual预测误差时间序列
ax10 = axes3[1, 0]
train_errors_res = y_residual_train - residual_train_pred
test_errors_res = y_residual_test - residual_test_pred
ax10.plot(residual_train_data.index, train_errors_res, 'b-', linewidth=1, alpha=0.5, label='训练集误差')
ax10.plot(residual_test_data.index, test_errors_res, 'ro-', markersize=5, linewidth=1.5, label='测试集误差')
ax10.axhline(y=0, color='black', linestyle='--', alpha=0.7)
ax10.axhline(y=test_errors_res.std(), color='red', linestyle=':', alpha=0.5, label='±1σ')
ax10.axhline(y=-test_errors_res.std(), color='red', linestyle=':', alpha=0.5)
ax10.set_ylabel('预测误差')
ax10.set_title('Residual预测误差分析')
ax10.legend()
ax10.grid(True, alpha=0.3)

# Residual特征重要性
ax11 = axes3[1, 1]
# 只显示前10个最重要的特征
top_features = residual_feature_importance.head(10).sort_values('importance', ascending=True)
ax11.barh(range(len(top_features)), top_features['importance'], color='purple')
ax11.set_yticks(range(len(top_features)))
ax11.set_yticklabels(top_features['feature'])
ax11.set_xlabel('特征重要性')
ax11.set_title('Residual模型Top10特征重要性')
ax11.grid(True, alpha=0.3, axis='x')

plt.tight_layout()
plt.show()

print(f"\n✅ 版本B完成！使用产能(productivity_lag1)的融合预测模型已准备就绪!")